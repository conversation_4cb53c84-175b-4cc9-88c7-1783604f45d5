// Code generated by 'ccgo stdlib/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o stdlib/stdlib_darwin_amd64.go -pkgname stdlib', DO NOT EDIT.

package stdlib

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BIG_ENDIAN                                = 4321                 // endian.h:94:1:
	BUS_ADRALN                                = 1                    // signal.h:241:1:
	BUS_ADRERR                                = 2                    // signal.h:242:1:
	BUS_NOOP                                  = 0                    // signal.h:239:1:
	BUS_OBJERR                                = 3                    // signal.h:243:1:
	BYTE_ORDER                                = 1234                 // endian.h:97:1:
	CLD_CONTINUED                             = 6                    // signal.h:258:1:
	CLD_DUMPED                                = 3                    // signal.h:255:1:
	CLD_EXITED                                = 1                    // signal.h:253:1:
	CLD_KILLED                                = 2                    // signal.h:254:1:
	CLD_NOOP                                  = 0                    // signal.h:251:1:
	CLD_STOPPED                               = 5                    // signal.h:257:1:
	CLD_TRAPPED                               = 4                    // signal.h:256:1:
	CPUMON_MAKE_FATAL                         = 0x1000               // resource.h:393:1:
	EXIT_FAILURE                              = 1                    // stdlib.h:102:1:
	EXIT_SUCCESS                              = 0                    // stdlib.h:103:1:
	FOOTPRINT_INTERVAL_RESET                  = 0x1                  // resource.h:398:1:
	FPE_FLTDIV                                = 1                    // signal.h:221:1:
	FPE_FLTINV                                = 5                    // signal.h:225:1:
	FPE_FLTOVF                                = 2                    // signal.h:222:1:
	FPE_FLTRES                                = 4                    // signal.h:224:1:
	FPE_FLTSUB                                = 6                    // signal.h:226:1:
	FPE_FLTUND                                = 3                    // signal.h:223:1:
	FPE_INTDIV                                = 7                    // signal.h:227:1:
	FPE_INTOVF                                = 8                    // signal.h:228:1:
	FPE_NOOP                                  = 0                    // signal.h:219:1:
	FP_CHOP                                   = 3                    // _structs.h:112:1:
	FP_PREC_24B                               = 0                    // _structs.h:103:1:
	FP_PREC_53B                               = 2                    // _structs.h:104:1:
	FP_PREC_64B                               = 3                    // _structs.h:105:1:
	FP_RND_DOWN                               = 1                    // _structs.h:110:1:
	FP_RND_NEAR                               = 0                    // _structs.h:109:1:
	FP_RND_UP                                 = 2                    // _structs.h:111:1:
	FP_STATE_BYTES                            = 512                  // _structs.h:276:1:
	ILL_BADSTK                                = 8                    // signal.h:215:1:
	ILL_COPROC                                = 7                    // signal.h:214:1:
	ILL_ILLADR                                = 5                    // signal.h:212:1:
	ILL_ILLOPC                                = 1                    // signal.h:208:1:
	ILL_ILLOPN                                = 4                    // signal.h:211:1:
	ILL_ILLTRP                                = 2                    // signal.h:209:1:
	ILL_NOOP                                  = 0                    // signal.h:206:1:
	ILL_PRVOPC                                = 3                    // signal.h:210:1:
	ILL_PRVREG                                = 6                    // signal.h:213:1:
	INT16_MAX                                 = 32767                // stdint.h:599:1:
	INT16_MIN                                 = -32768               // stdint.h:600:1:
	INT32_MAX                                 = 2147483647           // stdint.h:555:1:
	INT32_MIN                                 = -2147483648          // stdint.h:556:1:
	INT64_MAX                                 = 9223372036854775807  // stdint.h:461:1:
	INT64_MIN                                 = -9223372036854775808 // stdint.h:462:1:
	INT8_MAX                                  = 127                  // stdint.h:621:1:
	INT8_MIN                                  = -128                 // stdint.h:622:1:
	INTMAX_MAX                                = 9223372036854775807  // stdint.h:663:1:
	INTMAX_MIN                                = -9223372036854775808 // stdint.h:662:1:
	INTPTR_MAX                                = 9223372036854775807  // stdint.h:649:1:
	INTPTR_MIN                                = -9223372036854775808 // stdint.h:648:1:
	INT_FAST16_MAX                            = 32767                // stdint.h:615:1:
	INT_FAST16_MIN                            = -32768               // stdint.h:614:1:
	INT_FAST32_MAX                            = 2147483647           // stdint.h:574:1:
	INT_FAST32_MIN                            = -2147483648          // stdint.h:573:1:
	INT_FAST64_MAX                            = 9223372036854775807  // stdint.h:483:1:
	INT_FAST64_MIN                            = -9223372036854775808 // stdint.h:482:1:
	INT_FAST8_MAX                             = 127                  // stdint.h:634:1:
	INT_FAST8_MIN                             = -128                 // stdint.h:633:1:
	INT_LEAST16_MAX                           = 32767                // stdint.h:612:1:
	INT_LEAST16_MIN                           = -32768               // stdint.h:611:1:
	INT_LEAST32_MAX                           = 2147483647           // stdint.h:571:1:
	INT_LEAST32_MIN                           = -2147483648          // stdint.h:570:1:
	INT_LEAST64_MAX                           = 9223372036854775807  // stdint.h:480:1:
	INT_LEAST64_MIN                           = -9223372036854775808 // stdint.h:479:1:
	INT_LEAST8_MAX                            = 127                  // stdint.h:631:1:
	INT_LEAST8_MIN                            = -128                 // stdint.h:630:1:
	IOPOL_APPLICATION                         = 5                    // resource.h:427:1:
	IOPOL_ATIME_UPDATES_DEFAULT               = 0                    // resource.h:431:1:
	IOPOL_ATIME_UPDATES_OFF                   = 1                    // resource.h:432:1:
	IOPOL_DEFAULT                             = 0                    // resource.h:419:1:
	IOPOL_IMPORTANT                           = 1                    // resource.h:420:1:
	IOPOL_MATERIALIZE_DATALESS_FILES_DEFAULT  = 0                    // resource.h:434:1:
	IOPOL_MATERIALIZE_DATALESS_FILES_OFF      = 1                    // resource.h:435:1:
	IOPOL_MATERIALIZE_DATALESS_FILES_ON       = 2                    // resource.h:436:1:
	IOPOL_NORMAL                              = 1                    // resource.h:428:1:
	IOPOL_PASSIVE                             = 2                    // resource.h:421:1:
	IOPOL_SCOPE_DARWIN_BG                     = 2                    // resource.h:416:1:
	IOPOL_SCOPE_PROCESS                       = 0                    // resource.h:414:1:
	IOPOL_SCOPE_THREAD                        = 1                    // resource.h:415:1:
	IOPOL_STANDARD                            = 5                    // resource.h:424:1:
	IOPOL_THROTTLE                            = 3                    // resource.h:422:1:
	IOPOL_TYPE_DISK                           = 0                    // resource.h:408:1:
	IOPOL_TYPE_VFS_ATIME_UPDATES              = 2                    // resource.h:409:1:
	IOPOL_TYPE_VFS_MATERIALIZE_DATALESS_FILES = 3                    // resource.h:410:1:
	IOPOL_TYPE_VFS_STATFS_NO_DATA_VOLUME      = 4                    // resource.h:411:1:
	IOPOL_UTILITY                             = 4                    // resource.h:423:1:
	IOPOL_VFS_STATFS_FORCE_NO_DATA_VOLUME     = 1                    // resource.h:439:1:
	IOPOL_VFS_STATFS_NO_DATA_VOLUME_DEFAULT   = 0                    // resource.h:438:1:
	LITTLE_ENDIAN                             = 1234                 // endian.h:93:1:
	MINSIGSTKSZ                               = 32768                // signal.h:340:1:
	NSIG                                      = 32                   // signal.h:79:1:
	PDP_ENDIAN                                = 3412                 // endian.h:95:1:
	POLL_ERR                                  = 4                    // signal.h:264:1:
	POLL_HUP                                  = 6                    // signal.h:266:1:
	POLL_IN                                   = 1                    // signal.h:261:1:
	POLL_MSG                                  = 3                    // signal.h:263:1:
	POLL_OUT                                  = 2                    // signal.h:262:1:
	POLL_PRI                                  = 5                    // signal.h:265:1:
	PRIO_DARWIN_BG                            = 0x1000               // resource.h:120:1:
	PRIO_DARWIN_NONUI                         = 0x1001               // resource.h:126:1:
	PRIO_DARWIN_PROCESS                       = 4                    // resource.h:106:1:
	PRIO_DARWIN_THREAD                        = 3                    // resource.h:105:1:
	PRIO_MAX                                  = 20                   // resource.h:113:1:
	PRIO_MIN                                  = -20                  // resource.h:112:1:
	PRIO_PGRP                                 = 1                    // resource.h:101:1:
	PRIO_PROCESS                              = 0                    // resource.h:100:1:
	PRIO_USER                                 = 2                    // resource.h:102:1:
	PTRDIFF_MAX                               = 9223372036854775807  // stdint.h:652:1:
	PTRDIFF_MIN                               = -9223372036854775808 // stdint.h:651:1:
	RAND_MAX                                  = 0x7fffffff           // stdlib.h:105:1:
	RLIMIT_AS                                 = 5                    // resource.h:349:1:
	RLIMIT_CORE                               = 4                    // resource.h:348:1:
	RLIMIT_CPU                                = 0                    // resource.h:344:1:
	RLIMIT_CPU_USAGE_MONITOR                  = 0x2                  // resource.h:377:1:
	RLIMIT_DATA                               = 2                    // resource.h:346:1:
	RLIMIT_FOOTPRINT_INTERVAL                 = 0x4                  // resource.h:379:1:
	RLIMIT_FSIZE                              = 1                    // resource.h:345:1:
	RLIMIT_MEMLOCK                            = 6                    // resource.h:352:1:
	RLIMIT_NOFILE                             = 8                    // resource.h:355:1:
	RLIMIT_NPROC                              = 7                    // resource.h:353:1:
	RLIMIT_RSS                                = 5                    // resource.h:351:1:
	RLIMIT_STACK                              = 3                    // resource.h:347:1:
	RLIMIT_THREAD_CPULIMITS                   = 0x3                  // resource.h:378:1:
	RLIMIT_WAKEUPS_MONITOR                    = 0x1                  // resource.h:376:1:
	RLIM_NLIMITS                              = 9                    // resource.h:357:1:
	RSIZE_MAX                                 = 9223372036854775807  // stdint.h:658:1:
	RUSAGE_CHILDREN                           = -1                   // resource.h:141:1:
	RUSAGE_INFO_CURRENT                       = 4                    // resource.h:191:1:
	RUSAGE_INFO_V0                            = 0                    // resource.h:186:1:
	RUSAGE_INFO_V1                            = 1                    // resource.h:187:1:
	RUSAGE_INFO_V2                            = 2                    // resource.h:188:1:
	RUSAGE_INFO_V3                            = 3                    // resource.h:189:1:
	RUSAGE_INFO_V4                            = 4                    // resource.h:190:1:
	RUSAGE_SELF                               = 0                    // resource.h:140:1:
	SA_64REGSET                               = 0x0200               // signal.h:308:1:
	SA_NOCLDSTOP                              = 0x0008               // signal.h:301:1:
	SA_NOCLDWAIT                              = 0x0020               // signal.h:303:1:
	SA_NODEFER                                = 0x0010               // signal.h:302:1:
	SA_ONSTACK                                = 0x0001               // signal.h:298:1:
	SA_RESETHAND                              = 0x0004               // signal.h:300:1:
	SA_RESTART                                = 0x0002               // signal.h:299:1:
	SA_SIGINFO                                = 0x0040               // signal.h:304:1:
	SA_USERSPACE_MASK                         = 127                  // signal.h:314:1:
	SA_USERTRAMP                              = 0x0100               // signal.h:306:1:
	SEGV_ACCERR                               = 2                    // signal.h:235:1:
	SEGV_MAPERR                               = 1                    // signal.h:234:1:
	SEGV_NOOP                                 = 0                    // signal.h:232:1:
	SIGABRT                                   = 6                    // signal.h:89:1:
	SIGALRM                                   = 14                   // signal.h:102:1:
	SIGBUS                                    = 10                   // signal.h:98:1:
	SIGCHLD                                   = 20                   // signal.h:108:1:
	SIGCONT                                   = 19                   // signal.h:107:1:
	SIGEMT                                    = 7                    // signal.h:94:1:
	SIGEV_NONE                                = 0                    // signal.h:164:1:
	SIGEV_SIGNAL                              = 1                    // signal.h:165:1:
	SIGEV_THREAD                              = 3                    // signal.h:166:1:
	SIGFPE                                    = 8                    // signal.h:96:1:
	SIGHUP                                    = 1                    // signal.h:84:1:
	SIGILL                                    = 4                    // signal.h:87:1:
	SIGINFO                                   = 29                   // signal.h:120:1:
	SIGINT                                    = 2                    // signal.h:85:1:
	SIGIO                                     = 23                   // signal.h:112:1:
	SIGIOT                                    = 6                    // signal.h:93:1:
	SIGKILL                                   = 9                    // signal.h:97:1:
	SIGPIPE                                   = 13                   // signal.h:101:1:
	SIGPROF                                   = 27                   // signal.h:117:1:
	SIGQUIT                                   = 3                    // signal.h:86:1:
	SIGSEGV                                   = 11                   // signal.h:99:1:
	SIGSTKSZ                                  = 131072               // signal.h:341:1:
	SIGSTOP                                   = 17                   // signal.h:105:1:
	SIGSYS                                    = 12                   // signal.h:100:1:
	SIGTERM                                   = 15                   // signal.h:103:1:
	SIGTRAP                                   = 5                    // signal.h:88:1:
	SIGTSTP                                   = 18                   // signal.h:106:1:
	SIGTTIN                                   = 21                   // signal.h:109:1:
	SIGTTOU                                   = 22                   // signal.h:110:1:
	SIGURG                                    = 16                   // signal.h:104:1:
	SIGUSR1                                   = 30                   // signal.h:122:1:
	SIGUSR2                                   = 31                   // signal.h:123:1:
	SIGVTALRM                                 = 26                   // signal.h:116:1:
	SIGWINCH                                  = 28                   // signal.h:119:1:
	SIGXCPU                                   = 24                   // signal.h:114:1:
	SIGXFSZ                                   = 25                   // signal.h:115:1:
	SIG_ATOMIC_MAX                            = 2147483647           // stdint.h:668:1:
	SIG_ATOMIC_MIN                            = -2147483648          // stdint.h:667:1:
	SIG_BLOCK                                 = 1                    // signal.h:319:1:
	SIG_SETMASK                               = 3                    // signal.h:321:1:
	SIG_UNBLOCK                               = 2                    // signal.h:320:1:
	SIZE_MAX                                  = 18446744073709551615 // stdint.h:653:1:
	SI_ASYNCIO                                = 0x10004              // signal.h:327:1:
	SI_MESGQ                                  = 0x10005              // signal.h:328:1:
	SI_QUEUE                                  = 0x10002              // signal.h:325:1:
	SI_TIMER                                  = 0x10003              // signal.h:326:1:
	SI_USER                                   = 0x10001              // signal.h:324:1:
	SS_DISABLE                                = 0x0004               // signal.h:339:1:
	SS_ONSTACK                                = 0x0001               // signal.h:338:1:
	SV_INTERRUPT                              = 2                    // signal.h:355:1:
	SV_NOCLDSTOP                              = 8                    // signal.h:358:1:
	SV_NODEFER                                = 16                   // signal.h:357:1:
	SV_ONSTACK                                = 1                    // signal.h:354:1:
	SV_RESETHAND                              = 4                    // signal.h:356:1:
	SV_SIGINFO                                = 64                   // signal.h:359:1:
	TRAP_BRKPT                                = 1                    // signal.h:246:1:
	TRAP_TRACE                                = 2                    // signal.h:247:1:
	UINT16_MAX                                = 65535                // stdint.h:601:1:
	UINT32_MAX                                = 4294967295           // stdint.h:557:1:
	UINT64_MAX                                = 18446744073709551615 // stdint.h:463:1:
	UINT8_MAX                                 = 255                  // stdint.h:623:1:
	UINTMAX_MAX                               = 18446744073709551615 // stdint.h:664:1:
	UINTPTR_MAX                               = 18446744073709551615 // stdint.h:650:1:
	UINT_FAST16_MAX                           = 65535                // stdint.h:616:1:
	UINT_FAST32_MAX                           = 4294967295           // stdint.h:575:1:
	UINT_FAST64_MAX                           = 18446744073709551615 // stdint.h:484:1:
	UINT_FAST8_MAX                            = 255                  // stdint.h:635:1:
	UINT_LEAST16_MAX                          = 65535                // stdint.h:613:1:
	UINT_LEAST32_MAX                          = 4294967295           // stdint.h:572:1:
	UINT_LEAST64_MAX                          = 18446744073709551615 // stdint.h:481:1:
	UINT_LEAST8_MAX                           = 255                  // stdint.h:632:1:
	WAIT_ANY                                  = -1                   // wait.h:183:1:
	WAIT_MYPGRP                               = 0                    // wait.h:184:1:
	WAKEMON_DISABLE                           = 0x02                 // resource.h:385:1:
	WAKEMON_ENABLE                            = 0x01                 // resource.h:384:1:
	WAKEMON_GET_PARAMS                        = 0x04                 // resource.h:386:1:
	WAKEMON_MAKE_FATAL                        = 0x10                 // resource.h:388:1:
	WAKEMON_SET_DEFAULTS                      = 0x08                 // resource.h:387:1:
	WCHAR_MAX                                 = 2147483647           // stdint.h:678:1:
	WCHAR_MIN                                 = -2147483648          // stdint.h:682:1:
	WCONTINUED                                = 0x00000010           // wait.h:173:1:
	WCOREFLAG                                 = 0200                 // wait.h:132:1:
	WEXITED                                   = 0x00000004           // wait.h:168:1:
	WINT_MAX                                  = 2147483647           // stdint.h:674:1:
	WINT_MIN                                  = -2147483648          // stdint.h:673:1:
	WNOHANG                                   = 0x00000001           // wait.h:121:1:
	WNOWAIT                                   = 0x00000020           // wait.h:174:1:
	WSTOPPED                                  = 0x00000008           // wait.h:171:1:
	WUNTRACED                                 = 0x00000002           // wait.h:122:1:
	X_ALLOCA_H_                               = 0                    // alloca.h:25:1:
	X_BSD_I386__TYPES_H_                      = 0                    // _types.h:29:1:
	X_BSD_MACHINE_ENDIAN_H_                   = 0                    // endian.h:32:1:
	X_BSD_MACHINE_SIGNAL_H_                   = 0                    // signal.h:29:1:
	X_BSD_MACHINE_TYPES_H_                    = 0                    // types.h:32:1:
	X_BSD_MACHINE__TYPES_H_                   = 0                    // _types.h:29:1:
	X_CDEFS_H_                                = 0                    // cdefs.h:68:1:
	X_CT_RUNE_T                               = 0                    // _ct_rune_t.h:30:1:
	X_DARWIN_FEATURE_64_BIT_INODE             = 1                    // cdefs.h:745:1:
	X_DARWIN_FEATURE_ONLY_UNIX_CONFORMANCE    = 1                    // cdefs.h:771:1:
	X_DARWIN_FEATURE_UNIX_CONFORMANCE         = 3                    // cdefs.h:779:1:
	X_DEV_T                                   = 0                    // _dev_t.h:29:1:
	X_FILE_OFFSET_BITS                        = 64                   // <builtin>:25:1:
	X_FORTIFY_SOURCE                          = 2                    // _types.h:65:1:
	X_I386_SIGNAL_H_                          = 1                    // signal.h:34:1:
	X_I386__ENDIAN_H_                         = 0                    // endian.h:67:1:
	X_ID_T                                    = 0                    // _id_t.h:29:1:
	X_INT16_T                                 = 0                    // _int16_t.h:29:1:
	X_INT32_T                                 = 0                    // _int32_t.h:29:1:
	X_INT64_T                                 = 0                    // _int64_t.h:29:1:
	X_INT8_T                                  = 0                    // _int8_t.h:29:1:
	X_INTPTR_T                                = 0                    // _intptr_t.h:29:1:
	X_LP64                                    = 1                    // <predefined>:1:1:
	X_MACHTYPES_H_                            = 0                    // types.h:67:1:
	X_MACH_I386__STRUCTS_H_                   = 0                    // _structs.h:33:1:
	X_MACH_MACHINE__STRUCTS_H_                = 0                    // _structs.h:30:1:
	X_MALLOC_UNDERSCORE_MALLOC_H_             = 0                    // _malloc.h:25:1:
	X_MCONTEXT_T                              = 0                    // _mcontext.h:202:1:
	X_MODE_T                                  = 0                    // _mode_t.h:29:1:
	X_Nonnull                                 = 0                    // cdefs.h:243:1:
	X_Null_unspecified                        = 0                    // cdefs.h:246:1:
	X_Nullable                                = 0                    // cdefs.h:240:1:
	X_OS__OSBYTEORDERI386_H                   = 0                    // _OSByteOrder.h:30:1:
	X_OS__OSBYTEORDER_H                       = 0                    // _OSByteOrder.h:30:1:
	X_PID_T                                   = 0                    // _pid_t.h:29:1:
	X_PTHREAD_ATTR_T                          = 0                    // _pthread_attr_t.h:29:1:
	X_QUAD_HIGHWORD                           = 1                    // endian.h:78:1:
	X_QUAD_LOWWORD                            = 0                    // endian.h:79:1:
	X_RLIMIT_POSIX_FLAG                       = 0x1000               // resource.h:359:1:
	X_RUNE_T                                  = 0                    // _rune_t.h:29:1:
	X_SIGSET_T                                = 0                    // _sigset_t.h:29:1:
	X_SIZE_T                                  = 0                    // _size_t.h:29:1:
	X_STDLIB_H_                               = 0                    // stdlib.h:59:1:
	X_SYS_RESOURCE_H_                         = 0                    // resource.h:65:1:
	X_SYS_SIGNAL_H_                           = 0                    // signal.h:70:1:
	X_SYS_WAIT_H_                             = 0                    // wait.h:65:1:
	X_SYS__ENDIAN_H_                          = 0                    // _endian.h:91:1:
	X_SYS__PTHREAD_TYPES_H_                   = 0                    // _pthread_types.h:30:1:
	X_SYS__TYPES_H_                           = 0                    // _types.h:30:1:
	X_UID_T                                   = 0                    // _uid_t.h:29:1:
	X_UINT32_T                                = 0                    // _uint32_t.h:30:1:
	X_UINTPTR_T                               = 0                    // _uintptr_t.h:29:1:
	X_U_INT16_T                               = 0                    // _u_int16_t.h:29:1:
	X_U_INT32_T                               = 0                    // _u_int32_t.h:29:1:
	X_U_INT64_T                               = 0                    // _u_int64_t.h:29:1:
	X_U_INT8_T                                = 0                    // _u_int8_t.h:29:1:
	X_WCHAR_T                                 = 0                    // _wchar_t.h:32:1:
	X_WSTOPPED                                = 0177                 // wait.h:137:1:
)

// Copyright (c) 2000 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 1995 NeXT Computer, Inc. All Rights Reserved
// Copyright (c) 1982, 1986, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)wait.h	8.2 (Berkeley) 7/10/94

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// This file holds definitions relevent to the wait4 system call
// and the alternate interfaces that use it (wait, wait3, waitpid).

// [XSI] The type idtype_t shall be defined as an enumeration type whose
// possible values shall include at least P_ALL, P_PID, and P_PGID.
const ( /* wait.h:79:1: */
	P_ALL  = 0
	P_PID  = 1
	P_PGID = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

var X__darwin_check_fd_set_overflow uintptr /* <builtin>:146:5: */

// Copyright (c) 2000, 2002 - 2008 Apple Inc. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@
// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)stdlib.h	8.5 (Berkeley) 5/19/95

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

//
//     These macros are for use in OS header files. They enable function prototypes
//     and Objective-C methods to be tagged with the OS version in which they
//     were first available; and, if applicable, the OS version in which they
//     became deprecated.
//
//     The desktop Mac OS X and iOS each have different version numbers.
//     The __OSX_AVAILABLE_STARTING() macro allows you to specify both the desktop
//     and iOS version numbers.  For instance:
//         __OSX_AVAILABLE_STARTING(__MAC_10_2,__IPHONE_2_0)
//     means the function/method was first available on Mac OS X 10.2 on the desktop
//     and first available in iOS 2.0 on the iPhone.
//
//     If a function is available on one platform, but not the other a _NA (not
//     applicable) parameter is used.  For instance:
//             __OSX_AVAILABLE_STARTING(__MAC_10_3,__IPHONE_NA)
//     means that the function/method was first available on Mac OS X 10.3, and it
//     currently not implemented on the iPhone.
//
//     At some point, a function/method may be deprecated.  That means Apple
//     recommends applications stop using the function, either because there is a
//     better replacement or the functionality is being phased out.  Deprecated
//     functions/methods can be tagged with a __OSX_AVAILABLE_BUT_DEPRECATED()
//     macro which specifies the OS version where the function became available
//     as well as the OS version in which it became deprecated.  For instance:
//         __OSX_AVAILABLE_BUT_DEPRECATED(__MAC_10_0,__MAC_10_5,__IPHONE_NA,__IPHONE_NA)
//     means that the function/method was introduced in Mac OS X 10.0, then
//     became deprecated beginning in Mac OS X 10.5.  On iOS the function
//     has never been available.
//
//     For these macros to function properly, a program must specify the OS version range
//     it is targeting.  The min OS version is specified as an option to the compiler:
//     -mmacosx-version-min=10.x when building for Mac OS X, and -miphoneos-version-min=y.z
//     when building for the iPhone.  The upper bound for the OS version is rarely needed,
//     but it can be set on the command line via: -D__MAC_OS_X_VERSION_MAX_ALLOWED=10x0 for
//     Mac OS X and __IPHONE_OS_VERSION_MAX_ALLOWED = y0z00 for iOS.
//
//     Examples:
//
//         A function available in Mac OS X 10.5 and later, but not on the phone:
//
//             extern void mymacfunc() __OSX_AVAILABLE_STARTING(__MAC_10_5,__IPHONE_NA);
//
//
//         An Objective-C method in Mac OS X 10.5 and later, but not on the phone:
//
//             @interface MyClass : NSObject
//             -(void) mymacmethod __OSX_AVAILABLE_STARTING(__MAC_10_5,__IPHONE_NA);
//             @end
//
//
//         An enum available on the phone, but not available on Mac OS X:
//
//             #if __IPHONE_OS_VERSION_MIN_REQUIRED
//                 enum { myEnum = 1 };
//             #endif
//            Note: this works when targeting the Mac OS X platform because
//            __IPHONE_OS_VERSION_MIN_REQUIRED is undefined which evaluates to zero.
//
//
//         An enum with values added in different iPhoneOS versions:
//
// 			enum {
// 			    myX  = 1,	// Usable on iPhoneOS 2.1 and later
// 			    myY  = 2,	// Usable on iPhoneOS 3.0 and later
// 			    myZ  = 3,	// Usable on iPhoneOS 3.0 and later
// 				...
// 		      Note: you do not want to use #if with enumeration values
// 			  when a client needs to see all values at compile time
// 			  and use runtime logic to only use the viable values.
//
//
//     It is also possible to use the *_VERSION_MIN_REQUIRED in source code to make one
//     source base that can be compiled to target a range of OS versions.  It is best
//     to not use the _MAC_* and __IPHONE_* macros for comparisons, but rather their values.
//     That is because you might get compiled on an old OS that does not define a later
//     OS version macro, and in the C preprocessor undefined values evaluate to zero
//     in expresssions, which could cause the #if expression to evaluate in an unexpected
//     way.
//
//         #ifdef __MAC_OS_X_VERSION_MIN_REQUIRED
//             // code only compiled when targeting Mac OS X and not iPhone
//             // note use of 1050 instead of __MAC_10_5
//             #if __MAC_OS_X_VERSION_MIN_REQUIRED < 1050
//                 // code in here might run on pre-Leopard OS
//             #else
//                 // code here can assume Leopard or later
//             #endif
//         #endif
//
//

// __API_TO_BE_DEPRECATED is used as a version number in API that will be deprecated
// in an upcoming release. This soft deprecation is an intermediate step before formal
// deprecation to notify developers about the API before compiler warnings are generated.
// You can find all places in your code that use soft deprecated API by redefining the
// value of this macro to your current minimum deployment target, for example:
// (macOS)
//   clang -D__API_TO_BE_DEPRECATED=10.12 <other compiler flags>
// (iOS)
//   clang -D__API_TO_BE_DEPRECATED=11.0 <other compiler flags>

// __MAC_NA is not defined to a value but is uses as a token by macros to indicate that the API is unavailable

// __IPHONE_NA is not defined to a value but is uses as a token by macros to indicate that the API is unavailable

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

//
//     File:       AvailabilityInternal.h
//
//     Contains:   implementation details of __OSX_AVAILABLE_* macros from <Availability.h>
//

// compiler for Mac OS X sets __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__

// make sure a default max version is set

//
//  Macros for defining which versions/platform a given symbol can be used.
//
//  @see http://clang.llvm.org/docs/AttributeReference.html#availability
//

// Evaluate to nothing for compilers that don't support clang language extensions.

// Swift compiler version
// Allows for project-agnostic “epochs” for frameworks imported into Swift via the Clang importer, like #if _compiler_version for Swift
// Example:
//
//  #if __swift_compiler_version_at_least(800, 2, 20)
//  - (nonnull NSString *)description;
//  #else
//  - (NSString *)description;
//  #endif

// If __SPI_AVAILABLE has not been defined elsewhere, disable it.

// for use to document app extension usage

// for use marking APIs available info for Mac OSX

// for use marking APIs available info for iOS

// for use marking APIs available info for tvOS

// for use marking APIs available info for Watch OS

// for use marking APIs unavailable for swift

//
//  Macros for defining which versions/platform a given symbol can be used.
//
//  @see http://clang.llvm.org/docs/AttributeReference.html#availability
//
//  * Note that these macros are only compatible with clang compilers that
//  * support the following target selection options:
//  *
//  * -mmacosx-version-min
//  * -miphoneos-version-min
//  * -mwatchos-version-min
//  * -mtvos-version-min
//

// Evaluate to nothing for compilers that don't support clang language extensions.

// If SPI decorations have not been defined elsewhere, disable them.

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// This SDK is designed to work with clang and specific versions of
// gcc >= 4.0 with Apple's patch sets

// Compatibility with compilers and environments that don't support compiler
// feature checking function-like macros.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  __CONCAT can also concatenate double-quoted
// strings produced by the __STRING macro, but this only works with ANSI C.

// In non-ANSI C environments, new programs will want ANSI-only C keywords
// deleted from the program and old programs will want them left alone.
// When using a compiler other than gcc, programs using the ANSI C keywords
// const, inline etc. as normal identifiers should define -DNO_ANSI_KEYWORDS.
// When using "gcc -traditional", we assume that this is the intent; if
// __GNUC__ is defined but __STDC__ is not, we leave the new keywords alone.

// __unused denotes variables and functions that may not be used, preventing
// the compiler from warning about it if not used.

// __used forces variables and functions to be included even if it appears
// to the compiler that they are not used (and would thust be discarded).

// __cold marks code used for debugging or that is rarely taken
// and tells the compiler to optimize for size and outline code.

// __deprecated causes the compiler to produce a warning when encountering
// code using the deprecated functionality.
// __deprecated_msg() does the same, and compilers that support it will print
// a message along with the deprecation warning.
// This may require turning on such warning with the -Wdeprecated flag.
// __deprecated_enum_msg() should be used on enums, and compilers that support
// it will print the deprecation warning.
// __kpi_deprecated() specifically indicates deprecation of kernel programming
// interfaces in Kernel.framework used by KEXTs.

// __unavailable causes the compiler to error out when encountering
// code using the tagged function of variable.

// Delete pseudo-keywords wherever they are not available or needed.

// We use `__restrict' as a way to define the `restrict' type qualifier
// without disturbing older software that is unaware of C99 keywords.

// Compatibility with compilers and environments that don't support the
// nullability feature.

// __disable_tail_calls causes the compiler to not perform tail call
// optimization inside the marked function.

// __not_tail_called causes the compiler to prevent tail call optimization
// on statically bound calls to the function.  It has no effect on indirect
// calls.  Virtual functions, objective-c methods, and functions marked as
// "always_inline" cannot be marked as __not_tail_called.

// __result_use_check warns callers of a function that not using the function
// return value is a bug, i.e. dismissing malloc() return value results in a
// memory leak.

// __swift_unavailable causes the compiler to mark a symbol as specifically
// unavailable in Swift, regardless of any other availability in C.

// __abortlike is the attribute to put on functions like abort() that are
// typically used to mark assertions. These optimize the codegen
// for outlining while still maintaining debugability.

// Declaring inline functions within headers is error-prone due to differences
// across various versions of the C language and extensions.  __header_inline
// can be used to declare inline functions within system headers.  In cases
// where you want to force inlining instead of letting the compiler make
// the decision, you can use __header_always_inline.
//
// Be aware that using inline for functions which compilers may also provide
// builtins can behave differently under various compilers.  If you intend to
// provide an inline version of such a function, you may want to use a macro
// instead.
//
// The check for !__GNUC__ || __clang__ is because gcc doesn't correctly
// support c99 inline in some cases:
// http://gcc.gnu.org/bugzilla/show_bug.cgi?id=55965

// Compiler-dependent macros that bracket portions of code where the
// "-Wunreachable-code" warning should be ignored. Please use sparingly.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly.  Functions declared
// with these attributes will cause compilation warnings if there is a
// mismatch between the format string and subsequent function parameter
// types.

// Source compatibility only, ID string not emitted in object file

// __alloc_size can be used to label function arguments that represent the
// size of memory that the function allocates and returns. The one-argument
// form labels a single argument that gives the allocation size (where the
// arguments are numbered from 1):
//
// void	*malloc(size_t __size) __alloc_size(1);
//
// The two-argument form handles the case where the size is calculated as the
// product of two arguments:
//
// void	*calloc(size_t __count, size_t __size) __alloc_size(1,2);

// COMPILATION ENVIRONMENTS -- see compat(5) for additional detail
//
// DEFAULT	By default newly complied code will get POSIX APIs plus
//		Apple API extensions in scope.
//
//		Most users will use this compilation environment to avoid
//		behavioral differences between 32 and 64 bit code.
//
// LEGACY	Defining _NONSTD_SOURCE will get pre-POSIX APIs plus Apple
//		API extensions in scope.
//
//		This is generally equivalent to the Tiger release compilation
//		environment, except that it cannot be applied to 64 bit code;
//		its use is discouraged.
//
//		We expect this environment to be deprecated in the future.
//
// STRICT	Defining _POSIX_C_SOURCE or _XOPEN_SOURCE restricts the
//		available APIs to exactly the set of APIs defined by the
//		corresponding standard, based on the value defined.
//
//		A correct, portable definition for _POSIX_C_SOURCE is 200112L.
//		A correct, portable definition for _XOPEN_SOURCE is 600L.
//
//		Apple API extensions are not visible in this environment,
//		which can cause Apple specific code to fail to compile,
//		or behave incorrectly if prototypes are not in scope or
//		warnings about missing prototypes are not enabled or ignored.
//
// In any compilation environment, for correct symbol resolution to occur,
// function prototypes must be in scope.  It is recommended that all Apple
// tools users add either the "-Wall" or "-Wimplicit-function-declaration"
// compiler flags to their projects to be warned when a function is being
// used without a prototype in scope.

// These settings are particular to each product.
// Platform: MacOSX
// #undef __DARWIN_ONLY_UNIX_CONFORMANCE (automatically set for 64-bit)

// The __DARWIN_ALIAS macros are used to do symbol renaming; they allow
// legacy code to use the old symbol, thus maintaining binary compatibility
// while new code can use a standards compliant version of the same function.
//
// __DARWIN_ALIAS is used by itself if the function signature has not
// changed, it is used along with a #ifdef check for __DARWIN_UNIX03
// if the signature has changed.  Because the __LP64__ environment
// only supports UNIX03 semantics it causes __DARWIN_UNIX03 to be
// defined, but causes __DARWIN_ALIAS to do no symbol mangling.
//
// As a special case, when XCode is used to target a specific version of the
// OS, the manifest constant __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__
// will be defined by the compiler, with the digits representing major version
// time 100 + minor version times 10 (e.g. 10.5 := 1050).  If we are targeting
// pre-10.5, and it is the default compilation environment, revert the
// compilation environment to pre-__DARWIN_UNIX03.

// symbol suffixes used for symbol versioning

// symbol versioning macros

// symbol release macros
// Copyright (c) 2010 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.  This permits us to convert values for feature
// testing, as necessary, using only _POSIX_C_SOURCE.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1L		1003.1-1990
//  _POSIX_C_SOURCE == 2L		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309L		1003.1b-1993
//  _POSIX_C_SOURCE == 199506L		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112L		1003.1-2001
//  _POSIX_C_SOURCE == 200809L		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1L.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2L.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.

// POSIX C deprecation macros
// Copyright (c) 2010 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Set a single macro which will always be defined and can be used to determine
// the appropriate namespace.  For POSIX, these values will correspond to
// _POSIX_C_SOURCE value.  Currently there are two additional levels corresponding
// to ANSI (_ANSI_SOURCE) and Darwin extensions (_DARWIN_C_SOURCE)

// If the developer has neither requested a strict language mode nor a version
// of POSIX, turn on functionality provided by __STDC_WANT_LIB_EXT1__ as part
// of __DARWIN_C_FULL.

// long long is not supported in c89 (__STRICT_ANSI__), but g++ -ansi and
// c99 still want long longs.  While not perfect, we allow long longs for
// g++.

// ****************************************
//
//  Public darwin-specific feature macros
//

// _DARWIN_FEATURE_64_BIT_INODE indicates that the ino_t type is 64-bit, and
// structures modified for 64-bit inodes (like struct stat) will be used.

// _DARWIN_FEATURE_64_ONLY_BIT_INODE indicates that the ino_t type may only
// be 64-bit; there is no support for 32-bit ino_t when this macro is defined
// (and non-zero).  There is no struct stat64 either, as the regular
// struct stat will already be the 64-bit version.

// _DARWIN_FEATURE_ONLY_VERS_1050 indicates that only those APIs updated
// in 10.5 exists; no pre-10.5 variants are available.

// _DARWIN_FEATURE_ONLY_UNIX_CONFORMANCE indicates only UNIX conforming API
// are available (the legacy BSD APIs are not available)

// _DARWIN_FEATURE_UNIX_CONFORMANCE indicates whether UNIX conformance is on,
// and specifies the conformance level (3 is SUSv3)

// This macro casts away the qualifier from the variable
//
// Note: use at your own risk, removing qualifiers can result in
// catastrophic run-time failures.

// __XNU_PRIVATE_EXTERN is a linkage decoration indicating that a symbol can be
// used from other compilation units, but not other libraries or executables.

// Architecture validation for current SDK

// Similar to OS_ENUM/OS_CLOSED_ENUM/OS_OPTIONS/OS_CLOSED_OPTIONS
//
// This provides more advanced type checking on compilers supporting
// the proper extensions, even in C.

// Copyright (c) 2004, 2008, 2009 Apple Inc. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2003 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// This header file contains integer types.  It's intended to also contain
// flotaing point and other arithmetic types, as needed, later.

type X__int8_t = int8     /* _types.h:37:33 */
type X__uint8_t = uint8   /* _types.h:41:33 */
type X__int16_t = int16   /* _types.h:42:33 */
type X__uint16_t = uint16 /* _types.h:43:33 */
type X__int32_t = int32   /* _types.h:44:33 */
type X__uint32_t = uint32 /* _types.h:45:33 */
type X__int64_t = int64   /* _types.h:46:33 */
type X__uint64_t = uint64 /* _types.h:47:33 */

type X__darwin_intptr_t = int64   /* _types.h:49:33 */
type X__darwin_natural_t = uint32 /* _types.h:50:33 */

// The rune type below is declared to be an ``int'' instead of the more natural
// ``unsigned long'' or ``long''.  Two things are happening here.  It is not
// unsigned so that EOF (-1) can be naturally assigned to it and used.  Also,
// it looks like 10646 will be a 31 bit standard.  This means that if your
// ints cannot hold 32 bits, you will be in trouble.  The reason an int was
// chosen over a long is that the is*() and to*() routines take ints (says
// ANSI C), but they use __darwin_ct_rune_t instead of int.  By changing it
// here, you lose a bit of ANSI conformance, but your programs will still
// work.
//
// NOTE: rune_t is not covered by ANSI nor other standards, and should not
// be instantiated outside of lib/libc/locale.  Use wchar_t.  wchar_t and
// rune_t must be the same type.  Also wint_t must be no narrower than
// wchar_t, and should also be able to hold all members of the largest
// character set plus one extra value (WEOF). wint_t must be at least 16 bits.

type X__darwin_ct_rune_t = int32 /* _types.h:70:33 */ // ct_rune_t

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]int8
} /* _types.h:79:3 */

type X__darwin_mbstate_t = X__mbstate_t /* _types.h:81:33 */ // mbstate_t

type X__darwin_ptrdiff_t = int64 /* _types.h:84:33 */ // ptr1 - ptr2

type X__darwin_size_t = uint64 /* _types.h:92:33 */ // sizeof()

type X__darwin_va_list = X__builtin_va_list /* _types.h:98:33 */ // va_list

type X__darwin_wchar_t = int32 /* _types.h:104:33 */ // wchar_t

type X__darwin_rune_t = X__darwin_wchar_t /* _types.h:109:33 */ // rune_t

type X__darwin_wint_t = int32 /* _types.h:112:33 */ // wint_t

type X__darwin_clock_t = uint64        /* _types.h:117:33 */ // clock()
type X__darwin_socklen_t = X__uint32_t /* _types.h:118:33 */ // socklen_t (duh)
type X__darwin_ssize_t = int64         /* _types.h:119:33 */ // byte count or error
type X__darwin_time_t = int64          /* _types.h:120:33 */ // time()

// Type definitions; takes common type definitions that must be used
// in multiple header files due to [XSI], removes them from the system
// space, and puts them in the implementation space.

type X__darwin_blkcnt_t = X__int64_t                    /* _types.h:55:25 */ // total blocks
type X__darwin_blksize_t = X__int32_t                   /* _types.h:56:25 */ // preferred block size
type X__darwin_dev_t = X__int32_t                       /* _types.h:57:25 */ // dev_t
type X__darwin_fsblkcnt_t = uint32                      /* _types.h:58:25 */ // Used by statvfs and fstatvfs
type X__darwin_fsfilcnt_t = uint32                      /* _types.h:59:25 */ // Used by statvfs and fstatvfs
type X__darwin_gid_t = X__uint32_t                      /* _types.h:60:25 */ // [???] process and group IDs
type X__darwin_id_t = X__uint32_t                       /* _types.h:61:25 */ // [XSI] pid_t, uid_t, or gid_t
type X__darwin_ino64_t = X__uint64_t                    /* _types.h:62:25 */ // [???] Used for 64 bit inodes
type X__darwin_ino_t = X__darwin_ino64_t                /* _types.h:64:26 */ // [???] Used for inodes
type X__darwin_mach_port_name_t = X__darwin_natural_t   /* _types.h:68:28 */ // Used by mach
type X__darwin_mach_port_t = X__darwin_mach_port_name_t /* _types.h:69:35 */ // Used by mach
type X__darwin_mode_t = X__uint16_t                     /* _types.h:70:25 */ // [???] Some file attributes
type X__darwin_off_t = X__int64_t                       /* _types.h:71:25 */ // [???] Used for file sizes
type X__darwin_pid_t = X__int32_t                       /* _types.h:72:25 */ // [???] process and group IDs
type X__darwin_sigset_t = X__uint32_t                   /* _types.h:73:25 */ // [???] signal set
type X__darwin_suseconds_t = X__int32_t                 /* _types.h:74:25 */ // [???] microseconds
type X__darwin_uid_t = X__uint32_t                      /* _types.h:75:25 */ // [???] user IDs
type X__darwin_useconds_t = X__uint32_t                 /* _types.h:76:25 */ // [???] microseconds
type X__darwin_uuid_t = [16]uint8                       /* _types.h:77:25 */
type X__darwin_uuid_string_t = [37]int8                 /* _types.h:78:17 */

// Copyright (c) 2003-2013 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// pthread opaque structures

type X__darwin_pthread_handler_rec = struct {
	F__routine uintptr
	F__arg     uintptr
	F__next    uintptr
} /* _pthread_types.h:57:1 */

type X_opaque_pthread_attr_t = struct {
	F__sig    int64
	F__opaque [56]int8
} /* _pthread_types.h:63:1 */

type X_opaque_pthread_cond_t = struct {
	F__sig    int64
	F__opaque [40]int8
} /* _pthread_types.h:68:1 */

type X_opaque_pthread_condattr_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:73:1 */

type X_opaque_pthread_mutex_t = struct {
	F__sig    int64
	F__opaque [56]int8
} /* _pthread_types.h:78:1 */

type X_opaque_pthread_mutexattr_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:83:1 */

type X_opaque_pthread_once_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:88:1 */

type X_opaque_pthread_rwlock_t = struct {
	F__sig    int64
	F__opaque [192]int8
} /* _pthread_types.h:93:1 */

type X_opaque_pthread_rwlockattr_t = struct {
	F__sig    int64
	F__opaque [16]int8
} /* _pthread_types.h:98:1 */

type X_opaque_pthread_t = struct {
	F__sig           int64
	F__cleanup_stack uintptr
	F__opaque        [8176]int8
} /* _pthread_types.h:103:1 */

type X__darwin_pthread_attr_t = X_opaque_pthread_attr_t             /* _pthread_types.h:109:39 */
type X__darwin_pthread_cond_t = X_opaque_pthread_cond_t             /* _pthread_types.h:110:39 */
type X__darwin_pthread_condattr_t = X_opaque_pthread_condattr_t     /* _pthread_types.h:111:43 */
type X__darwin_pthread_key_t = uint64                               /* _pthread_types.h:112:23 */
type X__darwin_pthread_mutex_t = X_opaque_pthread_mutex_t           /* _pthread_types.h:113:40 */
type X__darwin_pthread_mutexattr_t = X_opaque_pthread_mutexattr_t   /* _pthread_types.h:114:44 */
type X__darwin_pthread_once_t = X_opaque_pthread_once_t             /* _pthread_types.h:115:39 */
type X__darwin_pthread_rwlock_t = X_opaque_pthread_rwlock_t         /* _pthread_types.h:116:41 */
type X__darwin_pthread_rwlockattr_t = X_opaque_pthread_rwlockattr_t /* _pthread_types.h:117:45 */
type X__darwin_pthread_t = uintptr                                  /* _pthread_types.h:118:34 */

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type X__darwin_nl_item = int32        /* _types.h:40:14 */
type X__darwin_wctrans_t = int32      /* _types.h:41:14 */
type X__darwin_wctype_t = X__uint32_t /* _types.h:43:20 */

// Copyright (c) 2000 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 1995 NeXT Computer, Inc. All Rights Reserved
// Copyright (c) 1982, 1986, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)wait.h	8.2 (Berkeley) 7/10/94

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// This file holds definitions relevent to the wait4 system call
// and the alternate interfaces that use it (wait, wait3, waitpid).

// [XSI] The type idtype_t shall be defined as an enumeration type whose
// possible values shall include at least P_ALL, P_PID, and P_PGID.
type Idtype_t = uint32 /* wait.h:83:3 */

// [XSI] The id_t and pid_t types shall be defined as described
// in <sys/types.h>
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Pid_t = X__darwin_pid_t /* _pid_t.h:31:31 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Id_t = X__darwin_id_t /* _id_t.h:31:25 */ // can hold pid_t, gid_t, or uid_t

// [XSI] The siginfo_t type shall be defined as described in <signal.h>
// [XSI] The rusage structure shall be defined as described in <sys/resource.h>
// [XSI] Inclusion of the <sys/wait.h> header may also make visible all
// symbols from <signal.h> and <sys/resource.h>
//
// NOTE:	This requirement is currently being satisfied by the direct
//		inclusion of <sys/signal.h> and <sys/resource.h>, below.
//
//		Software should not depend on the exposure of anything other
//		than the types siginfo_t and struct rusage as a result of
//		this inclusion.  If you depend on any types or manifest
//		values othe than siginfo_t and struct rusage from either of
//		those files, you should explicitly include them yourself, as
//		well, or in future releases your stware may not compile
//		without modification.
// Copyright (c) 2000-2006 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 1995 NeXT Computer, Inc. All Rights Reserved
// Copyright (c) 1982, 1986, 1989, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)signal.h	8.2 (Berkeley) 1/21/94

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2002 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2002 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 1992 NeXT Computer, Inc.
//

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

type Sig_atomic_t = int32 /* signal.h:39:13 */

// Language spec sez we must list exactly one parameter, even though we
// actually supply three.  Ugh!
// SIG_HOLD is chosen to avoid KERN_SIG_* values in <sys/signalvar.h>

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2002 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2017 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2004-2006 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// @OSF_COPYRIGHT@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.

// Copyright (c) 2000-2008 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

// Copyright (c) 2000-2003 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Basic integral types.  Omit the typedef if
// not possible for a machine/compiler combination.
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int8_t = int8 /* _int8_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int16_t = int16 /* _int16_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int32_t = int32 /* _int32_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int64_t = int64 /* _int64_t.h:30:33 */

// Copyright (c) 2016 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int8_t = uint8 /* _u_int8_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int16_t = uint16 /* _u_int16_t.h:30:41 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int32_t = uint32 /* _u_int32_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int64_t = uint64 /* _u_int64_t.h:30:33 */

type Register_t = Int64_t /* types.h:87:33 */

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.

type Intptr_t = X__darwin_intptr_t /* _intptr_t.h:32:33 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Uintptr_t = uint64 /* _uintptr_t.h:30:33 */

// These types are used for reserving the largest possible size.
type User_addr_t = U_int64_t  /* types.h:97:33 */
type User_size_t = U_int64_t  /* types.h:98:33 */
type User_ssize_t = Int64_t   /* types.h:99:33 */
type User_long_t = Int64_t    /* types.h:100:33 */
type User_ulong_t = U_int64_t /* types.h:101:33 */
type User_time_t = Int64_t    /* types.h:102:33 */
type User_off_t = Int64_t     /* types.h:103:33 */

// This defines the size of syscall arguments after copying into the kernel:
type Syscall_arg_t = U_int64_t /* types.h:111:33 */

// i386 is the structure that is exported to user threads for
// use in status/mutate calls.  This structure should never change.
//

type X__darwin_i386_thread_state = struct {
	F__eax    uint32
	F__ebx    uint32
	F__ecx    uint32
	F__edx    uint32
	F__edi    uint32
	F__esi    uint32
	F__ebp    uint32
	F__esp    uint32
	F__ss     uint32
	F__eflags uint32
	F__eip    uint32
	F__cs     uint32
	F__ds     uint32
	F__es     uint32
	F__fs     uint32
	F__gs     uint32
} /* _structs.h:46:1 */

// This structure should be double-word aligned for performance

type X__darwin_fp_control = struct {
	F__ccgo_pad1 [0]uint16
	F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
} /* _structs.h:92:1 */

type X__darwin_fp_control_t = X__darwin_fp_control /* _structs.h:117:28 */

// Status word.

type X__darwin_fp_status = struct {
	F__ccgo_pad1 [0]uint16
	F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
} /* _structs.h:150:1 */

type X__darwin_fp_status_t = X__darwin_fp_status /* _structs.h:167:27 */

// defn of 80bit x87 FPU or MMX register

type X__darwin_mmst_reg = struct {
	F__mmst_reg  [10]int8
	F__mmst_rsrv [6]int8
} /* _structs.h:194:1 */

// defn of 128 bit XMM regs

type X__darwin_xmm_reg = struct{ F__xmm_reg [16]int8 } /* _structs.h:213:1 */

// defn of 256 bit YMM regs

type X__darwin_ymm_reg = struct{ F__ymm_reg [32]int8 } /* _structs.h:229:1 */

// defn of 512 bit ZMM regs

type X__darwin_zmm_reg = struct{ F__zmm_reg [64]int8 } /* _structs.h:245:1 */

type X__darwin_opmask_reg = struct{ F__opmask_reg [8]int8 } /* _structs.h:259:1 */

// Floating point state.

type X__darwin_i386_float_state = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [224]int8
	F__fpu_reserved1 int32
} /* _structs.h:281:1 */

type X__darwin_i386_avx_state = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [224]int8
	F__fpu_reserved1 int32
	F__avx_reserved1 [64]int8
	F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
} /* _structs.h:318:1 */

type X__darwin_i386_avx512_state = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [224]int8
	F__fpu_reserved1 int32
	F__avx_reserved1 [64]int8
	F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
	F__fpu_k0        struct{ F__opmask_reg [8]int8 }
	F__fpu_k1        struct{ F__opmask_reg [8]int8 }
	F__fpu_k2        struct{ F__opmask_reg [8]int8 }
	F__fpu_k3        struct{ F__opmask_reg [8]int8 }
	F__fpu_k4        struct{ F__opmask_reg [8]int8 }
	F__fpu_k5        struct{ F__opmask_reg [8]int8 }
	F__fpu_k6        struct{ F__opmask_reg [8]int8 }
	F__fpu_k7        struct{ F__opmask_reg [8]int8 }
	F__fpu_zmmh0     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh1     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh2     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh3     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh4     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh5     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh6     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh7     struct{ F__ymm_reg [32]int8 }
} /* _structs.h:364:1 */

type X__darwin_i386_exception_state = struct {
	F__trapno     X__uint16_t
	F__cpu        X__uint16_t
	F__err        X__uint32_t
	F__faultvaddr X__uint32_t
} /* _structs.h:575:1 */

type X__darwin_x86_debug_state32 = struct {
	F__dr0 uint32
	F__dr1 uint32
	F__dr2 uint32
	F__dr3 uint32
	F__dr4 uint32
	F__dr5 uint32
	F__dr6 uint32
	F__dr7 uint32
} /* _structs.h:595:1 */

type X__x86_pagein_state = struct{ F__pagein_error int32 } /* _structs.h:622:1 */

// 64 bit versions of the above

type X__darwin_x86_thread_state64 = struct {
	F__rax    X__uint64_t
	F__rbx    X__uint64_t
	F__rcx    X__uint64_t
	F__rdx    X__uint64_t
	F__rdi    X__uint64_t
	F__rsi    X__uint64_t
	F__rbp    X__uint64_t
	F__rsp    X__uint64_t
	F__r8     X__uint64_t
	F__r9     X__uint64_t
	F__r10    X__uint64_t
	F__r11    X__uint64_t
	F__r12    X__uint64_t
	F__r13    X__uint64_t
	F__r14    X__uint64_t
	F__r15    X__uint64_t
	F__rip    X__uint64_t
	F__rflags X__uint64_t
	F__cs     X__uint64_t
	F__fs     X__uint64_t
	F__gs     X__uint64_t
} /* _structs.h:633:1 */

// 64 bit versions of the above (complete)

type X__darwin_x86_thread_full_state64 = struct {
	F__ss64 struct {
		F__rax    X__uint64_t
		F__rbx    X__uint64_t
		F__rcx    X__uint64_t
		F__rdx    X__uint64_t
		F__rdi    X__uint64_t
		F__rsi    X__uint64_t
		F__rbp    X__uint64_t
		F__rsp    X__uint64_t
		F__r8     X__uint64_t
		F__r9     X__uint64_t
		F__r10    X__uint64_t
		F__r11    X__uint64_t
		F__r12    X__uint64_t
		F__r13    X__uint64_t
		F__r14    X__uint64_t
		F__r15    X__uint64_t
		F__rip    X__uint64_t
		F__rflags X__uint64_t
		F__cs     X__uint64_t
		F__fs     X__uint64_t
		F__gs     X__uint64_t
	}
	F__ds     X__uint64_t
	F__es     X__uint64_t
	F__ss     X__uint64_t
	F__gsbase X__uint64_t
} /* _structs.h:691:1 */

type X__darwin_x86_float_state64 = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [96]int8
	F__fpu_reserved1 int32
} /* _structs.h:714:1 */

type X__darwin_x86_avx_state64 = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [96]int8
	F__fpu_reserved1 int32
	F__avx_reserved1 [64]int8
	F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
} /* _structs.h:765:1 */

type X__darwin_x86_avx512_state64 = struct {
	F__fpu_reserved [2]int32
	F__fpu_fcw      struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
	}
	F__fpu_fsw struct {
		F__ccgo_pad1 [0]uint16
		F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
	}
	F__fpu_ftw       X__uint8_t
	F__fpu_rsrv1     X__uint8_t
	F__fpu_fop       X__uint16_t
	F__fpu_ip        X__uint32_t
	F__fpu_cs        X__uint16_t
	F__fpu_rsrv2     X__uint16_t
	F__fpu_dp        X__uint32_t
	F__fpu_ds        X__uint16_t
	F__fpu_rsrv3     X__uint16_t
	F__fpu_mxcsr     X__uint32_t
	F__fpu_mxcsrmask X__uint32_t
	F__fpu_stmm0     struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm1 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm2 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm3 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm4 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm5 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm6 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_stmm7 struct {
		F__mmst_reg  [10]int8
		F__mmst_rsrv [6]int8
	}
	F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
	F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
	F__fpu_rsrv4     [96]int8
	F__fpu_reserved1 int32
	F__avx_reserved1 [64]int8
	F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
	F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
	F__fpu_k0        struct{ F__opmask_reg [8]int8 }
	F__fpu_k1        struct{ F__opmask_reg [8]int8 }
	F__fpu_k2        struct{ F__opmask_reg [8]int8 }
	F__fpu_k3        struct{ F__opmask_reg [8]int8 }
	F__fpu_k4        struct{ F__opmask_reg [8]int8 }
	F__fpu_k5        struct{ F__opmask_reg [8]int8 }
	F__fpu_k6        struct{ F__opmask_reg [8]int8 }
	F__fpu_k7        struct{ F__opmask_reg [8]int8 }
	F__fpu_zmmh0     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh1     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh2     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh3     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh4     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh5     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh6     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh7     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh8     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh9     struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh10    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh11    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh12    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh13    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh14    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmmh15    struct{ F__ymm_reg [32]int8 }
	F__fpu_zmm16     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm17     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm18     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm19     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm20     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm21     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm22     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm23     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm24     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm25     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm26     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm27     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm28     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm29     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm30     struct{ F__zmm_reg [64]int8 }
	F__fpu_zmm31     struct{ F__zmm_reg [64]int8 }
} /* _structs.h:833:1 */

type X__darwin_x86_exception_state64 = struct {
	F__trapno     X__uint16_t
	F__cpu        X__uint16_t
	F__err        X__uint32_t
	F__faultvaddr X__uint64_t
} /* _structs.h:1172:1 */

type X__darwin_x86_debug_state64 = struct {
	F__dr0 X__uint64_t
	F__dr1 X__uint64_t
	F__dr2 X__uint64_t
	F__dr3 X__uint64_t
	F__dr4 X__uint64_t
	F__dr5 X__uint64_t
	F__dr6 X__uint64_t
	F__dr7 X__uint64_t
} /* _structs.h:1192:1 */

type X__darwin_x86_cpmu_state64 = struct{ F__ctrs [16]X__uint64_t } /* _structs.h:1220:1 */

type X__darwin_mcontext32 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint32_t
	}
	F__ss struct {
		F__eax    uint32
		F__ebx    uint32
		F__ecx    uint32
		F__edx    uint32
		F__edi    uint32
		F__esi    uint32
		F__ebp    uint32
		F__esp    uint32
		F__ss     uint32
		F__eflags uint32
		F__eip    uint32
		F__cs     uint32
		F__ds     uint32
		F__es     uint32
		F__fs     uint32
		F__gs     uint32
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [224]int8
		F__fpu_reserved1 int32
	}
} /* _mcontext.h:39:1 */

type X__darwin_mcontext_avx32 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint32_t
	}
	F__ss struct {
		F__eax    uint32
		F__ebx    uint32
		F__ecx    uint32
		F__edx    uint32
		F__edi    uint32
		F__esi    uint32
		F__ebp    uint32
		F__esp    uint32
		F__ss     uint32
		F__eflags uint32
		F__eip    uint32
		F__cs     uint32
		F__ds     uint32
		F__es     uint32
		F__fs     uint32
		F__gs     uint32
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [224]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
	}
} /* _mcontext.h:47:1 */

type X__darwin_mcontext_avx512_32 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint32_t
	}
	F__ss struct {
		F__eax    uint32
		F__ebx    uint32
		F__ecx    uint32
		F__edx    uint32
		F__edi    uint32
		F__esi    uint32
		F__ebp    uint32
		F__esp    uint32
		F__ss     uint32
		F__eflags uint32
		F__eip    uint32
		F__cs     uint32
		F__ds     uint32
		F__es     uint32
		F__fs     uint32
		F__gs     uint32
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [224]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
		F__fpu_k0        struct{ F__opmask_reg [8]int8 }
		F__fpu_k1        struct{ F__opmask_reg [8]int8 }
		F__fpu_k2        struct{ F__opmask_reg [8]int8 }
		F__fpu_k3        struct{ F__opmask_reg [8]int8 }
		F__fpu_k4        struct{ F__opmask_reg [8]int8 }
		F__fpu_k5        struct{ F__opmask_reg [8]int8 }
		F__fpu_k6        struct{ F__opmask_reg [8]int8 }
		F__fpu_k7        struct{ F__opmask_reg [8]int8 }
		F__fpu_zmmh0     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh1     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh2     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh3     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh4     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh5     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh6     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh7     struct{ F__ymm_reg [32]int8 }
	}
} /* _mcontext.h:56:1 */

type X__darwin_mcontext64 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__rax    X__uint64_t
		F__rbx    X__uint64_t
		F__rcx    X__uint64_t
		F__rdx    X__uint64_t
		F__rdi    X__uint64_t
		F__rsi    X__uint64_t
		F__rbp    X__uint64_t
		F__rsp    X__uint64_t
		F__r8     X__uint64_t
		F__r9     X__uint64_t
		F__r10    X__uint64_t
		F__r11    X__uint64_t
		F__r12    X__uint64_t
		F__r13    X__uint64_t
		F__r14    X__uint64_t
		F__r15    X__uint64_t
		F__rip    X__uint64_t
		F__rflags X__uint64_t
		F__cs     X__uint64_t
		F__fs     X__uint64_t
		F__gs     X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:97:1 */

type X__darwin_mcontext64_full = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__ss64 struct {
			F__rax    X__uint64_t
			F__rbx    X__uint64_t
			F__rcx    X__uint64_t
			F__rdx    X__uint64_t
			F__rdi    X__uint64_t
			F__rsi    X__uint64_t
			F__rbp    X__uint64_t
			F__rsp    X__uint64_t
			F__r8     X__uint64_t
			F__r9     X__uint64_t
			F__r10    X__uint64_t
			F__r11    X__uint64_t
			F__r12    X__uint64_t
			F__r13    X__uint64_t
			F__r14    X__uint64_t
			F__r15    X__uint64_t
			F__rip    X__uint64_t
			F__rflags X__uint64_t
			F__cs     X__uint64_t
			F__fs     X__uint64_t
			F__gs     X__uint64_t
		}
		F__ds     X__uint64_t
		F__es     X__uint64_t
		F__ss     X__uint64_t
		F__gsbase X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:105:1 */

type X__darwin_mcontext_avx64 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__rax    X__uint64_t
		F__rbx    X__uint64_t
		F__rcx    X__uint64_t
		F__rdx    X__uint64_t
		F__rdi    X__uint64_t
		F__rsi    X__uint64_t
		F__rbp    X__uint64_t
		F__rsp    X__uint64_t
		F__r8     X__uint64_t
		F__r9     X__uint64_t
		F__r10    X__uint64_t
		F__r11    X__uint64_t
		F__r12    X__uint64_t
		F__r13    X__uint64_t
		F__r14    X__uint64_t
		F__r15    X__uint64_t
		F__rip    X__uint64_t
		F__rflags X__uint64_t
		F__cs     X__uint64_t
		F__fs     X__uint64_t
		F__gs     X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:113:1 */

type X__darwin_mcontext_avx64_full = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__ss64 struct {
			F__rax    X__uint64_t
			F__rbx    X__uint64_t
			F__rcx    X__uint64_t
			F__rdx    X__uint64_t
			F__rdi    X__uint64_t
			F__rsi    X__uint64_t
			F__rbp    X__uint64_t
			F__rsp    X__uint64_t
			F__r8     X__uint64_t
			F__r9     X__uint64_t
			F__r10    X__uint64_t
			F__r11    X__uint64_t
			F__r12    X__uint64_t
			F__r13    X__uint64_t
			F__r14    X__uint64_t
			F__r15    X__uint64_t
			F__rip    X__uint64_t
			F__rflags X__uint64_t
			F__cs     X__uint64_t
			F__fs     X__uint64_t
			F__gs     X__uint64_t
		}
		F__ds     X__uint64_t
		F__es     X__uint64_t
		F__ss     X__uint64_t
		F__gsbase X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:121:1 */

type X__darwin_mcontext_avx512_64 = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__rax    X__uint64_t
		F__rbx    X__uint64_t
		F__rcx    X__uint64_t
		F__rdx    X__uint64_t
		F__rdi    X__uint64_t
		F__rsi    X__uint64_t
		F__rbp    X__uint64_t
		F__rsp    X__uint64_t
		F__r8     X__uint64_t
		F__r9     X__uint64_t
		F__r10    X__uint64_t
		F__r11    X__uint64_t
		F__r12    X__uint64_t
		F__r13    X__uint64_t
		F__r14    X__uint64_t
		F__r15    X__uint64_t
		F__rip    X__uint64_t
		F__rflags X__uint64_t
		F__cs     X__uint64_t
		F__fs     X__uint64_t
		F__gs     X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
		F__fpu_k0        struct{ F__opmask_reg [8]int8 }
		F__fpu_k1        struct{ F__opmask_reg [8]int8 }
		F__fpu_k2        struct{ F__opmask_reg [8]int8 }
		F__fpu_k3        struct{ F__opmask_reg [8]int8 }
		F__fpu_k4        struct{ F__opmask_reg [8]int8 }
		F__fpu_k5        struct{ F__opmask_reg [8]int8 }
		F__fpu_k6        struct{ F__opmask_reg [8]int8 }
		F__fpu_k7        struct{ F__opmask_reg [8]int8 }
		F__fpu_zmmh0     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh1     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh2     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh3     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh4     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh5     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh6     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh7     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh8     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh9     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh10    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh11    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh12    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh13    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh14    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh15    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmm16     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm17     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm18     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm19     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm20     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm21     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm22     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm23     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm24     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm25     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm26     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm27     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm28     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm29     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm30     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm31     struct{ F__zmm_reg [64]int8 }
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:130:1 */

type X__darwin_mcontext_avx512_64_full = struct {
	F__es struct {
		F__trapno     X__uint16_t
		F__cpu        X__uint16_t
		F__err        X__uint32_t
		F__faultvaddr X__uint64_t
	}
	F__ss struct {
		F__ss64 struct {
			F__rax    X__uint64_t
			F__rbx    X__uint64_t
			F__rcx    X__uint64_t
			F__rdx    X__uint64_t
			F__rdi    X__uint64_t
			F__rsi    X__uint64_t
			F__rbp    X__uint64_t
			F__rsp    X__uint64_t
			F__r8     X__uint64_t
			F__r9     X__uint64_t
			F__r10    X__uint64_t
			F__r11    X__uint64_t
			F__r12    X__uint64_t
			F__r13    X__uint64_t
			F__r14    X__uint64_t
			F__r15    X__uint64_t
			F__rip    X__uint64_t
			F__rflags X__uint64_t
			F__cs     X__uint64_t
			F__fs     X__uint64_t
			F__gs     X__uint64_t
		}
		F__ds     X__uint64_t
		F__es     X__uint64_t
		F__ss     X__uint64_t
		F__gsbase X__uint64_t
	}
	F__fs struct {
		F__fpu_reserved [2]int32
		F__fpu_fcw      struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short : 2, unsigned short __pc: 2, unsigned short __rc: 2, unsigned short : 1, unsigned short : 3 */
		}
		F__fpu_fsw struct {
			F__ccgo_pad1 [0]uint16
			F__invalid   uint16 /* unsigned short __invalid: 1, unsigned short __denorm: 1, unsigned short __zdiv: 1, unsigned short __ovrfl: 1, unsigned short __undfl: 1, unsigned short __precis: 1, unsigned short __stkflt: 1, unsigned short __errsumm: 1, unsigned short __c0: 1, unsigned short __c1: 1, unsigned short __c2: 1, unsigned short __tos: 3, unsigned short __c3: 1, unsigned short __busy: 1 */
		}
		F__fpu_ftw       X__uint8_t
		F__fpu_rsrv1     X__uint8_t
		F__fpu_fop       X__uint16_t
		F__fpu_ip        X__uint32_t
		F__fpu_cs        X__uint16_t
		F__fpu_rsrv2     X__uint16_t
		F__fpu_dp        X__uint32_t
		F__fpu_ds        X__uint16_t
		F__fpu_rsrv3     X__uint16_t
		F__fpu_mxcsr     X__uint32_t
		F__fpu_mxcsrmask X__uint32_t
		F__fpu_stmm0     struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm1 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm2 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm3 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm4 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm5 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm6 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_stmm7 struct {
			F__mmst_reg  [10]int8
			F__mmst_rsrv [6]int8
		}
		F__fpu_xmm0      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm1      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm2      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm3      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm4      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm5      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm6      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm7      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm8      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm9      struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm10     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm11     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm12     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm13     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm14     struct{ F__xmm_reg [16]int8 }
		F__fpu_xmm15     struct{ F__xmm_reg [16]int8 }
		F__fpu_rsrv4     [96]int8
		F__fpu_reserved1 int32
		F__avx_reserved1 [64]int8
		F__fpu_ymmh0     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh1     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh2     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh3     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh4     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh5     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh6     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh7     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh8     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh9     struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh10    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh11    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh12    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh13    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh14    struct{ F__xmm_reg [16]int8 }
		F__fpu_ymmh15    struct{ F__xmm_reg [16]int8 }
		F__fpu_k0        struct{ F__opmask_reg [8]int8 }
		F__fpu_k1        struct{ F__opmask_reg [8]int8 }
		F__fpu_k2        struct{ F__opmask_reg [8]int8 }
		F__fpu_k3        struct{ F__opmask_reg [8]int8 }
		F__fpu_k4        struct{ F__opmask_reg [8]int8 }
		F__fpu_k5        struct{ F__opmask_reg [8]int8 }
		F__fpu_k6        struct{ F__opmask_reg [8]int8 }
		F__fpu_k7        struct{ F__opmask_reg [8]int8 }
		F__fpu_zmmh0     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh1     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh2     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh3     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh4     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh5     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh6     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh7     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh8     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh9     struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh10    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh11    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh12    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh13    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh14    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmmh15    struct{ F__ymm_reg [32]int8 }
		F__fpu_zmm16     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm17     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm18     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm19     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm20     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm21     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm22     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm23     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm24     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm25     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm26     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm27     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm28     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm29     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm30     struct{ F__zmm_reg [64]int8 }
		F__fpu_zmm31     struct{ F__zmm_reg [64]int8 }
	}
	F__ccgo_pad1 [4]byte
} /* _mcontext.h:138:1 */

type Mcontext_t = uintptr /* _mcontext.h:204:33 */

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2013 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Pthread_attr_t = X__darwin_pthread_attr_t /* _pthread_attr_t.h:31:33 */

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Structure used in sigaltstack call.

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.

type X__darwin_sigaltstack = struct {
	Fss_sp       uintptr
	Fss_size     X__darwin_size_t
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
} /* _sigaltstack.h:42:1 */

type Stack_t = X__darwin_sigaltstack /* _sigaltstack.h:48:33 */ // [???] signal stack

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type X__darwin_ucontext = struct {
	Fuc_onstack int32
	Fuc_sigmask X__darwin_sigset_t
	Fuc_stack   struct {
		Fss_sp       uintptr
		Fss_size     X__darwin_size_t
		Fss_flags    int32
		F__ccgo_pad1 [4]byte
	}
	Fuc_link     uintptr
	Fuc_mcsize   X__darwin_size_t
	Fuc_mcontext uintptr
} /* _ucontext.h:42:1 */

// user context
type Ucontext_t = X__darwin_ucontext /* _ucontext.h:56:33 */ // [???] user context

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Sigset_t = X__darwin_sigset_t /* _sigset_t.h:31:41 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Uid_t = X__darwin_uid_t /* _uid_t.h:31:31 */

type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* signal.h:158:1 */

type Sigevent = struct {
	Fsigev_notify int32
	Fsigev_signo  int32
	Fsigev_value  struct {
		F__ccgo_pad1 [0]uint64
		Fsival_int   int32
		F__ccgo_pad2 [4]byte
	}
	Fsigev_notify_function   uintptr
	Fsigev_notify_attributes uintptr
} /* signal.h:168:1 */

type X__siginfo = struct {
	Fsi_signo  int32
	Fsi_errno  int32
	Fsi_code   int32
	Fsi_pid    Pid_t
	Fsi_uid    Uid_t
	Fsi_status int32
	Fsi_addr   uintptr
	Fsi_value  struct {
		F__ccgo_pad1 [0]uint64
		Fsival_int   int32
		F__ccgo_pad2 [4]byte
	}
	Fsi_band int64
	F__pad   [7]uint64
} /* signal.h:177:9 */

type Siginfo_t = X__siginfo /* signal.h:188:3 */

// When the signal is SIGILL or SIGFPE, si_addr contains the address of
// the faulting instruction.
// When the signal is SIGSEGV or SIGBUS, si_addr contains the address of
// the faulting memory reference. Although for x86 there are cases of SIGSEGV
// for which si_addr cannot be determined and is NULL.
// If the signal is SIGCHLD, the si_pid field will contain the child process ID,
//  si_status contains the exit value or signal and
//  si_uid contains the real user ID of the process that sent the signal.

// Values for si_code

// Codes for SIGILL

// Codes for SIGFPE

// Codes for SIGSEGV

// Codes for SIGBUS

// Codes for SIGTRAP

// Codes for SIGCHLD

// Codes for SIGPOLL

// union for signal handlers
type X__sigaction_u = struct{ F__sa_handler uintptr } /* signal.h:269:1 */

// Signal vector template for Kernel user boundary
type X__sigaction = struct {
	F__sigaction_u struct{ F__sa_handler uintptr }
	Fsa_tramp      uintptr
	Fsa_mask       Sigset_t
	Fsa_flags      int32
} /* signal.h:276:1 */

// Signal vector "template" used in sigaction call.
type Sigaction = struct {
	F__sigaction_u struct{ F__sa_handler uintptr }
	Fsa_mask       Sigset_t
	Fsa_flags      int32
} /* signal.h:286:1 */

// if SA_SIGINFO is set, sa_sigaction is to be used instead of sa_handler.

// This will provide 64bit register set in a 32bit user address space

// the following are the only bits we support from user space, the
// rest are for kernel use only.

// Flags for sigprocmask:

// POSIX 1003.1b required values.

type Sig_t = uintptr /* signal.h:331:14 */ // type of signal function

// Structure used in sigaltstack call.

// 4.3 compatibility:
// Signal vector "template" used in sigvec call.
type Sigvec = struct {
	Fsv_handler uintptr
	Fsv_mask    int32
	Fsv_flags   int32
} /* signal.h:348:1 */

// Structure used in sigstack call.
type Sigstack = struct {
	Fss_sp       uintptr
	Fss_onstack  int32
	F__ccgo_pad1 [4]byte
} /* signal.h:367:1 */

type Uint64_t = uint64 /* stdint.h:98:25 */

type Int_least64_t = Int64_t   /* stdint.h:110:25 */
type Uint_least64_t = Uint64_t /* stdint.h:111:26 */
type Int_fast64_t = Int64_t    /* stdint.h:112:25 */
type Uint_fast64_t = Uint64_t  /* stdint.h:113:26 */

type Uint32_t = uint32 /* stdint.h:172:25 */

type Int_least32_t = Int32_t   /* stdint.h:184:25 */
type Uint_least32_t = Uint32_t /* stdint.h:185:26 */
type Int_fast32_t = Int32_t    /* stdint.h:186:25 */
type Uint_fast32_t = Uint32_t  /* stdint.h:187:26 */
type Uint16_t = uint16         /* stdint.h:207:25 */

type Int_least16_t = Int16_t   /* stdint.h:215:25 */
type Uint_least16_t = Uint16_t /* stdint.h:216:26 */
type Int_fast16_t = Int16_t    /* stdint.h:217:25 */
type Uint_fast16_t = Uint16_t  /* stdint.h:218:26 */
type Uint8_t = uint8           /* stdint.h:226:24 */

type Int_least8_t = Int8_t   /* stdint.h:232:24 */
type Uint_least8_t = Uint8_t /* stdint.h:233:25 */
type Int_fast8_t = Int8_t    /* stdint.h:234:24 */
type Uint_fast8_t = Uint8_t  /* stdint.h:235:25 */

// prevent glibc sys/types.h from defining conflicting types

// C99 7.18.1.4 Integer types capable of holding object pointers.

// C99 7.18.1.5 Greatest-width integer types.
type Intmax_t = int64   /* stdint.h:262:26 */
type Uintmax_t = uint64 /* stdint.h:263:26 */

// C99 7.18.4 Macros for minimum-width integer constants.
//
// The standard requires that integer constant macros be defined for all the
// minimum-width types defined above. As 8-, 16-, 32-, and 64-bit minimum-width
// types are required, the corresponding integer constant macros are defined
// here. This implementation also defines minimum-width types for every other
// integer width that the target implements, so corresponding macros are
// defined below, too.
//
// These macros are defined using the same successive-shrinking approach as
// the type definitions above. It is likewise important that macros are defined
// in order of decending width.
//
// Note that C++ should not check __STDC_CONSTANT_MACROS here, contrary to the
// claims of the C standard (see C++ 18.3.1p2, [cstdint.syn]).

// C99 7.18.2.1 Limits of exact-width integer types.
// C99 7.18.2.2 Limits of minimum-width integer types.
// C99 7.18.2.3 Limits of fastest minimum-width integer types.
//
// The presence of limit macros are completely optional in C99.  This
// implementation defines limits for all of the types (exact- and
// minimum-width) that it defines above, using the limits of the minimum-width
// type for any types that do not have exact-width representations.
//
// As in the type definitions, this section takes an approach of
// successive-shrinking to determine which limits to use for the standard (8,
// 16, 32, 64) bit widths when they don't have exact representations. It is
// therefore important that the definitions be kept in order of decending
// widths.
//
// Note that C++ should not check __STDC_LIMIT_MACROS here, contrary to the
// claims of the C standard (see C++ 18.3.1p2, [cstdint.syn]).

// Some utility macros

// C99 7.18.2.4 Limits of integer types capable of holding object pointers.
// C99 7.18.3 Limits of other integer types.

// ISO9899:2011 7.20 (C11 Annex K): Define RSIZE_MAX if __STDC_WANT_LIB_EXT1__
// is enabled.

// C99 7.18.2.5 Limits of greatest-width integer types.

// C99 7.18.3 Limits of other integer types.

// 7.18.4.2 Macros for greatest-width integer constants.

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

// [XSI] The timeval structure shall be defined as described in
// <sys/time.h>
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Timeval = struct {
	Ftv_sec      X__darwin_time_t
	Ftv_usec     X__darwin_suseconds_t
	F__ccgo_pad1 [4]byte
} /* _timeval.h:34:1 */

// The id_t type shall be defined as described in <sys/types.h>
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Resource limit type (low 63 bits, excluding the sign bit)
type Rlim_t = X__uint64_t /* resource.h:89:25 */

// ****
//
// PRIORITY

// Possible values of the first parameter to getpriority()/setpriority(),
// used to indicate the type of the second parameter.

// Range limitations for the value of the third parameter to setpriority().

// use PRIO_DARWIN_BG to set the current thread into "background" state
// which lowers CPU, disk IO, and networking priorites until thread terminates
// or "background" state is revoked

// use PRIO_DARWIN_NONUI to restrict a process's ability to make calls to
// the GPU. (deprecated)

// ****
//
// RESOURCE USAGE

// Possible values of the first parameter to getrusage(), used to indicate
// the scope of the information to be returned.

// A structure representing an accounting of resource utilization.  The
// address of an instance of this structure is the second parameter to
// getrusage().
//
// Note: All values other than ru_utime and ru_stime are implementaiton
//
//	defined and subject to change in a future release.  Their use
//	is discouraged for standards compliant programs.
type Rusage = struct {
	Fru_utime struct {
		Ftv_sec      X__darwin_time_t
		Ftv_usec     X__darwin_suseconds_t
		F__ccgo_pad1 [4]byte
	}
	Fru_stime struct {
		Ftv_sec      X__darwin_time_t
		Ftv_usec     X__darwin_suseconds_t
		F__ccgo_pad1 [4]byte
	}
	Fru_maxrss   int64
	Fru_ixrss    int64
	Fru_idrss    int64
	Fru_isrss    int64
	Fru_minflt   int64
	Fru_majflt   int64
	Fru_nswap    int64
	Fru_inblock  int64
	Fru_oublock  int64
	Fru_msgsnd   int64
	Fru_msgrcv   int64
	Fru_nsignals int64
	Fru_nvcsw    int64
	Fru_nivcsw   int64
} /* resource.h:152:1 */

// Flavors for proc_pid_rusage().

type Rusage_info_t = uintptr /* resource.h:193:14 */

type Rusage_info_v0 = struct {
	Fri_uuid               [16]Uint8_t
	Fri_user_time          Uint64_t
	Fri_system_time        Uint64_t
	Fri_pkg_idle_wkups     Uint64_t
	Fri_interrupt_wkups    Uint64_t
	Fri_pageins            Uint64_t
	Fri_wired_size         Uint64_t
	Fri_resident_size      Uint64_t
	Fri_phys_footprint     Uint64_t
	Fri_proc_start_abstime Uint64_t
	Fri_proc_exit_abstime  Uint64_t
} /* resource.h:195:1 */

type Rusage_info_v1 = struct {
	Fri_uuid                  [16]Uint8_t
	Fri_user_time             Uint64_t
	Fri_system_time           Uint64_t
	Fri_pkg_idle_wkups        Uint64_t
	Fri_interrupt_wkups       Uint64_t
	Fri_pageins               Uint64_t
	Fri_wired_size            Uint64_t
	Fri_resident_size         Uint64_t
	Fri_phys_footprint        Uint64_t
	Fri_proc_start_abstime    Uint64_t
	Fri_proc_exit_abstime     Uint64_t
	Fri_child_user_time       Uint64_t
	Fri_child_system_time     Uint64_t
	Fri_child_pkg_idle_wkups  Uint64_t
	Fri_child_interrupt_wkups Uint64_t
	Fri_child_pageins         Uint64_t
	Fri_child_elapsed_abstime Uint64_t
} /* resource.h:209:1 */

type Rusage_info_v2 = struct {
	Fri_uuid                  [16]Uint8_t
	Fri_user_time             Uint64_t
	Fri_system_time           Uint64_t
	Fri_pkg_idle_wkups        Uint64_t
	Fri_interrupt_wkups       Uint64_t
	Fri_pageins               Uint64_t
	Fri_wired_size            Uint64_t
	Fri_resident_size         Uint64_t
	Fri_phys_footprint        Uint64_t
	Fri_proc_start_abstime    Uint64_t
	Fri_proc_exit_abstime     Uint64_t
	Fri_child_user_time       Uint64_t
	Fri_child_system_time     Uint64_t
	Fri_child_pkg_idle_wkups  Uint64_t
	Fri_child_interrupt_wkups Uint64_t
	Fri_child_pageins         Uint64_t
	Fri_child_elapsed_abstime Uint64_t
	Fri_diskio_bytesread      Uint64_t
	Fri_diskio_byteswritten   Uint64_t
} /* resource.h:229:1 */

type Rusage_info_v3 = struct {
	Fri_uuid                          [16]Uint8_t
	Fri_user_time                     Uint64_t
	Fri_system_time                   Uint64_t
	Fri_pkg_idle_wkups                Uint64_t
	Fri_interrupt_wkups               Uint64_t
	Fri_pageins                       Uint64_t
	Fri_wired_size                    Uint64_t
	Fri_resident_size                 Uint64_t
	Fri_phys_footprint                Uint64_t
	Fri_proc_start_abstime            Uint64_t
	Fri_proc_exit_abstime             Uint64_t
	Fri_child_user_time               Uint64_t
	Fri_child_system_time             Uint64_t
	Fri_child_pkg_idle_wkups          Uint64_t
	Fri_child_interrupt_wkups         Uint64_t
	Fri_child_pageins                 Uint64_t
	Fri_child_elapsed_abstime         Uint64_t
	Fri_diskio_bytesread              Uint64_t
	Fri_diskio_byteswritten           Uint64_t
	Fri_cpu_time_qos_default          Uint64_t
	Fri_cpu_time_qos_maintenance      Uint64_t
	Fri_cpu_time_qos_background       Uint64_t
	Fri_cpu_time_qos_utility          Uint64_t
	Fri_cpu_time_qos_legacy           Uint64_t
	Fri_cpu_time_qos_user_initiated   Uint64_t
	Fri_cpu_time_qos_user_interactive Uint64_t
	Fri_billed_system_time            Uint64_t
	Fri_serviced_system_time          Uint64_t
} /* resource.h:251:1 */

type Rusage_info_v4 = struct {
	Fri_uuid                          [16]Uint8_t
	Fri_user_time                     Uint64_t
	Fri_system_time                   Uint64_t
	Fri_pkg_idle_wkups                Uint64_t
	Fri_interrupt_wkups               Uint64_t
	Fri_pageins                       Uint64_t
	Fri_wired_size                    Uint64_t
	Fri_resident_size                 Uint64_t
	Fri_phys_footprint                Uint64_t
	Fri_proc_start_abstime            Uint64_t
	Fri_proc_exit_abstime             Uint64_t
	Fri_child_user_time               Uint64_t
	Fri_child_system_time             Uint64_t
	Fri_child_pkg_idle_wkups          Uint64_t
	Fri_child_interrupt_wkups         Uint64_t
	Fri_child_pageins                 Uint64_t
	Fri_child_elapsed_abstime         Uint64_t
	Fri_diskio_bytesread              Uint64_t
	Fri_diskio_byteswritten           Uint64_t
	Fri_cpu_time_qos_default          Uint64_t
	Fri_cpu_time_qos_maintenance      Uint64_t
	Fri_cpu_time_qos_background       Uint64_t
	Fri_cpu_time_qos_utility          Uint64_t
	Fri_cpu_time_qos_legacy           Uint64_t
	Fri_cpu_time_qos_user_initiated   Uint64_t
	Fri_cpu_time_qos_user_interactive Uint64_t
	Fri_billed_system_time            Uint64_t
	Fri_serviced_system_time          Uint64_t
	Fri_logical_writes                Uint64_t
	Fri_lifetime_max_phys_footprint   Uint64_t
	Fri_instructions                  Uint64_t
	Fri_cycles                        Uint64_t
	Fri_billed_energy                 Uint64_t
	Fri_serviced_energy               Uint64_t
	Fri_interval_max_phys_footprint   Uint64_t
	Fri_runnable_time                 Uint64_t
} /* resource.h:282:1 */

type Rusage_info_current = Rusage_info_v4 /* resource.h:321:31 */

// ****
//
// RESOURCE LIMITS

// Symbolic constants for resource limits; since all limits are representable
// as a type rlim_t, we are permitted to define RLIM_SAVED_* in terms of
// RLIM_INFINITY.

// Possible values of the first parameter to getrlimit()/setrlimit(), to
// indicate for which resource the operation is being performed.

// A structure representing a resource limit.  The address of an instance
// of this structure is the second parameter to getrlimit()/setrlimit().
type Rlimit = struct {
	Frlim_cur Rlim_t
	Frlim_max Rlim_t
} /* resource.h:365:1 */

// proc_rlimit_control()
//
// Resource limit flavors

// Flags for wakeups monitor control.

// Flags for CPU usage monitor control.

// Flags for memory footprint interval tracking.

type Proc_rlimit_control_wakeupmon = struct {
	Fwm_flags Uint32_t
	Fwm_rate  Int32_t
} /* resource.h:400:1 */

// Deprecated:
// Structure of the information in the status word returned by wait4.
// If w_stopval==_WSTOPPED, then the second structure describes
// the information returned, else the first.
type Wait = struct{ Fw_status int32 } /* wait.h:194:1 */

// built-in for gcc

// built-in for gcc 3

// DO NOT REMOVE THIS COMMENT: fixincludes needs to see:
// _GCC_SIZE_T
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Ct_rune_t = X__darwin_ct_rune_t /* _ct_rune_t.h:32:28 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Rune_t = X__darwin_rune_t /* _rune_t.h:31:25 */

type Div_t = struct {
	Fquot int32
	Frem  int32
} /* stdlib.h:86:3 */

type Ldiv_t = struct {
	Fquot int64
	Frem  int64
} /* stdlib.h:91:3 */

type Lldiv_t = struct {
	Fquot int64
	Frem  int64
} /* stdlib.h:97:3 */

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Dev_t = X__darwin_dev_t /* _dev_t.h:31:31 */ // device number
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Mode_t = X__darwin_mode_t /* _mode_t.h:31:33 */ // getsubopt(3) external variable
// valloc is now declared in _malloc.h

// Poison the following routines if -fshort-wchar is set

var _ int8 /* gen.c:2:13: */
