// Code generated by 'ccgo stdlib/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o stdlib/stdlib_illumos_amd64.go -pkgname stdlib', DO NOT EDIT.

package stdlib

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	EXIT_FAILURE                    = 1                    // stdlib_iso.h:84:1:
	EXIT_SUCCESS                    = 0                    // stdlib_iso.h:85:1:
	INT16_MAX                       = 32767                // int_limits.h:93:1:
	INT16_MIN                       = -32768               // int_limits.h:211:1:
	INT32_MAX                       = 2147483647           // int_limits.h:94:1:
	INT32_MIN                       = -2147483648          // int_limits.h:212:1:
	INT64_MAX                       = 9223372036854775807  // int_limits.h:96:1:
	INT64_MIN                       = -9223372036854775808 // int_limits.h:214:1:
	INT8_MAX                        = 127                  // int_limits.h:92:1:
	INT8_MIN                        = -128                 // int_limits.h:210:1:
	INTMAX_MAX                      = 9223372036854775807  // int_limits.h:111:1:
	INTMAX_MIN                      = -9223372036854775808 // int_limits.h:220:1:
	INTPTR_MAX                      = 9223372036854775807  // int_limits.h:157:1:
	INTPTR_MIN                      = -9223372036854775808 // int_limits.h:241:1:
	INT_FAST16_MAX                  = 2147483647           // int_limits.h:137:1:
	INT_FAST16_MIN                  = -2147483648          // int_limits.h:233:1:
	INT_FAST32_MAX                  = 2147483647           // int_limits.h:138:1:
	INT_FAST32_MIN                  = -2147483648          // int_limits.h:234:1:
	INT_FAST64_MAX                  = 9223372036854775807  // int_limits.h:140:1:
	INT_FAST64_MIN                  = -9223372036854775808 // int_limits.h:236:1:
	INT_FAST8_MAX                   = 127                  // int_limits.h:136:1:
	INT_FAST8_MIN                   = -128                 // int_limits.h:232:1:
	INT_LEAST16_MAX                 = 32767                // int_limits.h:123:1:
	INT_LEAST16_MIN                 = -32768               // int_limits.h:226:1:
	INT_LEAST32_MAX                 = 2147483647           // int_limits.h:124:1:
	INT_LEAST32_MIN                 = -2147483648          // int_limits.h:227:1:
	INT_LEAST64_MAX                 = 9223372036854775807  // int_limits.h:126:1:
	INT_LEAST64_MIN                 = -9223372036854775808 // int_limits.h:229:1:
	INT_LEAST8_MAX                  = 127                  // int_limits.h:122:1:
	INT_LEAST8_MIN                  = -128                 // int_limits.h:225:1:
	PRIX32                          = "X"                  // int_fmtio.h:211:1:
	PRIX64                          = "lX"                 // int_fmtio.h:216:1:
	PRIXMAX                         = "lX"                 // int_fmtio.h:365:1:
	PRIXPTR                         = "lX"                 // int_fmtio.h:237:1:
	PRId32                          = "d"                  // int_fmtio.h:102:1:
	PRId64                          = "ld"                 // int_fmtio.h:107:1:
	PRIdMAX                         = "ld"                 // int_fmtio.h:360:1:
	PRIdPTR                         = "ld"                 // int_fmtio.h:232:1:
	PRIi32                          = "i"                  // int_fmtio.h:123:1:
	PRIi64                          = "li"                 // int_fmtio.h:128:1:
	PRIiMAX                         = "li"                 // int_fmtio.h:361:1:
	PRIiPTR                         = "li"                 // int_fmtio.h:233:1:
	PRIo32                          = "o"                  // int_fmtio.h:148:1:
	PRIo64                          = "lo"                 // int_fmtio.h:153:1:
	PRIoMAX                         = "lo"                 // int_fmtio.h:362:1:
	PRIoPTR                         = "lo"                 // int_fmtio.h:234:1:
	PRIu32                          = "u"                  // int_fmtio.h:169:1:
	PRIu64                          = "lu"                 // int_fmtio.h:174:1:
	PRIuMAX                         = "lu"                 // int_fmtio.h:364:1:
	PRIuPTR                         = "lu"                 // int_fmtio.h:235:1:
	PRIx32                          = "x"                  // int_fmtio.h:190:1:
	PRIx64                          = "lx"                 // int_fmtio.h:195:1:
	PRIxMAX                         = "lx"                 // int_fmtio.h:363:1:
	PRIxPTR                         = "lx"                 // int_fmtio.h:236:1:
	PTRDIFF_MAX                     = 9223372036854775807  // int_limits.h:166:1:
	PTRDIFF_MIN                     = -9223372036854775808 // int_limits.h:248:1:
	RAND_MAX                        = 32767                // stdlib_iso.h:86:1:
	SCNX16                          = "hX"                 // int_fmtio.h:336:1:
	SCNX32                          = "X"                  // int_fmtio.h:338:1:
	SCNX8                           = "hhX"                // int_fmtio.h:333:1:
	SCNd16                          = "hd"                 // int_fmtio.h:253:1:
	SCNd32                          = "d"                  // int_fmtio.h:255:1:
	SCNd8                           = "hhd"                // int_fmtio.h:250:1:
	SCNi16                          = "hi"                 // int_fmtio.h:269:1:
	SCNi32                          = "i"                  // int_fmtio.h:271:1:
	SCNi8                           = "hhi"                // int_fmtio.h:266:1:
	SCNo16                          = "ho"                 // int_fmtio.h:288:1:
	SCNo32                          = "o"                  // int_fmtio.h:290:1:
	SCNo8                           = "hho"                // int_fmtio.h:285:1:
	SCNu16                          = "hu"                 // int_fmtio.h:304:1:
	SCNu32                          = "u"                  // int_fmtio.h:306:1:
	SCNu8                           = "hhu"                // int_fmtio.h:301:1:
	SCNx16                          = "hx"                 // int_fmtio.h:320:1:
	SCNx32                          = "x"                  // int_fmtio.h:322:1:
	SCNx8                           = "hhx"                // int_fmtio.h:317:1:
	SIG_ATOMIC_MAX                  = 2147483647           // int_limits.h:186:1:
	SIG_ATOMIC_MIN                  = -2147483648          // int_limits.h:255:1:
	SIZE_MAX                        = 18446744073709551615 // int_limits.h:179:1:
	UINT16_MAX                      = 65535                // int_limits.h:102:1:
	UINT32_MAX                      = 4294967295           // int_limits.h:103:1:
	UINT64_MAX                      = 18446744073709551615 // int_limits.h:105:1:
	UINT8_MAX                       = 255                  // int_limits.h:101:1:
	UINTMAX_MAX                     = 18446744073709551615 // int_limits.h:117:1:
	UINTPTR_MAX                     = 18446744073709551615 // int_limits.h:158:1:
	UINT_FAST16_MAX                 = 4294967295           // int_limits.h:144:1:
	UINT_FAST32_MAX                 = 4294967295           // int_limits.h:145:1:
	UINT_FAST64_MAX                 = 18446744073709551615 // int_limits.h:147:1:
	UINT_FAST8_MAX                  = 255                  // int_limits.h:143:1:
	UINT_LEAST16_MAX                = 65535                // int_limits.h:130:1:
	UINT_LEAST32_MAX                = 4294967295           // int_limits.h:131:1:
	UINT_LEAST64_MAX                = 18446744073709551615 // int_limits.h:133:1:
	UINT_LEAST8_MAX                 = 255                  // int_limits.h:129:1:
	WCHAR_MAX                       = 2147483647           // int_limits.h:195:1:
	WCHAR_MIN                       = -2147483648          // int_limits.h:264:1:
	WINT_MAX                        = 2147483647           // int_limits.h:200:1:
	WINT_MIN                        = -2147483648          // int_limits.h:269:1:
	X_ALIGNMENT_REQUIRED            = 1                    // isa_defs.h:262:1:
	X_BIT_FIELDS_LTOH               = 0                    // isa_defs.h:245:1:
	X_BOOL_ALIGNMENT                = 1                    // isa_defs.h:248:1:
	X_CHAR_ALIGNMENT                = 1                    // isa_defs.h:249:1:
	X_CHAR_IS_SIGNED                = 0                    // isa_defs.h:247:1:
	X_DMA_USES_PHYSADDR             = 0                    // isa_defs.h:281:1:
	X_DONT_USE_1275_GENERIC_NAMES   = 0                    // isa_defs.h:287:1:
	X_DOUBLE_ALIGNMENT              = 8                    // isa_defs.h:256:1:
	X_DOUBLE_COMPLEX_ALIGNMENT      = 8                    // isa_defs.h:257:1:
	X_DTRACE_VERSION                = 1                    // feature_tests.h:490:1:
	X_FILE_OFFSET_BITS              = 64                   // <builtin>:25:1:
	X_FIRMWARE_NEEDS_FDISK          = 0                    // isa_defs.h:282:1:
	X_FLOAT_ALIGNMENT               = 4                    // isa_defs.h:252:1:
	X_FLOAT_COMPLEX_ALIGNMENT       = 4                    // isa_defs.h:253:1:
	X_HAVE_CPUID_INSN               = 0                    // isa_defs.h:288:1:
	X_IEEE_754                      = 0                    // isa_defs.h:246:1:
	X_INT64_TYPE                    = 0                    // int_types.h:82:1:
	X_INTTYPES_H                    = 0                    // inttypes.h:30:1:
	X_INT_ALIGNMENT                 = 4                    // isa_defs.h:251:1:
	X_ISO_CPP_14882_1998            = 0                    // feature_tests.h:466:1:
	X_ISO_C_9899_1999               = 0                    // feature_tests.h:472:1:
	X_ISO_C_9899_2011               = 0                    // feature_tests.h:478:1:
	X_ISO_STDLIB_C11_H              = 0                    // stdlib_c11.h:28:1:
	X_ISO_STDLIB_C99_H              = 0                    // stdlib_c99.h:41:1:
	X_ISO_STDLIB_ISO_H              = 0                    // stdlib_iso.h:47:1:
	X_LARGEFILE64_SOURCE            = 1                    // feature_tests.h:231:1:
	X_LARGEFILE_SOURCE              = 1                    // feature_tests.h:235:1:
	X_LITTLE_ENDIAN                 = 0                    // isa_defs.h:242:1:
	X_LONGLONG_TYPE                 = 0                    // feature_tests.h:412:1:
	X_LONG_ALIGNMENT                = 8                    // isa_defs.h:254:1:
	X_LONG_DOUBLE_ALIGNMENT         = 16                   // isa_defs.h:258:1:
	X_LONG_DOUBLE_COMPLEX_ALIGNMENT = 16                   // isa_defs.h:259:1:
	X_LONG_LONG_ALIGNMENT           = 8                    // isa_defs.h:255:1:
	X_LONG_LONG_ALIGNMENT_32        = 4                    // isa_defs.h:268:1:
	X_LONG_LONG_LTOH                = 0                    // isa_defs.h:244:1:
	X_LP64                          = 1                    // <predefined>:286:1:
	X_MAX_ALIGNMENT                 = 16                   // isa_defs.h:261:1:
	X_MODF16                        = "h"                  // int_fmtio.h:87:1:
	X_MODF8                         = "hh"                 // int_fmtio.h:86:1:
	X_MULTI_DATAMODEL               = 0                    // isa_defs.h:279:1:
	X_NORETURN_KYWD                 = 0                    // feature_tests.h:448:1:
	X_POINTER_ALIGNMENT             = 8                    // isa_defs.h:260:1:
	X_PRIX                          = "X"                  // int_fmtio.h:95:1:
	X_PRId                          = "d"                  // int_fmtio.h:90:1:
	X_PRIi                          = "i"                  // int_fmtio.h:91:1:
	X_PRIo                          = "o"                  // int_fmtio.h:92:1:
	X_PRIu                          = "u"                  // int_fmtio.h:93:1:
	X_PRIx                          = "x"                  // int_fmtio.h:94:1:
	X_PSM_MODULES                   = 0                    // isa_defs.h:284:1:
	X_RESTRICT_KYWD                 = 0                    // feature_tests.h:435:1:
	X_RTC_CONFIG                    = 0                    // isa_defs.h:285:1:
	X_SHORT_ALIGNMENT               = 2                    // isa_defs.h:250:1:
	X_SIZE_T                        = 0                    // stdlib_iso.h:76:1:
	X_SOFT_HOSTID                   = 0                    // isa_defs.h:286:1:
	X_SSIZE_T                       = 0                    // stdlib.h:177:1:
	X_STACK_GROWS_DOWNWARD          = 0                    // isa_defs.h:243:1:
	X_STDC_C11                      = 0                    // feature_tests.h:165:1:
	X_STDC_C99                      = 0                    // feature_tests.h:169:1:
	X_STDLIB_H                      = 0                    // stdlib.h:38:1:
	X_SUNOS_VTOC_16                 = 0                    // isa_defs.h:280:1:
	X_SYS_CCOMPILE_H                = 0                    // ccompile.h:32:1:
	X_SYS_FEATURE_TESTS_H           = 0                    // feature_tests.h:41:1:
	X_SYS_INTTYPES_H                = 0                    // inttypes.h:28:1:
	X_SYS_INT_CONST_H               = 0                    // int_const.h:39:1:
	X_SYS_INT_FMTIO_H               = 0                    // int_fmtio.h:30:1:
	X_SYS_INT_LIMITS_H              = 0                    // int_limits.h:39:1:
	X_SYS_INT_TYPES_H               = 0                    // int_types.h:30:1:
	X_SYS_ISA_DEFS_H                = 0                    // isa_defs.h:30:1:
	X_SYS_NULL_H                    = 0                    // null.h:17:1:
	X_SYS_STDINT_H                  = 0                    // stdint.h:28:1:
	X_UID_T                         = 0                    // stdlib.h:103:1:
	X_WCHAR_T                       = 0                    // stdlib_iso.h:103:1:
	X_XOPEN_VERSION                 = 3                    // feature_tests.h:392:1:
	Sun                             = 1                    // <predefined>:172:1:
	Unix                            = 1                    // <predefined>:175:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

type Div_t = struct {
	Fquot int32
	Frem  int32
} /* stdlib_iso.h:68:3 */

type Ldiv_t = struct {
	Fquot int64
	Frem  int64
} /* stdlib_iso.h:73:3 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// An application should not include this header directly.  Instead it
// should be included only through the inclusion of other Sun headers.
//
// The contents of this header is limited to identifiers specified in
// the C99 standard and in conflict with the C++ implementation of the
// standard header.  The C++ standard may adopt the C99 standard at
// which point it is expected that the symbols included here will
// become part of the C++ std namespace.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// The following have been added as a result of the ISO/IEC 9899:1999
// standard. For a strictly conforming C application, visibility is
// contingent on the value of __STDC_VERSION__ (see sys/feature_tests.h).
// For non-strictly conforming C applications, there are no restrictions
// on the C namespace.

type Lldiv_t = struct {
	Fquot int64
	Frem  int64
} /* stdlib_c99.h:61:3 */

// ISO C11 Annex K functions are not allowed to be in the standard
// namespace; however, it is implementation-defined as to whether or
// not they are in the global namespace and we opt to make them
// available to software.

// Allow global visibility for symbols defined in
// C++ "std" namespace in <iso/stdlib_iso.h>.

// Allow global visibility for symbols defined in
// C++ "std" namespace in <iso/stdlib_c11.h>.

type Uid_t = uint32 /* stdlib.h:104:22 */

// swab() has historically been in <stdlib.h> as delivered from AT&T
// and continues to be visible in the default compilation environment.
// As of Issue 4 of the X/Open Portability Guides, swab() was declared
// in <unistd.h>. As a result, with respect to X/Open namespace the
// swab() declaration in this header is only visible for the XPG3
// environment.
type Ssize_t = int64 /* stdlib.h:179:14 */

// OpenBSD and misc. compatibility functions

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <inttypes.h>, is specified by the ISO C standard,
// standard, ISO/IEC 9899:1999 Programming language - C and is
// also defined by SUSv3.
//
// ISO	  International Organization for Standardization.
// SUSv3  Single Unix Specification, Version 3

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2003 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This header, <sys/inttypes.h>, contains (through nested inclusion) the
// vast majority of the facilities specified for <inttypes.h> as defined
// by the ISO C Standard, ISO/IEC 9899:1999 Programming language - C.
//
// Kernel/Driver developers are encouraged to include this file to access
// the fixed size types, limits and utility macros. Application developers
// should use the standard defined header <inttypes.h>.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_types.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> defined in the ISO C standard, ISO/IEC 9899:1999
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<sys/types.h>		Provides only the "_t" types defined in this
//				file which is a subset of the contents of
//				<inttypes.h>.  (This can be appropriate for
//				all programs/modules except those claiming
//				ANSI-C conformance.)
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Basic / Extended integer types
//
// The following defines the basic fixed-size integer types.
//
// Implementations are free to typedef them to Standard C integer types or
// extensions that they support. If an implementation does not support one
// of the particular integer data types below, then it should not define the
// typedefs and macros corresponding to that data type.  Note that int8_t
// is not defined in -Xs mode on ISAs for which the ABI specifies "char"
// as an unsigned entity because there is no way to define an eight bit
// signed integral.
type Int8_t = int8   /* int_types.h:75:16 */
type Int16_t = int16 /* int_types.h:79:17 */
type Int32_t = int32 /* int_types.h:80:15 */
type Int64_t = int64 /* int_types.h:83:16 */

type Uint8_t = uint8   /* int_types.h:91:24 */
type Uint16_t = uint16 /* int_types.h:92:25 */
type Uint32_t = uint32 /* int_types.h:93:23 */
type Uint64_t = uint64 /* int_types.h:95:24 */

// intmax_t and uintmax_t are to be the longest (in number of bits) signed
// and unsigned integer types supported by the implementation.
type Intmax_t = int64   /* int_types.h:107:19 */
type Uintmax_t = uint64 /* int_types.h:108:19 */

// intptr_t and uintptr_t are signed and unsigned integer types large enough
// to hold any data pointer; that is, data pointers can be assigned into or
// from these integer types without losing precision.
type Intptr_t = int64   /* int_types.h:120:16 */
type Uintptr_t = uint64 /* int_types.h:121:24 */

// The following define the fastest integer types that can hold the
// specified number of bits.
type Int_fast8_t = int8   /* int_types.h:132:16 */
type Int_fast16_t = int32 /* int_types.h:136:15 */
type Int_fast32_t = int32 /* int_types.h:137:15 */
type Int_fast64_t = int64 /* int_types.h:139:16 */

type Uint_fast8_t = uint8   /* int_types.h:146:24 */
type Uint_fast16_t = uint32 /* int_types.h:147:23 */
type Uint_fast32_t = uint32 /* int_types.h:148:23 */
type Uint_fast64_t = uint64 /* int_types.h:150:24 */

// The following define the smallest integer types that can hold the
// specified number of bits.
type Int_least8_t = int8   /* int_types.h:162:16 */
type Int_least16_t = int16 /* int_types.h:166:17 */
type Int_least32_t = int32 /* int_types.h:167:15 */
type Int_least64_t = int64 /* int_types.h:169:16 */

// If these are changed, please update char16_t and char32_t in head/uchar.h.
type Uint_least8_t = uint8   /* int_types.h:179:24 */
type Uint_least16_t = uint16 /* int_types.h:180:25 */
type Uint_least32_t = uint32 /* int_types.h:181:23 */
type Uint_least64_t = uint64 /* int_types.h:183:24 */

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/int_limits.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_limits.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> as defined in the ISO C standard, ISO/IEC 9899:1999
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<limits.h>		This nested inclusion is disabled for strictly
//				ANSI-C conforming compilations.  The *_MIN
//				definitions are not visible to POSIX or XPG
//				conforming applications (due to what may be
//				a bug in the specification - this is under
//				investigation)
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Limits
//
// The following define the limits for the types defined in <sys/int_types.h>.
//
// INTMAX_MIN (minimum value of the largest supported signed integer type),
// INTMAX_MAX (maximum value of the largest supported signed integer type),
// and UINTMAX_MAX (maximum value of the largest supported unsigned integer
// type) can be set to implementation defined limits.
//
// NOTE : A programmer can test to see whether an implementation supports
// a particular size of integer by testing if the macro that gives the
// maximum for that datatype is defined. For example, if #ifdef UINT64_MAX
// tests false, the implementation does not support unsigned 64 bit integers.
//
// The type of these macros is intentionally unspecified.
//
// The types int8_t, int_least8_t, and int_fast8_t are not defined for ISAs
// where the ABI specifies "char" as unsigned when the translation mode is
// not ANSI-C.

// The following 2 macros are provided for testing whether the types
// intptr_t and uintptr_t (integers large enough to hold a void *) are
// defined in this header. They are needed in case the architecture can't
// represent a pointer in any standard integral type.

// Maximum limits of ptrdiff_t defined in <sys/types.h>

// Maximum value of a "size_t".  SIZE_MAX was previously defined
// in <limits.h>, however, the standards specify it be defined
// in <stdint.h>. The <stdint.h> headers includes this header as
// does <limits.h>. The value of SIZE_MAX should not deviate
// from the value of ULONG_MAX defined <sys/types.h>.

// Maximum limit of sig_atomic_t defined in <sys/types.h>

// Maximum limit of wchar_t. The WCHAR_* macros are also
// defined in <iso/wchar_iso.h>, but inclusion of that header
// will break ISO/IEC C namespace.

// Maximum limit of wint_t

// It is probably a bug in the POSIX specification (IEEE-1003.1-1990) that
// when including <limits.h> that the suffix _MAX is reserved but not the
// suffix _MIN.  However, until that issue is resolved....

// Minimum value of a pointer-holding signed integer type

// Minimum limits of ptrdiff_t defined in <sys/types.h>

// Minimum limit of sig_atomic_t defined in <sys/types.h>

// Minimum limit of wchar_t. The WCHAR_* macros are also
// defined in <iso/wchar_iso.h>, but inclusion of that header
// will break ISO/IEC C namespace.

// Minimum limit of wint_t

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/int_const.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_const.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> as proposed in the ISO/JTC1/SC22/WG14 C committee's working
// draft for the revision of the current ISO C standard, ISO/IEC 9899:1990
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.
//
// Use at your own risk.  This file will track the evolution of the revision
// of the current ISO C standard.  As of February 1996, the committee is
// squarely behind the fixed sized types.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Constants
//
// The following macros create constants of the types defined in
// <sys/int_types.h>. The intent is that:
//	Constants defined using these macros have a specific size and
//	signedness. The suffix used for int64_t and uint64_t (ll and ull)
//	are for examples only. Implementations are permitted to use other
//	suffixes.
//
// The "CSTYLED" comments are flags to an internal code style analysis tool
// telling it to silently accept the line which follows.  This internal
// standard requires a space between arguments, but the historical,
// non-ANSI-C ``method'' of concatenation can't tolerate those spaces.
// CSTYLED

// CSTYLED

// CSTYLED
// CSTYLED
// CSTYLED
// CSTYLED

// CSTYLED
// CSTYLED

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_fmtio.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> as defined by the ISO C Standard, ISO/IEC 9899:1999
// Programming language - C.
//
// ISO  International Organization for Standardization.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Formatted I/O
//
// The following macros can be used even when an implementation has not
// extended the printf/scanf family of functions.
//
// The form of the names of the macros is either "PRI" for printf specifiers
// or "SCN" for scanf specifiers, followed by the conversion specifier letter
// followed by the datatype size. For example, PRId32 is the macro for
// the printf d conversion specifier with the flags for 32 bit datatype.
//
// An example using one of these macros:
//
//	uint64_t u;
//	printf("u = %016" PRIx64 "\n", u);
//
// For the purpose of example, the definitions of the printf/scanf macros
// below have the values appropriate for a machine with 8 bit shorts, 16
// bit shorts, 32 bit ints, 32 or 64 bit longs depending on compilation
// mode, and 64 bit long longs.

// fprintf macros for signed integers

// fprintf macros for unsigned integers

// fprintf macros for pointers

// fscanf macros for signed integers

// fscanf macros for unsigned integers

// The following macros define I/O formats for intmax_t and uintmax_t.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2003 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This header is included by <stdint.h> which was introduced by
// the ISO C Standard, ISO/IEC 9899:1999 Programming language - C.
// The header is a subset of the <inttypes.h> header.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/int_limits.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/int_const.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Inclusion of <stddef.h> breaks namespace, therefore define wchar_t

// wchar_t is a built-in type in standard C++ and as such is not
// defined here when using standard C++. However, the GNU compiler
// fixincludes utility nonetheless creates its own version of this
// header for use by gcc and g++. In that version it adds a redundant
// guard for __cplusplus. To avoid the creation of a gcc/g++ specific
// header we need to include the following magic comment:
//
// we must use the C++ compiler's type
//
// The above comment should not be removed or changed until GNU
// gcc/fixinc/inclhack.def is updated to bypass this header.

type Imaxdiv_t = struct {
	Fquot int64
	Frem  int64
} /* inttypes.h:84:3 */

var _ int8 /* gen.c:2:13: */
