// Code generated by 'ccgo stdlib/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o stdlib/stdlib_netbsd_arm.go -pkgname stdlib', DO NOT EDIT.

package stdlib

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BIG_ENDIAN                    = 4321                 // endian.h:101:1:
	BYTE_ORDER                    = 1234                 // endian.h:103:1:
	EXIT_FAILURE                  = 1                    // stdlib.h:87:1:
	EXIT_SUCCESS                  = 0                    // stdlib.h:88:1:
	FD_SETSIZE                    = 256                  // fd_set.h:59:1:
	HN_AUTOSCALE                  = 0x20                 // stdlib.h:301:1:
	HN_B                          = 0x04                 // stdlib.h:297:1:
	HN_DECIMAL                    = 0x01                 // stdlib.h:295:1:
	HN_DIVISOR_1000               = 0x08                 // stdlib.h:298:1:
	HN_GETSCALE                   = 0x10                 // stdlib.h:300:1:
	HN_NOSPACE                    = 0x02                 // stdlib.h:296:1:
	INT16_MAX                     = 32767                // common_int_limits.h:53:1:
	INT16_MIN                     = -32768               // common_int_limits.h:47:1:
	INT32_MAX                     = 2147483647           // common_int_limits.h:54:1:
	INT32_MIN                     = -2147483648          // common_int_limits.h:48:1:
	INT64_MAX                     = 9223372036854775807  // common_int_limits.h:55:1:
	INT64_MIN                     = -9223372036854775808 // common_int_limits.h:49:1:
	INT8_MAX                      = 127                  // common_int_limits.h:52:1:
	INT8_MIN                      = -128                 // common_int_limits.h:46:1:
	INTMAX_MAX                    = 9223372036854775807  // common_int_limits.h:111:1:
	INTMAX_MIN                    = -9223372036854775808 // common_int_limits.h:110:1:
	INTPTR_MAX                    = 2147483647           // common_int_limits.h:105:1:
	INTPTR_MIN                    = -2147483648          // common_int_limits.h:104:1:
	INT_FAST16_MAX                = 2147483647           // common_int_limits.h:93:1:
	INT_FAST16_MIN                = -2147483648          // common_int_limits.h:87:1:
	INT_FAST32_MAX                = 2147483647           // common_int_limits.h:94:1:
	INT_FAST32_MIN                = -2147483648          // common_int_limits.h:88:1:
	INT_FAST64_MAX                = 9223372036854775807  // common_int_limits.h:95:1:
	INT_FAST64_MIN                = -9223372036854775808 // common_int_limits.h:89:1:
	INT_FAST8_MAX                 = 2147483647           // common_int_limits.h:92:1:
	INT_FAST8_MIN                 = -2147483648          // common_int_limits.h:86:1:
	INT_LEAST16_MAX               = 32767                // common_int_limits.h:73:1:
	INT_LEAST16_MIN               = -32768               // common_int_limits.h:67:1:
	INT_LEAST32_MAX               = 2147483647           // common_int_limits.h:74:1:
	INT_LEAST32_MIN               = -2147483648          // common_int_limits.h:68:1:
	INT_LEAST64_MAX               = 9223372036854775807  // common_int_limits.h:75:1:
	INT_LEAST64_MIN               = -9223372036854775808 // common_int_limits.h:69:1:
	INT_LEAST8_MAX                = 127                  // common_int_limits.h:72:1:
	INT_LEAST8_MIN                = -128                 // common_int_limits.h:66:1:
	LITTLE_ENDIAN                 = 1234                 // endian.h:100:1:
	NBBY                          = 8                    // types.h:316:1:
	NFDBITS                       = 32                   // fd_set.h:93:1:
	NODEVMAJOR                    = -1                   // types.h:258:1:
	PDP_ENDIAN                    = 3412                 // endian.h:102:1:
	PTRDIFF_MAX                   = 2147483647           // common_int_limits.h:121:1:
	PTRDIFF_MIN                   = -2147483648          // common_int_limits.h:120:1:
	RANDOM_MAX                    = 0x7fffffff           // stdlib.h:188:1:
	RAND_MAX                      = 0x7fffffff           // stdlib.h:90:1:
	SIG_ATOMIC_MAX                = 2147483647           // common_int_limits.h:125:1:
	SIG_ATOMIC_MIN                = -2147483648          // common_int_limits.h:124:1:
	SIZE_MAX                      = 4294967295           // common_int_limits.h:128:1:
	UINT16_MAX                    = 65535                // common_int_limits.h:59:1:
	UINT32_MAX                    = 4294967295           // common_int_limits.h:60:1:
	UINT64_MAX                    = 18446744073709551615 // common_int_limits.h:61:1:
	UINT8_MAX                     = 255                  // common_int_limits.h:58:1:
	UINTMAX_MAX                   = 18446744073709551615 // common_int_limits.h:112:1:
	UINTPTR_MAX                   = 4294967295           // common_int_limits.h:106:1:
	UINT_FAST16_MAX               = 4294967295           // common_int_limits.h:99:1:
	UINT_FAST32_MAX               = 4294967295           // common_int_limits.h:100:1:
	UINT_FAST64_MAX               = 18446744073709551615 // common_int_limits.h:101:1:
	UINT_FAST8_MAX                = 4294967295           // common_int_limits.h:98:1:
	UINT_LEAST16_MAX              = 65535                // common_int_limits.h:79:1:
	UINT_LEAST32_MAX              = 4294967295           // common_int_limits.h:80:1:
	UINT_LEAST64_MAX              = 18446744073709551615 // common_int_limits.h:81:1:
	UINT_LEAST8_MAX               = 255                  // common_int_limits.h:78:1:
	WCHAR_MAX                     = 2147483647           // wchar_limits.h:50:1:
	WCHAR_MIN                     = -2147483648          // wchar_limits.h:42:1:
	WINT_MAX                      = 2147483647           // wchar_limits.h:68:1:
	WINT_MIN                      = -2147483648          // wchar_limits.h:60:1:
	X_ARM_ARCH_4T                 = 0                    // cdefs.h:44:1:
	X_ARM_ARCH_5                  = 0                    // cdefs.h:40:1:
	X_ARM_ARCH_5T                 = 0                    // cdefs.h:36:1:
	X_ARM_ARCH_6                  = 0                    // cdefs.h:31:1:
	X_ARM_ARCH_7                  = 0                    // cdefs.h:20:1:
	X_ARM_ARCH_DWORD_OK           = 0                    // cdefs.h:51:1:
	X_ARM_ARCH_T2                 = 0                    // cdefs.h:24:1:
	X_ARM_BSWAP_H_                = 0                    // bswap.h:4:1:
	X_ARM_BYTE_SWAP_H_            = 0                    // byte_swap.h:33:1:
	X_ARM_CDEFS_H_                = 0                    // cdefs.h:4:1:
	X_ARM_INT_CONST_H_            = 0                    // int_const.h:33:1:
	X_ARM_INT_LIMITS_H_           = 0                    // int_limits.h:33:1:
	X_ARM_INT_MWGWTYPES_H_        = 0                    // int_mwgwtypes.h:33:1:
	X_ARM_INT_TYPES_H_            = 0                    // int_types.h:33:1:
	X_ARM_TYPES_H_                = 0                    // types.h:35:1:
	X_ARM_WCHAR_LIMITS_H_         = 0                    // wchar_limits.h:33:1:
	X_BIG_ENDIAN                  = 4321                 // endian.h:44:1:
	X_BSD_INT16_T_                = 0                    // types.h:65:1:
	X_BSD_INT32_T_                = 0                    // types.h:75:1:
	X_BSD_INT64_T_                = 0                    // types.h:85:1:
	X_BSD_INT8_T_                 = 0                    // types.h:55:1:
	X_BSD_INTPTR_T_               = 0                    // stdint.h:80:1:
	X_BSD_UINT16_T_               = 0                    // types.h:70:1:
	X_BSD_UINT32_T_               = 0                    // types.h:80:1:
	X_BSD_UINT64_T_               = 0                    // types.h:90:1:
	X_BSD_UINT8_T_                = 0                    // types.h:60:1:
	X_BSD_UINTPTR_T_              = 0                    // stdint.h:85:1:
	X_BYTE_ORDER                  = 1234                 // endian_machdep.h:7:1:
	X_FILE_OFFSET_BITS            = 64                   // <builtin>:25:1:
	X_LIB_PTHREAD_TYPES_H         = 0                    // pthread_types.h:33:1:
	X_LITTLE_ENDIAN               = 1234                 // endian.h:43:1:
	X_NETBSD_SOURCE               = 1                    // featuretest.h:70:1:
	X_PDP_ENDIAN                  = 3412                 // endian.h:45:1:
	X_PT_BARRIERATTR_DEAD         = 0xDEAD0808           // pthread_types.h:278:1:
	X_PT_BARRIERATTR_MAGIC        = 0x88880808           // pthread_types.h:277:1:
	X_PT_BARRIER_DEAD             = 0xDEAD0008           // pthread_types.h:270:1:
	X_PT_BARRIER_MAGIC            = 0x88880008           // pthread_types.h:269:1:
	X_PT_CONDATTR_DEAD            = 0xDEAD0006           // pthread_types.h:197:1:
	X_PT_CONDATTR_MAGIC           = 0x66660006           // pthread_types.h:196:1:
	X_PT_COND_DEAD                = 0xDEAD0005           // pthread_types.h:182:1:
	X_PT_COND_MAGIC               = 0x55550005           // pthread_types.h:181:1:
	X_PT_MUTEXATTR_DEAD           = 0xDEAD0004           // pthread_types.h:167:1:
	X_PT_MUTEXATTR_MAGIC          = 0x44440004           // pthread_types.h:166:1:
	X_PT_MUTEX_DEAD               = 0xDEAD0003           // pthread_types.h:139:1:
	X_PT_MUTEX_MAGIC              = 0x33330003           // pthread_types.h:138:1:
	X_PT_RWLOCKATTR_DEAD          = 0xDEAD0909           // pthread_types.h:253:1:
	X_PT_RWLOCKATTR_MAGIC         = 0x99990909           // pthread_types.h:252:1:
	X_PT_RWLOCK_DEAD              = 0xDEAD0009           // pthread_types.h:236:1:
	X_PT_RWLOCK_MAGIC             = 0x99990009           // pthread_types.h:235:1:
	X_PT_SPINLOCK_DEAD            = 0xDEAD0007           // pthread_types.h:213:1:
	X_PT_SPINLOCK_MAGIC           = 0x77770007           // pthread_types.h:212:1:
	X_PT_SPINLOCK_PSHARED         = 0x00000001           // pthread_types.h:214:1:
	X_QUAD_HIGHWORD               = 1                    // endian.h:84:1:
	X_QUAD_LOWWORD                = 0                    // endian.h:85:1:
	X_SIZE_T                      = 0                    // types.h:279:1:
	X_STDLIB_H_                   = 0                    // stdlib.h:35:1:
	X_SYS_ANSI_H_                 = 0                    // ansi.h:33:1:
	X_SYS_BSWAP_H_                = 0                    // bswap.h:6:1:
	X_SYS_CDEFS_ELF_H_            = 0                    // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_                = 0                    // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_          = 0                    // common_ansi.h:33:1:
	X_SYS_COMMON_INT_LIMITS_H_    = 0                    // common_int_limits.h:33:1:
	X_SYS_COMMON_INT_MWGWTYPES_H_ = 0                    // common_int_mwgwtypes.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_     = 0                    // common_int_types.h:33:1:
	X_SYS_ENDIAN_H_               = 0                    // endian.h:35:1:
	X_SYS_FD_SET_H_               = 0                    // fd_set.h:35:1:
	X_SYS_NULL_H_                 = 0                    // null.h:9:1:
	X_SYS_STDINT_H_               = 0                    // stdint.h:33:1:
	X_SYS_TYPES_H_                = 0                    // types.h:40:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// return true if value 'a' fits in type 't'

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: types.h,v 1.102 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Machine type dependent parameters.
//	$NetBSD: types.h,v 1.38 2019/04/06 03:06:25 thorpej Exp $

// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// 7.18.1.4 Integer types capable of holding object pointers

type X__intptr_t = int32   /* common_int_types.h:58:27 */
type X__uintptr_t = uint32 /* common_int_types.h:59:26 */

// This should have always been an 8-bit type, but since it's been exposed
// to user-space, we don't want ABI breakage there.
type X__cpu_simple_lock_nv_t = int32 /* types.h:75:14 */
type X__register_t = int32           /* types.h:77:14 */

//	$NetBSD: ansi.h,v 1.18 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: ansi.h,v 1.18 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */

type Int8_t = X__int8_t /* types.h:54:18 */

type Uint8_t = X__uint8_t /* types.h:59:19 */

type Int16_t = X__int16_t /* types.h:64:19 */

type Uint16_t = X__uint16_t /* types.h:69:20 */

type Int32_t = X__int32_t /* types.h:74:19 */

type Uint32_t = X__uint32_t /* types.h:79:20 */

type Int64_t = X__int64_t /* types.h:84:19 */

type Uint64_t = X__uint64_t /* types.h:89:20 */

type U_int8_t = Uint8_t   /* types.h:93:18 */
type U_int16_t = Uint16_t /* types.h:94:18 */
type U_int32_t = Uint32_t /* types.h:95:18 */
type U_int64_t = Uint64_t /* types.h:96:18 */

//	$NetBSD: endian.h,v 1.3 2001/06/23 12:20:27 bjh21 Exp $

//	$NetBSD: endian.h,v 1.30 2016/02/27 21:37:35 christos Exp $

// Copyright (c) 1987, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)endian.h	8.1 (Berkeley) 6/11/93

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Definitions for byte order, according to byte significance from low
// address to high.

// C-family endian-ness definitions

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: types.h,v 1.102 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

type In_addr_t = X__in_addr_t /* endian.h:58:21 */

type In_port_t = X__in_port_t /* endian.h:63:21 */

//      $NetBSD: bswap.h,v 1.19 2015/03/12 15:28:16 christos Exp $

// Written by Manuel Bouyer. Public domain

//	$NetBSD: stdint.h,v 1.8 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 2001, 2004 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Intptr_t = X__intptr_t /* stdint.h:79:20 */

type Uintptr_t = X__uintptr_t /* stdint.h:84:21 */

// $NetBSD: int_mwgwtypes.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_mwgwtypes.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.2 Minimum-width integer types

type Int_least8_t = int8     /* common_int_mwgwtypes.h:45:32 */
type Uint_least8_t = uint8   /* common_int_mwgwtypes.h:46:32 */
type Int_least16_t = int16   /* common_int_mwgwtypes.h:47:32 */
type Uint_least16_t = uint16 /* common_int_mwgwtypes.h:48:32 */
type Int_least32_t = int32   /* common_int_mwgwtypes.h:49:32 */
type Uint_least32_t = uint32 /* common_int_mwgwtypes.h:50:32 */
type Int_least64_t = int64   /* common_int_mwgwtypes.h:51:32 */
type Uint_least64_t = uint64 /* common_int_mwgwtypes.h:52:32 */

// 7.18.1.3 Fastest minimum-width integer types
type Int_fast8_t = int32    /* common_int_mwgwtypes.h:55:32 */
type Uint_fast8_t = uint32  /* common_int_mwgwtypes.h:56:32 */
type Int_fast16_t = int32   /* common_int_mwgwtypes.h:57:32 */
type Uint_fast16_t = uint32 /* common_int_mwgwtypes.h:58:32 */
type Int_fast32_t = int32   /* common_int_mwgwtypes.h:59:32 */
type Uint_fast32_t = uint32 /* common_int_mwgwtypes.h:60:32 */
type Int_fast64_t = int64   /* common_int_mwgwtypes.h:61:32 */
type Uint_fast64_t = uint64 /* common_int_mwgwtypes.h:62:32 */

// 7.18.1.5 Greatest-width integer types

type Intmax_t = int64   /* common_int_mwgwtypes.h:66:33 */
type Uintmax_t = uint64 /* common_int_mwgwtypes.h:67:32 */

type U_char = uint8   /* types.h:101:23 */
type U_short = uint16 /* types.h:102:24 */
type U_int = uint32   /* types.h:103:22 */
type U_long = uint32  /* types.h:104:23 */

type Unchar = uint8  /* types.h:106:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:107:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:108:22 */ // Sys V compatibility
type Ulong = uint32  /* types.h:109:23 */ // Sys V compatibility

type U_quad_t = Uint64_t /* types.h:112:18 */ // quads
type Quad_t = Int64_t    /* types.h:113:18 */
type Qaddr_t = uintptr   /* types.h:114:16 */

// The types longlong_t and u_longlong_t exist for use with the
// Sun-derived XDR routines involving these types, and their usage
// in other contexts is discouraged.  Further note that these types
// may not be equivalent to "long long" and "unsigned long long",
// they are only guaranteed to be signed and unsigned 64-bit types
// respectively.  Portable programs that need 64-bit types should use
// the C99 types int64_t and uint64_t instead.

type Longlong_t = Int64_t    /* types.h:126:18 */ // for XDR
type U_longlong_t = Uint64_t /* types.h:127:18 */ // for XDR

type Blkcnt_t = Int64_t  /* types.h:129:18 */ // fs block count
type Blksize_t = Int32_t /* types.h:130:18 */ // fs optimal block size

type Fsblkcnt_t = X__fsblkcnt_t /* types.h:133:22 */ // fs block count (statvfs)

type Fsfilcnt_t = X__fsfilcnt_t /* types.h:138:22 */ // fs file count

// We don't and shouldn't use caddr_t in the kernel anymore
type Caddr_t = X__caddr_t /* types.h:145:19 */ // core address

type Daddr_t = Int64_t /* types.h:154:18 */ // disk address

type Dev_t = Uint64_t   /* types.h:157:18 */ // device number
type Fixpt_t = Uint32_t /* types.h:158:18 */ // fixed point number

type Gid_t = X__gid_t /* types.h:161:18 */ // group id

type Id_t = Uint32_t  /* types.h:165:18 */ // group id, process id or user id
type Ino_t = Uint64_t /* types.h:166:18 */ // inode number
type Key_t = int32    /* types.h:167:15 */ // IPC key (for Sys V IPC)

type Mode_t = X__mode_t /* types.h:170:18 */ // permissions

type Nlink_t = Uint32_t /* types.h:174:18 */ // link count

type Off_t = X__off_t /* types.h:177:18 */ // file offset

type Pid_t = X__pid_t  /* types.h:182:18 */ // process id
type Lwpid_t = Int32_t /* types.h:185:18 */ // LWP id
type Rlim_t = Uint64_t /* types.h:186:18 */ // resource limit
type Segsz_t = Int32_t /* types.h:187:18 */ // segment size
type Swblk_t = Int32_t /* types.h:188:18 */ // swap offset

type Uid_t = X__uid_t /* types.h:191:18 */ // user id

type Mqd_t = int32 /* types.h:195:14 */

type Cpuid_t = uint32 /* types.h:197:23 */

type Psetid_t = int32 /* types.h:199:14 */

type X__cpu_simple_lock_t = X__cpu_simple_lock_nv_t /* types.h:201:41 */

// Major, minor numbers, dev_t's.
type X__devmajor_t = Int32_t /* types.h:255:17 */
type X__devminor_t = Int32_t /* types.h:255:31 */

type Clock_t = uint32 /* types.h:268:24 */

type Ssize_t = int32 /* types.h:284:24 */

type Time_t = X__int64_t /* types.h:289:23 */

type Clockid_t = int32 /* types.h:294:26 */

type Timer_t = int32 /* types.h:299:24 */

type Suseconds_t = int32 /* types.h:304:27 */

type Useconds_t = uint32 /* types.h:309:26 */

//	$NetBSD: fd_set.h,v 1.7 2018/06/24 12:05:40 kamil Exp $

// -
// Copyright (c) 1992, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	8.4 (Berkeley) 1/21/94

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// Implementation dependent defines, hidden from user space.
// POSIX does not specify them.

type X__fd_mask = X__uint32_t /* fd_set.h:46:20 */

// 32 = 2 ^ 5

// Select uses bit fields of file descriptors.  These macros manipulate
// such bit fields.  Note: FD_SETSIZE may be defined by the user.

type Fd_set1 = struct{ Ffds_bits [8]X__fd_mask } /* fd_set.h:66:9 */

// 32 = 2 ^ 5

// Select uses bit fields of file descriptors.  These macros manipulate
// such bit fields.  Note: FD_SETSIZE may be defined by the user.

type Fd_set = Fd_set1 /* fd_set.h:68:3 */

// Expose our internals if we are not required to hide them.

type Kauth_cred_t = uintptr /* types.h:318:27 */

type Pri_t = int32 /* types.h:320:13 */

//	$NetBSD: pthread_types.h,v 1.23 2017/09/09 23:21:45 kamil Exp $

// -
// Copyright (c) 2001, 2008 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Nathan J. Williams.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// We use the "pthread_spin_t" name internally; "pthread_spinlock_t" is the
// POSIX spinlock object.
//
// C++ expects to be using PTHREAD_FOO_INITIALIZER as a member initializer.
// This does not work for volatile types.  Since C++ does not touch the guts
// of those types, we do not include volatile in the C++ definitions.
type Pthread_spin_t = X__cpu_simple_lock_t /* pthread_types.h:43:29 */
type X__pthread_spin_t = Pthread_spin_t    /* pthread_types.h:48:24 */

// Copied from PTQ_HEAD in pthread_queue.h

type Pthread_queue_struct_t = struct {
	Fptqh_first uintptr
	Fptqh_last  uintptr
} /* pthread_types.h:61:1 */

type Pthread_queue_t = Pthread_queue_struct_t /* pthread_types.h:62:39 */
type X__pthread_attr_st = struct {
	Fpta_magic   uint32
	Fpta_flags   int32
	Fpta_private uintptr
} /* pthread_types.h:65:1 */

type X__pthread_mutex_st = struct {
	Fptm_magic      uint32
	Fptm_errorcheck X__pthread_spin_t
	F__8            struct {
		F__ccgo_pad1 [0]uint32
		Fptm_ceiling uint8
		F__ccgo_pad2 [3]byte
	}
	Fptm_owner    Pthread_t
	Fptm_waiters  uintptr
	Fptm_recursed uint32
	Fptm_spare2   uintptr
} /* pthread_types.h:66:1 */

type X__pthread_mutexattr_st = struct {
	Fptma_magic   uint32
	Fptma_private uintptr
} /* pthread_types.h:67:1 */

type X__pthread_cond_st = struct {
	Fptc_magic   uint32
	Fptc_lock    X__pthread_spin_t
	Fptc_waiters Pthread_queue_t
	Fptc_mutex   uintptr
	Fptc_private uintptr
} /* pthread_types.h:68:1 */

type X__pthread_condattr_st = struct {
	Fptca_magic   uint32
	Fptca_private uintptr
} /* pthread_types.h:69:1 */

type X__pthread_rwlock_st = struct {
	Fptr_magic     uint32
	Fptr_interlock X__pthread_spin_t
	Fptr_rblocked  Pthread_queue_t
	Fptr_wblocked  Pthread_queue_t
	Fptr_nreaders  uint32
	Fptr_owner     Pthread_t
	Fptr_private   uintptr
} /* pthread_types.h:71:1 */

type X__pthread_rwlockattr_st = struct {
	Fptra_magic   uint32
	Fptra_private uintptr
} /* pthread_types.h:72:1 */

type X__pthread_barrier_st = struct {
	Fptb_magic      uint32
	Fptb_lock       Pthread_spin_t
	Fptb_waiters    Pthread_queue_t
	Fptb_initcount  uint32
	Fptb_curcount   uint32
	Fptb_generation uint32
	Fptb_private    uintptr
} /* pthread_types.h:73:1 */

type X__pthread_barrierattr_st = struct {
	Fptba_magic   uint32
	Fptba_private uintptr
} /* pthread_types.h:74:1 */

type Pthread_t = uintptr                           /* pthread_types.h:76:29 */
type Pthread_attr_t = X__pthread_attr_st           /* pthread_types.h:77:34 */
type Pthread_mutex_t = X__pthread_mutex_st         /* pthread_types.h:78:35 */
type Pthread_mutexattr_t = X__pthread_mutexattr_st /* pthread_types.h:79:39 */
type Pthread_cond_t = X__pthread_cond_st           /* pthread_types.h:80:34 */
type Pthread_condattr_t = X__pthread_condattr_st   /* pthread_types.h:81:38 */
type X__pthread_once_st = struct {
	Fpto_mutex Pthread_mutex_t
	Fpto_done  int32
} /* pthread_types.h:82:9 */

type Pthread_once_t = X__pthread_once_st /* pthread_types.h:82:34 */
type X__pthread_spinlock_st = struct {
	Fpts_magic uint32
	Fpts_spin  X__pthread_spin_t
	Fpts_flags int32
} /* pthread_types.h:83:9 */

type Pthread_spinlock_t = X__pthread_spinlock_st       /* pthread_types.h:83:38 */
type Pthread_rwlock_t = X__pthread_rwlock_st           /* pthread_types.h:84:36 */
type Pthread_rwlockattr_t = X__pthread_rwlockattr_st   /* pthread_types.h:85:40 */
type Pthread_barrier_t = X__pthread_barrier_st         /* pthread_types.h:86:37 */
type Pthread_barrierattr_t = X__pthread_barrierattr_st /* pthread_types.h:87:41 */
type Pthread_key_t = int32                             /* pthread_types.h:88:13 */

type Div_t = struct {
	Fquot int32
	Frem  int32
} /* stdlib.h:59:3 */

type Ldiv_t = struct {
	Fquot int32
	Frem  int32
} /* stdlib.h:64:3 */

type Lldiv_t = struct {
	Fquot int64
	Frem  int64
} /* stdlib.h:74:3 */

type Qdiv_t = struct {
	Fquot Quad_t
	Frem  Quad_t
} /* stdlib.h:81:3 */

type Locale_t = uintptr /* stdlib.h:357:25 */

var _ uint8 /* gen.c:2:13: */
