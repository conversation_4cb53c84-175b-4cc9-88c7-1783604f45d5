// Code generated by 'ccgo stdio/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o stdio/stdio_netbsd_arm.go -pkgname stdio', DO NOT EDIT.

package stdio

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BUFSIZ                    = 1024      // stdio.h:182:1:
	EOF                       = -1        // stdio.h:183:1:
	FILENAME_MAX              = 1024      // stdio.h:192:1:
	FOPEN_MAX                 = 20        // stdio.h:191:1:
	FPARSELN_UNESCALL         = 0x0f      // stdio.h:411:1:
	FPARSELN_UNESCCOMM        = 0x04      // stdio.h:409:1:
	FPARSELN_UNESCCONT        = 0x02      // stdio.h:408:1:
	FPARSELN_UNESCESC         = 0x01      // stdio.h:407:1:
	FPARSELN_UNESCREST        = 0x08      // stdio.h:410:1:
	L_ctermid                 = 1024      // stdio.h:292:1:
	L_cuserid                 = 9         // stdio.h:293:1:
	L_tmpnam                  = 1024      // stdio.h:198:1:
	P_tmpdir                  = "/tmp/"   // stdio.h:196:1:
	SEEK_CUR                  = 1         // stdio.h:209:1:
	SEEK_END                  = 2         // stdio.h:212:1:
	SEEK_SET                  = 0         // stdio.h:206:1:
	TMP_MAX                   = 308915776 // stdio.h:201:1:
	X_ARM_ARCH_4T             = 0         // cdefs.h:44:1:
	X_ARM_ARCH_5              = 0         // cdefs.h:40:1:
	X_ARM_ARCH_5T             = 0         // cdefs.h:36:1:
	X_ARM_ARCH_6              = 0         // cdefs.h:31:1:
	X_ARM_ARCH_7              = 0         // cdefs.h:20:1:
	X_ARM_ARCH_DWORD_OK       = 0         // cdefs.h:51:1:
	X_ARM_ARCH_T2             = 0         // cdefs.h:24:1:
	X_ARM_CDEFS_H_            = 0         // cdefs.h:4:1:
	X_ARM_INT_TYPES_H_        = 0         // int_types.h:33:1:
	X_FILE_OFFSET_BITS        = 64        // <builtin>:25:1:
	X_FSTDIO                  = 0         // stdio.h:72:1:
	X_IOFBF                   = 0         // stdio.h:178:1:
	X_IOLBF                   = 1         // stdio.h:179:1:
	X_IONBF                   = 2         // stdio.h:180:1:
	X_NETBSD_SOURCE           = 1         // featuretest.h:70:1:
	X_STDIO_H_                = 0         // stdio.h:38:1:
	X_SYS_ANSI_H_             = 0         // ansi.h:33:1:
	X_SYS_CDEFS_ELF_H_        = 0         // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_            = 0         // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_      = 0         // common_ansi.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_ = 0         // common_int_types.h:33:1:
	X_SYS_NULL_H_             = 0         // null.h:9:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// return true if value 'a' fits in type 't'

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: ansi.h,v 1.18 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// ******** Integer types capable of holding object pointers

type X__intptr_t = int32   /* common_int_types.h:58:27 */
type X__uintptr_t = uint32 /* common_int_types.h:59:26 */

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */
type Ssize_t = int32                 /* stdio.h:49:23 */

//	$NetBSD: null.h,v 1.9 2010/07/06 11:56:20 kleink Exp $

// Written by Klaus Klein <<EMAIL>>, December 22, 1999.
// Public domain.

// This is fairly grotesque, but pure ANSI code must not inspect the
// innards of an fpos_t anyway.  The library internally uses off_t,
// which we assume is exactly as big as eight chars.
type X__sfpos = struct {
	F_pos         X__off_t
	F_mbstate_in  X__mbstate_t
	F_mbstate_out X__mbstate_t
} /* stdio.h:67:9 */

//	$NetBSD: null.h,v 1.9 2010/07/06 11:56:20 kleink Exp $

// Written by Klaus Klein <<EMAIL>>, December 22, 1999.
// Public domain.

// This is fairly grotesque, but pure ANSI code must not inspect the
// innards of an fpos_t anyway.  The library internally uses off_t,
// which we assume is exactly as big as eight chars.
type Fpos_t = X__sfpos /* stdio.h:70:3 */

// NB: to fit things in six character monocase externals, the stdio
// code uses the prefix `__s' for stdio objects, typically followed
// by a three-character attempt at a mnemonic.

// stdio buffers
type X__sbuf = struct {
	F_base uintptr
	F_size int32
} /* stdio.h:81:1 */

// stdio state variables.
//
// The following always hold:
//
//	if (_flags&(__SLBF|__SWR)) == (__SLBF|__SWR),
//		_lbfsize is -_bf._size, else _lbfsize is 0
//	if _flags&__SRD, _w is 0
//	if _flags&__SWR, _r is 0
//
// This ensures that the getc and putc macros (or inline functions) never
// try to write or read from a file that is in `read' or `write' mode.
// (Moreover, they can, and do, automatically switch from read mode to
// write mode, and back, on "r+" and "w+" files.)
//
// _lbfsize is used only to make the inline line-buffered output stream
// code as compact as possible.
//
// _ub, _up, and _ur are used when ungetc() pushes back more characters
// than fit in the current _bf, or when ungetc() pushes back a character
// that does not match the previous one in _bf.  When this happens,
// _ub._base becomes non-nil (i.e., a stream has ungetc() data iff
// _ub._base!=NULL) and _up and _ur save the current values of _p and _r.
//
// NB: see WARNING above before changing the layout of this structure!
type X__sFILE = struct {
	F_p     uintptr
	F_r     int32
	F_w     int32
	F_flags uint16
	F_file  int16
	F_bf    struct {
		F_base uintptr
		F_size int32
	}
	F_lbfsize int32
	F_cookie  uintptr
	F_close   uintptr
	F_read    uintptr
	F_seek    uintptr
	F_write   uintptr
	F_ext     struct {
		F_base uintptr
		F_size int32
	}
	F_up        uintptr
	F_ur        int32
	F_ubuf      [3]uint8
	F_nbuf      [1]uint8
	F_flush     uintptr
	F_lb_unused [4]uint8
	F_blksize   int32
	F_offset    X__off_t
} /* stdio.h:112:9 */

// stdio state variables.
//
// The following always hold:
//
//	if (_flags&(__SLBF|__SWR)) == (__SLBF|__SWR),
//		_lbfsize is -_bf._size, else _lbfsize is 0
//	if _flags&__SRD, _w is 0
//	if _flags&__SWR, _r is 0
//
// This ensures that the getc and putc macros (or inline functions) never
// try to write or read from a file that is in `read' or `write' mode.
// (Moreover, they can, and do, automatically switch from read mode to
// write mode, and back, on "r+" and "w+" files.)
//
// _lbfsize is used only to make the inline line-buffered output stream
// code as compact as possible.
//
// _ub, _up, and _ur are used when ungetc() pushes back more characters
// than fit in the current _bf, or when ungetc() pushes back a character
// that does not match the previous one in _bf.  When this happens,
// _ub._base becomes non-nil (i.e., a stream has ungetc() data iff
// _ub._base!=NULL) and _up and _ur save the current values of _p and _r.
//
// NB: see WARNING above before changing the layout of this structure!
type FILE = X__sFILE /* stdio.h:146:3 */

// X/Open CAE Specification Issue 5 Version 2
type Off_t = X__off_t /* stdio.h:376:18 */

type Locale_t = uintptr /* stdio.h:543:25 */

var _ uint8 /* gen.c:2:13: */
