// Code generated by 'ccgo signal/gen.c -crt-import-path  -export-defines  -export-enums  -export-externs X -export-fields F -export-structs  -export-typedefs  -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o signal/signal_darwin_arm64.go -pkgname signal', DO NOT EDIT.

package signal

var CAPI = map[string]struct{}{
	"__darwin_check_fd_set_overflow": {},
	"__sigbits":                      {},
}
