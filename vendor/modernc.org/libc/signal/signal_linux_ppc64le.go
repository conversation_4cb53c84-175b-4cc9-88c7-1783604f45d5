// Code generated by 'ccgo signal/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o signal/signal_linux_ppc64le.go -pkgname signal', DO NOT EDIT.

package signal

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AT_DCACHEBSIZE                      = 19                 // auxvec.h:9:1:
	AT_ICACHEBSIZE                      = 20                 // auxvec.h:10:1:
	AT_IGNOREPPC                        = 22                 // auxvec.h:13:1:
	AT_L1D_CACHEGEOMETRY                = 43                 // auxvec.h:45:1:
	AT_L1D_CACHESIZE                    = 42                 // auxvec.h:44:1:
	AT_L1I_CACHEGEOMETRY                = 41                 // auxvec.h:43:1:
	AT_L1I_CACHESIZE                    = 40                 // auxvec.h:42:1:
	AT_L2_CACHEGEOMETRY                 = 45                 // auxvec.h:47:1:
	AT_L2_CACHESIZE                     = 44                 // auxvec.h:46:1:
	AT_L3_CACHEGEOMETRY                 = 47                 // auxvec.h:49:1:
	AT_L3_CACHESIZE                     = 46                 // auxvec.h:48:1:
	AT_SYSINFO_EHDR                     = 33                 // auxvec.h:18:1:
	AT_UCACHEBSIZE                      = 21                 // auxvec.h:11:1:
	AT_VECTOR_SIZE_ARCH                 = 14                 // auxvec.h:51:1:
	ELF_NEBB                            = 3                  // elf.h:98:1:
	ELF_NFPREG                          = 33                 // elf.h:94:1:
	ELF_NGREG                           = 48                 // elf.h:93:1:
	ELF_NPKEY                           = 3                  // elf.h:100:1:
	ELF_NPMU                            = 5                  // elf.h:99:1:
	ELF_NTMSPRREG                       = 3                  // elf.h:97:1:
	ELF_NVMX                            = 34                 // elf.h:95:1:
	ELF_NVRREG                          = 34                 // elf.h:114:1:
	ELF_NVRREG32                        = 33                 // elf.h:113:1:
	ELF_NVSRHALFREG                     = 32                 // elf.h:115:1:
	ELF_NVSX                            = 32                 // elf.h:96:1:
	MINSIGSTKSZ                         = 4096               // sigstack.h:27:1:
	NFPREG                              = 33                 // ucontext.h:89:1:
	NGREG                               = 48                 // ucontext.h:88:1:
	NSIG                                = 65                 // signal.h:181:1:
	NVRREG                              = 34                 // ucontext.h:90:1:
	PPC_BREAKPOINT_CONDITION_AND        = 0x00000001         // ptrace.h:259:1:
	PPC_BREAKPOINT_CONDITION_AND_OR     = 0x00000003         // ptrace.h:262:1:
	PPC_BREAKPOINT_CONDITION_BE_ALL     = 0x00ff0000         // ptrace.h:263:1:
	PPC_BREAKPOINT_CONDITION_BE_SHIFT   = 16                 // ptrace.h:264:1:
	PPC_BREAKPOINT_CONDITION_EXACT      = 1                  // ptrace.h:260:1:
	PPC_BREAKPOINT_CONDITION_MODE       = 0x00000003         // ptrace.h:257:1:
	PPC_BREAKPOINT_CONDITION_NONE       = 0x00000000         // ptrace.h:258:1:
	PPC_BREAKPOINT_CONDITION_OR         = 0x00000002         // ptrace.h:261:1:
	PPC_BREAKPOINT_MODE_EXACT           = 0x00000000         // ptrace.h:249:1:
	PPC_BREAKPOINT_MODE_MASK            = 0x00000003         // ptrace.h:252:1:
	PPC_BREAKPOINT_MODE_RANGE_EXCLUSIVE = 0x00000002         // ptrace.h:251:1:
	PPC_BREAKPOINT_MODE_RANGE_INCLUSIVE = 0x00000001         // ptrace.h:250:1:
	PPC_BREAKPOINT_TRIGGER_EXECUTE      = 0x00000001         // ptrace.h:240:1:
	PPC_BREAKPOINT_TRIGGER_READ         = 0x00000002         // ptrace.h:241:1:
	PPC_BREAKPOINT_TRIGGER_RW           = 6                  // ptrace.h:243:1:
	PPC_BREAKPOINT_TRIGGER_WRITE        = 0x00000004         // ptrace.h:242:1:
	PPC_DEBUG_FEATURE_DATA_BP_ARCH_31   = 0x0000000000000020 // ptrace.h:221:1:
	PPC_DEBUG_FEATURE_DATA_BP_DAWR      = 0x0000000000000010 // ptrace.h:220:1:
	PPC_DEBUG_FEATURE_DATA_BP_MASK      = 0x0000000000000008 // ptrace.h:219:1:
	PPC_DEBUG_FEATURE_DATA_BP_RANGE     = 0x0000000000000004 // ptrace.h:218:1:
	PPC_DEBUG_FEATURE_INSN_BP_MASK      = 0x0000000000000002 // ptrace.h:217:1:
	PPC_DEBUG_FEATURE_INSN_BP_RANGE     = 0x0000000000000001 // ptrace.h:216:1:
	PPC_FEATURE2_ARCH_2_07              = 0x80000000         // cputable.h:40:1:
	PPC_FEATURE2_ARCH_3_00              = 0x00800000         // cputable.h:48:1:
	PPC_FEATURE2_ARCH_3_1               = 0x00040000         // cputable.h:53:1:
	PPC_FEATURE2_DARN                   = 0x00200000         // cputable.h:50:1:
	PPC_FEATURE2_DSCR                   = 0x20000000         // cputable.h:42:1:
	PPC_FEATURE2_EBB                    = 0x10000000         // cputable.h:43:1:
	PPC_FEATURE2_HAS_IEEE128            = 0x00400000         // cputable.h:49:1:
	PPC_FEATURE2_HTM                    = 0x40000000         // cputable.h:41:1:
	PPC_FEATURE2_HTM_NOSC               = 0x01000000         // cputable.h:47:1:
	PPC_FEATURE2_HTM_NO_SUSPEND         = 0x00080000         // cputable.h:52:1:
	PPC_FEATURE2_ISEL                   = 0x08000000         // cputable.h:44:1:
	PPC_FEATURE2_MMA                    = 0x00020000         // cputable.h:54:1:
	PPC_FEATURE2_SCV                    = 0x00100000         // cputable.h:51:1:
	PPC_FEATURE2_TAR                    = 0x04000000         // cputable.h:45:1:
	PPC_FEATURE2_VEC_CRYPTO             = 0x02000000         // cputable.h:46:1:
	PPC_FEATURE_32                      = 0x80000000         // cputable.h:6:1:
	PPC_FEATURE_601_INSTR               = 0x20000000         // cputable.h:8:1:
	PPC_FEATURE_64                      = 0x40000000         // cputable.h:7:1:
	PPC_FEATURE_ARCH_2_05               = 0x00001000         // cputable.h:25:1:
	PPC_FEATURE_ARCH_2_06               = 0x00000100         // cputable.h:29:1:
	PPC_FEATURE_BOOKE                   = 0x00008000         // cputable.h:22:1:
	PPC_FEATURE_CELL                    = 0x00010000         // cputable.h:21:1:
	PPC_FEATURE_HAS_4xxMAC              = 0x02000000         // cputable.h:12:1:
	PPC_FEATURE_HAS_ALTIVEC             = 0x10000000         // cputable.h:9:1:
	PPC_FEATURE_HAS_DFP                 = 0x00000400         // cputable.h:27:1:
	PPC_FEATURE_HAS_EFP_DOUBLE          = 0x00200000         // cputable.h:16:1:
	PPC_FEATURE_HAS_EFP_SINGLE          = 0x00400000         // cputable.h:15:1:
	PPC_FEATURE_HAS_FPU                 = 0x08000000         // cputable.h:10:1:
	PPC_FEATURE_HAS_MMU                 = 0x04000000         // cputable.h:11:1:
	PPC_FEATURE_HAS_SPE                 = 0x00800000         // cputable.h:14:1:
	PPC_FEATURE_HAS_VSX                 = 0x00000080         // cputable.h:30:1:
	PPC_FEATURE_ICACHE_SNOOP            = 0x00002000         // cputable.h:24:1:
	PPC_FEATURE_NO_TB                   = 0x00100000         // cputable.h:17:1:
	PPC_FEATURE_PA6T                    = 0x00000800         // cputable.h:26:1:
	PPC_FEATURE_POWER4                  = 0x00080000         // cputable.h:18:1:
	PPC_FEATURE_POWER5                  = 0x00040000         // cputable.h:19:1:
	PPC_FEATURE_POWER5_PLUS             = 0x00020000         // cputable.h:20:1:
	PPC_FEATURE_POWER6_EXT              = 0x00000200         // cputable.h:28:1:
	PPC_FEATURE_PPC_LE                  = 0x00000001         // cputable.h:37:1:
	PPC_FEATURE_PSERIES_PERFMON_COMPAT  = 0x00000040         // cputable.h:32:1:
	PPC_FEATURE_SMT                     = 0x00004000         // cputable.h:23:1:
	PPC_FEATURE_TRUE_LE                 = 0x00000002         // cputable.h:36:1:
	PPC_FEATURE_UNIFIED_CACHE           = 0x01000000         // cputable.h:13:1:
	PPC_PTRACE_DELHWDEBUG               = 0x87               // ptrace.h:197:1:
	PPC_PTRACE_GETHWDBGINFO             = 0x89               // ptrace.h:195:1:
	PPC_PTRACE_PEEKDATA_3264            = 0x94               // ptrace.h:187:1:
	PPC_PTRACE_PEEKTEXT_3264            = 0x95               // ptrace.h:186:1:
	PPC_PTRACE_PEEKUSR_3264             = 0x91               // ptrace.h:190:1:
	PPC_PTRACE_POKEDATA_3264            = 0x92               // ptrace.h:189:1:
	PPC_PTRACE_POKETEXT_3264            = 0x93               // ptrace.h:188:1:
	PPC_PTRACE_POKEUSR_3264             = 0x90               // ptrace.h:191:1:
	PPC_PTRACE_SETHWDEBUG               = 0x88               // ptrace.h:196:1:
	PTRACE_GETEVRREGS                   = 0x14               // ptrace.h:157:1:
	PTRACE_GETFPREGS                    = 0xe                // ptrace.h:180:1:
	PTRACE_GETREGS                      = 0xc                // ptrace.h:178:1:
	PTRACE_GETREGS64                    = 0x16               // ptrace.h:182:1:
	PTRACE_GETVRREGS                    = 0x12               // ptrace.h:152:1:
	PTRACE_GETVSRREGS                   = 0x1b               // ptrace.h:161:1:
	PTRACE_GET_DEBUGREG                 = 0x19               // ptrace.h:172:1:
	PTRACE_SETEVRREGS                   = 0x15               // ptrace.h:158:1:
	PTRACE_SETFPREGS                    = 0xf                // ptrace.h:181:1:
	PTRACE_SETREGS                      = 0xd                // ptrace.h:179:1:
	PTRACE_SETREGS64                    = 0x17               // ptrace.h:183:1:
	PTRACE_SETVRREGS                    = 0x13               // ptrace.h:153:1:
	PTRACE_SETVSRREGS                   = 0x1c               // ptrace.h:162:1:
	PTRACE_SET_DEBUGREG                 = 0x1a               // ptrace.h:173:1:
	PTRACE_SINGLEBLOCK                  = 0x100              // ptrace.h:193:1:
	PTRACE_SYSEMU                       = 0x1d               // ptrace.h:165:1:
	PTRACE_SYSEMU_SINGLESTEP            = 0x1e               // ptrace.h:166:1:
	PT_CCR                              = 38                 // ptrace.h:103:1:
	PT_CTR                              = 35                 // ptrace.h:100:1:
	PT_DAR                              = 41                 // ptrace.h:110:1:
	PT_DSCR                             = 44                 // ptrace.h:113:1:
	PT_DSISR                            = 42                 // ptrace.h:111:1:
	PT_FPR0                             = 48                 // ptrace.h:116:1:
	PT_FPSCR                            = 80                 // ptrace.h:125:1:
	PT_LNK                              = 36                 // ptrace.h:101:1:
	PT_MSR                              = 33                 // ptrace.h:98:1:
	PT_NIP                              = 32                 // ptrace.h:97:1:
	PT_ORIG_R3                          = 34                 // ptrace.h:99:1:
	PT_R0                               = 0                  // ptrace.h:64:1:
	PT_R1                               = 1                  // ptrace.h:65:1:
	PT_R10                              = 10                 // ptrace.h:74:1:
	PT_R11                              = 11                 // ptrace.h:75:1:
	PT_R12                              = 12                 // ptrace.h:76:1:
	PT_R13                              = 13                 // ptrace.h:77:1:
	PT_R14                              = 14                 // ptrace.h:78:1:
	PT_R15                              = 15                 // ptrace.h:79:1:
	PT_R16                              = 16                 // ptrace.h:80:1:
	PT_R17                              = 17                 // ptrace.h:81:1:
	PT_R18                              = 18                 // ptrace.h:82:1:
	PT_R19                              = 19                 // ptrace.h:83:1:
	PT_R2                               = 2                  // ptrace.h:66:1:
	PT_R20                              = 20                 // ptrace.h:84:1:
	PT_R21                              = 21                 // ptrace.h:85:1:
	PT_R22                              = 22                 // ptrace.h:86:1:
	PT_R23                              = 23                 // ptrace.h:87:1:
	PT_R24                              = 24                 // ptrace.h:88:1:
	PT_R25                              = 25                 // ptrace.h:89:1:
	PT_R26                              = 26                 // ptrace.h:90:1:
	PT_R27                              = 27                 // ptrace.h:91:1:
	PT_R28                              = 28                 // ptrace.h:92:1:
	PT_R29                              = 29                 // ptrace.h:93:1:
	PT_R3                               = 3                  // ptrace.h:67:1:
	PT_R30                              = 30                 // ptrace.h:94:1:
	PT_R31                              = 31                 // ptrace.h:95:1:
	PT_R4                               = 4                  // ptrace.h:68:1:
	PT_R5                               = 5                  // ptrace.h:69:1:
	PT_R6                               = 6                  // ptrace.h:70:1:
	PT_R7                               = 7                  // ptrace.h:71:1:
	PT_R8                               = 8                  // ptrace.h:72:1:
	PT_R9                               = 9                  // ptrace.h:73:1:
	PT_REGS_COUNT                       = 44                 // ptrace.h:114:1:
	PT_RESULT                           = 43                 // ptrace.h:112:1:
	PT_SOFTE                            = 39                 // ptrace.h:107:1:
	PT_TRAP                             = 40                 // ptrace.h:109:1:
	PT_VR0                              = 82                 // ptrace.h:128:1:
	PT_VRSAVE                           = 148                // ptrace.h:130:1:
	PT_VSCR                             = 147                // ptrace.h:129:1:
	PT_VSR0                             = 150                // ptrace.h:136:1:
	PT_VSR31                            = 212                // ptrace.h:137:1:
	PT_XER                              = 37                 // ptrace.h:102:1:
	R_PPC64_ADDR14                      = 7                  // elf.h:175:1:
	R_PPC64_ADDR14_BRNTAKEN             = 9                  // elf.h:177:1:
	R_PPC64_ADDR14_BRTAKEN              = 8                  // elf.h:176:1:
	R_PPC64_ADDR16                      = 3                  // elf.h:171:1:
	R_PPC64_ADDR16_DS                   = 56                 // elf.h:225:1:
	R_PPC64_ADDR16_HA                   = 6                  // elf.h:174:1:
	R_PPC64_ADDR16_HI                   = 5                  // elf.h:173:1:
	R_PPC64_ADDR16_HIGHER               = 39                 // elf.h:207:1:
	R_PPC64_ADDR16_HIGHERA              = 40                 // elf.h:208:1:
	R_PPC64_ADDR16_HIGHEST              = 41                 // elf.h:209:1:
	R_PPC64_ADDR16_HIGHESTA             = 42                 // elf.h:210:1:
	R_PPC64_ADDR16_LO                   = 4                  // elf.h:172:1:
	R_PPC64_ADDR16_LO_DS                = 57                 // elf.h:226:1:
	R_PPC64_ADDR24                      = 2                  // elf.h:170:1:
	R_PPC64_ADDR30                      = 37                 // elf.h:205:1:
	R_PPC64_ADDR32                      = 1                  // elf.h:169:1:
	R_PPC64_ADDR64                      = 38                 // elf.h:206:1:
	R_PPC64_COPY                        = 19                 // elf.h:187:1:
	R_PPC64_DTPMOD64                    = 68                 // elf.h:239:1:
	R_PPC64_DTPREL16                    = 74                 // elf.h:245:1:
	R_PPC64_DTPREL16_DS                 = 101                // elf.h:272:1:
	R_PPC64_DTPREL16_HA                 = 77                 // elf.h:248:1:
	R_PPC64_DTPREL16_HI                 = 76                 // elf.h:247:1:
	R_PPC64_DTPREL16_HIGHER             = 103                // elf.h:274:1:
	R_PPC64_DTPREL16_HIGHERA            = 104                // elf.h:275:1:
	R_PPC64_DTPREL16_HIGHEST            = 105                // elf.h:276:1:
	R_PPC64_DTPREL16_HIGHESTA           = 106                // elf.h:277:1:
	R_PPC64_DTPREL16_LO                 = 75                 // elf.h:246:1:
	R_PPC64_DTPREL16_LO_DS              = 102                // elf.h:273:1:
	R_PPC64_DTPREL64                    = 78                 // elf.h:249:1:
	R_PPC64_ENTRY                       = 118                // elf.h:282:1:
	R_PPC64_GLOB_DAT                    = 20                 // elf.h:188:1:
	R_PPC64_GOT16                       = 14                 // elf.h:182:1:
	R_PPC64_GOT16_DS                    = 58                 // elf.h:227:1:
	R_PPC64_GOT16_HA                    = 17                 // elf.h:185:1:
	R_PPC64_GOT16_HI                    = 16                 // elf.h:184:1:
	R_PPC64_GOT16_LO                    = 15                 // elf.h:183:1:
	R_PPC64_GOT16_LO_DS                 = 59                 // elf.h:228:1:
	R_PPC64_GOT_DTPREL16_DS             = 91                 // elf.h:262:1:
	R_PPC64_GOT_DTPREL16_HA             = 94                 // elf.h:265:1:
	R_PPC64_GOT_DTPREL16_HI             = 93                 // elf.h:264:1:
	R_PPC64_GOT_DTPREL16_LO_DS          = 92                 // elf.h:263:1:
	R_PPC64_GOT_TLSGD16                 = 79                 // elf.h:250:1:
	R_PPC64_GOT_TLSGD16_HA              = 82                 // elf.h:253:1:
	R_PPC64_GOT_TLSGD16_HI              = 81                 // elf.h:252:1:
	R_PPC64_GOT_TLSGD16_LO              = 80                 // elf.h:251:1:
	R_PPC64_GOT_TLSLD16                 = 83                 // elf.h:254:1:
	R_PPC64_GOT_TLSLD16_HA              = 86                 // elf.h:257:1:
	R_PPC64_GOT_TLSLD16_HI              = 85                 // elf.h:256:1:
	R_PPC64_GOT_TLSLD16_LO              = 84                 // elf.h:255:1:
	R_PPC64_GOT_TPREL16_DS              = 87                 // elf.h:258:1:
	R_PPC64_GOT_TPREL16_HA              = 90                 // elf.h:261:1:
	R_PPC64_GOT_TPREL16_HI              = 89                 // elf.h:260:1:
	R_PPC64_GOT_TPREL16_LO_DS           = 88                 // elf.h:259:1:
	R_PPC64_JMP_SLOT                    = 21                 // elf.h:189:1:
	R_PPC64_NONE                        = 0                  // elf.h:168:1:
	R_PPC64_NUM                         = 253                // elf.h:290:1:
	R_PPC64_PLT16_HA                    = 31                 // elf.h:199:1:
	R_PPC64_PLT16_HI                    = 30                 // elf.h:198:1:
	R_PPC64_PLT16_LO                    = 29                 // elf.h:197:1:
	R_PPC64_PLT16_LO_DS                 = 60                 // elf.h:229:1:
	R_PPC64_PLT32                       = 27                 // elf.h:195:1:
	R_PPC64_PLT64                       = 45                 // elf.h:213:1:
	R_PPC64_PLTGOT16                    = 52                 // elf.h:220:1:
	R_PPC64_PLTGOT16_DS                 = 65                 // elf.h:234:1:
	R_PPC64_PLTGOT16_HA                 = 55                 // elf.h:223:1:
	R_PPC64_PLTGOT16_HI                 = 54                 // elf.h:222:1:
	R_PPC64_PLTGOT16_LO                 = 53                 // elf.h:221:1:
	R_PPC64_PLTGOT16_LO_DS              = 66                 // elf.h:235:1:
	R_PPC64_PLTREL32                    = 28                 // elf.h:196:1:
	R_PPC64_PLTREL64                    = 46                 // elf.h:214:1:
	R_PPC64_REL14                       = 11                 // elf.h:179:1:
	R_PPC64_REL14_BRNTAKEN              = 13                 // elf.h:181:1:
	R_PPC64_REL14_BRTAKEN               = 12                 // elf.h:180:1:
	R_PPC64_REL16                       = 249                // elf.h:284:1:
	R_PPC64_REL16_HA                    = 252                // elf.h:287:1:
	R_PPC64_REL16_HI                    = 251                // elf.h:286:1:
	R_PPC64_REL16_LO                    = 250                // elf.h:285:1:
	R_PPC64_REL24                       = 10                 // elf.h:178:1:
	R_PPC64_REL32                       = 26                 // elf.h:194:1:
	R_PPC64_REL64                       = 44                 // elf.h:212:1:
	R_PPC64_RELATIVE                    = 22                 // elf.h:190:1:
	R_PPC64_SECTOFF                     = 33                 // elf.h:201:1:
	R_PPC64_SECTOFF_DS                  = 61                 // elf.h:230:1:
	R_PPC64_SECTOFF_HA                  = 36                 // elf.h:204:1:
	R_PPC64_SECTOFF_HI                  = 35                 // elf.h:203:1:
	R_PPC64_SECTOFF_LO                  = 34                 // elf.h:202:1:
	R_PPC64_SECTOFF_LO_DS               = 62                 // elf.h:231:1:
	R_PPC64_TLS                         = 67                 // elf.h:238:1:
	R_PPC64_TLSGD                       = 107                // elf.h:278:1:
	R_PPC64_TLSLD                       = 108                // elf.h:279:1:
	R_PPC64_TOC                         = 51                 // elf.h:219:1:
	R_PPC64_TOC16                       = 47                 // elf.h:215:1:
	R_PPC64_TOC16_DS                    = 63                 // elf.h:232:1:
	R_PPC64_TOC16_HA                    = 50                 // elf.h:218:1:
	R_PPC64_TOC16_HI                    = 49                 // elf.h:217:1:
	R_PPC64_TOC16_LO                    = 48                 // elf.h:216:1:
	R_PPC64_TOC16_LO_DS                 = 64                 // elf.h:233:1:
	R_PPC64_TOCSAVE                     = 109                // elf.h:280:1:
	R_PPC64_TPREL16                     = 69                 // elf.h:240:1:
	R_PPC64_TPREL16_DS                  = 95                 // elf.h:266:1:
	R_PPC64_TPREL16_HA                  = 72                 // elf.h:243:1:
	R_PPC64_TPREL16_HI                  = 71                 // elf.h:242:1:
	R_PPC64_TPREL16_HIGHER              = 97                 // elf.h:268:1:
	R_PPC64_TPREL16_HIGHERA             = 98                 // elf.h:269:1:
	R_PPC64_TPREL16_HIGHEST             = 99                 // elf.h:270:1:
	R_PPC64_TPREL16_HIGHESTA            = 100                // elf.h:271:1:
	R_PPC64_TPREL16_LO                  = 70                 // elf.h:241:1:
	R_PPC64_TPREL16_LO_DS               = 96                 // elf.h:267:1:
	R_PPC64_TPREL64                     = 73                 // elf.h:244:1:
	R_PPC64_UADDR16                     = 25                 // elf.h:193:1:
	R_PPC64_UADDR32                     = 24                 // elf.h:192:1:
	R_PPC64_UADDR64                     = 43                 // elf.h:211:1:
	R_PPC_ADDR14                        = 7                  // elf.h:28:1:
	R_PPC_ADDR14_BRNTAKEN               = 9                  // elf.h:30:1:
	R_PPC_ADDR14_BRTAKEN                = 8                  // elf.h:29:1:
	R_PPC_ADDR16                        = 3                  // elf.h:24:1:
	R_PPC_ADDR16_HA                     = 6                  // elf.h:27:1:
	R_PPC_ADDR16_HI                     = 5                  // elf.h:26:1:
	R_PPC_ADDR16_LO                     = 4                  // elf.h:25:1:
	R_PPC_ADDR24                        = 2                  // elf.h:23:1:
	R_PPC_ADDR32                        = 1                  // elf.h:22:1:
	R_PPC_COPY                          = 19                 // elf.h:40:1:
	R_PPC_DTPMOD32                      = 68                 // elf.h:61:1:
	R_PPC_DTPREL16                      = 74                 // elf.h:67:1:
	R_PPC_DTPREL16_HA                   = 77                 // elf.h:70:1:
	R_PPC_DTPREL16_HI                   = 76                 // elf.h:69:1:
	R_PPC_DTPREL16_LO                   = 75                 // elf.h:68:1:
	R_PPC_DTPREL32                      = 78                 // elf.h:71:1:
	R_PPC_GLOB_DAT                      = 20                 // elf.h:41:1:
	R_PPC_GOT16                         = 14                 // elf.h:35:1:
	R_PPC_GOT16_HA                      = 17                 // elf.h:38:1:
	R_PPC_GOT16_HI                      = 16                 // elf.h:37:1:
	R_PPC_GOT16_LO                      = 15                 // elf.h:36:1:
	R_PPC_GOT_DTPREL16                  = 91                 // elf.h:84:1:
	R_PPC_GOT_DTPREL16_HA               = 94                 // elf.h:87:1:
	R_PPC_GOT_DTPREL16_HI               = 93                 // elf.h:86:1:
	R_PPC_GOT_DTPREL16_LO               = 92                 // elf.h:85:1:
	R_PPC_GOT_TLSGD16                   = 79                 // elf.h:72:1:
	R_PPC_GOT_TLSGD16_HA                = 82                 // elf.h:75:1:
	R_PPC_GOT_TLSGD16_HI                = 81                 // elf.h:74:1:
	R_PPC_GOT_TLSGD16_LO                = 80                 // elf.h:73:1:
	R_PPC_GOT_TLSLD16                   = 83                 // elf.h:76:1:
	R_PPC_GOT_TLSLD16_HA                = 86                 // elf.h:79:1:
	R_PPC_GOT_TLSLD16_HI                = 85                 // elf.h:78:1:
	R_PPC_GOT_TLSLD16_LO                = 84                 // elf.h:77:1:
	R_PPC_GOT_TPREL16                   = 87                 // elf.h:80:1:
	R_PPC_GOT_TPREL16_HA                = 90                 // elf.h:83:1:
	R_PPC_GOT_TPREL16_HI                = 89                 // elf.h:82:1:
	R_PPC_GOT_TPREL16_LO                = 88                 // elf.h:81:1:
	R_PPC_JMP_SLOT                      = 21                 // elf.h:42:1:
	R_PPC_LOCAL24PC                     = 23                 // elf.h:44:1:
	R_PPC_NONE                          = 0                  // elf.h:21:1:
	R_PPC_NUM                           = 95                 // elf.h:90:1:
	R_PPC_PLT16_HA                      = 31                 // elf.h:52:1:
	R_PPC_PLT16_HI                      = 30                 // elf.h:51:1:
	R_PPC_PLT16_LO                      = 29                 // elf.h:50:1:
	R_PPC_PLT32                         = 27                 // elf.h:48:1:
	R_PPC_PLTREL24                      = 18                 // elf.h:39:1:
	R_PPC_PLTREL32                      = 28                 // elf.h:49:1:
	R_PPC_REL14                         = 11                 // elf.h:32:1:
	R_PPC_REL14_BRNTAKEN                = 13                 // elf.h:34:1:
	R_PPC_REL14_BRTAKEN                 = 12                 // elf.h:33:1:
	R_PPC_REL24                         = 10                 // elf.h:31:1:
	R_PPC_REL32                         = 26                 // elf.h:47:1:
	R_PPC_RELATIVE                      = 22                 // elf.h:43:1:
	R_PPC_SDAREL16                      = 32                 // elf.h:53:1:
	R_PPC_SECTOFF                       = 33                 // elf.h:54:1:
	R_PPC_SECTOFF_HA                    = 36                 // elf.h:57:1:
	R_PPC_SECTOFF_HI                    = 35                 // elf.h:56:1:
	R_PPC_SECTOFF_LO                    = 34                 // elf.h:55:1:
	R_PPC_TLS                           = 67                 // elf.h:60:1:
	R_PPC_TPREL16                       = 69                 // elf.h:62:1:
	R_PPC_TPREL16_HA                    = 72                 // elf.h:65:1:
	R_PPC_TPREL16_HI                    = 71                 // elf.h:64:1:
	R_PPC_TPREL16_LO                    = 70                 // elf.h:63:1:
	R_PPC_TPREL32                       = 73                 // elf.h:66:1:
	R_PPC_UADDR16                       = 25                 // elf.h:46:1:
	R_PPC_UADDR32                       = 24                 // elf.h:45:1:
	SA_INTERRUPT                        = 0x20000000         // sigaction.h:70:1:
	SA_NOCLDSTOP                        = 1                  // sigaction.h:56:1:
	SA_NOCLDWAIT                        = 2                  // sigaction.h:57:1:
	SA_NODEFER                          = 0x40000000         // sigaction.h:65:1:
	SA_NOMASK                           = 1073741824         // sigaction.h:73:1:
	SA_ONESHOT                          = 2147483648         // sigaction.h:74:1:
	SA_ONSTACK                          = 0x08000000         // sigaction.h:61:1:
	SA_RESETHAND                        = 0x80000000         // sigaction.h:67:1:
	SA_RESTART                          = 0x10000000         // sigaction.h:64:1:
	SA_SIGINFO                          = 4                  // sigaction.h:58:1:
	SA_STACK                            = 134217728          // sigaction.h:75:1:
	SIGABRT                             = 6                  // signum-generic.h:50:1:
	SIGALRM                             = 14                 // signum-generic.h:63:1:
	SIGBUS                              = 7                  // signum.h:35:1:
	SIGCHLD                             = 17                 // signum.h:41:1:
	SIGCLD                              = 17                 // signum-generic.h:88:1:
	SIGCONT                             = 18                 // signum.h:43:1:
	SIGFPE                              = 8                  // signum-generic.h:51:1:
	SIGHUP                              = 1                  // signum-generic.h:56:1:
	SIGILL                              = 4                  // signum-generic.h:49:1:
	SIGINT                              = 2                  // signum-generic.h:48:1:
	SIGIO                               = 29                 // signum-generic.h:86:1:
	SIGIOT                              = 6                  // signum-generic.h:87:1:
	SIGKILL                             = 9                  // signum-generic.h:59:1:
	SIGPIPE                             = 13                 // signum-generic.h:62:1:
	SIGPOLL                             = 29                 // signum.h:51:1:
	SIGPROF                             = 27                 // signum-generic.h:77:1:
	SIGPWR                              = 30                 // signum.h:32:1:
	SIGQUIT                             = 3                  // signum-generic.h:57:1:
	SIGSEGV                             = 11                 // signum-generic.h:52:1:
	SIGSTKFLT                           = 16                 // signum.h:31:1:
	SIGSTKSZ                            = 16384              // sigstack.h:30:1:
	SIGSTOP                             = 19                 // signum.h:45:1:
	SIGSYS                              = 31                 // signum.h:53:1:
	SIGTERM                             = 15                 // signum-generic.h:53:1:
	SIGTRAP                             = 5                  // signum-generic.h:58:1:
	SIGTSTP                             = 20                 // signum.h:47:1:
	SIGTTIN                             = 21                 // signum-generic.h:71:1:
	SIGTTOU                             = 22                 // signum-generic.h:72:1:
	SIGURG                              = 23                 // signum.h:49:1:
	SIGUSR1                             = 10                 // signum.h:37:1:
	SIGUSR2                             = 12                 // signum.h:39:1:
	SIGVTALRM                           = 26                 // signum-generic.h:76:1:
	SIGWINCH                            = 28                 // signum-generic.h:83:1:
	SIGXCPU                             = 24                 // signum-generic.h:74:1:
	SIGXFSZ                             = 25                 // signum-generic.h:75:1:
	SIG_BLOCK                           = 0                  // sigaction.h:79:1:
	SIG_SETMASK                         = 2                  // sigaction.h:81:1:
	SIG_UNBLOCK                         = 1                  // sigaction.h:80:1:
	X_ARCH_PPC                          = 1                  // <predefined>:198:1:
	X_ARCH_PPC64                        = 1                  // <predefined>:402:1:
	X_ARCH_PPCGR                        = 1                  // <predefined>:15:1:
	X_ARCH_PPCSQ                        = 1                  // <predefined>:43:1:
	X_ARCH_PWR4                         = 1                  // <predefined>:381:1:
	X_ARCH_PWR5                         = 1                  // <predefined>:90:1:
	X_ARCH_PWR5X                        = 1                  // <predefined>:137:1:
	X_ARCH_PWR6                         = 1                  // <predefined>:91:1:
	X_ARCH_PWR7                         = 1                  // <predefined>:92:1:
	X_ARCH_PWR8                         = 1                  // <predefined>:93:1:
	X_ASM_GENERIC_INT_L64_H             = 0                  // int-l64.h:10:1:
	X_ASM_POWERPC_AUXVEC_H              = 0                  // auxvec.h:3:1:
	X_ASM_POWERPC_ELF_H                 = 0                  // elf.h:11:1:
	X_ASM_POWERPC_POSIX_TYPES_H         = 0                  // posix_types.h:3:1:
	X_ASM_POWERPC_PTRACE_H              = 0                  // ptrace.h:25:1:
	X_ASM_POWERPC_SIGCONTEXT_H          = 0                  // sigcontext.h:3:1:
	X_ASM_POWERPC_TYPES_H               = 0                  // types.h:15:1:
	X_ATFILE_SOURCE                     = 1                  // features.h:342:1:
	X_BITS_ENDIANNESS_H                 = 1                  // endianness.h:2:1:
	X_BITS_ENDIAN_H                     = 1                  // endian.h:20:1:
	X_BITS_PTHREADTYPES_ARCH_H          = 1                  // pthreadtypes-arch.h:21:1:
	X_BITS_PTHREADTYPES_COMMON_H        = 1                  // pthreadtypes.h:20:1:
	X_BITS_SIGACTION_H                  = 1                  // sigaction.h:20:1:
	X_BITS_SIGCONTEXT_H                 = 1                  // sigcontext.h:19:1:
	X_BITS_SIGEVENT_CONSTS_H            = 1                  // sigevent-consts.h:20:1:
	X_BITS_SIGINFO_ARCH_H               = 1                  // siginfo-arch.h:3:1:
	X_BITS_SIGINFO_CONSTS_H             = 1                  // siginfo-consts.h:20:1:
	X_BITS_SIGNUM_GENERIC_H             = 1                  // signum-generic.h:20:1:
	X_BITS_SIGNUM_H                     = 1                  // signum.h:20:1:
	X_BITS_SIGSTACK_H                   = 1                  // sigstack.h:20:1:
	X_BITS_SIGTHREAD_H                  = 1                  // sigthread.h:20:1:
	X_BITS_SS_FLAGS_H                   = 1                  // ss_flags.h:20:1:
	X_BITS_TIME64_H                     = 1                  // time64.h:24:1:
	X_BITS_TYPESIZES_H                  = 1                  // typesizes.h:24:1:
	X_BITS_TYPES_H                      = 1                  // types.h:24:1:
	X_BSD_SIZE_T_                       = 0                  // stddef.h:189:1:
	X_BSD_SIZE_T_DEFINED_               = 0                  // stddef.h:192:1:
	X_CALL_ELF                          = 2                  // <predefined>:415:1:
	X_CALL_LINUX                        = 1                  // <predefined>:123:1:
	X_DEFAULT_SOURCE                    = 1                  // features.h:227:1:
	X_FEATURES_H                        = 1                  // features.h:19:1:
	X_FILE_OFFSET_BITS                  = 64                 // <builtin>:25:1:
	X_GCC_SIZE_T                        = 0                  // stddef.h:195:1:
	X_LINUX_POSIX_TYPES_H               = 0                  // posix_types.h:3:1:
	X_LINUX_TYPES_H                     = 0                  // types.h:3:1:
	X_LITTLE_ENDIAN                     = 1                  // <predefined>:37:1:
	X_LP64                              = 1                  // <predefined>:335:1:
	X_NSIG                              = 65                 // signum-generic.h:100:1:
	X_POSIX_C_SOURCE                    = 200809             // features.h:281:1:
	X_POSIX_SOURCE                      = 1                  // features.h:279:1:
	X_RWLOCK_INTERNAL_H                 = 0                  // struct_rwlock.h:21:1:
	X_SIGNAL_H                          = 0                  // signal.h:23:1:
	X_SIZET_                            = 0                  // stddef.h:196:1:
	X_SIZE_T                            = 0                  // stddef.h:183:1:
	X_SIZE_T_                           = 0                  // stddef.h:188:1:
	X_SIZE_T_DECLARED                   = 0                  // stddef.h:193:1:
	X_SIZE_T_DEFINED                    = 0                  // stddef.h:191:1:
	X_SIZE_T_DEFINED_                   = 0                  // stddef.h:190:1:
	X_STDC_PREDEF_H                     = 1                  // <predefined>:203:1:
	X_STRUCT_TIMESPEC                   = 1                  // struct_timespec.h:3:1:
	X_SYS_CDEFS_H                       = 1                  // cdefs.h:19:1:
	X_SYS_SIZE_T_H                      = 0                  // stddef.h:184:1:
	X_SYS_UCONTEXT_H                    = 1                  // ucontext.h:19:1:
	X_THREAD_MUTEX_INTERNAL_H           = 1                  // struct_mutex.h:20:1:
	X_THREAD_SHARED_TYPES_H             = 1                  // thread-shared-types.h:20:1:
	X_T_SIZE                            = 0                  // stddef.h:186:1:
	X_T_SIZE_                           = 0                  // stddef.h:185:1:
	Linux                               = 1                  // <predefined>:263:1:
	Unix                                = 1                  // <predefined>:222:1:
)

// POSIX names to access some of the members.

// sigevent constants.  Linux version.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// `sigev_notify' values.
const ( /* sigevent-consts.h:27:1: */
	SIGEV_SIGNAL = 0 // Notify via signal.
	SIGEV_NONE   = 1 // Other notification: meaningless.
	SIGEV_THREAD = 2 // Deliver via thread creation.

	SIGEV_THREAD_ID = 4
)

// `si_code' values for SIGSEGV signal.
const ( /* siginfo-consts.h:119:1: */
	SEGV_MAPERR  = 1 // Address not mapped to object.
	SEGV_ACCERR  = 2 // Invalid permissions for mapped object.
	SEGV_BNDERR  = 3 // Bounds checking failure.
	SEGV_PKUERR  = 4 // Protection key checking failure.
	SEGV_ACCADI  = 5 // ADI not enabled for mapped object.
	SEGV_ADIDERR = 6 // Disrupting MCD error.
	SEGV_ADIPERR = 7
)

// `si_code' values for SIGBUS signal.
const ( /* siginfo-consts.h:138:1: */
	BUS_ADRALN    = 1 // Invalid address alignment.
	BUS_ADRERR    = 2 // Non-existant physical address.
	BUS_OBJERR    = 3 // Object specific hardware error.
	BUS_MCEERR_AR = 4 // Hardware memory error: action required.
	BUS_MCEERR_AO = 5
)

// `si_code' values for SIGCHLD signal.
const ( /* siginfo-consts.h:172:1: */
	CLD_EXITED    = 1 // Child has exited.
	CLD_KILLED    = 2 // Child was killed.
	CLD_DUMPED    = 3 // Child terminated abnormally.
	CLD_TRAPPED   = 4 // Traced child has trapped.
	CLD_STOPPED   = 5 // Child has stopped.
	CLD_CONTINUED = 6
)

// `si_code' values for SIGPOLL signal.
const ( /* siginfo-consts.h:189:1: */
	POLL_IN  = 1 // Data input available.
	POLL_OUT = 2 // Output buffers available.
	POLL_MSG = 3 // Input message available.
	POLL_ERR = 4 // I/O error.
	POLL_PRI = 5 // High priority input available.
	POLL_HUP = 6
)

// X/Open requires some more fields with fixed names.

// siginfo constants.  Linux version.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Most of these constants are uniform across all architectures, but there
//    is one exception.
// Architecture-specific adjustments to siginfo_t.

// Values for `si_code'.  Positive values are reserved for kernel-generated
//
//	signals.
const ( /* siginfo-consts.h:35:1: */
	SI_ASYNCNL  = -60 // Sent by asynch name lookup completion.
	SI_DETHREAD = -7  // Sent by execve killing subsidiary
	// 				   threads.
	SI_TKILL   = -6 // Sent by tkill.
	SI_SIGIO   = -5 // Sent by queued SIGIO.
	SI_ASYNCIO = -4 // Sent by AIO completion.
	SI_MESGQ   = -3 // Sent by real time mesq state change.
	SI_TIMER   = -2 // Sent by timer expiration.
	SI_QUEUE   = -1 // Sent by sigqueue.
	SI_USER    = 0  // Sent by kill, sigsend.
	SI_KERNEL  = 128
)

// `si_code' values for SIGILL signal.
const ( /* siginfo-consts.h:71:1: */
	ILL_ILLOPC   = 1 // Illegal opcode.
	ILL_ILLOPN   = 2 // Illegal operand.
	ILL_ILLADR   = 3 // Illegal addressing mode.
	ILL_ILLTRP   = 4 // Illegal trap.
	ILL_PRVOPC   = 5 // Privileged opcode.
	ILL_PRVREG   = 6 // Privileged register.
	ILL_COPROC   = 7 // Coprocessor error.
	ILL_BADSTK   = 8 // Internal stack error.
	ILL_BADIADDR = 9
)

// `si_code' values for SIGFPE signal.
const ( /* siginfo-consts.h:94:1: */
	FPE_INTDIV   = 1  // Integer divide by zero.
	FPE_INTOVF   = 2  // Integer overflow.
	FPE_FLTDIV   = 3  // Floating point divide by zero.
	FPE_FLTOVF   = 4  // Floating point overflow.
	FPE_FLTUND   = 5  // Floating point underflow.
	FPE_FLTRES   = 6  // Floating point inexact result.
	FPE_FLTINV   = 7  // Floating point invalid operation.
	FPE_FLTSUB   = 8  // Subscript out of range.
	FPE_FLTUNK   = 14 // Undiagnosed floating-point exception.
	FPE_CONDTRAP = 15
)

// sigstack, sigaltstack definitions.
//    Copyright (C) 1998-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Minimum stack size for a signal handler.

// System default stack size.

// ss_flags values for stack_t.  Linux version.
//    Copyright (C) 1998-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Possible values for `ss_flags'.
const ( /* ss_flags.h:27:1: */
	SS_ONSTACK = 1
	SS_DISABLE = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__ieee128 = float64         /* <builtin>:47:21 */

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	ISO C99 Standard: 7.14 Signal handling <signal.h>

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// These are defined by the user (or the compiler)
//    to specify the desired environment:
//
//    __STRICT_ANSI__	ISO Standard C.
//    _ISOC99_SOURCE	Extensions to ISO C89 from ISO C99.
//    _ISOC11_SOURCE	Extensions to ISO C99 from ISO C11.
//    _ISOC2X_SOURCE	Extensions to ISO C99 from ISO C2X.
//    __STDC_WANT_LIB_EXT2__
// 			Extensions to ISO C99 from TR 27431-2:2010.
//    __STDC_WANT_IEC_60559_BFP_EXT__
// 			Extensions to ISO C11 from TS 18661-1:2014.
//    __STDC_WANT_IEC_60559_FUNCS_EXT__
// 			Extensions to ISO C11 from TS 18661-4:2015.
//    __STDC_WANT_IEC_60559_TYPES_EXT__
// 			Extensions to ISO C11 from TS 18661-3:2015.
//
//    _POSIX_SOURCE	IEEE Std 1003.1.
//    _POSIX_C_SOURCE	If ==1, like _POSIX_SOURCE; if >=2 add IEEE Std 1003.2;
// 			if >=199309L, add IEEE Std 1003.1b-1993;
// 			if >=199506L, add IEEE Std 1003.1c-1995;
// 			if >=200112L, all of IEEE 1003.1-2004
// 			if >=200809L, all of IEEE 1003.1-2008
//    _XOPEN_SOURCE	Includes POSIX and XPG things.  Set to 500 if
// 			Single Unix conformance is wanted, to 600 for the
// 			sixth revision, to 700 for the seventh revision.
//    _XOPEN_SOURCE_EXTENDED XPG things and X/Open Unix extensions.
//    _LARGEFILE_SOURCE	Some more functions for correct standard I/O.
//    _LARGEFILE64_SOURCE	Additional functionality from LFS for large files.
//    _FILE_OFFSET_BITS=N	Select default filesystem interface.
//    _ATFILE_SOURCE	Additional *at interfaces.
//    _GNU_SOURCE		All of the above, plus GNU extensions.
//    _DEFAULT_SOURCE	The default set of features (taking precedence over
// 			__STRICT_ANSI__).
//
//    _FORTIFY_SOURCE	Add security hardening to many library functions.
// 			Set to 1 or 2; 2 performs stricter checks than 1.
//
//    _REENTRANT, _THREAD_SAFE
// 			Obsolete; equivalent to _POSIX_C_SOURCE=199506L.
//
//    The `-ansi' switch to the GNU C compiler, and standards conformance
//    options such as `-std=c99', define __STRICT_ANSI__.  If none of
//    these are defined, or if _DEFAULT_SOURCE is defined, the default is
//    to have _POSIX_SOURCE set to one and _POSIX_C_SOURCE set to
//    200809L, as well as enabling miscellaneous functions from BSD and
//    SVID.  If more than one of these are defined, they accumulate.  For
//    example __STRICT_ANSI__, _POSIX_SOURCE and _POSIX_C_SOURCE together
//    give you ISO C, 1003.1, and 1003.2, but nothing else.
//
//    These are defined by this file and are used by the
//    header files to decide what to declare or define:
//
//    __GLIBC_USE (F)	Define things from feature set F.  This is defined
// 			to 1 or 0; the subsequent macros are either defined
// 			or undefined, and those tests should be moved to
// 			__GLIBC_USE.
//    __USE_ISOC11		Define ISO C11 things.
//    __USE_ISOC99		Define ISO C99 things.
//    __USE_ISOC95		Define ISO C90 AMD1 (C95) things.
//    __USE_ISOCXX11	Define ISO C++11 things.
//    __USE_POSIX		Define IEEE Std 1003.1 things.
//    __USE_POSIX2		Define IEEE Std 1003.2 things.
//    __USE_POSIX199309	Define IEEE Std 1003.1, and .1b things.
//    __USE_POSIX199506	Define IEEE Std 1003.1, .1b, .1c and .1i things.
//    __USE_XOPEN		Define XPG things.
//    __USE_XOPEN_EXTENDED	Define X/Open Unix things.
//    __USE_UNIX98		Define Single Unix V2 things.
//    __USE_XOPEN2K        Define XPG6 things.
//    __USE_XOPEN2KXSI     Define XPG6 XSI things.
//    __USE_XOPEN2K8       Define XPG7 things.
//    __USE_XOPEN2K8XSI    Define XPG7 XSI things.
//    __USE_LARGEFILE	Define correct standard I/O things.
//    __USE_LARGEFILE64	Define LFS things with separate names.
//    __USE_FILE_OFFSET64	Define 64bit interface as default.
//    __USE_MISC		Define things from 4.3BSD or System V Unix.
//    __USE_ATFILE		Define *at interfaces and AT_* constants for them.
//    __USE_GNU		Define GNU extensions.
//    __USE_FORTIFY_LEVEL	Additional security measures used, according to level.
//
//    The macros `__GNU_LIBRARY__', `__GLIBC__', and `__GLIBC_MINOR__' are
//    defined by this file unconditionally.  `__GNU_LIBRARY__' is provided
//    only for compatibility.  All new code should use the other symbols
//    to test for features.
//
//    All macros listed above as possibly being defined by this file are
//    explicitly undefined if they are not explicitly defined.
//    Feature-test macros that are not defined by the user or compiler
//    but are implied by the other feature-test macros defined (or by the
//    lack of any definitions) are defined by the file.
//
//    ISO C feature test macros depend on the definition of the macro
//    when an affected header is included, not when the first system
//    header is included, and so they are handled in
//    <bits/libc-header-start.h>, which does not have a multiple include
//    guard.  Feature test macros that can be handled from the first
//    system header included are handled here.

// Undefine everything, so we get a clean slate.

// Suppress kernel-name space pollution unless user expressedly asks
//    for it.

// Convenience macro to test the version of gcc.
//    Use like this:
//    #if __GNUC_PREREQ (2,8)
//    ... code requiring gcc 2.8 or later ...
//    #endif
//    Note: only works for GCC 2.0 and later, because __GNUC_MINOR__ was
//    added in 2.0.

// Similarly for clang.  Features added to GCC after version 4.2 may
//    or may not also be available in clang, and clang's definitions of
//    __GNUC(_MINOR)__ are fixed at 4 and 2 respectively.  Not all such
//    features can be queried via __has_extension/__has_feature.

// Whether to use feature set F.

// _BSD_SOURCE and _SVID_SOURCE are deprecated aliases for
//    _DEFAULT_SOURCE.  If _DEFAULT_SOURCE is present we do not
//    issue a warning; the expectation is that the source is being
//    transitioned to use the new macro.

// If _GNU_SOURCE was defined by the user, turn on all the other features.

// If nothing (other than _GNU_SOURCE and _DEFAULT_SOURCE) is defined,
//    define _DEFAULT_SOURCE.

// This is to enable the ISO C2X extension.

// This is to enable the ISO C11 extension.

// This is to enable the ISO C99 extension.

// This is to enable the ISO C90 Amendment 1:1995 extension.

// If none of the ANSI/POSIX macros are defined, or if _DEFAULT_SOURCE
//    is defined, use POSIX.1-2008 (or another version depending on
//    _XOPEN_SOURCE).

// Some C libraries once required _REENTRANT and/or _THREAD_SAFE to be
//    defined in all multithreaded code.  GNU libc has not required this
//    for many years.  We now treat them as compatibility synonyms for
//    _POSIX_C_SOURCE=199506L, which is the earliest level of POSIX with
//    comprehensive support for multithreaded code.  Using them never
//    lowers the selected level of POSIX conformance, only raises it.

// The function 'gets' existed in C89, but is impossible to use
//    safely.  It has been removed from ISO C11 and ISO C++14.  Note: for
//    compatibility with various implementations of <cstdio>, this test
//    must consider only the value of __cplusplus when compiling C++.

// GNU formerly extended the scanf functions with modified format
//    specifiers %as, %aS, and %a[...] that allocate a buffer for the
//    input using malloc.  This extension conflicts with ISO C99, which
//    defines %a as a standalone format specifier that reads a floating-
//    point number; moreover, POSIX.1-2008 provides the same feature
//    using the modifier letter 'm' instead (%ms, %mS, %m[...]).
//
//    We now follow C99 unless GNU extensions are active and the compiler
//    is specifically in C89 or C++98 mode (strict or not).  For
//    instance, with GCC, -std=gnu11 will have C99-compliant scanf with
//    or without -D_GNU_SOURCE, but -std=c89 -D_GNU_SOURCE will have the
//    old extension.

// Get definitions of __STDC_* predefined macros, if the compiler has
//    not preincluded this header automatically.
// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This macro indicates that the installed library is the GNU C Library.
//    For historic reasons the value now is 6 and this will stay from now
//    on.  The use of this variable is deprecated.  Use __GLIBC__ and
//    __GLIBC_MINOR__ now (see below) when you want to test for a specific
//    GNU C library version and use the values in <gnu/lib-names.h> to get
//    the sonames of the shared libraries.

// Major and minor version number of the GNU C library package.  Use
//    these macros to test for features in specific releases.

// This is here only because every header file already includes this one.
// Copyright (C) 1992-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We are almost always included from features.h.

// The GNU libc does not support any K&R compilers or the traditional mode
//    of ISO C compilers anymore.  Check for some of the combinations not
//    anymore supported.

// Some user header file might have defined this before.

// All functions, except those with callbacks or those that
//    synchronize memory, are leaf functions.

// GCC can always grok prototypes.  For C++ programs we add throw()
//    to help it optimize the function calls.  But this works only with
//    gcc 2.8.x and egcs.  For gcc 3.2 and up we even mark C functions
//    as non-throwing using a function attribute since programs can use
//    the -fexceptions options for C code as well.

// Compilers that are not clang may object to
//        #if defined __clang__ && __has_extension(...)
//    even though they do not need to evaluate the right-hand side of the &&.

// These two macros are not used in glibc anymore.  They are kept here
//    only because some other projects expect the macros to be defined.

// For these things, GCC behaves the ANSI way normally,
//    and the non-ANSI way under -traditional.

// This is not a typedef so `const __ptr_t' does the right thing.

// C++ needs to know that types and declarations are C, not C++.

// Fortify support.

// Support for flexible arrays.
//    Headers that should use flexible arrays only if they're "real"
//    (e.g. only if they won't affect sizeof()) should test
//    #if __glibc_c99_flexarr_available.

// __asm__ ("xyz") is used throughout the headers to rename functions
//    at the assembly language level.  This is wrapped by the __REDIRECT
//    macro, in order to support compilers that can do this some other
//    way.  When compilers don't support asm-names at all, we have to do
//    preprocessor tricks instead (which don't have exactly the right
//    semantics, but it's the best we can do).
//
//    Example:
//    int __REDIRECT(setpgrp, (__pid_t pid, __pid_t pgrp), setpgid);

//
// #elif __SOME_OTHER_COMPILER__
//
// # define __REDIRECT(name, proto, alias) name proto; 	_Pragma("let " #name " = " #alias)

// GCC has various useful declarations that can be made with the
//    `__attribute__' syntax.  All of the ways we use this do fine if
//    they are omitted for compilers that don't understand it.

// At some point during the gcc 2.96 development the `malloc' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Tell the compiler which arguments to an allocation function
//    indicate the size of the allocation.

// At some point during the gcc 2.96 development the `pure' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// This declaration tells the compiler that the value is constant.

// At some point during the gcc 3.1 development the `used' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Since version 3.2, gcc allows marking deprecated functions.

// Since version 4.5, gcc also allows one to specify the message printed
//    when a deprecated function is used.  clang claims to be gcc 4.2, but
//    may also support this feature.

// At some point during the gcc 2.8 development the `format_arg' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.
//    If several `format_arg' attributes are given for the same function, in
//    gcc-3.0 and older, all but the last one are ignored.  In newer gccs,
//    all designated arguments are considered.

// At some point during the gcc 2.97 development the `strfmon' format
//    attribute for functions was introduced.  We don't want to use it
//    unconditionally (although this would be possible) since it
//    generates warnings.

// The nonull function attribute allows to mark pointer parameters which
//    must not be NULL.

// If fortification mode, we warn about unused results of certain
//    function calls which can lead to problems.

// Forces a function to be always inlined.
// The Linux kernel defines __always_inline in stddef.h (283d7573), and
//    it conflicts with this definition.  Therefore undefine it first to
//    allow either header to be included first.

// Associate error messages with the source location of the call site rather
//    than with the source location inside the function.

// GCC 4.3 and above with -std=c99 or -std=gnu99 implements ISO C99
//    inline semantics, unless -fgnu89-inline is used.  Using __GNUC_STDC_INLINE__
//    or __GNUC_GNU_INLINE is not a good enough check for gcc because gcc versions
//    older than 4.3 may define these macros and still not guarantee GNU inlining
//    semantics.
//
//    clang++ identifies itself as gcc-4.2, but has support for GNU inlining
//    semantics, that can be checked for by using the __GNUC_STDC_INLINE_ and
//    __GNUC_GNU_INLINE__ macro definitions.

// GCC 4.3 and above allow passing all anonymous arguments of an
//    __extern_always_inline function to some other vararg function.

// It is possible to compile containing GCC extensions even if GCC is
//    run in pedantic mode if the uses are carefully marked using the
//    `__extension__' keyword.  But this is not generally available before
//    version 2.8.

// __restrict is known in EGCS 1.2 and above.

// ISO C99 also allows to declare arrays as non-overlapping.  The syntax is
//      array_name[restrict]
//    GCC 3.1 supports this.

// Describes a char array whose address can safely be passed as the first
//    argument to strncpy and strncat, as the char array is not necessarily
//    a NUL-terminated string.

// Undefine (also defined in libc-symbols.h).
// Copies attributes from the declaration or type referenced by
//    the argument.

// Determine the wordsize from the preprocessor defines.

// Properties of long double type.  ldbl-opt version.
//    Copyright (C) 2016-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License  published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// __glibc_macro_warning (MESSAGE) issues warning MESSAGE.  This is
//    intended for use in preprocessor macros.
//
//    Note: MESSAGE must be a _single_ string; concatenation of string
//    literals is not supported.

// Generic selection (ISO C11) is a C-only feature, available in GCC
//    since version 4.9.  Previous versions do not provide generic
//    selection, even though they might set __STDC_VERSION__ to 201112L,
//    when in -std=c11 mode.  Thus, we must check for !defined __GNUC__
//    when testing __STDC_VERSION__ for generic selection support.
//    On the other hand, Clang also defines __GNUC__, so a clang-specific
//    check is required to enable the use of generic selection.

// If we don't have __REDIRECT, prototypes will be missing if
//    __USE_FILE_OFFSET64 but not __USE_LARGEFILE[64].

// Decide whether we can define 'extern inline' functions in headers.

// This is here only because every header file already includes this one.
//    Get the definitions of all the appropriate `__stub_FUNCTION' symbols.
//    <gnu/stubs.h> contains `#define __stub_FUNCTION' when FUNCTION is a stub
//    that will always return failure (and set errno to ENOSYS).
// This file is automatically generated.
//    This file selects the right generated file of `__stub_FUNCTION' macros
//    based on the architecture being compiled for.

// Determine the wordsize from the preprocessor defines.

// This file is automatically generated.
//    It defines a symbol `__stub_FUNCTION' for each function
//    in the C library which is a stub, meaning it will fail
//    every time called, usually setting errno to ENOSYS.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Bit size of the time_t type at glibc build time, general case.
//    Copyright (C) 2018-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Size in bits of the 'time_t' type of the default ABI.

// Convenience types.
type X__u_char = uint8   /* types.h:31:23 */
type X__u_short = uint16 /* types.h:32:28 */
type X__u_int = uint32   /* types.h:33:22 */
type X__u_long = uint64  /* types.h:34:27 */

// Fixed-size types, underlying types depend on word size and compiler.
type X__int8_t = int8     /* types.h:37:21 */
type X__uint8_t = uint8   /* types.h:38:23 */
type X__int16_t = int16   /* types.h:39:26 */
type X__uint16_t = uint16 /* types.h:40:28 */
type X__int32_t = int32   /* types.h:41:20 */
type X__uint32_t = uint32 /* types.h:42:22 */
type X__int64_t = int64   /* types.h:44:25 */
type X__uint64_t = uint64 /* types.h:45:27 */

// Smallest types with at least a given width.
type X__int_least8_t = X__int8_t     /* types.h:52:18 */
type X__uint_least8_t = X__uint8_t   /* types.h:53:19 */
type X__int_least16_t = X__int16_t   /* types.h:54:19 */
type X__uint_least16_t = X__uint16_t /* types.h:55:20 */
type X__int_least32_t = X__int32_t   /* types.h:56:19 */
type X__uint_least32_t = X__uint32_t /* types.h:57:20 */
type X__int_least64_t = X__int64_t   /* types.h:58:19 */
type X__uint_least64_t = X__uint64_t /* types.h:59:20 */

// quad_t is also 64 bits.
type X__quad_t = int64    /* types.h:63:18 */
type X__u_quad_t = uint64 /* types.h:64:27 */

// Largest integral types.
type X__intmax_t = int64   /* types.h:72:18 */
type X__uintmax_t = uint64 /* types.h:73:27 */

// The machine-dependent file <bits/typesizes.h> defines __*_T_TYPE
//    macros for each of the OS types we define below.  The definitions
//    of those macros must use the following macros for underlying types.
//    We define __S<SIZE>_TYPE and __U<SIZE>_TYPE for the signed and unsigned
//    variants of each of the following integer types on this machine.
//
// 	16		-- "natural" 16-bit type (always short)
// 	32		-- "natural" 32-bit type (always int)
// 	64		-- "natural" 64-bit type (long or long long)
// 	LONG32		-- 32-bit type, traditionally long
// 	QUAD		-- 64-bit type, traditionally long long
// 	WORD		-- natural type of __WORDSIZE bits (int or long)
// 	LONGWORD	-- type of __WORDSIZE bits, traditionally long
//
//    We distinguish WORD/LONGWORD, 32/LONG32, and 64/QUAD so that the
//    conventional uses of `long' or `long long' type modifiers match the
//    types we define, even when a less-adorned type would be the same size.
//    This matters for (somewhat) portably writing printf/scanf formats for
//    these types, where using the appropriate l or ll format modifiers can
//    make the typedefs and the formats match up across all GNU platforms.  If
//    we used `long' when it's 64 bits where `long long' is expected, then the
//    compiler would warn about the formats not matching the argument types,
//    and the programmer changing them to shut up the compiler would break the
//    program's portability.
//
//    Here we assume what is presently the case in all the GCC configurations
//    we support: long long is always 64 bits, long is always word/address size,
//    and int is always 32 bits.

// No need to mark the typedef with __extension__.
// bits/typesizes.h -- underlying types for *_t.  Generic version.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// See <bits/types.h> for the meaning of these macros.  This file exists so
//    that <bits/types.h> need not vary across different GNU platforms.

// Tell the libc code that off_t and off64_t are actually the same type
//    for all ABI purposes, even if possibly expressed as different base types
//    for C type-checking purposes.

// Same for ino_t and ino64_t.

// And for rlim_t and rlim64_t.

// And for fsblkcnt_t, fsblkcnt64_t, fsfilcnt_t and fsfilcnt64_t.

// Number of descriptors that can fit in an `fd_set'.

// bits/time64.h -- underlying types for __time64_t.  Generic version.
//    Copyright (C) 2018-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define __TIME64_T_TYPE so that it is always a 64-bit type.

// If we already have 64-bit time type then use it.

type X__dev_t = uint64                     /* types.h:145:25 */ // Type of device numbers.
type X__uid_t = uint32                     /* types.h:146:25 */ // Type of user identifications.
type X__gid_t = uint32                     /* types.h:147:25 */ // Type of group identifications.
type X__ino_t = uint64                     /* types.h:148:25 */ // Type of file serial numbers.
type X__ino64_t = uint64                   /* types.h:149:27 */ // Type of file serial numbers (LFS).
type X__mode_t = uint32                    /* types.h:150:26 */ // Type of file attribute bitmasks.
type X__nlink_t = uint64                   /* types.h:151:27 */ // Type of file link counts.
type X__off_t = int64                      /* types.h:152:25 */ // Type of file sizes and offsets.
type X__off64_t = int64                    /* types.h:153:27 */ // Type of file sizes and offsets (LFS).
type X__pid_t = int32                      /* types.h:154:25 */ // Type of process identifications.
type X__fsid_t = struct{ F__val [2]int32 } /* types.h:155:26 */ // Type of file system IDs.
type X__clock_t = int64                    /* types.h:156:27 */ // Type of CPU usage counts.
type X__rlim_t = uint64                    /* types.h:157:26 */ // Type for resource measurement.
type X__rlim64_t = uint64                  /* types.h:158:28 */ // Type for resource measurement (LFS).
type X__id_t = uint32                      /* types.h:159:24 */ // General type for IDs.
type X__time_t = int64                     /* types.h:160:26 */ // Seconds since the Epoch.
type X__useconds_t = uint32                /* types.h:161:30 */ // Count of microseconds.
type X__suseconds_t = int64                /* types.h:162:31 */ // Signed count of microseconds.

type X__daddr_t = int32 /* types.h:164:27 */ // The type of a disk address.
type X__key_t = int32   /* types.h:165:25 */ // Type of an IPC key.

// Clock ID used in clock and timer functions.
type X__clockid_t = int32 /* types.h:168:29 */

// Timer ID returned by `timer_create'.
type X__timer_t = uintptr /* types.h:171:12 */

// Type to represent block size.
type X__blksize_t = int64 /* types.h:174:29 */

// Types from the Large File Support interface.

// Type to count number of disk blocks.
type X__blkcnt_t = int64   /* types.h:179:28 */
type X__blkcnt64_t = int64 /* types.h:180:30 */

// Type to count file system blocks.
type X__fsblkcnt_t = uint64   /* types.h:183:30 */
type X__fsblkcnt64_t = uint64 /* types.h:184:32 */

// Type to count file system nodes.
type X__fsfilcnt_t = uint64   /* types.h:187:30 */
type X__fsfilcnt64_t = uint64 /* types.h:188:32 */

// Type of miscellaneous file system fields.
type X__fsword_t = int64 /* types.h:191:28 */

type X__ssize_t = int64 /* types.h:193:27 */ // Type of a byte count, or error.

// Signed long type used in system calls.
type X__syscall_slong_t = int64 /* types.h:196:33 */
// Unsigned long type used in system calls.
type X__syscall_ulong_t = uint64 /* types.h:198:33 */

// These few don't really vary by system, they always correspond
//
//	to one of the other defined types.
type X__loff_t = X__off64_t /* types.h:202:19 */ // Type of file sizes and offsets (LFS).
type X__caddr_t = uintptr   /* types.h:203:14 */

// Duplicates info from stdint.h but this is used in unistd.h.
type X__intptr_t = int64 /* types.h:206:25 */

// Duplicate info from sys/socket.h.
type X__socklen_t = uint32 /* types.h:209:23 */

// C99: An integer type that can be accessed as an atomic entity,
//
//	even in the presence of asynchronous interrupts.
//	It is not currently necessary for this to be machine-specific.
type X__sig_atomic_t = int32 /* types.h:214:13 */

// Seconds since the Epoch, visible to user code when time_t is too
//    narrow only for consistency with the old way of widening too-narrow
//    types.  User code should never use __time64_t.

// Signal number definitions.  Linux version.
//    Copyright (C) 1995-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Signal number constants.  Generic template.
//    Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Fake signal functions.

// We define here all the signal names listed in POSIX (1003.1-2008);
//    as of 1003.1-2013, no additional signals have been added by POSIX.
//    We also define here signal names that historically exist in every
//    real-world POSIX variant (e.g. SIGWINCH).
//
//    Signals in the 1-15 range are defined with their historical numbers.
//    For other signals, we use the BSD numbers.
//    There are two unallocated signal numbers in the 1-31 range: 7 and 29.
//    Signal number 0 is reserved for use as kill(pid, 0), to test whether
//    a process exists without sending it a signal.

// ISO C99 signals.

// Historical signals specified by POSIX.

// New(er) POSIX signals (1003.1-2008, 1003.1-2013).

// Nonstandard signals found in all modern POSIX systems
//    (including both BSD and Linux).

// Archaic names for compatibility.

// Not all systems support real-time signals.  bits/signum.h indicates
//    that they are supported by overriding __SIGRTMAX to a value greater
//    than __SIGRTMIN.  These constants give the kernel-level hard limits,
//    but some real-time signals may be used internally by glibc.  Do not
//    use these constants in application code; use SIGRTMIN and SIGRTMAX
//    (defined in signal.h) instead.

// Biggest signal number + 1 (including real-time signals).

// Adjustments and additions to the signal number constants for
//    most Linux systems.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// An integral type that can be modified atomically, without the
//
//	possibility of a signal arriving in the middle of the operation.
type Sig_atomic_t = X__sig_atomic_t /* sig_atomic_t.h:8:24 */

type X__sigset_t = struct{ F__val [16]uint64 } /* __sigset_t.h:8:3 */

// A set of signals to be blocked, unblocked, or waited for.
type Sigset_t = X__sigset_t /* sigset_t.h:7:20 */

type Pid_t = X__pid_t /* signal.h:40:17 */
type Uid_t = X__uid_t /* signal.h:46:17 */

// We need `struct timespec' later on.
// NB: Include guard matches what <linux/time.h> uses.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Endian macros for string.h functions
//    Copyright (C) 1992-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Definitions for byte order, according to significance of bytes,
//    from low addresses to high addresses.  The value is what you get by
//    putting '4' in the most significant byte, '3' in the second most
//    significant byte, '2' in the second least significant byte, and '1'
//    in the least significant byte, and then writing down one digit for
//    each byte, starting with the byte at the lowest address at the left,
//    and proceeding to the byte with the highest address at the right.

// This file defines `__BYTE_ORDER' for the particular machine.

// PowerPC has selectable endianness.

// Some machines may need to use a different endianness for floating point
//    values.

// POSIX.1b structure for a time value.  This is like a `struct timeval' but
//
//	has nanoseconds instead of microseconds.
type Timespec = struct {
	Ftv_sec  X__time_t
	Ftv_nsec X__syscall_slong_t
} /* struct_timespec.h:10:1 */

// Determine the wordsize from the preprocessor defines.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Type for data associated with a signal.
type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* __sigval_t.h:24:1 */

type X__sigval_t = Sigval /* __sigval_t.h:30:22 */

// Some fields of siginfo_t have architecture-specific variations.
// Architecture-specific adjustments to siginfo_t.

// This architecture has no adjustments to make to siginfo_t.

type Siginfo_t = struct {
	Fsi_signo  int32
	Fsi_errno  int32
	Fsi_code   int32
	F__pad0    int32
	F_sifields struct {
		F__ccgo_pad1 [0]uint64
		F_pad        [28]int32
	}
} /* siginfo_t.h:124:5 */

// Architectures might also add architecture-specific constants.
//    These are all considered GNU extensions.

// Define __sigval_t.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// To avoid sigval_t (not a standard type name) having C++ name
//    mangling depending on whether the selected standard includes union
//    sigval, it should not be defined at all when using a standard for
//    which the sigval name is not reserved; in that case, headers should
//    not include <bits/types/sigval_t.h> and should use only the
//    internal __sigval_t name.

type Sigval_t = X__sigval_t /* sigval_t.h:16:20 */

// Determine the wordsize from the preprocessor defines.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Forward declaration.
type Pthread_attr_t1 = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [56]uint8
} /* sigevent_t.h:17:9 */

// Determine the wordsize from the preprocessor defines.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Forward declaration.
type Pthread_attr_t = Pthread_attr_t1 /* sigevent_t.h:17:30 */

// Structure to transport application-defined values with signals.
type Sigevent = struct {
	Fsigev_value  X__sigval_t
	Fsigev_signo  int32
	Fsigev_notify int32
	F_sigev_un    struct {
		F__ccgo_pad1 [0]uint64
		F_pad        [12]int32
	}
} /* sigevent_t.h:22:9 */

// Structure to transport application-defined values with signals.
type Sigevent_t = Sigevent /* sigevent_t.h:42:5 */

// Type of a signal handler.
type X__sighandler_t = uintptr /* signal.h:72:14 */

// 4.4 BSD uses the name `sig_t' for this.
type Sig_t = X__sighandler_t /* signal.h:190:24 */

// Get the system-specific definitions of `struct sigaction'
//    and the `SA_*' and `SIG_*'. constants.
// The proper definitions for Linux's sigaction.
//    Copyright (C) 1993-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Structure describing the action to be taken when a signal arrives.
type Sigaction = struct {
	F__sigaction_handler struct{ Fsa_handler X__sighandler_t }
	Fsa_mask             X__sigset_t
	Fsa_flags            int32
	F__ccgo_pad1         [4]byte
	Fsa_restorer         uintptr
} /* sigaction.h:27:1 */

// Get machine-dependent `struct sigcontext' and signal subcodes.
// Copyright (C) 1996-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Kernel headers before 2.1.1 define a struct sigcontext_struct, but
//    we need sigcontext.

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note

// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version
// 2 of the License, or (at your option) any later version.

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note
// Copyright (C) 2001 PPC64 Team, IBM Corp
//
// This struct defines the way the registers are stored on the
// kernel stack during a system call or other kernel entry.
//
// this should only contain __volatile__ regs
// since we can keep non-volatile in the thread_struct
// should set this up when only volatiles are saved
// by intr code.
//
// Since this is going on the stack, *CARE MUST BE TAKEN* to insure
// that the overall structure is a multiple of 16 bytes in length.
//
// Note that the offsets of the fields in this struct correspond with
// the PT_* values below.  This simplifies arch/powerpc/kernel/ptrace.c.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version
// 2 of the License, or (at your option) any later version.

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note
// This file is never included by application software unless
// explicitly requested (e.g., via linux/types.h) in which case the
// application is Linux specific so (user-) name space pollution is
// not a major issue.  However, for interoperability, libraries still
// need to be careful to avoid a name clashes.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version
// 2 of the License, or (at your option) any later version.

// This is here because we used to use l64 for 64bit powerpc
// and we don't want to impact user mode with our change to ll64
// in the kernel.
//
// However, some user programs are fine with this.  They can
// flag __SANE_USERSPACE_TYPES__ to get int-ll64.h here.
// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
// asm-generic/int-l64.h
//
// Integer declarations for architectures which use "long"
// for 64-bit types.

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// There seems to be no way of detecting this automatically from user
// space, so 64 bit architectures should override this in their
// bitsperlong.h. In particular, an architecture that supports
// both 32 and 64 bit user space must not rely on CONFIG_64BIT
// to decide it, but rather check a compiler provided macro.

// __xx is ok: it doesn't pollute the POSIX namespace. Use these in the
// header files exported to user space

type X__s8 = int8  /* int-l64.h:20:25 */
type X__u8 = uint8 /* int-l64.h:21:23 */

type X__s16 = int16  /* int-l64.h:23:26 */
type X__u16 = uint16 /* int-l64.h:24:24 */

type X__s32 = int32  /* int-l64.h:26:24 */
type X__u32 = uint32 /* int-l64.h:27:22 */

type X__s64 = int64  /* int-l64.h:29:25 */
type X__u64 = uint64 /* int-l64.h:30:23 */

type X__vector128 = struct {
	F__ccgo_pad1 [0]uint64
	Fu           [4]X__u32
} /* types.h:36:32 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// This allows for 1024 file descriptors: if NR_OPEN is ever grown
// beyond that you'll have to change this too. But 1024 fd's seem to be
// enough even for such "real" unices like OSF/1, so hopefully this is
// one limit that doesn't have to be changed [again].
//
// Note that POSIX wants the FD_CLEAR(fd,fdsetp) defines to be in
// <sys/time.h> (and thus <linux/time.h>) - but this is a more logical
// place for them. Solved by having dummy defines in <sys/time.h>.

// This macro may have been defined in <gnu/types.h>. But we always
// use the one here.

type X__kernel_fd_set = struct{ Ffds_bits [16]uint64 } /* posix_types.h:27:3 */

// Type of a signal handler.
type X__kernel_sighandler_t = uintptr /* posix_types.h:30:14 */

// Type of a SYSV IPC key.
type X__kernel_key_t = int32 /* posix_types.h:33:13 */
type X__kernel_mqd_t = int32 /* posix_types.h:34:13 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.  Also, we cannot
// assume GCC is being used.

type X__kernel_old_dev_t = uint64 /* posix_types.h:12:23 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.
//
// First the types that are often defined in different ways across
// architectures, so that you can override them.

type X__kernel_long_t = int64   /* posix_types.h:15:15 */
type X__kernel_ulong_t = uint64 /* posix_types.h:16:23 */

type X__kernel_ino_t = X__kernel_ulong_t /* posix_types.h:20:26 */

type X__kernel_mode_t = uint32 /* posix_types.h:24:22 */

type X__kernel_pid_t = int32 /* posix_types.h:28:14 */

type X__kernel_ipc_pid_t = int32 /* posix_types.h:32:14 */

type X__kernel_uid_t = uint32 /* posix_types.h:36:22 */
type X__kernel_gid_t = uint32 /* posix_types.h:37:22 */

type X__kernel_suseconds_t = X__kernel_long_t /* posix_types.h:41:26 */

type X__kernel_daddr_t = int32 /* posix_types.h:45:14 */

type X__kernel_uid32_t = uint32 /* posix_types.h:49:22 */
type X__kernel_gid32_t = uint32 /* posix_types.h:50:22 */

type X__kernel_old_uid_t = X__kernel_uid_t /* posix_types.h:54:24 */
type X__kernel_old_gid_t = X__kernel_gid_t /* posix_types.h:55:24 */

// Most 32 bit architectures use "unsigned int" size_t,
// and all 64 bit architectures use "unsigned long" size_t.
type X__kernel_size_t = X__kernel_ulong_t   /* posix_types.h:72:26 */
type X__kernel_ssize_t = X__kernel_long_t   /* posix_types.h:73:25 */
type X__kernel_ptrdiff_t = X__kernel_long_t /* posix_types.h:74:25 */

type X__kernel_fsid_t = struct{ Fval [2]int32 } /* posix_types.h:81:3 */

// anything below here should be completely generic
type X__kernel_off_t = X__kernel_long_t      /* posix_types.h:87:25 */
type X__kernel_loff_t = int64                /* posix_types.h:88:19 */
type X__kernel_old_time_t = X__kernel_long_t /* posix_types.h:89:25 */
type X__kernel_time_t = X__kernel_long_t     /* posix_types.h:90:25 */
type X__kernel_time64_t = int64              /* posix_types.h:91:19 */
type X__kernel_clock_t = X__kernel_long_t    /* posix_types.h:92:25 */
type X__kernel_timer_t = int32               /* posix_types.h:93:14 */
type X__kernel_clockid_t = int32             /* posix_types.h:94:14 */
type X__kernel_caddr_t = uintptr             /* posix_types.h:95:14 */
type X__kernel_uid16_t = uint16              /* posix_types.h:96:24 */
type X__kernel_gid16_t = uint16              /* posix_types.h:97:24 */

// Below are truly Linux-specific types that should never collide with
// any application/library that wants linux/types.h.

type X__le16 = X__u16 /* types.h:24:25 */
type X__be16 = X__u16 /* types.h:25:25 */
type X__le32 = X__u32 /* types.h:26:25 */
type X__be32 = X__u32 /* types.h:27:25 */
type X__le64 = X__u64 /* types.h:28:25 */
type X__be64 = X__u64 /* types.h:29:25 */

type X__sum16 = X__u16 /* types.h:31:25 */
type X__wsum = X__u32  /* types.h:32:25 */

// aligned_u64 should be used in defining kernel<->userspace ABIs to avoid
// common 32/64-bit compat problems.
// 64-bit values align to 4-byte boundaries on x86_32 (and possibly other
// architectures) and to 8-byte boundaries on 64-bit architectures.  The new
// aligned_64 type enforces 8-byte alignment so that structs containing
// aligned_64 values have the same alignment on 32-bit and 64-bit architectures.
// No conversions are necessary between 32-bit user-space and a 64-bit kernel.

type X__poll_t = uint32 /* types.h:47:28 */

type Pt_regs = struct {
	Fgpr       [32]uint64
	Fnip       uint64
	Fmsr       uint64
	Forig_gpr3 uint64
	Fctr       uint64
	Flink      uint64
	Fxer       uint64
	Fccr       uint64
	Fsofte     uint64
	Ftrap      uint64
	Fdar       uint64
	Fdsisr     uint64
	Fresult    uint64
} /* ptrace.h:32:1 */

// Offsets used by 'ptrace' system call interface.
// These can't be changed without breaking binary compatibility
// with MkLinux, etc.

// Only store first 32 VSRs here. The second 32 VSRs in VR0-31

// Get/set all the altivec registers v0..v31, vscr, vrsave, in one go.
// The transfer totals 34 quadword.  Quadwords 0-31 contain the
// corresponding vector registers.  Quadword 32 contains the vscr as the
// last word (offset 12) within that quadword.  Quadword 33 contains the
// vrsave as the first word (offset 0) within the quadword.
//
// This definition of the VMX state is compatible with the current PPC32
// ptrace interface.  This allows signal handling and ptrace to use the same
// structures.  This also simplifies the implementation of a bi-arch
// (combined (32- and 64-bit) gdb.

// Get/set all the upper 32-bits of the SPE registers, accumulator, and
// spefscr, in one go

// Get the first 32 128bit VSX registers

// Syscall emulation defines

// Get or set a debug register. The first 16 are DABR registers and the
// second 16 are IABR registers.

// (new) PTRACE requests using the same numbers as x86 and the same
// argument ordering. Additionally, they support more registers too

// Calls to trace a 64bit program from a 32bit program

type Ppc_debug_info = struct {
	Fversion             X__u32
	Fnum_instruction_bps X__u32
	Fnum_data_bps        X__u32
	Fnum_condition_regs  X__u32
	Fdata_bp_alignment   X__u32
	Fsizeof_condition    X__u32
	Ffeatures            X__u64
} /* ptrace.h:201:1 */

// features will have bits indication whether there is support for:

type Ppc_hw_breakpoint = struct {
	Fversion         X__u32
	Ftrigger_type    X__u32
	Faddr_mode       X__u32
	Fcondition_mode  X__u32
	Faddr            X__u64
	Faddr2           X__u64
	Fcondition_value X__u64
} /* ptrace.h:225:1 */

// Trigger Type

// Address Mode

// Condition Mode

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note
// ELF register definitions..
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version
// 2 of the License, or (at your option) any later version.

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note
// Copyright (C) 2001 PPC64 Team, IBM Corp
//
// This struct defines the way the registers are stored on the
// kernel stack during a system call or other kernel entry.
//
// this should only contain __volatile__ regs
// since we can keep non-volatile in the thread_struct
// should set this up when only volatiles are saved
// by intr code.
//
// Since this is going on the stack, *CARE MUST BE TAKEN* to insure
// that the overall structure is a multiple of 16 bytes in length.
//
// Note that the offsets of the fields in this struct correspond with
// the PT_* values below.  This simplifies arch/powerpc/kernel/ptrace.c.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version
// 2 of the License, or (at your option) any later version.
// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// in AT_HWCAP

// Reserved - do not use		0x00000004

// in AT_HWCAP2

// IMPORTANT!
// All future PPC_FEATURE definitions should be allocated in cooperation with
// OPAL / skiboot firmware, in accordance with the ibm,powerpc-cpu-features
// device tree binding.

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// We need to put in some extra aux table entries to tell glibc what
// the cache block size is, so it can use the dcbz instruction safely.
// A special ignored type value for PPC, for glibc compatibility.

// The vDSO location. We have to use the same value as x86 for glibc's
// sake :-)

// AT_*CACHEBSIZE above represent the cache *block* size which is
// the size that is affected by the cache management instructions.
//
// It doesn't nececssarily matches the cache *line* size which is
// more of a performance tuning hint. Additionally the latter can
// be different for the different cache levels.
//
// The set of entries below represent more extensive information
// about the caches, in the form of two entry per cache type,
// one entry containing the cache size in bytes, and the other
// containing the cache line size in bytes in the bottom 16 bits
// and the cache associativity in the next 16 bits.
//
// The associativity is such that if N is the 16-bit value, the
// cache is N way set associative. A value if 0xffff means fully
// associative, a value of 1 means directly mapped.
//
// For all these fields, a value of 0 means that the information
// is not known.

// PowerPC relocations defined by the ABIs

// PowerPC relocations defined for the TLS access ABI.

// keep this the last entry.

type Elf_greg_t64 = uint64              /* elf.h:102:23 */
type Elf_gregset_t64 = [48]Elf_greg_t64 /* elf.h:103:22 */

type Elf_greg_t32 = uint32                  /* elf.h:105:22 */
type Elf_gregset_t32 = [48]Elf_greg_t32     /* elf.h:106:22 */
type Compat_elf_gregset_t = Elf_gregset_t32 /* elf.h:107:25 */

// ELF_ARCH, CLASS, and DATA are used to set parameters in the core dumps.
type Elf_greg_t = Elf_greg_t64       /* elf.h:119:22 */
type Elf_gregset_t = Elf_gregset_t64 /* elf.h:120:25 */

// Floating point registers
type Elf_fpreg_t = float64            /* elf.h:138:16 */
type Elf_fpregset_t = [33]Elf_fpreg_t /* elf.h:139:21 */

// Altivec registers
// The entries with indexes 0-31 contain the corresponding vector registers.
// The entry with index 32 contains the vscr as the last word (offset 12)
// within the quadword.  This allows the vscr to be stored as either a
// quadword (since it must be copied via a vector register to/from storage)
// or as a word.
//
// 64-bit kernel notes: The entry at index 33 contains the vrsave as the first
// word (offset 0) within the quadword.
//
// This definition of the VMX state is compatible with the current PPC32
// ptrace interface.  This allows signal handling and ptrace to use the same
// structures.  This also simplifies the implementation of a bi-arch
// (combined (32- and 64-bit) gdb.
//
// Note that it's _not_ compatible with 32 bits ucontext which stuffs the
// vrsave along with vscr and so only uses 33 vectors for the register set
type Elf_vrreg_t = X__vector128           /* elf.h:160:21 */
type Elf_vrregset_t = [34]Elf_vrreg_t     /* elf.h:161:21 */
type Elf_vrregset_t32 = [33]Elf_vrreg_t   /* elf.h:163:21 */
type Elf_vsrreghalf_t32 = [32]Elf_fpreg_t /* elf.h:164:21 */

// PowerPC64 relocations defined by the ABIs

// PowerPC64 relocations defined for the TLS access ABI.

// Keep this the last entry.

// There's actually a third entry here, but it's unused
type Ppc64_opd_entry = struct {
	Ffuncaddr uint64
	Fr2       uint64
} /* elf.h:293:1 */

type Sigcontext = struct {
	F_unused     [4]uint64
	Fsignal      int32
	F_pad0       int32
	Fhandler     uint64
	Foldmask     uint64
	Fregs        uintptr
	Fgp_regs     Elf_gregset_t
	Ffp_regs     Elf_fpregset_t
	Fv_regs      uintptr
	Fvmx_reserve [101]int64
} /* sigcontext.h:17:1 */

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Define stack_t.  Linux version.
//    Copyright (C) 1998-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Structure describing a signal stack.
type Stack_t = struct {
	Fss_sp       uintptr
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
	Fss_size     Size_t
} /* stack_t.h:31:5 */

// For 64-bit kernels with Altivec support, a machine context is exactly
// a sigcontext.  For older kernel (without Altivec) the sigcontext matches
// the mcontext upto but not including the v_regs field.  For kernels that
// don't set AT_HWCAP or return AT_HWCAP without PPC_FEATURE_HAS_ALTIVEC the
// v_regs field may not exist and should not be referenced.  The v_regs field
// can be referenced safely only after verifying that PPC_FEATURE_HAS_ALTIVEC
// is set in AT_HWCAP.

// Number of general registers.

type Gregset_t = [48]uint64   /* ucontext.h:93:23 */
type Fpregset_t = [33]float64 /* ucontext.h:94:16 */

// Container for Altivec/VMX Vector Status and Control Register.  Only 32-bits
//
//	but can only be copied to/from a 128-bit vector register.  So we allocated
//	a whole quadword speedup save/restore.
type X_libc_vscr = struct {
	Fvscr_word uint32
	F__pad     [3]uint32
} /* ucontext.h:99:9 */

// Container for Altivec/VMX Vector Status and Control Register.  Only 32-bits
//
//	but can only be copied to/from a 128-bit vector register.  So we allocated
//	a whole quadword speedup save/restore.
type Vscr_t = X_libc_vscr /* ucontext.h:108:3 */

// Container for Altivec/VMX registers and status.
//
//	Must to be aligned on a 16-byte boundary.
type X_libc_vrstate = struct {
	Fvrregs [32][4]uint32
	Fvscr   Vscr_t
	Fvrsave uint32
	F__pad  [3]uint32
} /* ucontext.h:112:9 */

// Container for Altivec/VMX registers and status.
//
//	Must to be aligned on a 16-byte boundary.
type Vrregset_t = X_libc_vrstate /* ucontext.h:118:3 */

type Mcontext_t = struct {
	F__glibc_reserved [4]uint64
	Fsignal           int32
	F__pad0           int32
	Fhandler          uint64
	Foldmask          uint64
	Fregs             uintptr
	Fgp_regs          Gregset_t
	Ffp_regs          Fpregset_t
	Fv_regs           uintptr
	Fvmx_reserve      [69]int64
} /* ucontext.h:150:3 */

// Userlevel context.
type Ucontext_t1 = struct {
	Fuc_flags    uint64
	Fuc_link     uintptr
	Fuc_stack    Stack_t
	Fuc_sigmask  Sigset_t
	Fuc_mcontext Mcontext_t
} /* ucontext.h:155:9 */

// Userlevel context.
type Ucontext_t = Ucontext_t1 /* ucontext.h:196:5 */

// Define struct sigstack.
//    Copyright (C) 1998-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Structure describing a signal stack (obsolete).
type Sigstack = struct {
	Fss_sp       uintptr
	Fss_onstack  int32
	F__ccgo_pad1 [4]byte
} /* struct_sigstack.h:23:1 */

// Some of the functions for handling signals in threaded programs must
//    be defined here.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Common definition of pthread_mutex_t.

type X__pthread_internal_list = struct {
	F__prev uintptr
	F__next uintptr
} /* thread-shared-types.h:49:9 */

// Some of the functions for handling signals in threaded programs must
//    be defined here.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Common definition of pthread_mutex_t.

type X__pthread_list_t = X__pthread_internal_list /* thread-shared-types.h:53:3 */

type X__pthread_internal_slist = struct{ F__next uintptr } /* thread-shared-types.h:55:9 */

type X__pthread_slist_t = X__pthread_internal_slist /* thread-shared-types.h:58:3 */

// Arch-specific mutex definitions.  A generic implementation is provided
//    by sysdeps/nptl/bits/struct_mutex.h.  If required, an architecture
//    can override it by defining:
//
//    1. struct __pthread_mutex_s (used on both pthread_mutex_t and mtx_t
//       definition).  It should contains at least the internal members
//       defined in the generic version.
//
//    2. __LOCK_ALIGNMENT for any extra attribute for internal lock used with
//       atomic operations.
//
//    3. The macro __PTHREAD_MUTEX_INITIALIZER used for static initialization.
//       It should initialize the mutex internal flag.

// PowerPC internal mutex struct definitions.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

type X__pthread_mutex_s = struct {
	F__lock    int32
	F__count   uint32
	F__owner   int32
	F__nusers  uint32
	F__kind    int32
	F__spins   int16
	F__elision int16
	F__list    X__pthread_list_t
} /* struct_mutex.h:22:1 */

// Arch-sepecific read-write lock definitions.  A generic implementation is
//    provided by struct_rwlock.h.  If required, an architecture can override it
//    by defining:
//
//    1. struct __pthread_rwlock_arch_t (used on pthread_rwlock_t definition).
//       It should contain at least the internal members defined in the
//       generic version.
//
//    2. The macro __PTHREAD_RWLOCK_INITIALIZER used for static initialization.
//       It should initialize the rwlock internal type.

// PowerPC internal rwlock struct definitions.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

type X__pthread_rwlock_arch_t = struct {
	F__readers       uint32
	F__writers       uint32
	F__wrphase_futex uint32
	F__writers_futex uint32
	F__pad3          uint32
	F__pad4          uint32
	F__cur_writer    int32
	F__shared        int32
	F__rwelision     uint8
	F__pad1          [7]uint8
	F__pad2          uint64
	F__flags         uint32
	F__ccgo_pad1     [4]byte
} /* struct_rwlock.h:23:1 */

// Common definition of pthread_cond_t.

type X__pthread_cond_s = struct {
	F__0            struct{ F__wseq uint64 }
	F__8            struct{ F__g1_start uint64 }
	F__g_refs       [2]uint32
	F__g_size       [2]uint32
	F__g1_orig_size uint32
	F__wrefs        uint32
	F__g_signals    [2]uint32
} /* thread-shared-types.h:92:1 */

// Thread identifiers.  The structure of the attribute type is not
//
//	exposed on purpose.
type Pthread_t = uint64 /* pthreadtypes.h:27:27 */

// Data structures for mutex handling.  The structure of the attribute
//
//	type is not exposed on purpose.
type Pthread_mutexattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:36:3 */

// Data structure for condition variable handling.  The structure of
//
//	the attribute type is not exposed on purpose.
type Pthread_condattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:45:3 */

// Keys for thread-specific data
type Pthread_key_t = uint32 /* pthreadtypes.h:49:22 */

// Once-only execution
type Pthread_once_t = int32 /* pthreadtypes.h:53:30 */

type Pthread_mutex_t = struct{ F__data X__pthread_mutex_s } /* pthreadtypes.h:72:3 */

type Pthread_cond_t = struct{ F__data X__pthread_cond_s } /* pthreadtypes.h:80:3 */

// Data structure for reader-writer lock variable handling.  The
//
//	structure of the attribute type is deliberately not exposed.
type Pthread_rwlock_t = struct{ F__data X__pthread_rwlock_arch_t } /* pthreadtypes.h:91:3 */

type Pthread_rwlockattr_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [8]uint8
} /* pthreadtypes.h:97:3 */

// POSIX spinlock data type.
type Pthread_spinlock_t = int32 /* pthreadtypes.h:103:22 */

// POSIX barriers data type.  The structure of the type is
//
//	deliberately not exposed.
type Pthread_barrier_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [32]uint8
} /* pthreadtypes.h:112:3 */

type Pthread_barrierattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:118:3 */

// System-specific extensions.
// System-specific extensions of <signal.h>, Linux version.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

var _ uint8 /* gen.c:2:13: */
