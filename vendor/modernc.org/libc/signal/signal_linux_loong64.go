// Code generated by 'ccgo signal/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o signal/signal_linux_loong64.go -pkgname signal', DO NOT EDIT.

package signal

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	CONTEXT_INFO_ALIGN           = 16            // sigcontext.h:28:1:
	FPU_CTX_ALIGN                = 8             // sigcontext.h:37:1:
	FPU_CTX_MAGIC                = 0x46505501    // sigcontext.h:36:1:
	LARCH_NGREG                  = 32            // ucontext.h:31:1:
	LARCH_REG_A0                 = 4             // ucontext.h:37:1:
	LARCH_REG_NARGS              = 8             // ucontext.h:39:1:
	LARCH_REG_RA                 = 1             // ucontext.h:33:1:
	LARCH_REG_S0                 = 23            // ucontext.h:35:1:
	LARCH_REG_S1                 = 24            // ucontext.h:36:1:
	LARCH_REG_S2                 = 25            // ucontext.h:38:1:
	LARCH_REG_SP                 = 3             // ucontext.h:34:1:
	LASX_CTX_ALIGN               = 32            // sigcontext.h:55:1:
	LASX_CTX_MAGIC               = 0x41535801    // sigcontext.h:54:1:
	LSX_CTX_ALIGN                = 16            // sigcontext.h:46:1:
	LSX_CTX_MAGIC                = 0x53580001    // sigcontext.h:45:1:
	MINSIGSTKSZ                  = 4096          // sigstack.h:27:1:
	NSIG                         = 65            // signal.h:184:1:
	SA_INTERRUPT                 = 0x20000000    // sigaction.h:70:1:
	SA_NOCLDSTOP                 = 1             // sigaction.h:56:1:
	SA_NOCLDWAIT                 = 2             // sigaction.h:57:1:
	SA_NODEFER                   = 0x40000000    // sigaction.h:65:1:
	SA_NOMASK                    = 1073741824    // sigaction.h:73:1:
	SA_ONESHOT                   = 2147483648    // sigaction.h:74:1:
	SA_ONSTACK                   = 0x08000000    // sigaction.h:61:1:
	SA_RESETHAND                 = 0x80000000    // sigaction.h:67:1:
	SA_RESTART                   = 0x10000000    // sigaction.h:64:1:
	SA_SIGINFO                   = 4             // sigaction.h:58:1:
	SA_STACK                     = 134217728     // sigaction.h:75:1:
	SC_ADDRERR_RD                = 1073741824    // sigcontext.h:17:1:
	SC_ADDRERR_WR                = 2147483648    // sigcontext.h:19:1:
	SC_USED_FP                   = 1             // sigcontext.h:15:1:
	SIGABRT                      = 6             // signum-generic.h:50:1:
	SIGALRM                      = 14            // signum-generic.h:61:1:
	SIGBUS                       = 7             // signum-arch.h:33:1:
	SIGCHLD                      = 17            // signum-arch.h:41:1:
	SIGCLD                       = 17            // signum-arch.h:59:1:
	SIGCONT                      = 18            // signum-arch.h:40:1:
	SIGFPE                       = 8             // signum-generic.h:51:1:
	SIGHUP                       = 1             // signum-generic.h:56:1:
	SIGILL                       = 4             // signum-generic.h:49:1:
	SIGINT                       = 2             // signum-generic.h:48:1:
	SIGIO                        = 29            // signum-arch.h:57:1:
	SIGIOT                       = 6             // signum-arch.h:58:1:
	SIGKILL                      = 9             // signum-generic.h:59:1:
	SIGPIPE                      = 13            // signum-generic.h:60:1:
	SIGPOLL                      = 29            // signum-arch.h:44:1:
	SIGPROF                      = 27            // signum-arch.h:48:1:
	SIGPWR                       = 30            // signum-arch.h:30:1:
	SIGQUIT                      = 3             // signum-generic.h:57:1:
	SIGSEGV                      = 11            // signum-generic.h:52:1:
	SIGSTKFLT                    = 16            // signum-arch.h:29:1:
	SIGSTKSZ                     = 16384         // sigstack.h:30:1:
	SIGSTOP                      = 19            // signum-arch.h:38:1:
	SIGSYS                       = 31            // signum-arch.h:34:1:
	SIGTERM                      = 15            // signum-generic.h:53:1:
	SIGTRAP                      = 5             // signum-generic.h:58:1:
	SIGTSTP                      = 20            // signum-arch.h:39:1:
	SIGTTIN                      = 21            // signum-arch.h:42:1:
	SIGTTOU                      = 22            // signum-arch.h:43:1:
	SIGURG                       = 23            // signum-arch.h:37:1:
	SIGUSR1                      = 10            // signum-arch.h:49:1:
	SIGUSR2                      = 12            // signum-arch.h:50:1:
	SIGVTALRM                    = 26            // signum-arch.h:47:1:
	SIGWINCH                     = 28            // signum-arch.h:54:1:
	SIGXCPU                      = 24            // signum-arch.h:46:1:
	SIGXFSZ                      = 25            // signum-arch.h:45:1:
	SIG_BLOCK                    = 0             // sigaction.h:79:1:
	SIG_SETMASK                  = 2             // sigaction.h:81:1:
	SIG_UNBLOCK                  = 1             // sigaction.h:80:1:
	X_ABILP64                    = 3             // <predefined>:377:1:
	X_ASM_GENERIC_INT_LL64_H     = 0             // int-ll64.h:10:1:
	X_ASM_GENERIC_TYPES_H        = 0             // types.h:3:1:
	X_ASM_SIGCONTEXT_H           = 0             // sigcontext.h:9:1:
	X_ATFILE_SOURCE              = 1             // features.h:353:1:
	X_BITS_ATOMIC_WIDE_COUNTER_H = 0             // atomic_wide_counter.h:20:1:
	X_BITS_ENDIANNESS_H          = 1             // endianness.h:2:1:
	X_BITS_ENDIAN_H              = 1             // endian.h:20:1:
	X_BITS_PTHREADTYPES_ARCH_H   = 1             // pthreadtypes-arch.h:21:1:
	X_BITS_PTHREADTYPES_COMMON_H = 1             // pthreadtypes.h:20:1:
	X_BITS_SIGACTION_H           = 1             // sigaction.h:20:1:
	X_BITS_SIGCONTEXT_H          = 1             // sigcontext.h:19:1:
	X_BITS_SIGEVENT_CONSTS_H     = 1             // sigevent-consts.h:20:1:
	X_BITS_SIGINFO_ARCH_H        = 1             // siginfo-arch.h:3:1:
	X_BITS_SIGINFO_CONSTS_H      = 1             // siginfo-consts.h:20:1:
	X_BITS_SIGNUM_ARCH_H         = 1             // signum-arch.h:20:1:
	X_BITS_SIGNUM_GENERIC_H      = 1             // signum-generic.h:20:1:
	X_BITS_SIGSTACK_H            = 1             // sigstack.h:20:1:
	X_BITS_SIGTHREAD_H           = 1             // sigthread.h:20:1:
	X_BITS_SS_FLAGS_H            = 1             // ss_flags.h:20:1:
	X_BITS_TIME64_H              = 1             // time64.h:24:1:
	X_BITS_TYPESIZES_H           = 1             // typesizes.h:24:1:
	X_BITS_TYPES_H               = 1             // types.h:24:1:
	X_BSD_SIZE_T_                = 0             // stddef.h:193:1:
	X_BSD_SIZE_T_DEFINED_        = 0             // stddef.h:196:1:
	X_DEFAULT_SOURCE             = 1             // features.h:238:1:
	X_FEATURES_H                 = 1             // features.h:19:1:
	X_FILE_OFFSET_BITS           = 64            // <builtin>:25:1:
	X_GCC_SIZE_T                 = 0             // stddef.h:200:1:
	X_LINUX_POSIX_TYPES_H        = 0             // posix_types.h:3:1:
	X_LINUX_STDDEF_H             = 0             // stddef.h:3:1:
	X_LINUX_TYPES_H              = 0             // types.h:3:1:
	X_LOONGARCH_ARCH             = "loongarch64" // <predefined>:214:1:
	X_LOONGARCH_ARCH_LOONGARCH64 = 1             // <predefined>:340:1:
	X_LOONGARCH_FPSET            = 32            // <predefined>:265:1:
	X_LOONGARCH_SIM              = 3             // <predefined>:233:1:
	X_LOONGARCH_SPFPSET          = 32            // <predefined>:88:1:
	X_LOONGARCH_SZINT            = 32            // <predefined>:230:1:
	X_LOONGARCH_SZLONG           = 64            // <predefined>:388:1:
	X_LOONGARCH_SZPTR            = 64            // <predefined>:200:1:
	X_LOONGARCH_TUNE             = "la464"       // <predefined>:245:1:
	X_LOONGARCH_TUNE_LA464       = 1             // <predefined>:63:1:
	X_LP64                       = 1             // <predefined>:372:1:
	X_NSIG                       = 65            // signum-generic.h:79:1:
	X_POSIX_C_SOURCE             = 200809        // features.h:292:1:
	X_POSIX_SOURCE               = 1             // features.h:290:1:
	X_SIGNAL_H                   = 0             // signal.h:23:1:
	X_SIZET_                     = 0             // stddef.h:201:1:
	X_SIZE_T                     = 0             // stddef.h:187:1:
	X_SIZE_T_                    = 0             // stddef.h:192:1:
	X_SIZE_T_DECLARED            = 0             // stddef.h:197:1:
	X_SIZE_T_DEFINED             = 0             // stddef.h:195:1:
	X_SIZE_T_DEFINED_            = 0             // stddef.h:194:1:
	X_STDC_PREDEF_H              = 1             // <predefined>:223:1:
	X_STRUCT_TIMESPEC            = 1             // struct_timespec.h:3:1:
	X_SYS_CDEFS_H                = 1             // cdefs.h:20:1:
	X_SYS_SIZE_T_H               = 0             // stddef.h:188:1:
	X_SYS_UCONTEXT_H             = 1             // ucontext.h:23:1:
	X_THREAD_MUTEX_INTERNAL_H    = 1             // struct_mutex.h:20:1:
	X_THREAD_SHARED_TYPES_H      = 1             // thread-shared-types.h:20:1:
	X_T_SIZE                     = 0             // stddef.h:190:1:
	X_T_SIZE_                    = 0             // stddef.h:189:1:
	Linux                        = 1             // <predefined>:308:1:
	Unix                         = 1             // <predefined>:247:1:
)

// POSIX names to access some of the members.

// sigevent constants.  Linux version.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// `sigev_notify' values.
const ( /* sigevent-consts.h:27:1: */
	SIGEV_SIGNAL = 0 // Notify via signal.
	SIGEV_NONE   = 1 // Other notification: meaningless.
	SIGEV_THREAD = 2 // Deliver via thread creation.

	SIGEV_THREAD_ID = 4
)

// `si_code' values for SIGSEGV signal.
const ( /* siginfo-consts.h:119:1: */
	SEGV_MAPERR  = 1 // Address not mapped to object.
	SEGV_ACCERR  = 2 // Invalid permissions for mapped object.
	SEGV_BNDERR  = 3 // Bounds checking failure.
	SEGV_PKUERR  = 4 // Protection key checking failure.
	SEGV_ACCADI  = 5 // ADI not enabled for mapped object.
	SEGV_ADIDERR = 6 // Disrupting MCD error.
	SEGV_ADIPERR = 7 // Precise MCD exception.
	SEGV_MTEAERR = 8 // Asynchronous ARM MTE error.
	SEGV_MTESERR = 9
)

// `si_code' values for SIGBUS signal.
const ( /* siginfo-consts.h:142:1: */
	BUS_ADRALN    = 1 // Invalid address alignment.
	BUS_ADRERR    = 2 // Non-existent physical address.
	BUS_OBJERR    = 3 // Object specific hardware error.
	BUS_MCEERR_AR = 4 // Hardware memory error: action required.
	BUS_MCEERR_AO = 5
)

// `si_code' values for SIGCHLD signal.
const ( /* siginfo-consts.h:176:1: */
	CLD_EXITED    = 1 // Child has exited.
	CLD_KILLED    = 2 // Child was killed.
	CLD_DUMPED    = 3 // Child terminated abnormally.
	CLD_TRAPPED   = 4 // Traced child has trapped.
	CLD_STOPPED   = 5 // Child has stopped.
	CLD_CONTINUED = 6
)

// `si_code' values for SIGPOLL signal.
const ( /* siginfo-consts.h:193:1: */
	POLL_IN  = 1 // Data input available.
	POLL_OUT = 2 // Output buffers available.
	POLL_MSG = 3 // Input message available.
	POLL_ERR = 4 // I/O error.
	POLL_PRI = 5 // High priority input available.
	POLL_HUP = 6
)

// X/Open requires some more fields with fixed names.

// siginfo constants.  Linux version.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Most of these constants are uniform across all architectures, but there
//    is one exception.
// Architecture-specific adjustments to siginfo_t.

// Values for `si_code'.  Positive values are reserved for kernel-generated
//
//	signals.
const ( /* siginfo-consts.h:35:1: */
	SI_ASYNCNL  = -60 // Sent by asynch name lookup completion.
	SI_DETHREAD = -7  // Sent by execve killing subsidiary
	// 				   threads.
	SI_TKILL   = -6 // Sent by tkill.
	SI_SIGIO   = -5 // Sent by queued SIGIO.
	SI_ASYNCIO = -4 // Sent by AIO completion.
	SI_MESGQ   = -3 // Sent by real time mesq state change.
	SI_TIMER   = -2 // Sent by timer expiration.
	SI_QUEUE   = -1 // Sent by sigqueue.
	SI_USER    = 0  // Sent by kill, sigsend.
	SI_KERNEL  = 128
)

// `si_code' values for SIGILL signal.
const ( /* siginfo-consts.h:71:1: */
	ILL_ILLOPC   = 1 // Illegal opcode.
	ILL_ILLOPN   = 2 // Illegal operand.
	ILL_ILLADR   = 3 // Illegal addressing mode.
	ILL_ILLTRP   = 4 // Illegal trap.
	ILL_PRVOPC   = 5 // Privileged opcode.
	ILL_PRVREG   = 6 // Privileged register.
	ILL_COPROC   = 7 // Coprocessor error.
	ILL_BADSTK   = 8 // Internal stack error.
	ILL_BADIADDR = 9
)

// `si_code' values for SIGFPE signal.
const ( /* siginfo-consts.h:94:1: */
	FPE_INTDIV   = 1  // Integer divide by zero.
	FPE_INTOVF   = 2  // Integer overflow.
	FPE_FLTDIV   = 3  // Floating point divide by zero.
	FPE_FLTOVF   = 4  // Floating point overflow.
	FPE_FLTUND   = 5  // Floating point underflow.
	FPE_FLTRES   = 6  // Floating point inexact result.
	FPE_FLTINV   = 7  // Floating point invalid operation.
	FPE_FLTSUB   = 8  // Subscript out of range.
	FPE_FLTUNK   = 14 // Undiagnosed floating-point exception.
	FPE_CONDTRAP = 15
)

// sigstack, sigaltstack definitions.
//    Copyright (C) 2022-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Minimum stack size for a signal handler.

// System default stack size.

// Definition of MINSIGSTKSZ and SIGSTKSZ.  Linux version.
//    Copyright (C) 2020-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// ss_flags values for stack_t.  Linux version.
//    Copyright (C) 1998-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Possible values for `ss_flags'.
const ( /* ss_flags.h:27:1: */
	SS_ONSTACK = 1
	SS_DISABLE = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	ISO C99 Standard: 7.14 Signal handling <signal.h>

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// These are defined by the user (or the compiler)
//    to specify the desired environment:
//
//    __STRICT_ANSI__	ISO Standard C.
//    _ISOC99_SOURCE	Extensions to ISO C89 from ISO C99.
//    _ISOC11_SOURCE	Extensions to ISO C99 from ISO C11.
//    _ISOC2X_SOURCE	Extensions to ISO C99 from ISO C2X.
//    __STDC_WANT_LIB_EXT2__
// 			Extensions to ISO C99 from TR 27431-2:2010.
//    __STDC_WANT_IEC_60559_BFP_EXT__
// 			Extensions to ISO C11 from TS 18661-1:2014.
//    __STDC_WANT_IEC_60559_FUNCS_EXT__
// 			Extensions to ISO C11 from TS 18661-4:2015.
//    __STDC_WANT_IEC_60559_TYPES_EXT__
// 			Extensions to ISO C11 from TS 18661-3:2015.
//    __STDC_WANT_IEC_60559_EXT__
// 			ISO C2X interfaces defined only in Annex F.
//
//    _POSIX_SOURCE	IEEE Std 1003.1.
//    _POSIX_C_SOURCE	If ==1, like _POSIX_SOURCE; if >=2 add IEEE Std 1003.2;
// 			if >=199309L, add IEEE Std 1003.1b-1993;
// 			if >=199506L, add IEEE Std 1003.1c-1995;
// 			if >=200112L, all of IEEE 1003.1-2004
// 			if >=200809L, all of IEEE 1003.1-2008
//    _XOPEN_SOURCE	Includes POSIX and XPG things.  Set to 500 if
// 			Single Unix conformance is wanted, to 600 for the
// 			sixth revision, to 700 for the seventh revision.
//    _XOPEN_SOURCE_EXTENDED XPG things and X/Open Unix extensions.
//    _LARGEFILE_SOURCE	Some more functions for correct standard I/O.
//    _LARGEFILE64_SOURCE	Additional functionality from LFS for large files.
//    _FILE_OFFSET_BITS=N	Select default filesystem interface.
//    _ATFILE_SOURCE	Additional *at interfaces.
//    _DYNAMIC_STACK_SIZE_SOURCE Select correct (but non compile-time constant)
// 			MINSIGSTKSZ, SIGSTKSZ and PTHREAD_STACK_MIN.
//    _GNU_SOURCE		All of the above, plus GNU extensions.
//    _DEFAULT_SOURCE	The default set of features (taking precedence over
// 			__STRICT_ANSI__).
//
//    _FORTIFY_SOURCE	Add security hardening to many library functions.
// 			Set to 1, 2 or 3; 3 performs stricter checks than 2, which
// 			performs stricter checks than 1.
//
//    _REENTRANT, _THREAD_SAFE
// 			Obsolete; equivalent to _POSIX_C_SOURCE=199506L.
//
//    The `-ansi' switch to the GNU C compiler, and standards conformance
//    options such as `-std=c99', define __STRICT_ANSI__.  If none of
//    these are defined, or if _DEFAULT_SOURCE is defined, the default is
//    to have _POSIX_SOURCE set to one and _POSIX_C_SOURCE set to
//    200809L, as well as enabling miscellaneous functions from BSD and
//    SVID.  If more than one of these are defined, they accumulate.  For
//    example __STRICT_ANSI__, _POSIX_SOURCE and _POSIX_C_SOURCE together
//    give you ISO C, 1003.1, and 1003.2, but nothing else.
//
//    These are defined by this file and are used by the
//    header files to decide what to declare or define:
//
//    __GLIBC_USE (F)	Define things from feature set F.  This is defined
// 			to 1 or 0; the subsequent macros are either defined
// 			or undefined, and those tests should be moved to
// 			__GLIBC_USE.
//    __USE_ISOC11		Define ISO C11 things.
//    __USE_ISOC99		Define ISO C99 things.
//    __USE_ISOC95		Define ISO C90 AMD1 (C95) things.
//    __USE_ISOCXX11	Define ISO C++11 things.
//    __USE_POSIX		Define IEEE Std 1003.1 things.
//    __USE_POSIX2		Define IEEE Std 1003.2 things.
//    __USE_POSIX199309	Define IEEE Std 1003.1, and .1b things.
//    __USE_POSIX199506	Define IEEE Std 1003.1, .1b, .1c and .1i things.
//    __USE_XOPEN		Define XPG things.
//    __USE_XOPEN_EXTENDED	Define X/Open Unix things.
//    __USE_UNIX98		Define Single Unix V2 things.
//    __USE_XOPEN2K        Define XPG6 things.
//    __USE_XOPEN2KXSI     Define XPG6 XSI things.
//    __USE_XOPEN2K8       Define XPG7 things.
//    __USE_XOPEN2K8XSI    Define XPG7 XSI things.
//    __USE_LARGEFILE	Define correct standard I/O things.
//    __USE_LARGEFILE64	Define LFS things with separate names.
//    __USE_FILE_OFFSET64	Define 64bit interface as default.
//    __USE_MISC		Define things from 4.3BSD or System V Unix.
//    __USE_ATFILE		Define *at interfaces and AT_* constants for them.
//    __USE_DYNAMIC_STACK_SIZE Define correct (but non compile-time constant)
// 			MINSIGSTKSZ, SIGSTKSZ and PTHREAD_STACK_MIN.
//    __USE_GNU		Define GNU extensions.
//    __USE_FORTIFY_LEVEL	Additional security measures used, according to level.
//
//    The macros `__GNU_LIBRARY__', `__GLIBC__', and `__GLIBC_MINOR__' are
//    defined by this file unconditionally.  `__GNU_LIBRARY__' is provided
//    only for compatibility.  All new code should use the other symbols
//    to test for features.
//
//    All macros listed above as possibly being defined by this file are
//    explicitly undefined if they are not explicitly defined.
//    Feature-test macros that are not defined by the user or compiler
//    but are implied by the other feature-test macros defined (or by the
//    lack of any definitions) are defined by the file.
//
//    ISO C feature test macros depend on the definition of the macro
//    when an affected header is included, not when the first system
//    header is included, and so they are handled in
//    <bits/libc-header-start.h>, which does not have a multiple include
//    guard.  Feature test macros that can be handled from the first
//    system header included are handled here.

// Undefine everything, so we get a clean slate.

// Suppress kernel-name space pollution unless user expressedly asks
//    for it.

// Convenience macro to test the version of gcc.
//    Use like this:
//    #if __GNUC_PREREQ (2,8)
//    ... code requiring gcc 2.8 or later ...
//    #endif
//    Note: only works for GCC 2.0 and later, because __GNUC_MINOR__ was
//    added in 2.0.

// Similarly for clang.  Features added to GCC after version 4.2 may
//    or may not also be available in clang, and clang's definitions of
//    __GNUC(_MINOR)__ are fixed at 4 and 2 respectively.  Not all such
//    features can be queried via __has_extension/__has_feature.

// Whether to use feature set F.

// _BSD_SOURCE and _SVID_SOURCE are deprecated aliases for
//    _DEFAULT_SOURCE.  If _DEFAULT_SOURCE is present we do not
//    issue a warning; the expectation is that the source is being
//    transitioned to use the new macro.

// If _GNU_SOURCE was defined by the user, turn on all the other features.

// If nothing (other than _GNU_SOURCE and _DEFAULT_SOURCE) is defined,
//    define _DEFAULT_SOURCE.

// This is to enable the ISO C2X extension.

// This is to enable the ISO C11 extension.

// This is to enable the ISO C99 extension.

// This is to enable the ISO C90 Amendment 1:1995 extension.

// If none of the ANSI/POSIX macros are defined, or if _DEFAULT_SOURCE
//    is defined, use POSIX.1-2008 (or another version depending on
//    _XOPEN_SOURCE).

// Some C libraries once required _REENTRANT and/or _THREAD_SAFE to be
//    defined in all multithreaded code.  GNU libc has not required this
//    for many years.  We now treat them as compatibility synonyms for
//    _POSIX_C_SOURCE=199506L, which is the earliest level of POSIX with
//    comprehensive support for multithreaded code.  Using them never
//    lowers the selected level of POSIX conformance, only raises it.

// Features part to handle 64-bit time_t support.
//    Copyright (C) 2021-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We need to know the word size in order to check the time size.
// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Bit size of the time_t type at glibc build time, general case.
//    Copyright (C) 2018-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Size in bits of the 'time_t' type of the default ABI.

// The function 'gets' existed in C89, but is impossible to use
//    safely.  It has been removed from ISO C11 and ISO C++14.  Note: for
//    compatibility with various implementations of <cstdio>, this test
//    must consider only the value of __cplusplus when compiling C++.

// GNU formerly extended the scanf functions with modified format
//    specifiers %as, %aS, and %a[...] that allocate a buffer for the
//    input using malloc.  This extension conflicts with ISO C99, which
//    defines %a as a standalone format specifier that reads a floating-
//    point number; moreover, POSIX.1-2008 provides the same feature
//    using the modifier letter 'm' instead (%ms, %mS, %m[...]).
//
//    We now follow C99 unless GNU extensions are active and the compiler
//    is specifically in C89 or C++98 mode (strict or not).  For
//    instance, with GCC, -std=gnu11 will have C99-compliant scanf with
//    or without -D_GNU_SOURCE, but -std=c89 -D_GNU_SOURCE will have the
//    old extension.

// ISO C2X added support for a 0b or 0B prefix on binary constants as
//    inputs to strtol-family functions (base 0 or 2).  This macro is
//    used to condition redirection in headers to allow that redirection
//    to be disabled when building those functions, despite _GNU_SOURCE
//    being defined.

// Get definitions of __STDC_* predefined macros, if the compiler has
//    not preincluded this header automatically.
// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This macro indicates that the installed library is the GNU C Library.
//    For historic reasons the value now is 6 and this will stay from now
//    on.  The use of this variable is deprecated.  Use __GLIBC__ and
//    __GLIBC_MINOR__ now (see below) when you want to test for a specific
//    GNU C library version and use the values in <gnu/lib-names.h> to get
//    the sonames of the shared libraries.

// Major and minor version number of the GNU C library package.  Use
//    these macros to test for features in specific releases.

// This is here only because every header file already includes this one.
// Copyright (C) 1992-2023 Free Software Foundation, Inc.
//    Copyright The GNU Toolchain Authors.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We are almost always included from features.h.

// The GNU libc does not support any K&R compilers or the traditional mode
//    of ISO C compilers anymore.  Check for some of the combinations not
//    supported anymore.

// Some user header file might have defined this before.

// Compilers that lack __has_attribute may object to
//        #if defined __has_attribute && __has_attribute (...)
//    even though they do not need to evaluate the right-hand side of the &&.
//    Similarly for __has_builtin, etc.

// All functions, except those with callbacks or those that
//    synchronize memory, are leaf functions.

// GCC can always grok prototypes.  For C++ programs we add throw()
//    to help it optimize the function calls.  But this only works with
//    gcc 2.8.x and egcs.  For gcc 3.4 and up we even mark C functions
//    as non-throwing using a function attribute since programs can use
//    the -fexceptions options for C code as well.

// These two macros are not used in glibc anymore.  They are kept here
//    only because some other projects expect the macros to be defined.

// For these things, GCC behaves the ANSI way normally,
//    and the non-ANSI way under -traditional.

// This is not a typedef so `const __ptr_t' does the right thing.

// C++ needs to know that types and declarations are C, not C++.

// Fortify support.

// Use __builtin_dynamic_object_size at _FORTIFY_SOURCE=3 when available.

// Support for flexible arrays.
//    Headers that should use flexible arrays only if they're "real"
//    (e.g. only if they won't affect sizeof()) should test
//    #if __glibc_c99_flexarr_available.

// __asm__ ("xyz") is used throughout the headers to rename functions
//    at the assembly language level.  This is wrapped by the __REDIRECT
//    macro, in order to support compilers that can do this some other
//    way.  When compilers don't support asm-names at all, we have to do
//    preprocessor tricks instead (which don't have exactly the right
//    semantics, but it's the best we can do).
//
//    Example:
//    int __REDIRECT(setpgrp, (__pid_t pid, __pid_t pgrp), setpgid);

//
// #elif __SOME_OTHER_COMPILER__
//
// # define __REDIRECT(name, proto, alias) name proto; 	_Pragma("let " #name " = " #alias)

// GCC and clang have various useful declarations that can be made with
//    the '__attribute__' syntax.  All of the ways we use this do fine if
//    they are omitted for compilers that don't understand it.

// At some point during the gcc 2.96 development the `malloc' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Tell the compiler which arguments to an allocation function
//    indicate the size of the allocation.

// Tell the compiler which argument to an allocation function
//    indicates the alignment of the allocation.

// At some point during the gcc 2.96 development the `pure' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// This declaration tells the compiler that the value is constant.

// At some point during the gcc 3.1 development the `used' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Since version 3.2, gcc allows marking deprecated functions.

// Since version 4.5, gcc also allows one to specify the message printed
//    when a deprecated function is used.  clang claims to be gcc 4.2, but
//    may also support this feature.

// At some point during the gcc 2.8 development the `format_arg' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.
//    If several `format_arg' attributes are given for the same function, in
//    gcc-3.0 and older, all but the last one are ignored.  In newer gccs,
//    all designated arguments are considered.

// At some point during the gcc 2.97 development the `strfmon' format
//    attribute for functions was introduced.  We don't want to use it
//    unconditionally (although this would be possible) since it
//    generates warnings.

// The nonnull function attribute marks pointer parameters that
//    must not be NULL.  This has the name __nonnull in glibc,
//    and __attribute_nonnull__ in files shared with Gnulib to avoid
//    collision with a different __nonnull in DragonFlyBSD 5.9.

// The returns_nonnull function attribute marks the return type of the function
//    as always being non-null.

// If fortification mode, we warn about unused results of certain
//    function calls which can lead to problems.

// Forces a function to be always inlined.
// The Linux kernel defines __always_inline in stddef.h (283d7573), and
//    it conflicts with this definition.  Therefore undefine it first to
//    allow either header to be included first.

// Associate error messages with the source location of the call site rather
//    than with the source location inside the function.

// GCC 4.3 and above with -std=c99 or -std=gnu99 implements ISO C99
//    inline semantics, unless -fgnu89-inline is used.  Using __GNUC_STDC_INLINE__
//    or __GNUC_GNU_INLINE is not a good enough check for gcc because gcc versions
//    older than 4.3 may define these macros and still not guarantee GNU inlining
//    semantics.
//
//    clang++ identifies itself as gcc-4.2, but has support for GNU inlining
//    semantics, that can be checked for by using the __GNUC_STDC_INLINE_ and
//    __GNUC_GNU_INLINE__ macro definitions.

// GCC 4.3 and above allow passing all anonymous arguments of an
//    __extern_always_inline function to some other vararg function.

// It is possible to compile containing GCC extensions even if GCC is
//    run in pedantic mode if the uses are carefully marked using the
//    `__extension__' keyword.  But this is not generally available before
//    version 2.8.

// __restrict is known in EGCS 1.2 and above, and in clang.
//    It works also in C++ mode (outside of arrays), but only when spelled
//    as '__restrict', not 'restrict'.

// ISO C99 also allows to declare arrays as non-overlapping.  The syntax is
//      array_name[restrict]
//    GCC 3.1 and clang support this.
//    This syntax is not usable in C++ mode.

// Describes a char array whose address can safely be passed as the first
//    argument to strncpy and strncat, as the char array is not necessarily
//    a NUL-terminated string.

// Undefine (also defined in libc-symbols.h).
// Copies attributes from the declaration or type referenced by
//    the argument.

// Gnulib avoids including these, as they don't work on non-glibc or
//    older glibc platforms.
// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Properties of long double type.  ldbl-128 version.
//    Copyright (C) 2016-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License  published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// long double is distinct from double, so there is nothing to
//    define here.

// __glibc_macro_warning (MESSAGE) issues warning MESSAGE.  This is
//    intended for use in preprocessor macros.
//
//    Note: MESSAGE must be a _single_ string; concatenation of string
//    literals is not supported.

// Generic selection (ISO C11) is a C-only feature, available in GCC
//    since version 4.9.  Previous versions do not provide generic
//    selection, even though they might set __STDC_VERSION__ to 201112L,
//    when in -std=c11 mode.  Thus, we must check for !defined __GNUC__
//    when testing __STDC_VERSION__ for generic selection support.
//    On the other hand, Clang also defines __GNUC__, so a clang-specific
//    check is required to enable the use of generic selection.

// Designates a 1-based positional argument ref-index of pointer type
//    that can be used to access size-index elements of the pointed-to
//    array according to access mode, or at least one element when
//    size-index is not provided:
//      access (access-mode, <ref-index> [, <size-index>])
// For _FORTIFY_SOURCE == 3 we use __builtin_dynamic_object_size, which may
//    use the access attribute to get object sizes from function definition
//    arguments, so we can't use them on functions we fortify.  Drop the object
//    size hints for such functions.

// Designates dealloc as a function to call to deallocate objects
//    allocated by the declared function.

// Specify that a function such as setjmp or vfork may return
//    twice.

// If we don't have __REDIRECT, prototypes will be missing if
//    __USE_FILE_OFFSET64 but not __USE_LARGEFILE[64].

// Decide whether we can define 'extern inline' functions in headers.

// This is here only because every header file already includes this one.
//    Get the definitions of all the appropriate `__stub_FUNCTION' symbols.
//    <gnu/stubs.h> contains `#define __stub_FUNCTION' when FUNCTION is a stub
//    that will always return failure (and set errno to ENOSYS).
// This file is automatically generated.
//    This file selects the right generated file of `__stub_FUNCTION' macros
//    based on the architecture being compiled for.

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This file is automatically generated.
//    It defines a symbol `__stub_FUNCTION' for each function
//    in the C library which is a stub, meaning it will fail
//    every time called, usually setting errno to ENOSYS.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Bit size of the time_t type at glibc build time, general case.
//    Copyright (C) 2018-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Size in bits of the 'time_t' type of the default ABI.

// Convenience types.
type X__u_char = uint8   /* types.h:31:23 */
type X__u_short = uint16 /* types.h:32:28 */
type X__u_int = uint32   /* types.h:33:22 */
type X__u_long = uint64  /* types.h:34:27 */

// Fixed-size types, underlying types depend on word size and compiler.
type X__int8_t = int8     /* types.h:37:21 */
type X__uint8_t = uint8   /* types.h:38:23 */
type X__int16_t = int16   /* types.h:39:26 */
type X__uint16_t = uint16 /* types.h:40:28 */
type X__int32_t = int32   /* types.h:41:20 */
type X__uint32_t = uint32 /* types.h:42:22 */
type X__int64_t = int64   /* types.h:44:25 */
type X__uint64_t = uint64 /* types.h:45:27 */

// Smallest types with at least a given width.
type X__int_least8_t = int8     /* types.h:52:18 */
type X__uint_least8_t = uint8   /* types.h:53:19 */
type X__int_least16_t = int16   /* types.h:54:19 */
type X__uint_least16_t = uint16 /* types.h:55:20 */
type X__int_least32_t = int32   /* types.h:56:19 */
type X__uint_least32_t = uint32 /* types.h:57:20 */
type X__int_least64_t = int64   /* types.h:58:19 */
type X__uint_least64_t = uint64 /* types.h:59:20 */

// quad_t is also 64 bits.
type X__quad_t = int64    /* types.h:63:18 */
type X__u_quad_t = uint64 /* types.h:64:27 */

// Largest integral types.
type X__intmax_t = int64   /* types.h:72:18 */
type X__uintmax_t = uint64 /* types.h:73:27 */

// The machine-dependent file <bits/typesizes.h> defines __*_T_TYPE
//    macros for each of the OS types we define below.  The definitions
//    of those macros must use the following macros for underlying types.
//    We define __S<SIZE>_TYPE and __U<SIZE>_TYPE for the signed and unsigned
//    variants of each of the following integer types on this machine.
//
// 	16		-- "natural" 16-bit type (always short)
// 	32		-- "natural" 32-bit type (always int)
// 	64		-- "natural" 64-bit type (long or long long)
// 	LONG32		-- 32-bit type, traditionally long
// 	QUAD		-- 64-bit type, traditionally long long
// 	WORD		-- natural type of __WORDSIZE bits (int or long)
// 	LONGWORD	-- type of __WORDSIZE bits, traditionally long
//
//    We distinguish WORD/LONGWORD, 32/LONG32, and 64/QUAD so that the
//    conventional uses of `long' or `long long' type modifiers match the
//    types we define, even when a less-adorned type would be the same size.
//    This matters for (somewhat) portably writing printf/scanf formats for
//    these types, where using the appropriate l or ll format modifiers can
//    make the typedefs and the formats match up across all GNU platforms.  If
//    we used `long' when it's 64 bits where `long long' is expected, then the
//    compiler would warn about the formats not matching the argument types,
//    and the programmer changing them to shut up the compiler would break the
//    program's portability.
//
//    Here we assume what is presently the case in all the GCC configurations
//    we support: long long is always 64 bits, long is always word/address size,
//    and int is always 32 bits.

// No need to mark the typedef with __extension__.
// bits/typesizes.h -- underlying types for *_t.  For the generic Linux ABI.
//    Copyright (C) 2011-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// See <bits/types.h> for the meaning of these macros.  This file exists so
//    that <bits/types.h> need not vary across different GNU platforms.

// Tell the libc code that off_t and off64_t are actually the same type
//    for all ABI purposes, even if possibly expressed as different base types
//    for C type-checking purposes.

// Same for ino_t and ino64_t.

// And for __rlim_t and __rlim64_t.

// And for fsblkcnt_t, fsblkcnt64_t, fsfilcnt_t and fsfilcnt64_t.

// And for getitimer, setitimer and rusage

// Number of descriptors that can fit in an `fd_set'.

// bits/time64.h -- underlying types for __time64_t.  Generic version.
//    Copyright (C) 2018-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define __TIME64_T_TYPE so that it is always a 64-bit type.

// If we already have 64-bit time type then use it.

type X__dev_t = uint64                     /* types.h:145:25 */ // Type of device numbers.
type X__uid_t = uint32                     /* types.h:146:25 */ // Type of user identifications.
type X__gid_t = uint32                     /* types.h:147:25 */ // Type of group identifications.
type X__ino_t = uint64                     /* types.h:148:25 */ // Type of file serial numbers.
type X__ino64_t = uint64                   /* types.h:149:27 */ // Type of file serial numbers (LFS).
type X__mode_t = uint32                    /* types.h:150:26 */ // Type of file attribute bitmasks.
type X__nlink_t = uint32                   /* types.h:151:27 */ // Type of file link counts.
type X__off_t = int64                      /* types.h:152:25 */ // Type of file sizes and offsets.
type X__off64_t = int64                    /* types.h:153:27 */ // Type of file sizes and offsets (LFS).
type X__pid_t = int32                      /* types.h:154:25 */ // Type of process identifications.
type X__fsid_t = struct{ F__val [2]int32 } /* types.h:155:26 */ // Type of file system IDs.
type X__clock_t = int64                    /* types.h:156:27 */ // Type of CPU usage counts.
type X__rlim_t = uint64                    /* types.h:157:26 */ // Type for resource measurement.
type X__rlim64_t = uint64                  /* types.h:158:28 */ // Type for resource measurement (LFS).
type X__id_t = uint32                      /* types.h:159:24 */ // General type for IDs.
type X__time_t = int64                     /* types.h:160:26 */ // Seconds since the Epoch.
type X__useconds_t = uint32                /* types.h:161:30 */ // Count of microseconds.
type X__suseconds_t = int64                /* types.h:162:31 */ // Signed count of microseconds.
type X__suseconds64_t = int64              /* types.h:163:33 */

type X__daddr_t = int32 /* types.h:165:27 */ // The type of a disk address.
type X__key_t = int32   /* types.h:166:25 */ // Type of an IPC key.

// Clock ID used in clock and timer functions.
type X__clockid_t = int32 /* types.h:169:29 */

// Timer ID returned by `timer_create'.
type X__timer_t = uintptr /* types.h:172:12 */

// Type to represent block size.
type X__blksize_t = int32 /* types.h:175:29 */

// Types from the Large File Support interface.

// Type to count number of disk blocks.
type X__blkcnt_t = int64   /* types.h:180:28 */
type X__blkcnt64_t = int64 /* types.h:181:30 */

// Type to count file system blocks.
type X__fsblkcnt_t = uint64   /* types.h:184:30 */
type X__fsblkcnt64_t = uint64 /* types.h:185:32 */

// Type to count file system nodes.
type X__fsfilcnt_t = uint64   /* types.h:188:30 */
type X__fsfilcnt64_t = uint64 /* types.h:189:32 */

// Type of miscellaneous file system fields.
type X__fsword_t = int64 /* types.h:192:28 */

type X__ssize_t = int64 /* types.h:194:27 */ // Type of a byte count, or error.

// Signed long type used in system calls.
type X__syscall_slong_t = int64 /* types.h:197:33 */
// Unsigned long type used in system calls.
type X__syscall_ulong_t = uint64 /* types.h:199:33 */

// These few don't really vary by system, they always correspond
//
//	to one of the other defined types.
type X__loff_t = int64    /* types.h:203:19 */ // Type of file sizes and offsets (LFS).
type X__caddr_t = uintptr /* types.h:204:14 */

// Duplicates info from stdint.h but this is used in unistd.h.
type X__intptr_t = int64 /* types.h:207:25 */

// Duplicate info from sys/socket.h.
type X__socklen_t = uint32 /* types.h:210:23 */

// C99: An integer type that can be accessed as an atomic entity,
//
//	even in the presence of asynchronous interrupts.
//	It is not currently necessary for this to be machine-specific.
type X__sig_atomic_t = int32 /* types.h:215:13 */

// Seconds since the Epoch, visible to user code when time_t is too
//    narrow only for consistency with the old way of widening too-narrow
//    types.  User code should never use __time64_t.

// Signal number constants.  Generic template.
//    Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Fake signal functions.

// We define here all the signal names listed in POSIX (1003.1-2008);
//    as of 1003.1-2013, no additional signals have been added by POSIX.
//    We also define here signal names that historically exist in every
//    real-world POSIX variant (e.g. SIGWINCH).
//
//    Signals in the 1-15 range are defined with their historical numbers.
//    For other signals, we use the BSD numbers.
//    There are two unallocated signal numbers in the 1-31 range: 7 and 29.
//    Signal number 0 is reserved for use as kill(pid, 0), to test whether
//    a process exists without sending it a signal.

// ISO C99 signals.

// Historical signals specified by POSIX.

// Archaic names for compatibility.

// Not all systems support real-time signals.  bits/signum.h indicates
//    that they are supported by overriding __SIGRTMAX to a value greater
//    than __SIGRTMIN.  These constants give the kernel-level hard limits,
//    but some real-time signals may be used internally by glibc.  Do not
//    use these constants in application code; use SIGRTMIN and SIGRTMAX
//    (defined in signal.h) instead.

// Include system specific bits.
// Signal number definitions.  Linux version.
//    Copyright (C) 1995-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Adjustments and additions to the signal number constants for
//    most Linux systems.

// Historical signals specified by POSIX.

// New(er) POSIX signals (1003.1-2008, 1003.1-2013).

// Nonstandard signals found in all modern POSIX systems
//    (including both BSD and Linux).

// Archaic names for compatibility.

// Biggest signal number + 1 (including real-time signals).

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// An integral type that can be modified atomically, without the
//
//	possibility of a signal arriving in the middle of the operation.
type Sig_atomic_t = int32 /* sig_atomic_t.h:8:24 */

type X__sigset_t = struct{ F__val [16]uint64 } /* __sigset_t.h:8:3 */

// A set of signals to be blocked, unblocked, or waited for.
type Sigset_t = X__sigset_t /* sigset_t.h:7:20 */

type Pid_t = int32  /* signal.h:40:17 */
type Uid_t = uint32 /* signal.h:46:17 */

// We need `struct timespec' later on.
// NB: Include guard matches what <linux/time.h> uses.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Endian macros for string.h functions
//    Copyright (C) 1992-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Definitions for byte order, according to significance of bytes,
//    from low addresses to high addresses.  The value is what you get by
//    putting '4' in the most significant byte, '3' in the second most
//    significant byte, '2' in the second least significant byte, and '1'
//    in the least significant byte, and then writing down one digit for
//    each byte, starting with the byte at the lowest address at the left,
//    and proceeding to the byte with the highest address at the right.

// This file defines `__BYTE_ORDER' for the particular machine.

// LoongArch is little-endian.

// Some machines may need to use a different endianness for floating point
//    values.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Returned by `time'.
type Time_t = int64 /* time_t.h:10:18 */

// POSIX.1b structure for a time value.  This is like a `struct timeval' but
//
//	has nanoseconds instead of microseconds.
type Timespec = struct {
	Ftv_sec  int64
	Ftv_nsec int64
} /* struct_timespec.h:11:1 */

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Type for data associated with a signal.
type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* __sigval_t.h:24:1 */

type X__sigval_t = Sigval /* __sigval_t.h:30:22 */

// Some fields of siginfo_t have architecture-specific variations.
// Architecture-specific adjustments to siginfo_t.

// This architecture has no adjustments to make to siginfo_t.

type Siginfo_t = struct {
	Fsi_signo  int32
	Fsi_errno  int32
	Fsi_code   int32
	F__pad0    int32
	F_sifields struct {
		F__ccgo_pad1 [0]uint64
		F_pad        [28]int32
	}
} /* siginfo_t.h:124:5 */

// Architectures might also add architecture-specific constants.
//    These are all considered GNU extensions.

// Define __sigval_t.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// To avoid sigval_t (not a standard type name) having C++ name
//    mangling depending on whether the selected standard includes union
//    sigval, it should not be defined at all when using a standard for
//    which the sigval name is not reserved; in that case, headers should
//    not include <bits/types/sigval_t.h> and should use only the
//    internal __sigval_t name.

type Sigval_t = X__sigval_t /* sigval_t.h:16:20 */

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Forward declaration.
type Pthread_attr_t1 = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [56]int8
} /* sigevent_t.h:17:9 */

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Define __sigval_t.
//    Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Forward declaration.
type Pthread_attr_t = Pthread_attr_t1 /* sigevent_t.h:17:30 */

// Structure to transport application-defined values with signals.
type Sigevent = struct {
	Fsigev_value  X__sigval_t
	Fsigev_signo  int32
	Fsigev_notify int32
	F_sigev_un    struct {
		F__ccgo_pad1 [0]uint64
		F_pad        [12]int32
	}
} /* sigevent_t.h:22:9 */

// Structure to transport application-defined values with signals.
type Sigevent_t = Sigevent /* sigevent_t.h:42:5 */

// Type of a signal handler.
type X__sighandler_t = uintptr /* signal.h:72:14 */

// 4.4 BSD uses the name `sig_t' for this.
type Sig_t = uintptr /* signal.h:193:24 */

// Get the system-specific definitions of `struct sigaction'
//    and the `SA_*' and `SIG_*'. constants.
// The proper definitions for Linux's sigaction.
//    Copyright (C) 1993-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Structure describing the action to be taken when a signal arrives.
type Sigaction = struct {
	F__sigaction_handler struct{ Fsa_handler uintptr }
	Fsa_mask             X__sigset_t
	Fsa_flags            int32
	F__ccgo_pad1         [4]byte
	Fsa_restorer         uintptr
} /* sigaction.h:27:1 */

// Get machine-dependent `struct sigcontext' and signal subcodes.
// Copyright (C) 1996-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Kernel headers before 2.1.1 define a struct sigcontext_struct, but
//    we need sigcontext.

// SPDX-License-Identifier: GPL-2.0+ WITH Linux-syscall-note
// Author: Hanlu Li <<EMAIL>>
//         Huacai Chen <<EMAIL>>
//
// Copyright (C) 2020-2022 Loongson Technology Corporation Limited

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
// int-ll64 is used everywhere now.
// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
// asm-generic/int-ll64.h
//
// Integer declarations for architectures which use "long long"
// for 64-bit types.

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// In order to keep safe and avoid regression, only unify uapi
// bitsperlong.h for some archs which are using newer toolchains
// that have the definitions of __CHAR_BIT__ and __SIZEOF_LONG__.
// See the following link for more info:
// https://lore.kernel.org/linux-arch/<EMAIL>/

// __xx is ok: it doesn't pollute the POSIX namespace. Use these in the
// header files exported to user space

type X__s8 = int8  /* int-ll64.h:20:25 */
type X__u8 = uint8 /* int-ll64.h:21:23 */

type X__s16 = int16  /* int-ll64.h:23:26 */
type X__u16 = uint16 /* int-ll64.h:24:24 */

type X__s32 = int32  /* int-ll64.h:26:24 */
type X__u32 = uint32 /* int-ll64.h:27:22 */

type X__s64 = int64  /* int-ll64.h:30:44 */
type X__u64 = uint64 /* int-ll64.h:31:42 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// *
// __struct_group() - Create a mirrored named and anonyomous struct
//
// @TAG: The tag name for the named sub-struct (usually empty)
// @NAME: The identifier name of the mirrored sub-struct
// @ATTRS: Any struct attributes (usually empty)
// @MEMBERS: The member declarations for the mirrored structs
//
// Used to create an anonymous union of two structs with identical layout
// and size: one anonymous and one named. The former's members can be used
// normally without sub-struct naming, and the latter can be used to
// reason about the start, end, and size of the group of struct members.
// The named struct can also be explicitly tagged for layer reuse, as well
// as both having struct attributes appended.

// *
// __DECLARE_FLEX_ARRAY() - Declare a flexible array usable in a union
//
// @TYPE: The type of each flexible array element
// @NAME: The name of the flexible array member
//
// In order to have a flexible array member in a union or alone in a
// struct, it needs to be wrapped in an anonymous struct with at least 1
// named member, but that member can be empty.

// This allows for 1024 file descriptors: if NR_OPEN is ever grown
// beyond that you'll have to change this too. But 1024 fd's seem to be
// enough even for such "real" unices like OSF/1, so hopefully this is
// one limit that doesn't have to be changed [again].
//
// Note that POSIX wants the FD_CLEAR(fd,fdsetp) defines to be in
// <sys/time.h> (and thus <linux/time.h>) - but this is a more logical
// place for them. Solved by having dummy defines in <sys/time.h>.

// This macro may have been defined in <gnu/types.h>. But we always
// use the one here.

type X__kernel_fd_set = struct{ Ffds_bits [16]uint64 } /* posix_types.h:27:3 */

// Type of a signal handler.
type X__kernel_sighandler_t = uintptr /* posix_types.h:30:14 */

// Type of a SYSV IPC key.
type X__kernel_key_t = int32 /* posix_types.h:33:13 */
type X__kernel_mqd_t = int32 /* posix_types.h:34:13 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.
//
// First the types that are often defined in different ways across
// architectures, so that you can override them.

type X__kernel_long_t = int64   /* posix_types.h:15:15 */
type X__kernel_ulong_t = uint64 /* posix_types.h:16:23 */

type X__kernel_ino_t = uint64 /* posix_types.h:20:26 */

type X__kernel_mode_t = uint32 /* posix_types.h:24:22 */

type X__kernel_pid_t = int32 /* posix_types.h:28:14 */

type X__kernel_ipc_pid_t = int32 /* posix_types.h:32:14 */

type X__kernel_uid_t = uint32 /* posix_types.h:36:22 */
type X__kernel_gid_t = uint32 /* posix_types.h:37:22 */

type X__kernel_suseconds_t = int64 /* posix_types.h:41:26 */

type X__kernel_daddr_t = int32 /* posix_types.h:45:14 */

type X__kernel_uid32_t = uint32 /* posix_types.h:49:22 */
type X__kernel_gid32_t = uint32 /* posix_types.h:50:22 */

type X__kernel_old_uid_t = uint32 /* posix_types.h:54:24 */
type X__kernel_old_gid_t = uint32 /* posix_types.h:55:24 */

type X__kernel_old_dev_t = uint32 /* posix_types.h:59:22 */

// Most 32 bit architectures use "unsigned int" size_t,
// and all 64 bit architectures use "unsigned long" size_t.
type X__kernel_size_t = uint64   /* posix_types.h:72:26 */
type X__kernel_ssize_t = int64   /* posix_types.h:73:25 */
type X__kernel_ptrdiff_t = int64 /* posix_types.h:74:25 */

type X__kernel_fsid_t = struct{ Fval [2]int32 } /* posix_types.h:81:3 */

// anything below here should be completely generic
type X__kernel_off_t = int64      /* posix_types.h:87:25 */
type X__kernel_loff_t = int64     /* posix_types.h:88:19 */
type X__kernel_old_time_t = int64 /* posix_types.h:89:25 */
type X__kernel_time_t = int64     /* posix_types.h:90:25 */
type X__kernel_time64_t = int64   /* posix_types.h:91:19 */
type X__kernel_clock_t = int64    /* posix_types.h:92:25 */
type X__kernel_timer_t = int32    /* posix_types.h:93:14 */
type X__kernel_clockid_t = int32  /* posix_types.h:94:14 */
type X__kernel_caddr_t = uintptr  /* posix_types.h:95:14 */
type X__kernel_uid16_t = uint16   /* posix_types.h:96:24 */
type X__kernel_gid16_t = uint16   /* posix_types.h:97:24 */

// type X__s128 = libc.Int128  /* types.h:12:29 */
// type X__u128 = libc.Uint128 /* types.h:13:27 */

// Below are truly Linux-specific types that should never collide with
// any application/library that wants linux/types.h.

// sparse defines __CHECKER__; see Documentation/dev-tools/sparse.rst

// The kernel doesn't use this legacy form, but user space does

type X__le16 = uint16 /* types.h:31:25 */
type X__be16 = uint16 /* types.h:32:25 */
type X__le32 = uint32 /* types.h:33:25 */
type X__be32 = uint32 /* types.h:34:25 */
type X__le64 = uint64 /* types.h:35:25 */
type X__be64 = uint64 /* types.h:36:25 */

type X__sum16 = uint16 /* types.h:38:25 */
type X__wsum = uint32  /* types.h:39:25 */

// aligned_u64 should be used in defining kernel<->userspace ABIs to avoid
// common 32/64-bit compat problems.
// 64-bit values align to 4-byte boundaries on x86_32 (and possibly other
// architectures) and to 8-byte boundaries on 64-bit architectures.  The new
// aligned_64 type enforces 8-byte alignment so that structs containing
// aligned_64 values have the same alignment on 32-bit and 64-bit architectures.
// No conversions are necessary between 32-bit user-space and a 64-bit kernel.

type X__poll_t = uint32 /* types.h:54:28 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// FP context was used
// Address error was due to memory load
// Address error was due to memory store

type Sigcontext = struct {
	F__ccgo_pad1 [0]uint64
	Fsc_pc       uint64
	Fsc_regs     [32]uint64
	Fsc_flags    uint32
	F__ccgo_pad2 [4]byte
} /* sigcontext.h:21:1 */

type Sctx_info = struct {
	Fmagic   uint32
	Fsize    uint32
	Fpadding uint64
} /* sigcontext.h:29:1 */

// FPU context
type Fpu_context = struct {
	Fregs        [32]uint64
	Ffcc         uint64
	Ffcsr        uint32
	F__ccgo_pad1 [4]byte
} /* sigcontext.h:38:1 */

// LSX context
type Lsx_context = struct {
	Fregs        [64]uint64
	Ffcc         uint64
	Ffcsr        uint32
	F__ccgo_pad1 [4]byte
} /* sigcontext.h:47:1 */

// LASX context
type Lasx_context = struct {
	Fregs        [128]uint64
	Ffcc         uint64
	Ffcsr        uint32
	F__ccgo_pad1 [4]byte
} /* sigcontext.h:56:1 */

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Define stack_t.  Linux version.
//    Copyright (C) 1998-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1989-2023 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Structure describing a signal stack.
type Stack_t = struct {
	Fss_sp       uintptr
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
	Fss_size     uint64
} /* stack_t.h:31:5 */

// This will define `ucontext_t' and `mcontext_t'.
// struct ucontext definition.
//    Copyright (C) 2022-2023 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// Don't rely on this, the interface is currently messed up and may need to
//    be broken to be fixed.

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define stack_t.  Linux version.
//    Copyright (C) 1998-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

type Greg_t = uint64 /* ucontext.h:41:27 */
// Container for all general registers.
type Gregset_t = [32]uint64 /* ucontext.h:43:16 */

type Mcontext_t1 = struct {
	F__ccgo_pad1 [0]uint64
	F__pc        uint64
	F__gregs     [32]uint64
	F__flags     uint32
	F__ccgo_pad2 [4]byte
} /* ucontext.h:46:9 */

type Mcontext_t = Mcontext_t1 /* ucontext.h:52:3 */

// Userlevel context.
type Ucontext_t1 = struct {
	F__uc_flags  uint64
	Fuc_link     uintptr
	Fuc_stack    Stack_t
	Fuc_sigmask  Sigset_t
	Fuc_mcontext Mcontext_t
} /* ucontext.h:55:9 */

// Userlevel context.
type Ucontext_t = Ucontext_t1 /* ucontext.h:62:3 */

// Define struct sigstack.
//    Copyright (C) 1998-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Structure describing a signal stack (obsolete).
type Sigstack = struct {
	Fss_sp       uintptr
	Fss_onstack  int32
	F__ccgo_pad1 [4]byte
} /* struct_sigstack.h:23:1 */

// Some of the functions for handling signals in threaded programs must
//    be defined here.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Monotonically increasing wide counters (at least 62 bits).
//    Copyright (C) 2016-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Counter that is monotonically increasing (by less than 2**31 per
//
//	increment), with a single writer, and an arbitrary number of
//	readers.
type X__atomic_wide_counter = struct{ F__value64 uint64 } /* atomic_wide_counter.h:33:3 */

// Common definition of pthread_mutex_t.

type X__pthread_internal_list = struct {
	F__prev uintptr
	F__next uintptr
} /* thread-shared-types.h:51:9 */

// Common definition of pthread_mutex_t.

type X__pthread_list_t = X__pthread_internal_list /* thread-shared-types.h:55:3 */

type X__pthread_internal_slist = struct{ F__next uintptr } /* thread-shared-types.h:57:9 */

type X__pthread_slist_t = X__pthread_internal_slist /* thread-shared-types.h:60:3 */

// Arch-specific mutex definitions.  A generic implementation is provided
//    by sysdeps/nptl/bits/struct_mutex.h.  If required, an architecture
//    can override it by defining:
//
//    1. struct __pthread_mutex_s (used on both pthread_mutex_t and mtx_t
//       definition).  It should contains at least the internal members
//       defined in the generic version.
//
//    2. __LOCK_ALIGNMENT for any extra attribute for internal lock used with
//       atomic operations.
//
//    3. The macro __PTHREAD_MUTEX_INITIALIZER used for static initialization.
//       It should initialize the mutex internal flag.

// Default mutex implementation struct definitions.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Generic struct for both POSIX and C11 mutexes.  New ports are expected
//    to use the default layout, however architecture can redefine it to
//    add arch-specific extension (such as lock-elision).  The struct have
//    a size of 32 bytes on LP32 and 40 bytes on LP64 architectures.

type X__pthread_mutex_s = struct {
	F__lock   int32
	F__count  uint32
	F__owner  int32
	F__nusers uint32
	F__kind   int32
	F__spins  int32
	F__list   X__pthread_list_t
} /* struct_mutex.h:27:1 */

// Arch-sepecific read-write lock definitions.  A generic implementation is
//    provided by struct_rwlock.h.  If required, an architecture can override it
//    by defining:
//
//    1. struct __pthread_rwlock_arch_t (used on pthread_rwlock_t definition).
//       It should contain at least the internal members defined in the
//       generic version.
//
//    2. The macro __PTHREAD_RWLOCK_INITIALIZER used for static initialization.
//       It should initialize the rwlock internal type.

// Default read-write lock implementation struct definitions.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Endian macros for string.h functions
//    Copyright (C) 1992-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Generic struct for both POSIX read-write lock.  New ports are expected
//    to use the default layout, however archictetures can redefine it to add
//    arch-specific extensions (such as lock-elision).  The struct have a size
//    of 32 bytes on both LP32 and LP64 architectures.

type X__pthread_rwlock_arch_t = struct {
	F__readers       uint32
	F__writers       uint32
	F__wrphase_futex uint32
	F__writers_futex uint32
	F__pad3          uint32
	F__pad4          uint32
	F__flags         uint8
	F__shared        uint8
	F__pad1          uint8
	F__pad2          uint8
	F__cur_writer    int32
} /* struct_rwlock.h:29:1 */

// Common definition of pthread_cond_t.

type X__pthread_cond_s = struct {
	F__wseq         X__atomic_wide_counter
	F__g1_start     X__atomic_wide_counter
	F__g_refs       [2]uint32
	F__g_size       [2]uint32
	F__g1_orig_size uint32
	F__wrefs        uint32
	F__g_signals    [2]uint32
} /* thread-shared-types.h:94:1 */

type X__tss_t = uint32  /* thread-shared-types.h:105:22 */
type X__thrd_t = uint64 /* thread-shared-types.h:106:27 */

type X__once_flag = struct{ F__data int32 } /* thread-shared-types.h:111:3 */

// Thread identifiers.  The structure of the attribute type is not
//
//	exposed on purpose.
type Pthread_t = uint64 /* pthreadtypes.h:27:27 */

// Data structures for mutex handling.  The structure of the attribute
//
//	type is not exposed on purpose.
type Pthread_mutexattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:36:3 */

// Data structure for condition variable handling.  The structure of
//
//	the attribute type is not exposed on purpose.
type Pthread_condattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:45:3 */

// Keys for thread-specific data
type Pthread_key_t = uint32 /* pthreadtypes.h:49:22 */

// Once-only execution
type Pthread_once_t = int32 /* pthreadtypes.h:53:30 */

type Pthread_mutex_t = struct{ F__data X__pthread_mutex_s } /* pthreadtypes.h:72:3 */

type Pthread_cond_t = struct{ F__data X__pthread_cond_s } /* pthreadtypes.h:80:3 */

// Data structure for reader-writer lock variable handling.  The
//
//	structure of the attribute type is deliberately not exposed.
type Pthread_rwlock_t = struct {
	F__ccgo_pad1 [0]uint64
	F__data      X__pthread_rwlock_arch_t
	F__ccgo_pad2 [24]byte
} /* pthreadtypes.h:91:3 */

type Pthread_rwlockattr_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [8]int8
} /* pthreadtypes.h:97:3 */

// POSIX spinlock data type.
type Pthread_spinlock_t = int32 /* pthreadtypes.h:103:22 */

// POSIX barriers data type.  The structure of the type is
//
//	deliberately not exposed.
type Pthread_barrier_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [32]int8
} /* pthreadtypes.h:112:3 */

type Pthread_barrierattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:118:3 */

// System-specific extensions.
// System-specific extensions of <signal.h>, Linux version.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

var _ int8 /* gen.c:2:13: */
