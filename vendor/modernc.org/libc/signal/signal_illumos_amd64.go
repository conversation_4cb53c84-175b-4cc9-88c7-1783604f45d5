// Code generated by 'ccgo signal/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o signal/signal_illumos_amd64.go -pkgname signal', DO NOT EDIT.

package signal

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BUS_ADRALN                           = 1          // machsig.h:109:1:
	BUS_ADRERR                           = 2          // machsig.h:110:1:
	BUS_OBJERR                           = 3          // machsig.h:112:1:
	CLD_CONTINUED                        = 6          // siginfo.h:169:1:
	CLD_DUMPED                           = 3          // siginfo.h:166:1:
	CLD_EXITED                           = 1          // siginfo.h:164:1:
	CLD_KILLED                           = 2          // siginfo.h:165:1:
	CLD_STOPPED                          = 5          // siginfo.h:168:1:
	CLD_TRAPPED                          = 4          // siginfo.h:167:1:
	CLOCKS_PER_SEC                       = 1000000    // time_iso.h:78:1:
	CLOCK_HIGHRES                        = 4          // time_impl.h:126:1:
	CLOCK_MONOTONIC                      = 4          // time_impl.h:124:1:
	CLOCK_PROCESS_CPUTIME_ID             = 5          // time_impl.h:125:1:
	CLOCK_PROF                           = 2          // time_impl.h:127:1:
	CLOCK_REALTIME                       = 3          // time_impl.h:123:1:
	CLOCK_THREAD_CPUTIME_ID              = 2          // time_impl.h:122:1:
	CLOCK_VIRTUAL                        = 1          // time_impl.h:121:1:
	DST_AUST                             = 2          // time.h:115:1:
	DST_AUSTALT                          = 10         // time.h:123:1:
	DST_CAN                              = 6          // time.h:119:1:
	DST_EET                              = 5          // time.h:118:1:
	DST_GB                               = 7          // time.h:120:1:
	DST_MET                              = 4          // time.h:117:1:
	DST_NONE                             = 0          // time.h:113:1:
	DST_RUM                              = 8          // time.h:121:1:
	DST_TUR                              = 9          // time.h:122:1:
	DST_USA                              = 1          // time.h:114:1:
	DST_WET                              = 3          // time.h:116:1:
	EMT_CPCOVF                           = 1          // machsig.h:74:1:
	FC_ALIGN                             = 0x2        // faultcode.h:54:1:
	FC_HWERR                             = 0x1        // faultcode.h:53:1:
	FC_NOMAP                             = 0x5        // faultcode.h:57:1:
	FC_NOSUPPORT                         = 0x6        // faultcode.h:58:1:
	FC_OBJERR                            = 0x3        // faultcode.h:55:1:
	FC_PROT                              = 0x4        // faultcode.h:56:1:
	FD_SETSIZE                           = 65536      // select.h:88:1:
	FPE_FLTDEN                           = 9          // machsig.h:90:1:
	FPE_FLTDIV                           = 3          // machsig.h:84:1:
	FPE_FLTINV                           = 7          // machsig.h:88:1:
	FPE_FLTOVF                           = 4          // machsig.h:85:1:
	FPE_FLTRES                           = 6          // machsig.h:87:1:
	FPE_FLTSUB                           = 8          // machsig.h:89:1:
	FPE_FLTUND                           = 5          // machsig.h:86:1:
	FPE_INTDIV                           = 1          // machsig.h:82:1:
	FPE_INTOVF                           = 2          // machsig.h:83:1:
	ILL_BADSTK                           = 8          // machsig.h:64:1:
	ILL_COPROC                           = 7          // machsig.h:63:1:
	ILL_ILLADR                           = 3          // machsig.h:59:1:
	ILL_ILLOPC                           = 1          // machsig.h:57:1:
	ILL_ILLOPN                           = 2          // machsig.h:58:1:
	ILL_ILLTRP                           = 4          // machsig.h:60:1:
	ILL_PRVOPC                           = 5          // machsig.h:61:1:
	ILL_PRVREG                           = 6          // machsig.h:62:1:
	ITIMER_PROF                          = 2          // time.h:201:1:
	ITIMER_REAL                          = 0          // time.h:199:1:
	ITIMER_REALPROF                      = 3          // time.h:204:1:
	ITIMER_VIRTUAL                       = 1          // time.h:200:1:
	MAXSIG                               = 74         // signal.h:163:1:
	MICROSEC                             = 1000000    // time.h:246:1:
	MILLISEC                             = 1000       // time.h:245:1:
	MINSIGSTKSZ                          = 2048       // signal.h:166:1:
	NANOSEC                              = 1000000000 // time.h:247:1:
	NBBY                                 = 8          // select.h:103:1:
	NSIG                                 = 75         // signal.h:162:1:
	NSIGBUS                              = 3          // machsig.h:115:1:
	NSIGCLD                              = 6          // siginfo.h:172:1:
	NSIGEMT                              = 1          // machsig.h:75:1:
	NSIGFPE                              = 9          // machsig.h:92:1:
	NSIGILL                              = 8          // machsig.h:66:1:
	NSIGPOLL                             = 6          // siginfo.h:187:1:
	NSIGPROF                             = 1          // siginfo.h:198:1:
	NSIGSEGV                             = 2          // machsig.h:102:1:
	NSIGTRAP                             = 6          // siginfo.h:157:1:
	POLL_ERR                             = 4          // siginfo.h:182:1:
	POLL_HUP                             = 6          // siginfo.h:184:1:
	POLL_IN                              = 1          // siginfo.h:179:1:
	POLL_MSG                             = 3          // siginfo.h:181:1:
	POLL_OUT                             = 2          // siginfo.h:180:1:
	POLL_PRI                             = 5          // siginfo.h:183:1:
	PROF_SIG                             = 1          // siginfo.h:197:1:
	P_INITPGID                           = 0          // procset.h:50:1:
	P_INITPID                            = 1          // procset.h:48:1:
	P_INITUID                            = 0          // procset.h:49:1:
	P_MYID                               = -1         // types.h:632:1:
	REG_LABEL_BP                         = 2          // machtypes.h:44:1:
	REG_LABEL_MAX                        = 8          // machtypes.h:51:1:
	REG_LABEL_PC                         = 0          // machtypes.h:42:1:
	REG_LABEL_R12                        = 4          // machtypes.h:47:1:
	REG_LABEL_R13                        = 5          // machtypes.h:48:1:
	REG_LABEL_R14                        = 6          // machtypes.h:49:1:
	REG_LABEL_R15                        = 7          // machtypes.h:50:1:
	REG_LABEL_RBX                        = 3          // machtypes.h:46:1:
	REG_LABEL_SP                         = 1          // machtypes.h:43:1:
	SA_NOCLDSTOP                         = 0x00020000 // signal.h:128:1:
	SA_NOCLDWAIT                         = 0x00010000 // signal.h:155:1:
	SA_NODEFER                           = 0x00000010 // signal.h:152:1:
	SA_ONSTACK                           = 0x00000001 // signal.h:138:1:
	SA_RESETHAND                         = 0x00000002 // signal.h:139:1:
	SA_RESTART                           = 0x00000004 // signal.h:140:1:
	SA_SIGINFO                           = 0x00000008 // signal.h:146:1:
	SEC                                  = 1          // time.h:244:1:
	SEGV_ACCERR                          = 2          // machsig.h:100:1:
	SEGV_MAPERR                          = 1          // machsig.h:99:1:
	SIG2STR_MAX                          = 32         // signal.h:95:1:
	SIGABRT                              = 6          // signal_iso.h:58:1:
	SIGALRM                              = 14         // signal_iso.h:66:1:
	SIGBUS                               = 10         // signal_iso.h:62:1:
	SIGCANCEL                            = 36         // signal_iso.h:90:1:
	SIGCHLD                              = 18         // signal_iso.h:71:1:
	SIGCLD                               = 18         // signal_iso.h:70:1:
	SIGCONT                              = 25         // signal_iso.h:79:1:
	SIGEMT                               = 7          // signal_iso.h:59:1:
	SIGEV_NONE                           = 1          // siginfo.h:95:1:
	SIGEV_PORT                           = 4          // siginfo.h:98:1:
	SIGEV_SIGNAL                         = 2          // siginfo.h:96:1:
	SIGEV_THREAD                         = 3          // siginfo.h:97:1:
	SIGFPE                               = 8          // signal_iso.h:60:1:
	SIGFREEZE                            = 34         // signal_iso.h:88:1:
	SIGHUP                               = 1          // signal_iso.h:52:1:
	SIGILL                               = 4          // signal_iso.h:55:1:
	SIGINFO                              = 41         // signal_iso.h:95:1:
	SIGINT                               = 2          // signal_iso.h:53:1:
	SIGIO                                = 22         // signal_iso.h:76:1:
	SIGIOT                               = 6          // signal_iso.h:57:1:
	SIGJVM1                              = 39         // signal_iso.h:93:1:
	SIGJVM2                              = 40         // signal_iso.h:94:1:
	SIGKILL                              = 9          // signal_iso.h:61:1:
	SIGLOST                              = 37         // signal_iso.h:91:1:
	SIGLWP                               = 33         // signal_iso.h:87:1:
	SIGPIPE                              = 13         // signal_iso.h:65:1:
	SIGPOLL                              = 22         // signal_iso.h:75:1:
	SIGPROF                              = 29         // signal_iso.h:83:1:
	SIGPWR                               = 19         // signal_iso.h:72:1:
	SIGQUIT                              = 3          // signal_iso.h:54:1:
	SIGSEGV                              = 11         // signal_iso.h:63:1:
	SIGSTKSZ                             = 8192       // signal.h:167:1:
	SIGSTOP                              = 23         // signal_iso.h:77:1:
	SIGSYS                               = 12         // signal_iso.h:64:1:
	SIGTERM                              = 15         // signal_iso.h:67:1:
	SIGTHAW                              = 35         // signal_iso.h:89:1:
	SIGTRAP                              = 5          // signal_iso.h:56:1:
	SIGTSTP                              = 24         // signal_iso.h:78:1:
	SIGTTIN                              = 26         // signal_iso.h:80:1:
	SIGTTOU                              = 27         // signal_iso.h:81:1:
	SIGURG                               = 21         // signal_iso.h:74:1:
	SIGUSR1                              = 16         // signal_iso.h:68:1:
	SIGUSR2                              = 17         // signal_iso.h:69:1:
	SIGVTALRM                            = 28         // signal_iso.h:82:1:
	SIGWAITING                           = 32         // signal_iso.h:86:1:
	SIGWINCH                             = 20         // signal_iso.h:73:1:
	SIGXCPU                              = 30         // signal_iso.h:84:1:
	SIGXFSZ                              = 31         // signal_iso.h:85:1:
	SIGXRES                              = 38         // signal_iso.h:92:1:
	SIG_BLOCK                            = 1          // signal_iso.h:131:1:
	SIG_SETMASK                          = 3          // signal_iso.h:133:1:
	SIG_UNBLOCK                          = 2          // signal_iso.h:132:1:
	SI_ASYNCIO                           = -4         // siginfo.h:134:1:
	SI_DTRACE                            = 2050       // siginfo.h:128:1:
	SI_LWP                               = -1         // siginfo.h:131:1:
	SI_MAXSZ                             = 256        // siginfo.h:206:1:
	SI_MESGQ                             = -5         // siginfo.h:135:1:
	SI_NOINFO                            = 32767      // siginfo.h:127:1:
	SI_QUEUE                             = -2         // siginfo.h:132:1:
	SI_RCTL                              = 2049       // siginfo.h:129:1:
	SI_TIMER                             = -3         // siginfo.h:133:1:
	SI_USER                              = 0          // siginfo.h:130:1:
	SN_CANCEL                            = 2          // signal.h:226:1:
	SN_PROC                              = 1          // signal.h:225:1:
	SN_SEND                              = 3          // signal.h:227:1:
	SS_DISABLE                           = 0x00000002 // signal.h:170:1:
	SS_ONSTACK                           = 0x00000001 // signal.h:169:1:
	TIMER_ABSTIME                        = 0x1        // time_impl.h:134:1:
	TIMER_RELTIME                        = 0x0        // time_impl.h:133:1:
	TIME_UTC                             = 0x1        // time.h:306:1:
	TRAP_BRKPT                           = 1          // siginfo.h:150:1:
	TRAP_DTRACE                          = 6          // siginfo.h:155:1:
	TRAP_RWATCH                          = 3          // siginfo.h:152:1:
	TRAP_TRACE                           = 2          // siginfo.h:151:1:
	TRAP_WWATCH                          = 4          // siginfo.h:153:1:
	TRAP_XWATCH                          = 5          // siginfo.h:154:1:
	X_ACL_ACE_ENABLED                    = 0x2        // unistd.h:349:1:
	X_ACL_ACLENT_ENABLED                 = 0x1        // unistd.h:348:1:
	X_ALIGNMENT_REQUIRED                 = 1          // isa_defs.h:262:1:
	X_BIT_FIELDS_LTOH                    = 0          // isa_defs.h:245:1:
	X_BOOL_ALIGNMENT                     = 1          // isa_defs.h:248:1:
	X_CASE_INSENSITIVE                   = 0x2        // unistd.h:342:1:
	X_CASE_SENSITIVE                     = 0x1        // unistd.h:341:1:
	X_CHAR_ALIGNMENT                     = 1          // isa_defs.h:249:1:
	X_CHAR_IS_SIGNED                     = 0          // isa_defs.h:247:1:
	X_CLOCKID_T                          = 0          // types.h:568:1:
	X_CLOCK_T                            = 0          // types.h:563:1:
	X_COND_MAGIC                         = 0x4356     // types.h:426:1:
	X_CS_LFS64_CFLAGS                    = 72         // unistd.h:61:1:
	X_CS_LFS64_LDFLAGS                   = 73         // unistd.h:62:1:
	X_CS_LFS64_LIBS                      = 74         // unistd.h:63:1:
	X_CS_LFS64_LINTFLAGS                 = 75         // unistd.h:64:1:
	X_CS_LFS_CFLAGS                      = 68         // unistd.h:56:1:
	X_CS_LFS_LDFLAGS                     = 69         // unistd.h:57:1:
	X_CS_LFS_LIBS                        = 70         // unistd.h:58:1:
	X_CS_LFS_LINTFLAGS                   = 71         // unistd.h:59:1:
	X_CS_PATH                            = 65         // unistd.h:50:1:
	X_CS_POSIX_V6_ILP32_OFF32_CFLAGS     = 800        // unistd.h:85:1:
	X_CS_POSIX_V6_ILP32_OFF32_LDFLAGS    = 801        // unistd.h:86:1:
	X_CS_POSIX_V6_ILP32_OFF32_LIBS       = 802        // unistd.h:87:1:
	X_CS_POSIX_V6_ILP32_OFF32_LINTFLAGS  = 803        // unistd.h:88:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_CFLAGS    = 804        // unistd.h:89:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LDFLAGS   = 805        // unistd.h:90:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LIBS      = 806        // unistd.h:91:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LINTFLAGS = 807        // unistd.h:92:1:
	X_CS_POSIX_V6_LP64_OFF64_CFLAGS      = 808        // unistd.h:93:1:
	X_CS_POSIX_V6_LP64_OFF64_LDFLAGS     = 809        // unistd.h:94:1:
	X_CS_POSIX_V6_LP64_OFF64_LIBS        = 810        // unistd.h:95:1:
	X_CS_POSIX_V6_LP64_OFF64_LINTFLAGS   = 811        // unistd.h:96:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_CFLAGS    = 812        // unistd.h:97:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LDFLAGS   = 813        // unistd.h:98:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LIBS      = 814        // unistd.h:99:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LINTFLAGS = 815        // unistd.h:100:1:
	X_CS_POSIX_V6_WIDTH_RESTRICTED_ENVS  = 816        // unistd.h:101:1:
	X_CS_XBS5_ILP32_OFF32_CFLAGS         = 700        // unistd.h:67:1:
	X_CS_XBS5_ILP32_OFF32_LDFLAGS        = 701        // unistd.h:68:1:
	X_CS_XBS5_ILP32_OFF32_LIBS           = 702        // unistd.h:69:1:
	X_CS_XBS5_ILP32_OFF32_LINTFLAGS      = 703        // unistd.h:70:1:
	X_CS_XBS5_ILP32_OFFBIG_CFLAGS        = 705        // unistd.h:71:1:
	X_CS_XBS5_ILP32_OFFBIG_LDFLAGS       = 706        // unistd.h:72:1:
	X_CS_XBS5_ILP32_OFFBIG_LIBS          = 707        // unistd.h:73:1:
	X_CS_XBS5_ILP32_OFFBIG_LINTFLAGS     = 708        // unistd.h:74:1:
	X_CS_XBS5_LP64_OFF64_CFLAGS          = 709        // unistd.h:75:1:
	X_CS_XBS5_LP64_OFF64_LDFLAGS         = 710        // unistd.h:76:1:
	X_CS_XBS5_LP64_OFF64_LIBS            = 711        // unistd.h:77:1:
	X_CS_XBS5_LP64_OFF64_LINTFLAGS       = 712        // unistd.h:78:1:
	X_CS_XBS5_LPBIG_OFFBIG_CFLAGS        = 713        // unistd.h:79:1:
	X_CS_XBS5_LPBIG_OFFBIG_LDFLAGS       = 714        // unistd.h:80:1:
	X_CS_XBS5_LPBIG_OFFBIG_LIBS          = 715        // unistd.h:81:1:
	X_CS_XBS5_LPBIG_OFFBIG_LINTFLAGS     = 716        // unistd.h:82:1:
	X_DMA_USES_PHYSADDR                  = 0          // isa_defs.h:281:1:
	X_DONT_USE_1275_GENERIC_NAMES        = 0          // isa_defs.h:287:1:
	X_DOUBLE_ALIGNMENT                   = 8          // isa_defs.h:256:1:
	X_DOUBLE_COMPLEX_ALIGNMENT           = 8          // isa_defs.h:257:1:
	X_DTRACE_VERSION                     = 1          // feature_tests.h:490:1:
	X_FILE_OFFSET_BITS                   = 64         // <builtin>:25:1:
	X_FIRMWARE_NEEDS_FDISK               = 0          // isa_defs.h:282:1:
	X_FLOAT_ALIGNMENT                    = 4          // isa_defs.h:252:1:
	X_FLOAT_COMPLEX_ALIGNMENT            = 4          // isa_defs.h:253:1:
	X_HAVE_CPUID_INSN                    = 0          // isa_defs.h:288:1:
	X_IEEE_754                           = 0          // isa_defs.h:246:1:
	X_INT64_TYPE                         = 0          // int_types.h:82:1:
	X_INT_ALIGNMENT                      = 4          // isa_defs.h:251:1:
	X_ISO_CPP_14882_1998                 = 0          // feature_tests.h:466:1:
	X_ISO_C_9899_1999                    = 0          // feature_tests.h:472:1:
	X_ISO_C_9899_2011                    = 0          // feature_tests.h:478:1:
	X_ISO_SIGNAL_ISO_H                   = 0          // signal_iso.h:46:1:
	X_ISO_TIME_ISO_H                     = 0          // time_iso.h:46:1:
	X_LARGEFILE64_SOURCE                 = 1          // feature_tests.h:231:1:
	X_LARGEFILE_SOURCE                   = 1          // feature_tests.h:235:1:
	X_LITTLE_ENDIAN                      = 0          // isa_defs.h:242:1:
	X_LOCALE_T                           = 0          // time.h:291:1:
	X_LONGLONG_TYPE                      = 0          // feature_tests.h:412:1:
	X_LONG_ALIGNMENT                     = 8          // isa_defs.h:254:1:
	X_LONG_DOUBLE_ALIGNMENT              = 16         // isa_defs.h:258:1:
	X_LONG_DOUBLE_COMPLEX_ALIGNMENT      = 16         // isa_defs.h:259:1:
	X_LONG_LONG_ALIGNMENT                = 8          // isa_defs.h:255:1:
	X_LONG_LONG_ALIGNMENT_32             = 4          // isa_defs.h:268:1:
	X_LONG_LONG_LTOH                     = 0          // isa_defs.h:244:1:
	X_LP64                               = 1          // <predefined>:286:1:
	X_MAX_ALIGNMENT                      = 16         // isa_defs.h:261:1:
	X_MULTI_DATAMODEL                    = 0          // isa_defs.h:279:1:
	X_MUTEX_MAGIC                        = 0x4d58     // types.h:424:1:
	X_NBBY                               = 8          // select.h:100:1:
	X_NORETURN_KYWD                      = 0          // feature_tests.h:448:1:
	X_OFF_T                              = 0          // types.h:142:1:
	X_PC_2_SYMLINKS                      = 19         // unistd.h:309:1:
	X_PC_ACCESS_FILTERING                = 25         // unistd.h:315:1:
	X_PC_ACL_ENABLED                     = 20         // unistd.h:310:1:
	X_PC_ALLOC_SIZE_MIN                  = 13         // unistd.h:303:1:
	X_PC_ASYNC_IO                        = 10         // unistd.h:299:1:
	X_PC_CASE_BEHAVIOR                   = 22         // unistd.h:312:1:
	X_PC_CHOWN_RESTRICTED                = 9          // unistd.h:297:1:
	X_PC_FILESIZEBITS                    = 67         // unistd.h:325:1:
	X_PC_LAST                            = 101        // unistd.h:336:1:
	X_PC_LINK_MAX                        = 1          // unistd.h:289:1:
	X_PC_MAX_CANON                       = 2          // unistd.h:290:1:
	X_PC_MAX_INPUT                       = 3          // unistd.h:291:1:
	X_PC_MIN_HOLE_SIZE                   = 21         // unistd.h:311:1:
	X_PC_NAME_MAX                        = 4          // unistd.h:292:1:
	X_PC_NO_TRUNC                        = 7          // unistd.h:295:1:
	X_PC_PATH_MAX                        = 5          // unistd.h:293:1:
	X_PC_PIPE_BUF                        = 6          // unistd.h:294:1:
	X_PC_PRIO_IO                         = 11         // unistd.h:300:1:
	X_PC_REC_INCR_XFER_SIZE              = 14         // unistd.h:304:1:
	X_PC_REC_MAX_XFER_SIZE               = 15         // unistd.h:305:1:
	X_PC_REC_MIN_XFER_SIZE               = 16         // unistd.h:306:1:
	X_PC_REC_XFER_ALIGN                  = 17         // unistd.h:307:1:
	X_PC_SATTR_ENABLED                   = 23         // unistd.h:313:1:
	X_PC_SATTR_EXISTS                    = 24         // unistd.h:314:1:
	X_PC_SYMLINK_MAX                     = 18         // unistd.h:308:1:
	X_PC_SYNC_IO                         = 12         // unistd.h:301:1:
	X_PC_TIMESTAMP_RESOLUTION            = 26         // unistd.h:317:1:
	X_PC_VDISABLE                        = 8          // unistd.h:296:1:
	X_PC_XATTR_ENABLED                   = 100        // unistd.h:330:1:
	X_PC_XATTR_EXISTS                    = 101        // unistd.h:331:1:
	X_POINTER_ALIGNMENT                  = 8          // isa_defs.h:260:1:
	X_POSIX2_CHAR_TERM                   = 1          // unistd.h:391:1:
	X_POSIX2_C_BIND                      = 1          // unistd.h:401:1:
	X_POSIX2_C_DEV                       = 1          // unistd.h:402:1:
	X_POSIX2_C_VERSION                   = 199209     // unistd.h:376:1:
	X_POSIX2_FORT_RUN                    = 1          // unistd.h:403:1:
	X_POSIX2_LOCALEDEF                   = 1          // unistd.h:404:1:
	X_POSIX2_SW_DEV                      = 1          // unistd.h:405:1:
	X_POSIX2_UPE                         = 1          // unistd.h:406:1:
	X_POSIX2_VERSION                     = 199209     // unistd.h:363:1:
	X_POSIX_REGEXP                       = 1          // unistd.h:410:1:
	X_POSIX_SHELL                        = 1          // unistd.h:411:1:
	X_POSIX_VERSION                      = 199506     // unistd.h:355:1:
	X_PSM_MODULES                        = 0          // isa_defs.h:284:1:
	X_PTRDIFF_T                          = 0          // types.h:112:1:
	X_RESTRICT_KYWD                      = 0          // feature_tests.h:435:1:
	X_RTC_CONFIG                         = 0          // isa_defs.h:285:1:
	X_RWL_MAGIC                          = 0x5257     // types.h:427:1:
	X_SC_2_CHAR_TERM                     = 66         // unistd.h:175:1:
	X_SC_2_C_BIND                        = 45         // unistd.h:153:1:
	X_SC_2_C_DEV                         = 46         // unistd.h:154:1:
	X_SC_2_C_VERSION                     = 47         // unistd.h:155:1:
	X_SC_2_FORT_DEV                      = 48         // unistd.h:156:1:
	X_SC_2_FORT_RUN                      = 49         // unistd.h:157:1:
	X_SC_2_LOCALEDEF                     = 50         // unistd.h:158:1:
	X_SC_2_PBS                           = 724        // unistd.h:246:1:
	X_SC_2_PBS_ACCOUNTING                = 725        // unistd.h:247:1:
	X_SC_2_PBS_CHECKPOINT                = 726        // unistd.h:248:1:
	X_SC_2_PBS_LOCATE                    = 728        // unistd.h:249:1:
	X_SC_2_PBS_MESSAGE                   = 729        // unistd.h:250:1:
	X_SC_2_PBS_TRACK                     = 730        // unistd.h:251:1:
	X_SC_2_SW_DEV                        = 51         // unistd.h:159:1:
	X_SC_2_UPE                           = 52         // unistd.h:160:1:
	X_SC_2_VERSION                       = 53         // unistd.h:161:1:
	X_SC_ADVISORY_INFO                   = 731        // unistd.h:252:1:
	X_SC_AIO_LISTIO_MAX                  = 18         // unistd.h:125:1:
	X_SC_AIO_MAX                         = 19         // unistd.h:126:1:
	X_SC_AIO_PRIO_DELTA_MAX              = 20         // unistd.h:127:1:
	X_SC_ARG_MAX                         = 1          // unistd.h:106:1:
	X_SC_ASYNCHRONOUS_IO                 = 21         // unistd.h:128:1:
	X_SC_ATEXIT_MAX                      = 76         // unistd.h:179:1:
	X_SC_AVPHYS_PAGES                    = 501        // unistd.h:190:1:
	X_SC_BARRIERS                        = 732        // unistd.h:253:1:
	X_SC_BC_BASE_MAX                     = 54         // unistd.h:162:1:
	X_SC_BC_DIM_MAX                      = 55         // unistd.h:163:1:
	X_SC_BC_SCALE_MAX                    = 56         // unistd.h:164:1:
	X_SC_BC_STRING_MAX                   = 57         // unistd.h:165:1:
	X_SC_CHILD_MAX                       = 2          // unistd.h:107:1:
	X_SC_CLK_TCK                         = 3          // unistd.h:108:1:
	X_SC_CLOCK_SELECTION                 = 733        // unistd.h:254:1:
	X_SC_COHER_BLKSZ                     = 503        // unistd.h:196:1:
	X_SC_COLL_WEIGHTS_MAX                = 58         // unistd.h:166:1:
	X_SC_CPUID_MAX                       = 517        // unistd.h:211:1:
	X_SC_CPUTIME                         = 734        // unistd.h:255:1:
	X_SC_DCACHE_ASSOC                    = 513        // unistd.h:206:1:
	X_SC_DCACHE_BLKSZ                    = 510        // unistd.h:203:1:
	X_SC_DCACHE_LINESZ                   = 508        // unistd.h:201:1:
	X_SC_DCACHE_SZ                       = 506        // unistd.h:199:1:
	X_SC_DCACHE_TBLKSZ                   = 511        // unistd.h:204:1:
	X_SC_DELAYTIMER_MAX                  = 22         // unistd.h:129:1:
	X_SC_EPHID_MAX                       = 518        // unistd.h:212:1:
	X_SC_EXPR_NEST_MAX                   = 59         // unistd.h:167:1:
	X_SC_FSYNC                           = 23         // unistd.h:130:1:
	X_SC_GETGR_R_SIZE_MAX                = 569        // unistd.h:220:1:
	X_SC_GETPW_R_SIZE_MAX                = 570        // unistd.h:221:1:
	X_SC_HOST_NAME_MAX                   = 735        // unistd.h:256:1:
	X_SC_ICACHE_ASSOC                    = 512        // unistd.h:205:1:
	X_SC_ICACHE_BLKSZ                    = 509        // unistd.h:202:1:
	X_SC_ICACHE_LINESZ                   = 507        // unistd.h:200:1:
	X_SC_ICACHE_SZ                       = 505        // unistd.h:198:1:
	X_SC_IOV_MAX                         = 77         // unistd.h:180:1:
	X_SC_IPV6                            = 762        // unistd.h:283:1:
	X_SC_JOB_CONTROL                     = 6          // unistd.h:111:1:
	X_SC_LINE_MAX                        = 60         // unistd.h:168:1:
	X_SC_LOGIN_NAME_MAX                  = 571        // unistd.h:222:1:
	X_SC_LOGNAME_MAX                     = 10         // unistd.h:116:1:
	X_SC_MAPPED_FILES                    = 24         // unistd.h:131:1:
	X_SC_MAXPID                          = 514        // unistd.h:208:1:
	X_SC_MEMLOCK                         = 25         // unistd.h:132:1:
	X_SC_MEMLOCK_RANGE                   = 26         // unistd.h:133:1:
	X_SC_MEMORY_PROTECTION               = 27         // unistd.h:134:1:
	X_SC_MESSAGE_PASSING                 = 28         // unistd.h:135:1:
	X_SC_MONOTONIC_CLOCK                 = 736        // unistd.h:257:1:
	X_SC_MQ_OPEN_MAX                     = 29         // unistd.h:136:1:
	X_SC_MQ_PRIO_MAX                     = 30         // unistd.h:137:1:
	X_SC_NGROUPS_MAX                     = 4          // unistd.h:109:1:
	X_SC_NPROCESSORS_CONF                = 14         // unistd.h:120:1:
	X_SC_NPROCESSORS_MAX                 = 516        // unistd.h:210:1:
	X_SC_NPROCESSORS_ONLN                = 15         // unistd.h:121:1:
	X_SC_OPEN_MAX                        = 5          // unistd.h:110:1:
	X_SC_PAGESIZE                        = 11         // unistd.h:117:1:
	X_SC_PAGE_SIZE                       = 11         // unistd.h:182:1:
	X_SC_PASS_MAX                        = 9          // unistd.h:115:1:
	X_SC_PHYS_PAGES                      = 500        // unistd.h:189:1:
	X_SC_PRIORITIZED_IO                  = 31         // unistd.h:138:1:
	X_SC_PRIORITY_SCHEDULING             = 32         // unistd.h:139:1:
	X_SC_RAW_SOCKETS                     = 763        // unistd.h:284:1:
	X_SC_READER_WRITER_LOCKS             = 737        // unistd.h:258:1:
	X_SC_REALTIME_SIGNALS                = 33         // unistd.h:140:1:
	X_SC_REGEXP                          = 738        // unistd.h:259:1:
	X_SC_RE_DUP_MAX                      = 61         // unistd.h:169:1:
	X_SC_RTSIG_MAX                       = 34         // unistd.h:141:1:
	X_SC_SAVED_IDS                       = 7          // unistd.h:112:1:
	X_SC_SEMAPHORES                      = 35         // unistd.h:142:1:
	X_SC_SEM_NSEMS_MAX                   = 36         // unistd.h:143:1:
	X_SC_SEM_VALUE_MAX                   = 37         // unistd.h:144:1:
	X_SC_SHARED_MEMORY_OBJECTS           = 38         // unistd.h:145:1:
	X_SC_SHELL                           = 739        // unistd.h:260:1:
	X_SC_SIGQUEUE_MAX                    = 39         // unistd.h:146:1:
	X_SC_SIGRT_MAX                       = 41         // unistd.h:148:1:
	X_SC_SIGRT_MIN                       = 40         // unistd.h:147:1:
	X_SC_SPAWN                           = 740        // unistd.h:261:1:
	X_SC_SPIN_LOCKS                      = 741        // unistd.h:262:1:
	X_SC_SPLIT_CACHE                     = 504        // unistd.h:197:1:
	X_SC_SPORADIC_SERVER                 = 742        // unistd.h:263:1:
	X_SC_SS_REPL_MAX                     = 743        // unistd.h:264:1:
	X_SC_STACK_PROT                      = 515        // unistd.h:209:1:
	X_SC_STREAM_MAX                      = 16         // unistd.h:122:1:
	X_SC_SYMLOOP_MAX                     = 744        // unistd.h:265:1:
	X_SC_SYNCHRONIZED_IO                 = 42         // unistd.h:149:1:
	X_SC_THREADS                         = 576        // unistd.h:227:1:
	X_SC_THREAD_ATTR_STACKADDR           = 577        // unistd.h:228:1:
	X_SC_THREAD_ATTR_STACKSIZE           = 578        // unistd.h:229:1:
	X_SC_THREAD_CPUTIME                  = 745        // unistd.h:266:1:
	X_SC_THREAD_DESTRUCTOR_ITERATIONS    = 568        // unistd.h:219:1:
	X_SC_THREAD_KEYS_MAX                 = 572        // unistd.h:223:1:
	X_SC_THREAD_PRIORITY_SCHEDULING      = 579        // unistd.h:230:1:
	X_SC_THREAD_PRIO_INHERIT             = 580        // unistd.h:231:1:
	X_SC_THREAD_PRIO_PROTECT             = 581        // unistd.h:232:1:
	X_SC_THREAD_PROCESS_SHARED           = 582        // unistd.h:233:1:
	X_SC_THREAD_SAFE_FUNCTIONS           = 583        // unistd.h:234:1:
	X_SC_THREAD_SPORADIC_SERVER          = 746        // unistd.h:267:1:
	X_SC_THREAD_STACK_MIN                = 573        // unistd.h:224:1:
	X_SC_THREAD_THREADS_MAX              = 574        // unistd.h:225:1:
	X_SC_TIMEOUTS                        = 747        // unistd.h:268:1:
	X_SC_TIMERS                          = 43         // unistd.h:150:1:
	X_SC_TIMER_MAX                       = 44         // unistd.h:151:1:
	X_SC_TRACE                           = 748        // unistd.h:269:1:
	X_SC_TRACE_EVENT_FILTER              = 749        // unistd.h:270:1:
	X_SC_TRACE_EVENT_NAME_MAX            = 750        // unistd.h:271:1:
	X_SC_TRACE_INHERIT                   = 751        // unistd.h:272:1:
	X_SC_TRACE_LOG                       = 752        // unistd.h:273:1:
	X_SC_TRACE_NAME_MAX                  = 753        // unistd.h:274:1:
	X_SC_TRACE_SYS_MAX                   = 754        // unistd.h:275:1:
	X_SC_TRACE_USER_EVENT_MAX            = 755        // unistd.h:276:1:
	X_SC_TTY_NAME_MAX                    = 575        // unistd.h:226:1:
	X_SC_TYPED_MEMORY_OBJECTS            = 756        // unistd.h:277:1:
	X_SC_TZNAME_MAX                      = 17         // unistd.h:123:1:
	X_SC_T_IOV_MAX                       = 79         // unistd.h:186:1:
	X_SC_UADDR_MAX                       = 519        // unistd.h:213:1:
	X_SC_V6_ILP32_OFF32                  = 757        // unistd.h:278:1:
	X_SC_V6_ILP32_OFFBIG                 = 758        // unistd.h:279:1:
	X_SC_V6_LP64_OFF64                   = 759        // unistd.h:280:1:
	X_SC_V6_LPBIG_OFFBIG                 = 760        // unistd.h:281:1:
	X_SC_VERSION                         = 8          // unistd.h:113:1:
	X_SC_XBS5_ILP32_OFF32                = 720        // unistd.h:240:1:
	X_SC_XBS5_ILP32_OFFBIG               = 721        // unistd.h:241:1:
	X_SC_XBS5_LP64_OFF64                 = 722        // unistd.h:242:1:
	X_SC_XBS5_LPBIG_OFFBIG               = 723        // unistd.h:243:1:
	X_SC_XOPEN_CRYPT                     = 62         // unistd.h:170:1:
	X_SC_XOPEN_ENH_I18N                  = 63         // unistd.h:171:1:
	X_SC_XOPEN_LEGACY                    = 717        // unistd.h:237:1:
	X_SC_XOPEN_REALTIME                  = 718        // unistd.h:238:1:
	X_SC_XOPEN_REALTIME_THREADS          = 719        // unistd.h:239:1:
	X_SC_XOPEN_SHM                       = 64         // unistd.h:172:1:
	X_SC_XOPEN_STREAMS                   = 761        // unistd.h:282:1:
	X_SC_XOPEN_UNIX                      = 78         // unistd.h:181:1:
	X_SC_XOPEN_VERSION                   = 12         // unistd.h:118:1:
	X_SC_XOPEN_XCU_VERSION               = 67         // unistd.h:176:1:
	X_SEMA_MAGIC                         = 0x534d     // types.h:425:1:
	X_SHORT_ALIGNMENT                    = 2          // isa_defs.h:250:1:
	X_SIGEVENT                           = 0          // time.h:132:1:
	X_SIGNAL_H                           = 0          // signal.h:33:1:
	X_SIGRTMAX                           = 74         // signal_iso.h:99:1:
	X_SIGRTMIN                           = 42         // signal_iso.h:98:1:
	X_SIGSET_T                           = 0          // select.h:73:1:
	X_SIGVAL                             = 0          // time.h:124:1:
	X_SIZE_T                             = 0          // types.h:540:1:
	X_SOFT_HOSTID                        = 0          // isa_defs.h:286:1:
	X_SSIZE_T                            = 0          // types.h:549:1:
	X_STACK_GROWS_DOWNWARD               = 0          // isa_defs.h:243:1:
	X_STACK_T                            = 0          // signal.h:174:1:
	X_STDC_C11                           = 0          // feature_tests.h:165:1:
	X_STDC_C99                           = 0          // feature_tests.h:169:1:
	X_SUNOS_VTOC_16                      = 0          // isa_defs.h:280:1:
	X_SUSECONDS_T                        = 0          // types.h:343:1:
	X_SYS_CCOMPILE_H                     = 0          // ccompile.h:32:1:
	X_SYS_FEATURE_TESTS_H                = 0          // feature_tests.h:41:1:
	X_SYS_INT_TYPES_H                    = 0          // int_types.h:30:1:
	X_SYS_ISA_DEFS_H                     = 0          // isa_defs.h:30:1:
	X_SYS_MACHSIG_H                      = 0          // machsig.h:32:1:
	X_SYS_MACHTYPES_H                    = 0          // machtypes.h:27:1:
	X_SYS_NULL_H                         = 0          // null.h:17:1:
	X_SYS_PROCSET_H                      = 0          // procset.h:32:1:
	X_SYS_SELECT_H                       = 0          // select.h:45:1:
	X_SYS_SIGINFO_H                      = 0          // siginfo.h:31:1:
	X_SYS_SIGNAL_H                       = 0          // signal.h:42:1:
	X_SYS_SIGNAL_ISO_H                   = 0          // signal_iso.h:44:1:
	X_SYS_TIME_H                         = 0          // time.h:27:1:
	X_SYS_TIME_IMPL_H                    = 0          // time_impl.h:38:1:
	X_SYS_TYPES_H                        = 0          // types.h:35:1:
	X_SYS_UNISTD_H                       = 0          // unistd.h:40:1:
	X_TIMER_T                            = 0          // types.h:573:1:
	X_TIME_H                             = 0          // time.h:37:1:
	X_TIME_T                             = 0          // types.h:558:1:
	X_UID_T                              = 0          // types.h:400:1:
	X_VM_FAULTCODE_H                     = 0          // faultcode.h:40:1:
	X_XOPEN_ENH_I18N                     = 1          // unistd.h:389:1:
	X_XOPEN_REALTIME                     = 1          // unistd.h:388:1:
	X_XOPEN_SHM                          = 1          // unistd.h:390:1:
	X_XOPEN_STREAMS                      = 1          // unistd.h:412:1:
	X_XOPEN_UNIX                         = 0          // unistd.h:382:1:
	X_XOPEN_VERSION                      = 3          // feature_tests.h:392:1:
	X_XOPEN_XCU_VERSION                  = 4          // unistd.h:385:1:
	X_XOPEN_XPG3                         = 0          // unistd.h:380:1:
	X_XOPEN_XPG4                         = 0          // unistd.h:381:1:
	Sun                                  = 1          // <predefined>:172:1:
	Unix                                 = 1          // <predefined>:175:1:
)

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015, Joyent, Inc.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//	This file defines the data needed to specify a set of
//	processes.  These types are used by the sigsend, sigsendset,
//	priocntl, priocntlset, waitid, evexit, and evexitset system
//	calls.

// The following defines the values for an identifier type.  It
// specifies the interpretation of an id value.  An idtype and
// id together define a simple set of processes.
const ( /* procset.h:58:1: */
	P_PID  = 0 // A process identifier.
	P_PPID = 1 // A parent process identifier.
	P_PGID = 2 // A process group (job control group)
	// identifier.
	P_SID    = 3  // A session identifier.
	P_CID    = 4  // A scheduling class identifier.
	P_UID    = 5  // A user identifier.
	P_GID    = 6  // A group identifier.
	P_ALL    = 7  // All processes.
	P_LWPID  = 8  // An LWP identifier.
	P_TASKID = 9  // A task identifier.
	P_PROJID = 10 // A project identifier.
	P_POOLID = 11 // A pool identifier.
	P_ZONEID = 12 // A zone identifier.
	P_CTID   = 13 // A (process) contract identifier.
	P_CPUID  = 14 // CPU identifier.
	P_PSETID = 15
)

// The following defines the operations which can be performed to
// combine two simple sets of processes to form another set of
// processes.
const ( /* procset.h:89:1: */
	POP_DIFF = 0 // Set difference.  The processes which
	// are in the left operand set and not
	// in the right operand set.
	POP_AND = 1 // Set disjunction.  The processes
	// which are in both the left and right
	// operand sets.
	POP_OR = 2 // Set conjunction.  The processes
	// which are in either the left or the
	// right operand sets (or both).
	POP_XOR = 3
) // used for block sizes

// The boolean_t type has had a varied amount of exposure over the years in
// terms of how its enumeration constants have been exposed. In particular, it
// originally used the __XOPEN_OR_POSIX macro to determine whether to prefix the
// B_TRUE and B_FALSE with an underscore. This check never included the
// question of if we were in a strict ANSI C environment or whether extensions
// were defined.
//
// Compilers such as clang started defaulting to always including an
// XOPEN_SOURCE declaration on behalf of users, but also noted __EXTENSIONS__.
// This would lead most software that had used the non-underscore versions to
// need it. As such, we have adjusted the non-strict XOPEN environment to retain
// its old behavior so as to minimize namespace pollution; however, we instead
// include both variants of the definitions in the generally visible version
// allowing software written in either world to hopefully end up in a good
// place.
//
// This isn't perfect, but should hopefully minimize the pain for folks actually
// trying to build software.
const ( /* types.h:215:1: */
	B_FALSE   = 0
	B_TRUE    = 1
	X_B_FALSE = 0
	X_B_TRUE  = 1
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 EveryCity Ltd. All rights reserved.
// Copyright 2019 Joyent, Inc.

// This file contains definitions designed to enable different compilers
// to be used harmoniously on Solaris systems.

// Allow for version tests for compiler bugs and features.

// analogous to lint's PRINTFLIKEn

// Handle the kernel printf routines that can take '%b' too

// This one's pretty obvious -- the function never returns

// The function is 'extern inline' and expects GNU C89 behaviour, not C99
// behaviour.
//
// Should only be used on 'extern inline' definitions for GCC.

// The function has control flow such that it may return multiple times (in
// the manner of setjmp or vfork)

// This is an appropriate label for functions that do not
// modify their arguments, e.g. strlen()

// This is a stronger form of __pure__. Can be used for functions
// that do not modify their arguments and don't depend on global
// memory.

// This attribute, attached to a variable, means that the variable is meant to
// be possibly unused. GCC will not produce a warning for this variable.

// Shorthand versions for readability

// In release build, disable warnings about variables
// which are used only for debugging.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// This header file serves to group a set of well known defines and to
// set these for each instruction set architecture.  These defines may
// be divided into two groups;  characteristics of the processor and
// implementation choices for Solaris on a processor.
//
// Processor Characteristics:
//
// _LITTLE_ENDIAN / _BIG_ENDIAN:
//	The natural byte order of the processor.  A pointer to an int points
//	to the least/most significant byte of that int.
//
// _STACK_GROWS_UPWARD / _STACK_GROWS_DOWNWARD:
//	The processor specific direction of stack growth.  A push onto the
//	stack increases/decreases the stack pointer, so it stores data at
//	successively higher/lower addresses.  (Stackless machines ignored
//	without regrets).
//
// _LONG_LONG_HTOL / _LONG_LONG_LTOH:
//	A pointer to a long long points to the most/least significant long
//	within that long long.
//
// _BIT_FIELDS_HTOL / _BIT_FIELDS_LTOH:
//	The C compiler assigns bit fields from the high/low to the low/high end
//	of an int (most to least significant vs. least to most significant).
//
// _IEEE_754:
//	The processor (or supported implementations of the processor)
//	supports the ieee-754 floating point standard.  No other floating
//	point standards are supported (or significant).  Any other supported
//	floating point formats are expected to be cased on the ISA processor
//	symbol.
//
// _CHAR_IS_UNSIGNED / _CHAR_IS_SIGNED:
//	The C Compiler implements objects of type `char' as `unsigned' or
//	`signed' respectively.  This is really an implementation choice of
//	the compiler writer, but it is specified in the ABI and tends to
//	be uniform across compilers for an instruction set architecture.
//	Hence, it has the properties of a processor characteristic.
//
// _CHAR_ALIGNMENT / _SHORT_ALIGNMENT / _INT_ALIGNMENT / _LONG_ALIGNMENT /
// _LONG_LONG_ALIGNMENT / _DOUBLE_ALIGNMENT / _LONG_DOUBLE_ALIGNMENT /
// _POINTER_ALIGNMENT / _FLOAT_ALIGNMENT:
//	The ABI defines alignment requirements of each of the primitive
//	object types.  Some, if not all, may be hardware requirements as
// 	well.  The values are expressed in "byte-alignment" units.
//
// _MAX_ALIGNMENT:
//	The most stringent alignment requirement as specified by the ABI.
//	Equal to the maximum of all the above _XXX_ALIGNMENT values.
//
// _MAX_ALIGNMENT_TYPE:
// 	The name of the C type that has the value descried in _MAX_ALIGNMENT.
//
// _ALIGNMENT_REQUIRED:
//	True or false (1 or 0) whether or not the hardware requires the ABI
//	alignment.
//
// _LONG_LONG_ALIGNMENT_32
//	The 32-bit ABI supported by a 64-bit kernel may have different
//	alignment requirements for primitive object types.  The value of this
//	identifier is expressed in "byte-alignment" units.
//
// _HAVE_CPUID_INSN
//	This indicates that the architecture supports the 'cpuid'
//	instruction as defined by Intel.  (Intel allows other vendors
//	to extend the instruction for their own purposes.)
//
//
// Implementation Choices:
//
// _ILP32 / _LP64:
//	This specifies the compiler data type implementation as specified in
//	the relevant ABI.  The choice between these is strongly influenced
//	by the underlying hardware, but is not absolutely tied to it.
//	Currently only two data type models are supported:
//
//	_ILP32:
//		Int/Long/Pointer are 32 bits.  This is the historical UNIX
//		and Solaris implementation.  Due to its historical standing,
//		this is the default case.
//
//	_LP64:
//		Long/Pointer are 64 bits, Int is 32 bits.  This is the chosen
//		implementation for 64-bit ABIs such as SPARC V9.
//
//	_I32LPx:
//		A compilation environment where 'int' is 32-bit, and
//		longs and pointers are simply the same size.
//
//	In all cases, Char is 8 bits and Short is 16 bits.
//
// _SUNOS_VTOC_8 / _SUNOS_VTOC_16 / _SVR4_VTOC_16:
//	This specifies the form of the disk VTOC (or label):
//
//	_SUNOS_VTOC_8:
//		This is a VTOC form which is upwardly compatible with the
//		SunOS 4.x disk label and allows 8 partitions per disk.
//
//	_SUNOS_VTOC_16:
//		In this format the incore vtoc image matches the ondisk
//		version.  It allows 16 slices per disk, and is not
//		compatible with the SunOS 4.x disk label.
//
//	Note that these are not the only two VTOC forms possible and
//	additional forms may be added.  One possible form would be the
//	SVr4 VTOC form.  The symbol for that is reserved now, although
//	it is not implemented.
//
//	_SVR4_VTOC_16:
//		This VTOC form is compatible with the System V Release 4
//		VTOC (as implemented on the SVr4 Intel and 3b ports) with
//		16 partitions per disk.
//
//
// _DMA_USES_PHYSADDR / _DMA_USES_VIRTADDR
//	This describes the type of addresses used by system DMA:
//
//	_DMA_USES_PHYSADDR:
//		This type of DMA, used in the x86 implementation,
//		requires physical addresses for DMA buffers.  The 24-bit
//		addresses used by some legacy boards is the source of the
//		"low-memory" (<16MB) requirement for some devices using DMA.
//
//	_DMA_USES_VIRTADDR:
//		This method of DMA allows the use of virtual addresses for
//		DMA transfers.
//
// _FIRMWARE_NEEDS_FDISK / _NO_FDISK_PRESENT
//      This indicates the presence/absence of an fdisk table.
//
//      _FIRMWARE_NEEDS_FDISK
//              The fdisk table is required by system firmware.  If present,
//              it allows a disk to be subdivided into multiple fdisk
//              partitions, each of which is equivalent to a separate,
//              virtual disk.  This enables the co-existence of multiple
//              operating systems on a shared hard disk.
//
//      _NO_FDISK_PRESENT
//              If the fdisk table is absent, it is assumed that the entire
//              media is allocated for a single operating system.
//
// _HAVE_TEM_FIRMWARE
//	Defined if this architecture has the (fallback) option of
//	using prom_* calls for doing I/O if a suitable kernel driver
//	is not available to do it.
//
// _DONT_USE_1275_GENERIC_NAMES
//		Controls whether or not device tree node names should
//		comply with the IEEE 1275 "Generic Names" Recommended
//		Practice. With _DONT_USE_GENERIC_NAMES, device-specific
//		names identifying the particular device will be used.
//
// __i386_COMPAT
//	This indicates whether the i386 ABI is supported as a *non-native*
//	mode for the platform.  When this symbol is defined:
//	-	32-bit xstat-style system calls are enabled
//	-	32-bit xmknod-style system calls are enabled
//	-	32-bit system calls use i386 sizes -and- alignments
//
//	Note that this is NOT defined for the i386 native environment!
//
// __x86
//	This is ONLY a synonym for defined(__i386) || defined(__amd64)
//	which is useful only insofar as these two architectures share
//	common attributes.  Analogous to __sparc.
//
// _PSM_MODULES
//	This indicates whether or not the implementation uses PSM
//	modules for processor support, reading /etc/mach from inside
//	the kernel to extract a list.
//
// _RTC_CONFIG
//	This indicates whether or not the implementation uses /etc/rtc_config
//	to configure the real-time clock in the kernel.
//
// _UNIX_KRTLD
//	This indicates that the implementation uses a dynamically
//	linked unix + krtld to form the core kernel image at boot
//	time, or (in the absence of this symbol) a prelinked kernel image.
//
// _OBP
//	This indicates the firmware interface is OBP.
//
// _SOFT_HOSTID
//	This indicates that the implementation obtains the hostid
//	from the file /etc/hostid, rather than from hardware.

// The following set of definitions characterize Solaris on AMD's
// 64-bit systems.

// Define the appropriate "processor characteristics"

// Different alignment constraints for the i386 ABI in compatibility mode

// Define the appropriate "implementation choices".

// The feature test macro __i386 is generic for all processors implementing
// the Intel 386 instruction set or a superset of it.  Specifically, this
// includes all members of the 386, 486, and Pentium family of processors.

// Values of _POSIX_C_SOURCE
//
//		undefined   not a POSIX compilation
//		1	    POSIX.1-1990 compilation
//		2	    POSIX.2-1992 compilation
//		199309L	    POSIX.1b-1993 compilation (Real Time)
//		199506L	    POSIX.1c-1995 compilation (POSIX Threads)
//		200112L	    POSIX.1-2001 compilation (Austin Group Revision)
//		200809L     POSIX.1-2008 compilation

// The feature test macros __XOPEN_OR_POSIX, _STRICT_STDC, _STRICT_SYMBOLS,
// and _STDC_C99 are Sun implementation specific macros created in order to
// compress common standards specified feature test macros for easier reading.
// These macros should not be used by the application developer as
// unexpected results may occur. Instead, the user should reference
// standards(7) for correct usage of the standards feature test macros.
//
// __XOPEN_OR_POSIX     Used in cases where a symbol is defined by both
//                      X/Open or POSIX or in the negative, when neither
//                      X/Open or POSIX defines a symbol.
//
// _STRICT_STDC         __STDC__ is specified by the C Standards and defined
//                      by the compiler. For Sun compilers the value of
//                      __STDC__ is either 1, 0, or not defined based on the
//                      compilation mode (see cc(1)). When the value of
//                      __STDC__ is 1 and in the absence of any other feature
//                      test macros, the namespace available to the application
//                      is limited to only those symbols defined by the C
//                      Standard. _STRICT_STDC provides a more readable means
//                      of identifying symbols defined by the standard, or in
//                      the negative, symbols that are extensions to the C
//                      Standard. See additional comments for GNU C differences.
//
// _STDC_C99            __STDC_VERSION__ is specified by the C standards and
//                      defined by the compiler and indicates the version of
//                      the C standard. A value of 199901L indicates a
//                      compiler that complies with ISO/IEC 9899:1999, other-
//                      wise known as the C99 standard.
//
// _STDC_C11		Like _STDC_C99 except that the value of __STDC_VERSION__
//                      is 201112L indicating a compiler that compiles with
//                      ISO/IEC 9899:2011, otherwise known as the C11 standard.
//
// _STRICT_SYMBOLS	Used in cases where symbol visibility is restricted
//                      by the standards, and the user has not explicitly
//                      relaxed the strictness via __EXTENSIONS__.

// ISO/IEC 9899:1990 and it's revisions, ISO/IEC 9899:1999 and ISO/IEC
// 99899:2011 specify the following predefined macro name:
//
// __STDC__	The integer constant 1, intended to indicate a conforming
//		implementation.
//
// Furthermore, a strictly conforming program shall use only those features
// of the language and library specified in these standards. A conforming
// implementation shall accept any strictly conforming program.
//
// Based on these requirements, Sun's C compiler defines __STDC__ to 1 for
// strictly conforming environments and __STDC__ to 0 for environments that
// use ANSI C semantics but allow extensions to the C standard. For non-ANSI
// C semantics, Sun's C compiler does not define __STDC__.
//
// The GNU C project interpretation is that __STDC__ should always be defined
// to 1 for compilation modes that accept ANSI C syntax regardless of whether
// or not extensions to the C standard are used. Violations of conforming
// behavior are conditionally flagged as warnings via the use of the
// -pedantic option. In addition to defining __STDC__ to 1, the GNU C
// compiler also defines __STRICT_ANSI__ as a means of specifying strictly
// conforming environments using the -ansi or -std=<standard> options.
//
// In the absence of any other compiler options, Sun and GNU set the value
// of __STDC__ as follows when using the following options:
//
//				Value of __STDC__  __STRICT_ANSI__
//
// cc -Xa (default)			0	      undefined
// cc -Xt (transitional)		0             undefined
// cc -Xc (strictly conforming)		1	      undefined
// cc -Xs (K&R C)		    undefined	      undefined
//
// gcc (default)			1	      undefined
// gcc -ansi, -std={c89, c99,...)	1               defined
// gcc -traditional (K&R)	    undefined	      undefined
//
// The default compilation modes for Sun C compilers versus GNU C compilers
// results in a differing value for __STDC__ which results in a more
// restricted namespace when using Sun compilers. To allow both GNU and Sun
// interpretations to peacefully co-exist, we use the following Sun
// implementation _STRICT_STDC_ macro:

// Compiler complies with ISO/IEC 9899:1999 or ISO/IEC 9989:2011

// Use strict symbol visibility.

// This is a variant of _STRICT_SYMBOLS that is meant to cover headers that are
// governed by POSIX, but have not been governed by ISO C. One can go two ways
// on what should happen if an application actively includes (not transitively)
// a header that isn't part of the ISO C spec, we opt to say that if someone has
// gone out of there way then they're doing it for a reason and that is an act
// of non-compliance and therefore it's not up to us to hide away every symbol.
//
// In general, prefer using _STRICT_SYMBOLS, but this is here in particular for
// cases where in the past we have only used a POSIX related check and we don't
// wish to make something stricter. Often applications are relying on the
// ability to, or more realistically unwittingly, have _STRICT_STDC declared and
// still use these interfaces.

// Large file interfaces:
//
//	_LARGEFILE_SOURCE
//		1		large file-related additions to POSIX
//				interfaces requested (fseeko, etc.)
//	_LARGEFILE64_SOURCE
//		1		transitional large-file-related interfaces
//				requested (seek64, stat64, etc.)
//
// The corresponding announcement macros are respectively:
//	_LFS_LARGEFILE
//	_LFS64_LARGEFILE
// (These are set in <unistd.h>.)
//
// Requesting _LARGEFILE64_SOURCE implies requesting _LARGEFILE_SOURCE as
// well.
//
// The large file interfaces are made visible regardless of the initial values
// of the feature test macros under certain circumstances:
//    -	If no explicit standards-conforming environment is requested (neither
//	of _POSIX_SOURCE nor _XOPEN_SOURCE is defined and the value of
//	__STDC__ does not imply standards conformance).
//    -	Extended system interfaces are explicitly requested (__EXTENSIONS__
//	is defined).
//    -	Access to in-kernel interfaces is requested (_KERNEL or _KMEMUSER is
//	defined).  (Note that this dependency is an artifact of the current
//	kernel implementation and may change in future releases.)

// Large file compilation environment control:
//
// The setting of _FILE_OFFSET_BITS controls the size of various file-related
// types and governs the mapping between file-related source function symbol
// names and the corresponding binary entry points.
//
// In the 32-bit environment, the default value is 32; if not set, set it to
// the default here, to simplify tests in other headers.
//
// In the 64-bit compilation environment, the only value allowed is 64.

// Use of _XOPEN_SOURCE
//
// The following X/Open specifications are supported:
//
// X/Open Portability Guide, Issue 3 (XPG3)
// X/Open CAE Specification, Issue 4 (XPG4)
// X/Open CAE Specification, Issue 4, Version 2 (XPG4v2)
// X/Open CAE Specification, Issue 5 (XPG5)
// Open Group Technical Standard, Issue 6 (XPG6), also referred to as
//    IEEE Std. 1003.1-2001 and ISO/IEC 9945:2002.
// Open Group Technical Standard, Issue 7 (XPG7), also referred to as
//    IEEE Std. 1003.1-2008 and ISO/IEC 9945:2009.
//
// XPG4v2 is also referred to as UNIX 95 (SUS or SUSv1).
// XPG5 is also referred to as UNIX 98 or the Single Unix Specification,
//     Version 2 (SUSv2)
// XPG6 is the result of a merge of the X/Open and POSIX specifications
//     and as such is also referred to as IEEE Std. 1003.1-2001 in
//     addition to UNIX 03 and SUSv3.
// XPG7 is also referred to as UNIX 08 and SUSv4.
//
// When writing a conforming X/Open application, as per the specification
// requirements, the appropriate feature test macros must be defined at
// compile time. These are as follows. For more info, see standards(7).
//
// Feature Test Macro				     Specification
// ------------------------------------------------  -------------
// _XOPEN_SOURCE                                         XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4                   XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1           XPG4v2
// _XOPEN_SOURCE = 500                                   XPG5
// _XOPEN_SOURCE = 600  (or POSIX_C_SOURCE=200112L)      XPG6
// _XOPEN_SOURCE = 700  (or POSIX_C_SOURCE=200809L)      XPG7
//
// In order to simplify the guards within the headers, the following
// implementation private test macros have been created. Applications
// must NOT use these private test macros as unexpected results will
// occur.
//
// Note that in general, the use of these private macros is cumulative.
// For example, the use of _XPG3 with no other restrictions on the X/Open
// namespace will make the symbols visible for XPG3 through XPG6
// compilation environments. The use of _XPG4_2 with no other X/Open
// namespace restrictions indicates that the symbols were introduced in
// XPG4v2 and are therefore visible for XPG4v2 through XPG6 compilation
// environments, but not for XPG3 or XPG4 compilation environments.
//
// _XPG3    X/Open Portability Guide, Issue 3 (XPG3)
// _XPG4    X/Open CAE Specification, Issue 4 (XPG4)
// _XPG4_2  X/Open CAE Specification, Issue 4, Version 2 (XPG4v2/UNIX 95/SUS)
// _XPG5    X/Open CAE Specification, Issue 5 (XPG5/UNIX 98/SUSv2)
// _XPG6    Open Group Technical Standard, Issue 6 (XPG6/UNIX 03/SUSv3)
// _XPG7    Open Group Technical Standard, Issue 7 (XPG7/UNIX 08/SUSv4)

// X/Open Portability Guide, Issue 3

// _XOPEN_VERSION is defined by the X/Open specifications and is not
// normally defined by the application, except in the case of an XPG4
// application.  On the implementation side, _XOPEN_VERSION defined with
// the value of 3 indicates an XPG3 application. _XOPEN_VERSION defined
// with the value of 4 indicates an XPG4 or XPG4v2 (UNIX 95) application.
// _XOPEN_VERSION  defined with a value of 500 indicates an XPG5 (UNIX 98)
// application and with a value of 600 indicates an XPG6 (UNIX 03)
// application and with a value of 700 indicates an XPG7 (UNIX 08).
// The appropriate version is determined by the use of the
// feature test macros described earlier.  The value of _XOPEN_VERSION
// defaults to 3 otherwise indicating support for XPG3 applications.

// ANSI C and ISO 9899:1990 say the type long long doesn't exist in strictly
// conforming environments.  ISO 9899:1999 says it does.
//
// The presence of _LONGLONG_TYPE says "long long exists" which is therefore
// defined in all but strictly conforming environments that disallow it.

// The following macro defines a value for the ISO C99 restrict
// keyword so that _RESTRICT_KYWD resolves to "restrict" if
// an ISO C99 compiler is used, "__restrict" for c++ and "" (null string)
// if any other compiler is used. This allows for the use of single
// prototype declarations regardless of compiler version.

// The following macro defines a value for the ISO C11 _Noreturn
// keyword so that _NORETURN_KYWD resolves to "_Noreturn" if
// an ISO C11 compiler is used and "" (null string) if any other
// compiler is used. This allows for the use of single prototype
// declarations regardless of compiler version.

// ISO/IEC 9899:2011 Annex K

// The following macro indicates header support for the ANSI C++
// standard.  The ISO/IEC designation for this is ISO/IEC FDIS 14882.

// The following macro indicates header support for the C99 standard,
// ISO/IEC 9899:1999, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011 Annex K, Programming Languages - C.

// The following macro indicates header support for DTrace. The value is an
// integer that corresponds to the major version number for DTrace.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// Machine dependent definitions moved to <sys/machtypes.h>.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Machine dependent types:
//
//	intel ia32 Version

type X_label_t = struct{ Fval [8]int64 } /* machtypes.h:59:9 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 EveryCity Ltd. All rights reserved.
// Copyright 2019 Joyent, Inc.

// This file contains definitions designed to enable different compilers
// to be used harmoniously on Solaris systems.

// Allow for version tests for compiler bugs and features.

// analogous to lint's PRINTFLIKEn

// Handle the kernel printf routines that can take '%b' too

// This one's pretty obvious -- the function never returns

// The function is 'extern inline' and expects GNU C89 behaviour, not C99
// behaviour.
//
// Should only be used on 'extern inline' definitions for GCC.

// The function has control flow such that it may return multiple times (in
// the manner of setjmp or vfork)

// This is an appropriate label for functions that do not
// modify their arguments, e.g. strlen()

// This is a stronger form of __pure__. Can be used for functions
// that do not modify their arguments and don't depend on global
// memory.

// This attribute, attached to a variable, means that the variable is meant to
// be possibly unused. GCC will not produce a warning for this variable.

// Shorthand versions for readability

// In release build, disable warnings about variables
// which are used only for debugging.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// This header file serves to group a set of well known defines and to
// set these for each instruction set architecture.  These defines may
// be divided into two groups;  characteristics of the processor and
// implementation choices for Solaris on a processor.
//
// Processor Characteristics:
//
// _LITTLE_ENDIAN / _BIG_ENDIAN:
//	The natural byte order of the processor.  A pointer to an int points
//	to the least/most significant byte of that int.
//
// _STACK_GROWS_UPWARD / _STACK_GROWS_DOWNWARD:
//	The processor specific direction of stack growth.  A push onto the
//	stack increases/decreases the stack pointer, so it stores data at
//	successively higher/lower addresses.  (Stackless machines ignored
//	without regrets).
//
// _LONG_LONG_HTOL / _LONG_LONG_LTOH:
//	A pointer to a long long points to the most/least significant long
//	within that long long.
//
// _BIT_FIELDS_HTOL / _BIT_FIELDS_LTOH:
//	The C compiler assigns bit fields from the high/low to the low/high end
//	of an int (most to least significant vs. least to most significant).
//
// _IEEE_754:
//	The processor (or supported implementations of the processor)
//	supports the ieee-754 floating point standard.  No other floating
//	point standards are supported (or significant).  Any other supported
//	floating point formats are expected to be cased on the ISA processor
//	symbol.
//
// _CHAR_IS_UNSIGNED / _CHAR_IS_SIGNED:
//	The C Compiler implements objects of type `char' as `unsigned' or
//	`signed' respectively.  This is really an implementation choice of
//	the compiler writer, but it is specified in the ABI and tends to
//	be uniform across compilers for an instruction set architecture.
//	Hence, it has the properties of a processor characteristic.
//
// _CHAR_ALIGNMENT / _SHORT_ALIGNMENT / _INT_ALIGNMENT / _LONG_ALIGNMENT /
// _LONG_LONG_ALIGNMENT / _DOUBLE_ALIGNMENT / _LONG_DOUBLE_ALIGNMENT /
// _POINTER_ALIGNMENT / _FLOAT_ALIGNMENT:
//	The ABI defines alignment requirements of each of the primitive
//	object types.  Some, if not all, may be hardware requirements as
// 	well.  The values are expressed in "byte-alignment" units.
//
// _MAX_ALIGNMENT:
//	The most stringent alignment requirement as specified by the ABI.
//	Equal to the maximum of all the above _XXX_ALIGNMENT values.
//
// _MAX_ALIGNMENT_TYPE:
// 	The name of the C type that has the value descried in _MAX_ALIGNMENT.
//
// _ALIGNMENT_REQUIRED:
//	True or false (1 or 0) whether or not the hardware requires the ABI
//	alignment.
//
// _LONG_LONG_ALIGNMENT_32
//	The 32-bit ABI supported by a 64-bit kernel may have different
//	alignment requirements for primitive object types.  The value of this
//	identifier is expressed in "byte-alignment" units.
//
// _HAVE_CPUID_INSN
//	This indicates that the architecture supports the 'cpuid'
//	instruction as defined by Intel.  (Intel allows other vendors
//	to extend the instruction for their own purposes.)
//
//
// Implementation Choices:
//
// _ILP32 / _LP64:
//	This specifies the compiler data type implementation as specified in
//	the relevant ABI.  The choice between these is strongly influenced
//	by the underlying hardware, but is not absolutely tied to it.
//	Currently only two data type models are supported:
//
//	_ILP32:
//		Int/Long/Pointer are 32 bits.  This is the historical UNIX
//		and Solaris implementation.  Due to its historical standing,
//		this is the default case.
//
//	_LP64:
//		Long/Pointer are 64 bits, Int is 32 bits.  This is the chosen
//		implementation for 64-bit ABIs such as SPARC V9.
//
//	_I32LPx:
//		A compilation environment where 'int' is 32-bit, and
//		longs and pointers are simply the same size.
//
//	In all cases, Char is 8 bits and Short is 16 bits.
//
// _SUNOS_VTOC_8 / _SUNOS_VTOC_16 / _SVR4_VTOC_16:
//	This specifies the form of the disk VTOC (or label):
//
//	_SUNOS_VTOC_8:
//		This is a VTOC form which is upwardly compatible with the
//		SunOS 4.x disk label and allows 8 partitions per disk.
//
//	_SUNOS_VTOC_16:
//		In this format the incore vtoc image matches the ondisk
//		version.  It allows 16 slices per disk, and is not
//		compatible with the SunOS 4.x disk label.
//
//	Note that these are not the only two VTOC forms possible and
//	additional forms may be added.  One possible form would be the
//	SVr4 VTOC form.  The symbol for that is reserved now, although
//	it is not implemented.
//
//	_SVR4_VTOC_16:
//		This VTOC form is compatible with the System V Release 4
//		VTOC (as implemented on the SVr4 Intel and 3b ports) with
//		16 partitions per disk.
//
//
// _DMA_USES_PHYSADDR / _DMA_USES_VIRTADDR
//	This describes the type of addresses used by system DMA:
//
//	_DMA_USES_PHYSADDR:
//		This type of DMA, used in the x86 implementation,
//		requires physical addresses for DMA buffers.  The 24-bit
//		addresses used by some legacy boards is the source of the
//		"low-memory" (<16MB) requirement for some devices using DMA.
//
//	_DMA_USES_VIRTADDR:
//		This method of DMA allows the use of virtual addresses for
//		DMA transfers.
//
// _FIRMWARE_NEEDS_FDISK / _NO_FDISK_PRESENT
//      This indicates the presence/absence of an fdisk table.
//
//      _FIRMWARE_NEEDS_FDISK
//              The fdisk table is required by system firmware.  If present,
//              it allows a disk to be subdivided into multiple fdisk
//              partitions, each of which is equivalent to a separate,
//              virtual disk.  This enables the co-existence of multiple
//              operating systems on a shared hard disk.
//
//      _NO_FDISK_PRESENT
//              If the fdisk table is absent, it is assumed that the entire
//              media is allocated for a single operating system.
//
// _HAVE_TEM_FIRMWARE
//	Defined if this architecture has the (fallback) option of
//	using prom_* calls for doing I/O if a suitable kernel driver
//	is not available to do it.
//
// _DONT_USE_1275_GENERIC_NAMES
//		Controls whether or not device tree node names should
//		comply with the IEEE 1275 "Generic Names" Recommended
//		Practice. With _DONT_USE_GENERIC_NAMES, device-specific
//		names identifying the particular device will be used.
//
// __i386_COMPAT
//	This indicates whether the i386 ABI is supported as a *non-native*
//	mode for the platform.  When this symbol is defined:
//	-	32-bit xstat-style system calls are enabled
//	-	32-bit xmknod-style system calls are enabled
//	-	32-bit system calls use i386 sizes -and- alignments
//
//	Note that this is NOT defined for the i386 native environment!
//
// __x86
//	This is ONLY a synonym for defined(__i386) || defined(__amd64)
//	which is useful only insofar as these two architectures share
//	common attributes.  Analogous to __sparc.
//
// _PSM_MODULES
//	This indicates whether or not the implementation uses PSM
//	modules for processor support, reading /etc/mach from inside
//	the kernel to extract a list.
//
// _RTC_CONFIG
//	This indicates whether or not the implementation uses /etc/rtc_config
//	to configure the real-time clock in the kernel.
//
// _UNIX_KRTLD
//	This indicates that the implementation uses a dynamically
//	linked unix + krtld to form the core kernel image at boot
//	time, or (in the absence of this symbol) a prelinked kernel image.
//
// _OBP
//	This indicates the firmware interface is OBP.
//
// _SOFT_HOSTID
//	This indicates that the implementation obtains the hostid
//	from the file /etc/hostid, rather than from hardware.

// The following set of definitions characterize Solaris on AMD's
// 64-bit systems.

// Define the appropriate "processor characteristics"

// Different alignment constraints for the i386 ABI in compatibility mode

// Define the appropriate "implementation choices".

// The feature test macro __i386 is generic for all processors implementing
// the Intel 386 instruction set or a superset of it.  Specifically, this
// includes all members of the 386, 486, and Pentium family of processors.

// Values of _POSIX_C_SOURCE
//
//		undefined   not a POSIX compilation
//		1	    POSIX.1-1990 compilation
//		2	    POSIX.2-1992 compilation
//		199309L	    POSIX.1b-1993 compilation (Real Time)
//		199506L	    POSIX.1c-1995 compilation (POSIX Threads)
//		200112L	    POSIX.1-2001 compilation (Austin Group Revision)
//		200809L     POSIX.1-2008 compilation

// The feature test macros __XOPEN_OR_POSIX, _STRICT_STDC, _STRICT_SYMBOLS,
// and _STDC_C99 are Sun implementation specific macros created in order to
// compress common standards specified feature test macros for easier reading.
// These macros should not be used by the application developer as
// unexpected results may occur. Instead, the user should reference
// standards(7) for correct usage of the standards feature test macros.
//
// __XOPEN_OR_POSIX     Used in cases where a symbol is defined by both
//                      X/Open or POSIX or in the negative, when neither
//                      X/Open or POSIX defines a symbol.
//
// _STRICT_STDC         __STDC__ is specified by the C Standards and defined
//                      by the compiler. For Sun compilers the value of
//                      __STDC__ is either 1, 0, or not defined based on the
//                      compilation mode (see cc(1)). When the value of
//                      __STDC__ is 1 and in the absence of any other feature
//                      test macros, the namespace available to the application
//                      is limited to only those symbols defined by the C
//                      Standard. _STRICT_STDC provides a more readable means
//                      of identifying symbols defined by the standard, or in
//                      the negative, symbols that are extensions to the C
//                      Standard. See additional comments for GNU C differences.
//
// _STDC_C99            __STDC_VERSION__ is specified by the C standards and
//                      defined by the compiler and indicates the version of
//                      the C standard. A value of 199901L indicates a
//                      compiler that complies with ISO/IEC 9899:1999, other-
//                      wise known as the C99 standard.
//
// _STDC_C11		Like _STDC_C99 except that the value of __STDC_VERSION__
//                      is 201112L indicating a compiler that compiles with
//                      ISO/IEC 9899:2011, otherwise known as the C11 standard.
//
// _STRICT_SYMBOLS	Used in cases where symbol visibility is restricted
//                      by the standards, and the user has not explicitly
//                      relaxed the strictness via __EXTENSIONS__.

// ISO/IEC 9899:1990 and it's revisions, ISO/IEC 9899:1999 and ISO/IEC
// 99899:2011 specify the following predefined macro name:
//
// __STDC__	The integer constant 1, intended to indicate a conforming
//		implementation.
//
// Furthermore, a strictly conforming program shall use only those features
// of the language and library specified in these standards. A conforming
// implementation shall accept any strictly conforming program.
//
// Based on these requirements, Sun's C compiler defines __STDC__ to 1 for
// strictly conforming environments and __STDC__ to 0 for environments that
// use ANSI C semantics but allow extensions to the C standard. For non-ANSI
// C semantics, Sun's C compiler does not define __STDC__.
//
// The GNU C project interpretation is that __STDC__ should always be defined
// to 1 for compilation modes that accept ANSI C syntax regardless of whether
// or not extensions to the C standard are used. Violations of conforming
// behavior are conditionally flagged as warnings via the use of the
// -pedantic option. In addition to defining __STDC__ to 1, the GNU C
// compiler also defines __STRICT_ANSI__ as a means of specifying strictly
// conforming environments using the -ansi or -std=<standard> options.
//
// In the absence of any other compiler options, Sun and GNU set the value
// of __STDC__ as follows when using the following options:
//
//				Value of __STDC__  __STRICT_ANSI__
//
// cc -Xa (default)			0	      undefined
// cc -Xt (transitional)		0             undefined
// cc -Xc (strictly conforming)		1	      undefined
// cc -Xs (K&R C)		    undefined	      undefined
//
// gcc (default)			1	      undefined
// gcc -ansi, -std={c89, c99,...)	1               defined
// gcc -traditional (K&R)	    undefined	      undefined
//
// The default compilation modes for Sun C compilers versus GNU C compilers
// results in a differing value for __STDC__ which results in a more
// restricted namespace when using Sun compilers. To allow both GNU and Sun
// interpretations to peacefully co-exist, we use the following Sun
// implementation _STRICT_STDC_ macro:

// Compiler complies with ISO/IEC 9899:1999 or ISO/IEC 9989:2011

// Use strict symbol visibility.

// This is a variant of _STRICT_SYMBOLS that is meant to cover headers that are
// governed by POSIX, but have not been governed by ISO C. One can go two ways
// on what should happen if an application actively includes (not transitively)
// a header that isn't part of the ISO C spec, we opt to say that if someone has
// gone out of there way then they're doing it for a reason and that is an act
// of non-compliance and therefore it's not up to us to hide away every symbol.
//
// In general, prefer using _STRICT_SYMBOLS, but this is here in particular for
// cases where in the past we have only used a POSIX related check and we don't
// wish to make something stricter. Often applications are relying on the
// ability to, or more realistically unwittingly, have _STRICT_STDC declared and
// still use these interfaces.

// Large file interfaces:
//
//	_LARGEFILE_SOURCE
//		1		large file-related additions to POSIX
//				interfaces requested (fseeko, etc.)
//	_LARGEFILE64_SOURCE
//		1		transitional large-file-related interfaces
//				requested (seek64, stat64, etc.)
//
// The corresponding announcement macros are respectively:
//	_LFS_LARGEFILE
//	_LFS64_LARGEFILE
// (These are set in <unistd.h>.)
//
// Requesting _LARGEFILE64_SOURCE implies requesting _LARGEFILE_SOURCE as
// well.
//
// The large file interfaces are made visible regardless of the initial values
// of the feature test macros under certain circumstances:
//    -	If no explicit standards-conforming environment is requested (neither
//	of _POSIX_SOURCE nor _XOPEN_SOURCE is defined and the value of
//	__STDC__ does not imply standards conformance).
//    -	Extended system interfaces are explicitly requested (__EXTENSIONS__
//	is defined).
//    -	Access to in-kernel interfaces is requested (_KERNEL or _KMEMUSER is
//	defined).  (Note that this dependency is an artifact of the current
//	kernel implementation and may change in future releases.)

// Large file compilation environment control:
//
// The setting of _FILE_OFFSET_BITS controls the size of various file-related
// types and governs the mapping between file-related source function symbol
// names and the corresponding binary entry points.
//
// In the 32-bit environment, the default value is 32; if not set, set it to
// the default here, to simplify tests in other headers.
//
// In the 64-bit compilation environment, the only value allowed is 64.

// Use of _XOPEN_SOURCE
//
// The following X/Open specifications are supported:
//
// X/Open Portability Guide, Issue 3 (XPG3)
// X/Open CAE Specification, Issue 4 (XPG4)
// X/Open CAE Specification, Issue 4, Version 2 (XPG4v2)
// X/Open CAE Specification, Issue 5 (XPG5)
// Open Group Technical Standard, Issue 6 (XPG6), also referred to as
//    IEEE Std. 1003.1-2001 and ISO/IEC 9945:2002.
// Open Group Technical Standard, Issue 7 (XPG7), also referred to as
//    IEEE Std. 1003.1-2008 and ISO/IEC 9945:2009.
//
// XPG4v2 is also referred to as UNIX 95 (SUS or SUSv1).
// XPG5 is also referred to as UNIX 98 or the Single Unix Specification,
//     Version 2 (SUSv2)
// XPG6 is the result of a merge of the X/Open and POSIX specifications
//     and as such is also referred to as IEEE Std. 1003.1-2001 in
//     addition to UNIX 03 and SUSv3.
// XPG7 is also referred to as UNIX 08 and SUSv4.
//
// When writing a conforming X/Open application, as per the specification
// requirements, the appropriate feature test macros must be defined at
// compile time. These are as follows. For more info, see standards(7).
//
// Feature Test Macro				     Specification
// ------------------------------------------------  -------------
// _XOPEN_SOURCE                                         XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4                   XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1           XPG4v2
// _XOPEN_SOURCE = 500                                   XPG5
// _XOPEN_SOURCE = 600  (or POSIX_C_SOURCE=200112L)      XPG6
// _XOPEN_SOURCE = 700  (or POSIX_C_SOURCE=200809L)      XPG7
//
// In order to simplify the guards within the headers, the following
// implementation private test macros have been created. Applications
// must NOT use these private test macros as unexpected results will
// occur.
//
// Note that in general, the use of these private macros is cumulative.
// For example, the use of _XPG3 with no other restrictions on the X/Open
// namespace will make the symbols visible for XPG3 through XPG6
// compilation environments. The use of _XPG4_2 with no other X/Open
// namespace restrictions indicates that the symbols were introduced in
// XPG4v2 and are therefore visible for XPG4v2 through XPG6 compilation
// environments, but not for XPG3 or XPG4 compilation environments.
//
// _XPG3    X/Open Portability Guide, Issue 3 (XPG3)
// _XPG4    X/Open CAE Specification, Issue 4 (XPG4)
// _XPG4_2  X/Open CAE Specification, Issue 4, Version 2 (XPG4v2/UNIX 95/SUS)
// _XPG5    X/Open CAE Specification, Issue 5 (XPG5/UNIX 98/SUSv2)
// _XPG6    Open Group Technical Standard, Issue 6 (XPG6/UNIX 03/SUSv3)
// _XPG7    Open Group Technical Standard, Issue 7 (XPG7/UNIX 08/SUSv4)

// X/Open Portability Guide, Issue 3

// _XOPEN_VERSION is defined by the X/Open specifications and is not
// normally defined by the application, except in the case of an XPG4
// application.  On the implementation side, _XOPEN_VERSION defined with
// the value of 3 indicates an XPG3 application. _XOPEN_VERSION defined
// with the value of 4 indicates an XPG4 or XPG4v2 (UNIX 95) application.
// _XOPEN_VERSION  defined with a value of 500 indicates an XPG5 (UNIX 98)
// application and with a value of 600 indicates an XPG6 (UNIX 03)
// application and with a value of 700 indicates an XPG7 (UNIX 08).
// The appropriate version is determined by the use of the
// feature test macros described earlier.  The value of _XOPEN_VERSION
// defaults to 3 otherwise indicating support for XPG3 applications.

// ANSI C and ISO 9899:1990 say the type long long doesn't exist in strictly
// conforming environments.  ISO 9899:1999 says it does.
//
// The presence of _LONGLONG_TYPE says "long long exists" which is therefore
// defined in all but strictly conforming environments that disallow it.

// The following macro defines a value for the ISO C99 restrict
// keyword so that _RESTRICT_KYWD resolves to "restrict" if
// an ISO C99 compiler is used, "__restrict" for c++ and "" (null string)
// if any other compiler is used. This allows for the use of single
// prototype declarations regardless of compiler version.

// The following macro defines a value for the ISO C11 _Noreturn
// keyword so that _NORETURN_KYWD resolves to "_Noreturn" if
// an ISO C11 compiler is used and "" (null string) if any other
// compiler is used. This allows for the use of single prototype
// declarations regardless of compiler version.

// ISO/IEC 9899:2011 Annex K

// The following macro indicates header support for the ANSI C++
// standard.  The ISO/IEC designation for this is ISO/IEC FDIS 14882.

// The following macro indicates header support for the C99 standard,
// ISO/IEC 9899:1999, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011 Annex K, Programming Languages - C.

// The following macro indicates header support for DTrace. The value is an
// integer that corresponds to the major version number for DTrace.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// Machine dependent definitions moved to <sys/machtypes.h>.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Machine dependent types:
//
//	intel ia32 Version

type Label_t = X_label_t /* machtypes.h:59:54 */

type Lock_t = uint8 /* machtypes.h:63:23 */ // lock work for busy wait

// Include fixed width type declarations proposed by the ISO/JTC1/SC22/WG14 C
// committee's working draft for the revision of the current ISO C standard,
// ISO/IEC 9899:1990 Programming language - C.  These are not currently
// required by any standard but constitute a useful, general purpose set
// of type definitions which is namespace clean with respect to all standards.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_types.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> defined in the ISO C standard, ISO/IEC 9899:1999
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<sys/types.h>		Provides only the "_t" types defined in this
//				file which is a subset of the contents of
//				<inttypes.h>.  (This can be appropriate for
//				all programs/modules except those claiming
//				ANSI-C conformance.)
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Basic / Extended integer types
//
// The following defines the basic fixed-size integer types.
//
// Implementations are free to typedef them to Standard C integer types or
// extensions that they support. If an implementation does not support one
// of the particular integer data types below, then it should not define the
// typedefs and macros corresponding to that data type.  Note that int8_t
// is not defined in -Xs mode on ISAs for which the ABI specifies "char"
// as an unsigned entity because there is no way to define an eight bit
// signed integral.
type Int8_t = int8   /* int_types.h:75:16 */
type Int16_t = int16 /* int_types.h:79:17 */
type Int32_t = int32 /* int_types.h:80:15 */
type Int64_t = int64 /* int_types.h:83:16 */

type Uint8_t = uint8   /* int_types.h:91:24 */
type Uint16_t = uint16 /* int_types.h:92:25 */
type Uint32_t = uint32 /* int_types.h:93:23 */
type Uint64_t = uint64 /* int_types.h:95:24 */

// intmax_t and uintmax_t are to be the longest (in number of bits) signed
// and unsigned integer types supported by the implementation.
type Intmax_t = int64   /* int_types.h:107:19 */
type Uintmax_t = uint64 /* int_types.h:108:19 */

// intptr_t and uintptr_t are signed and unsigned integer types large enough
// to hold any data pointer; that is, data pointers can be assigned into or
// from these integer types without losing precision.
type Intptr_t = int64   /* int_types.h:120:16 */
type Uintptr_t = uint64 /* int_types.h:121:24 */

// The following define the fastest integer types that can hold the
// specified number of bits.
type Int_fast8_t = int8   /* int_types.h:132:16 */
type Int_fast16_t = int32 /* int_types.h:136:15 */
type Int_fast32_t = int32 /* int_types.h:137:15 */
type Int_fast64_t = int64 /* int_types.h:139:16 */

type Uint_fast8_t = uint8   /* int_types.h:146:24 */
type Uint_fast16_t = uint32 /* int_types.h:147:23 */
type Uint_fast32_t = uint32 /* int_types.h:148:23 */
type Uint_fast64_t = uint64 /* int_types.h:150:24 */

// The following define the smallest integer types that can hold the
// specified number of bits.
type Int_least8_t = int8   /* int_types.h:162:16 */
type Int_least16_t = int16 /* int_types.h:166:17 */
type Int_least32_t = int32 /* int_types.h:167:15 */
type Int_least64_t = int64 /* int_types.h:169:16 */

// If these are changed, please update char16_t and char32_t in head/uchar.h.
type Uint_least8_t = uint8   /* int_types.h:179:24 */
type Uint_least16_t = uint16 /* int_types.h:180:25 */
type Uint_least32_t = uint32 /* int_types.h:181:23 */
type Uint_least64_t = uint64 /* int_types.h:183:24 */

// Strictly conforming ANSI C environments prior to the 1999
// revision of the C Standard (ISO/IEC 9899:1999) do not have
// the long long data type.
type Longlong_t = int64    /* types.h:72:20 */
type U_longlong_t = uint64 /* types.h:73:28 */

// These types (t_{u}scalar_t) exist because the XTI/TPI/DLPI standards had
// to use them instead of int32_t and uint32_t because DEC had
// shipped 64-bit wide.
type T_scalar_t = int32   /* types.h:92:18 */
type T_uscalar_t = uint32 /* types.h:93:18 */

// POSIX Extensions
type Uchar_t = uint8   /* types.h:102:23 */
type Ushort_t = uint16 /* types.h:103:24 */
type Uint_t = uint32   /* types.h:104:22 */
type Ulong_t = uint64  /* types.h:105:23 */

type Caddr_t = uintptr /* types.h:107:15 */ // ?<core address> type
type Daddr_t = int64   /* types.h:108:15 */ // <disk address> type
type Cnt_t = int16     /* types.h:109:16 */ // pointer difference

// VM-related types
type Pfn_t = uint64   /* types.h:123:18 */ // page frame number
type Pgcnt_t = uint64 /* types.h:124:18 */ // number of pages
type Spgcnt_t = int64 /* types.h:125:15 */ // signed number of pages

type Use_t = uint8          /* types.h:127:18 */ // use count for swap.
type Sysid_t = int16        /* types.h:128:16 */
type Index_t = int16        /* types.h:129:16 */
type Timeout_id_t = uintptr /* types.h:130:15 */ // opaque handle from timeout(9F)
type Bufcall_id_t = uintptr /* types.h:131:15 */ // opaque handle from bufcall(9F)

// The size of off_t and related types depends on the setting of
// _FILE_OFFSET_BITS.  (Note that other system headers define other types
// related to those defined here.)
//
// If _LARGEFILE64_SOURCE is defined, variants of these types that are
// explicitly 64 bits wide become available.

type Off_t = int64 /* types.h:145:15 */ // offsets within files

type Off64_t = int64 /* types.h:152:16 */ // offsets within files

type Ino_t = uint64      /* types.h:161:18 */ // expanded inode type
type Blkcnt_t = int64    /* types.h:162:15 */ // count of file blocks
type Fsblkcnt_t = uint64 /* types.h:163:18 */ // count of file system blocks
type Fsfilcnt_t = uint64 /* types.h:164:18 */ // count of files

type Ino64_t = uint64      /* types.h:174:16 */ // expanded inode type
type Blkcnt64_t = int64    /* types.h:175:18 */ // count of file blocks
type Fsblkcnt64_t = uint64 /* types.h:176:20 */ // count of file system blocks
type Fsfilcnt64_t = uint64 /* types.h:177:20 */ // count of files

type Blksize_t = int32 /* types.h:187:14 */ // used for block sizes

// The boolean_t type has had a varied amount of exposure over the years in
// terms of how its enumeration constants have been exposed. In particular, it
// originally used the __XOPEN_OR_POSIX macro to determine whether to prefix the
// B_TRUE and B_FALSE with an underscore. This check never included the
// question of if we were in a strict ANSI C environment or whether extensions
// were defined.
//
// Compilers such as clang started defaulting to always including an
// XOPEN_SOURCE declaration on behalf of users, but also noted __EXTENSIONS__.
// This would lead most software that had used the non-underscore versions to
// need it. As such, we have adjusted the non-strict XOPEN environment to retain
// its old behavior so as to minimize namespace pollution; however, we instead
// include both variants of the definitions in the generally visible version
// allowing software written in either world to hopefully end up in a good
// place.
//
// This isn't perfect, but should hopefully minimize the pain for folks actually
// trying to build software.
type Boolean_t = uint32 /* types.h:215:69 */

// The {u,}pad64_t types can be used in structures such that those structures
// may be accessed by code produced by compilation environments which don't
// support a 64 bit integral datatype.  The intention is not to allow
// use of these fields in such environments, but to maintain the alignment
// and offsets of the structure.
//
// Similar comments for {u,}pad128_t.
//
// Note that these types do NOT generate any stronger alignment constraints
// than those available in the underlying ABI.  See <sys/isa_defs.h>
type Pad64_t = int64   /* types.h:240:18 */
type Upad64_t = uint64 /* types.h:241:18 */

type Pad128_t = struct {
	F_q          float64
	F__ccgo_pad1 [8]byte
} /* types.h:257:3 */

type Upad128_t = struct {
	F_q          float64
	F__ccgo_pad1 [8]byte
} /* types.h:262:3 */

type Offset_t = int64    /* types.h:264:20 */
type U_offset_t = uint64 /* types.h:265:22 */
type Len_t = uint64      /* types.h:266:22 */
type Diskaddr_t = uint64 /* types.h:267:22 */

// Definitions remaining from previous partial support for 64-bit file
// offsets.  This partial support for devices greater than 2gb requires
// compiler support for long long.
type Lloff_t = struct{ F_f int64 } /* types.h:284:3 */

type Lldaddr_t = struct{ F_f int64 } /* types.h:304:3 */

type K_fltset_t = uint32 /* types.h:317:16 */ // kernel fault set type

// The following type is for various kinds of identifiers.  The
// actual type must be the same for all since some system calls
// (such as sigsend) take arguments that may be any of these
// types.  The enumeration type idtype_t defined in sys/procset.h
// is used to indicate what type of id is being specified --
// a process id, process group id, session id, scheduling class id,
// user id, group id, project id, task id or zone id.
type Id_t = int32 /* types.h:329:14 */

type Lgrp_id_t = int32 /* types.h:334:15 */ // lgroup ID

// Type useconds_t is an unsigned integral type capable of storing
// values at least in the range of zero to 1,000,000.
type Useconds_t = uint32 /* types.h:340:17 */ // Time, in microseconds

type Suseconds_t = int64 /* types.h:344:14 */ // signed # of microseconds

// Typedefs for dev_t components.
type Major_t = uint32 /* types.h:351:16 */ // major part of device number
type Minor_t = uint32 /* types.h:352:16 */ // minor part of device number

// The data type of a thread priority.
type Pri_t = int16 /* types.h:361:15 */

// The data type for a CPU flags field.  (Can be extended to larger unsigned
// types, if needed, limited by ability to update atomically.)
type Cpu_flag_t = uint16 /* types.h:367:18 */

// For compatibility reasons the following typedefs (prefixed o_)
// can't grow regardless of the EFT definition. Although,
// applications should not explicitly use these typedefs
// they may be included via a system header definition.
// WARNING: These typedefs may be removed in a future
// release.
//
//	ex. the definitions in s5inode.h (now obsoleted)
//		remained small to preserve compatibility
//		in the S5 file system type.
type O_mode_t = uint16 /* types.h:380:18 */ // old file attribute type
type O_dev_t = int16   /* types.h:381:15 */ // old device type
type O_uid_t = uint16  /* types.h:382:18 */ // old UID type
type O_gid_t = uint16  /* types.h:383:17 */ // old GID type
type O_nlink_t = int16 /* types.h:384:15 */ // old file link type
type O_pid_t = int16   /* types.h:385:15 */ // old process id type
type O_ino_t = uint16  /* types.h:386:18 */ // old inode type

// POSIX and XOPEN Declarations
type Key_t = int32   /* types.h:392:13 */ // IPC key type
type Mode_t = uint32 /* types.h:394:16 */ // file attribute type

type Uid_t = uint32 /* types.h:401:22 */ // UID type

type Gid_t = uint32 /* types.h:404:15 */ // GID type

type Datalink_id_t = uint32 /* types.h:406:18 */
type Vrid_t = uint32        /* types.h:407:18 */

type Taskid_t = int32 /* types.h:409:17 */
type Projid_t = int32 /* types.h:410:17 */
type Poolid_t = int32 /* types.h:411:14 */
type Zoneid_t = int32 /* types.h:412:14 */
type Ctid_t = int32   /* types.h:413:14 */

// POSIX definitions are same as defined in thread.h and synch.h.
// Any changes made to here should be reflected in corresponding
// files as described in comments.
type Pthread_t = uint32     /* types.h:420:16 */ // = thread_t in thread.h
type Pthread_key_t = uint32 /* types.h:421:16 */ // = thread_key_t in thread.h

// "Magic numbers" tagging synchronization object types

type X_pthread_mutex = struct {
	F__pthread_mutex_flags struct {
		F__pthread_mutex_flag1   uint16
		F__pthread_mutex_flag2   uint8
		F__pthread_mutex_ceiling uint8
		F__pthread_mutex_type    uint16
		F__pthread_mutex_magic   uint16
	}
	F__pthread_mutex_lock struct {
		F__ccgo_pad1            [0]uint64
		F__pthread_mutex_lock64 struct{ F__pthread_mutex_pad [8]uint8 }
	}
	F__pthread_mutex_data uint64
} /* types.h:429:9 */

// = thread_key_t in thread.h

// "Magic numbers" tagging synchronization object types

type Pthread_mutex_t = X_pthread_mutex /* types.h:448:3 */

type X_pthread_cond = struct {
	F__pthread_cond_flags struct {
		F__pthread_cond_flag  [4]uint8
		F__pthread_cond_type  uint16
		F__pthread_cond_magic uint16
	}
	F__pthread_cond_data uint64
} /* types.h:450:9 */

type Pthread_cond_t = X_pthread_cond /* types.h:457:3 */

// UNIX 98 Extension
type X_pthread_rwlock = struct {
	F__pthread_rwlock_readers  int32
	F__pthread_rwlock_type     uint16
	F__pthread_rwlock_magic    uint16
	F__pthread_rwlock_mutex    Pthread_mutex_t
	F__pthread_rwlock_readercv Pthread_cond_t
	F__pthread_rwlock_writercv Pthread_cond_t
} /* types.h:462:9 */

// UNIX 98 Extension
type Pthread_rwlock_t = X_pthread_rwlock /* types.h:469:3 */

// SUSV3
type Pthread_barrier_t = struct {
	F__pthread_barrier_count    uint32
	F__pthread_barrier_current  uint32
	F__pthread_barrier_cycle    uint64
	F__pthread_barrier_reserved uint64
	F__pthread_barrier_lock     Pthread_mutex_t
	F__pthread_barrier_cond     Pthread_cond_t
} /* types.h:481:3 */

type Pthread_spinlock_t = Pthread_mutex_t /* types.h:483:25 */

// attributes for threads, dynamically allocated by library
type X_pthread_attr = struct{ F__pthread_attrp uintptr } /* types.h:488:9 */

// attributes for threads, dynamically allocated by library
type Pthread_attr_t = X_pthread_attr /* types.h:490:3 */

// attributes for mutex, dynamically allocated by library
type X_pthread_mutexattr = struct{ F__pthread_mutexattrp uintptr } /* types.h:495:9 */

// attributes for mutex, dynamically allocated by library
type Pthread_mutexattr_t = X_pthread_mutexattr /* types.h:497:3 */

// attributes for cond, dynamically allocated by library
type X_pthread_condattr = struct{ F__pthread_condattrp uintptr } /* types.h:502:9 */

// attributes for cond, dynamically allocated by library
type Pthread_condattr_t = X_pthread_condattr /* types.h:504:3 */

// pthread_once
type X_once = struct{ F__pthread_once_pad [4]uint64 } /* types.h:509:9 */

// pthread_once
type Pthread_once_t = X_once /* types.h:511:3 */

// UNIX 98 Extensions
// attributes for rwlock, dynamically allocated by library
type X_pthread_rwlockattr = struct{ F__pthread_rwlockattrp uintptr } /* types.h:517:9 */

// UNIX 98 Extensions
// attributes for rwlock, dynamically allocated by library
type Pthread_rwlockattr_t = X_pthread_rwlockattr /* types.h:519:3 */

// SUSV3
// attributes for pthread_barrier_t, dynamically allocated by library
type Pthread_barrierattr_t = struct{ F__pthread_barrierattrp uintptr } /* types.h:527:3 */

type Dev_t = uint64 /* types.h:529:17 */ // expanded device type

type Nlink_t = uint32 /* types.h:532:16 */ // file link type
type Pid_t = int32    /* types.h:533:13 */ // size of something in bytes

type Ssize_t = int64 /* types.h:551:14 */ // size of something in bytes or -1

type Time_t = int64 /* types.h:559:15 */ // time of day in seconds

type Clock_t = int64 /* types.h:564:15 */ // relative time in a specified resolution

type Clockid_t = int32 /* types.h:569:13 */ // clock identifier type

type Timer_t = int32 /* types.h:574:13 */ // timer identifier type

// BEGIN CSTYLED
type Unchar = uint8  /* types.h:580:23 */
type Ushort = uint16 /* types.h:581:24 */
type Uint = uint32   /* types.h:582:22 */
type Ulong = uint64  /* types.h:583:23 */
// END CSTYLED

// The following is the value of type id_t to use to indicate the
// caller's current id.  See procset.h for the type idtype_t
// which defines which kind of id is being specified.

// The following value of type pfn_t is used to indicate
// invalid page frame number.

// BEGIN CSTYLED
type U_char = uint8                   /* types.h:650:23 */
type U_short = uint16                 /* types.h:651:24 */
type U_int = uint32                   /* types.h:652:22 */
type U_long = uint64                  /* types.h:653:23 */
type X_quad = struct{ Fval [2]int32 } /* types.h:654:9 */

type Quad_t = X_quad /* types.h:654:38 */ // used by UFS
type Quad = Quad_t   /* types.h:655:17 */ // used by UFS
// END CSTYLED

// Nested include for BSD/sockets source compatibility.
// (The select macros used to be defined here).
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Time expressed in seconds and nanoseconds

type Timespec = struct {
	Ftv_sec  int64
	Ftv_nsec int64
} /* time_impl.h:57:9 */

// used by UFS
// END CSTYLED

// Nested include for BSD/sockets source compatibility.
// (The select macros used to be defined here).
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Time expressed in seconds and nanoseconds

type Timespec_t = Timespec /* time_impl.h:60:3 */

type Timestruc_t = Timespec /* time_impl.h:81:25 */ // definition per SVr4

// The following has been left in for backward compatibility. Portable
// applications should not use the structure name timestruc.

// Timer specification
type Itimerspec = struct {
	Fit_interval struct {
		Ftv_sec  int64
		Ftv_nsec int64
	}
	Fit_value struct {
		Ftv_sec  int64
		Ftv_nsec int64
	}
} /* time_impl.h:95:9 */

// definition per SVr4

// The following has been left in for backward compatibility. Portable
// applications should not use the structure name timestruc.

// Timer specification
type Itimerspec_t = Itimerspec /* time_impl.h:98:3 */

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright (c) 1982, 1986, 1993 Regents of the University of California.
// All rights reserved.  The Berkeley software License Agreement
// specifies the terms and conditions for redistribution.

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2020 OmniOS Community Edition (OmniOSce) Association.

// Copyright (c) 2013, 2016 by Delphix. All rights reserved.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Structure returned by gettimeofday(2) system call,
// and used in other calls.

type Timeval = struct {
	Ftv_sec  int64
	Ftv_usec int64
} /* time.h:54:1 */

type Timezone = struct {
	Ftz_minuteswest int32
	Ftz_dsttime     int32
} /* time.h:86:1 */

// Needed for longlong_t type.  Placement of this due to <sys/types.h>
// including <sys/select.h> which relies on the presense of the itimerval
// structure.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// Operations on timevals.

// Names of the interval timers, and structure
// defining a timer setting.
// time and when system is running on
// behalf of the process.
// time profiling of multithreaded
// programs.

type Itimerval = struct {
	Fit_interval struct {
		Ftv_sec  int64
		Ftv_usec int64
	}
	Fit_value struct {
		Ftv_sec  int64
		Ftv_usec int64
	}
} /* time.h:209:1 */

//	Definitions for commonly used resolutions.

// Time expressed as a 64-bit nanosecond counter.
type Hrtime_t = int64 /* time.h:265:20 */

// The inclusion of <time.h> is historical and was added for
// backward compatibility in delta 1.2 when a number of definitions
// were moved out of <sys/time.h>.  More recently, the timespec and
// itimerspec structure definitions, along with the _CLOCK_*, CLOCK_*,
// _TIMER_*, and TIMER_* symbols were moved to <sys/time_impl.h>,
// which is now included by <time.h>.  This change was due to POSIX
// 1003.1b-1993 and X/Open UNIX 98 requirements.  For non-POSIX and
// non-X/Open applications, including this header will still make
// visible these definitions.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2010 Nexenta Systems, Inc.  Al rights reserved.
// Copyright 2016 Joyent, Inc.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
// Copyright 2014 PALO, Richard.
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// An application should not include this header directly.  Instead it
// should be included only through the inclusion of other Sun headers.
//
// The contents of this header is limited to identifiers specified in the
// C Standard.  Any new identifiers specified in future amendments to the
// C Standard must be placed in this header.  If these new identifiers
// are required to also be in the C++ Standard "std" namespace, then for
// anything other than macro definitions, corresponding "using" directives
// must also be added to <time.h.h>.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file and its contents are supplied under the terms of the
// Common Development and Distribution License ("CDDL"), version 1.0.
// You may only use this file in accordance with the terms of version
// 1.0 of the CDDL.
//
// A full copy of the text of the CDDL should have accompanied this
// source.  A copy of the CDDL is also available via the Internet at
// http://www.illumos.org/license/CDDL.

// Copyright 2014-2016 PALO, Richard.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// POSIX.1-2008 requires that the NULL macro be cast to type void *.

type Tm = struct {
	Ftm_sec   int32
	Ftm_min   int32
	Ftm_hour  int32
	Ftm_mday  int32
	Ftm_mon   int32
	Ftm_year  int32
	Ftm_wday  int32
	Ftm_yday  int32
	Ftm_isdst int32
} /* time_iso.h:80:1 */

// Neither X/Open nor POSIX allow the inclusion of <signal.h> for the
// definition of the sigevent structure.  Both require the inclusion
// of <signal.h> and <time.h> when using the timer_create() function.
// However, X/Open also specifies that the sigevent structure be defined
// in <time.h> as described in the header <signal.h>.  This prevents
// compiler warnings for applications that only include <time.h> and not
// also <signal.h>.  The sigval union and the sigevent structure is
// therefore defined both here and in <sys/siginfo.h> which gets included
// via inclusion of <signal.h>.
type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* time.h:125:1 */

type Sigevent = struct {
	Fsigev_notify int32
	Fsigev_signo  int32
	Fsigev_value  struct {
		F__ccgo_pad1 [0]uint64
		Fsival_int   int32
		F__ccgo_pad2 [4]byte
	}
	Fsigev_notify_function   uintptr
	Fsigev_notify_attributes uintptr
	F__sigev_pad2            int32
	F__ccgo_pad1             [4]byte
} /* time.h:133:1 */

type Locale_t = uintptr /* time.h:292:24 */

// The inclusion of <sys/select.h> is needed for the FD_CLR,
// FD_ISSET, FD_SET, and FD_SETSIZE macros as well as the
// select() prototype defined in the XOpen specifications
// beginning with XSH4v2.  Placement required after definition
// for itimerval.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// The sigset_t type is defined in <sys/signal.h> and duplicated
// in <sys/ucontext.h> as a result of XPG4v2 requirements. XPG6
// now allows the visibility of signal.h in this header, however
// an order of inclusion problem occurs as a result of inclusion
// of <sys/select.h> in <signal.h> under certain conditions.
// Rather than include <sys/signal.h> here, we've duplicated
// the sigset_t type instead. This type is required for the XPG6
// introduced pselect() function also declared in this header.
type Sigset_t = struct{ F__sigbits [4]uint32 } /* select.h:76:3 */

// Select uses bit masks of file descriptors in longs.
// These macros manipulate such bit fields.
// FD_SETSIZE may be defined by the user, but the default here
// should be >= RLIM_FD_MAX.

type Fd_mask = int64  /* select.h:92:14 */
type Fds_mask = int64 /* select.h:94:14 */

//  The value of _NBBY needs to be consistant with the value
//  of NBBY in <sys/param.h>.

type Fd_set1 = struct{ Ffds_bits [1024]int64 } /* select.h:120:9 */

//  The value of _NBBY needs to be consistant with the value
//  of NBBY in <sys/param.h>.

type Fd_set = Fd_set1 /* select.h:125:3 */ // System Private interface to sysconf()

type Sig_atomic_t = int32 /* signal_iso.h:58:13 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015, Joyent, Inc.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015, Joyent, Inc.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// An application should not include this header directly.  Instead it
// should be included only through the inclusion of other Sun headers.
//
// The contents of this header is limited to identifiers specified in the
// C Standard.  Any new identifiers specified in future amendments to the
// C Standard must be placed in this header.  If these new identifiers
// are required to also be in the C++ Standard "std" namespace, then for
// anything other than macro definitions, corresponding "using" directives
// must also be added to <sys/signal.h.h>.

// We need <sys/siginfo.h> for the declaration of siginfo_t.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// The union sigval is also defined in <time.h> as per X/Open and
// POSIX requirements.

// The sigevent structure is also defined in <time.h> as per X/Open and
// POSIX requirements.

// values of sigev_notify

// negative signal codes are reserved for future use for user generated
// signals

// Get the machine dependent signal codes (SIGILL, SIGFPE, SIGSEGV, and
// SIGBUS) from <sys/machsig.h>

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2003 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// machsig.h is the machine dependent portion of siginfo.h (and is
// included by siginfo.h). A version of machsig.h should exist for
// each architecture. The codes for SIGILL, SIGFPU, SIGSEGV and SIGBUS
// are in this file. The codes for SIGTRAP, SIGCLD(SIGCHLD), and
// SIGPOLL are architecture independent and may be found in siginfo.h.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// This file describes the data type returned by vm routines
// which handle faults.
//
// If FC_CODE(fc) == FC_OBJERR, then FC_ERRNO(fc) contains the errno value
// returned by the underlying object mapped at the fault address.

type Faultcode_t = int32 /* faultcode.h:66:13 */

// SIGILL signal codes

// SIGEMT signal codes

// SIGFPE signal codes

// SIGSEGV signal codes

// SIGBUS signal codes

// SIGTRAP signal codes

// SIGCLD signal codes

// SIGPOLL signal codes

// SIGPROF signal codes

// Inclusion of <sys/time_impl.h> is needed for the declaration of
// timestruc_t.  However, since inclusion of <sys/time_impl.h> results
// in X/Open and POSIX namespace pollution, the definition for
// timestruct_t has been duplicated in a standards namespace safe header
// <sys/time_std_impl.h>.  In <sys/time_std_impl.h>, the structure
// name, tag, and member names, as well as the type itself, all have
// leading underscores to protect namespace.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

// The inclusion of <sys/types.h> is needed for definitions of pid_t, etc.
// Placement here is due to a dependency in <sys/select.h> which is included
// by <sys/types.h> for the sigevent structure.  Hence this inclusion must
// follow that definition.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

type Siginfo = struct {
	Fsi_signo int32
	Fsi_code  int32
	Fsi_errno int32
	Fsi_pad   int32
	F__data   struct {
		F__ccgo_pad1 [0]uint64
		F__pad       [60]int32
	}
} /* siginfo.h:237:9 */

// SIGILL signal codes

// SIGEMT signal codes

// SIGFPE signal codes

// SIGSEGV signal codes

// SIGBUS signal codes

// SIGTRAP signal codes

// SIGCLD signal codes

// SIGPOLL signal codes

// SIGPROF signal codes

// Inclusion of <sys/time_impl.h> is needed for the declaration of
// timestruc_t.  However, since inclusion of <sys/time_impl.h> results
// in X/Open and POSIX namespace pollution, the definition for
// timestruct_t has been duplicated in a standards namespace safe header
// <sys/time_std_impl.h>.  In <sys/time_std_impl.h>, the structure
// name, tag, and member names, as well as the type itself, all have
// leading underscores to protect namespace.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

// The inclusion of <sys/types.h> is needed for definitions of pid_t, etc.
// Placement here is due to a dependency in <sys/select.h> which is included
// by <sys/types.h> for the sigevent structure.  Hence this inclusion must
// follow that definition.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

type Siginfo_t = Siginfo /* siginfo.h:304:3 */

// XXX -- internal version is identical to siginfo_t but without the padding.
// This must be maintained in sync with it.

type K_siginfo = struct {
	Fsi_signo int32
	Fsi_code  int32
	Fsi_errno int32
	Fsi_pad   int32
	F__data   struct {
		F__proc struct {
			F__pid       int32
			F__ccgo_pad1 [4]byte
			F__pdata     struct {
				F__kill struct {
					F__uid       uint32
					F__ccgo_pad1 [4]byte
					F__value     struct {
						F__ccgo_pad1 [0]uint64
						Fsival_int   int32
						F__ccgo_pad2 [4]byte
					}
				}
				F__ccgo_pad1 [8]byte
			}
			F__ctid   int32
			F__zoneid int32
		}
	}
} /* siginfo.h:379:9 */

// XXX -- internal version is identical to siginfo_t but without the padding.
// This must be maintained in sync with it.

type K_siginfo_t = K_siginfo /* siginfo.h:438:3 */

type Sigqueue = struct {
	Fsq_next     uintptr
	Fsq_info     K_siginfo_t
	Fsq_func     uintptr
	Fsq_backptr  uintptr
	Fsq_external int32
	F__ccgo_pad1 [4]byte
} /* siginfo.h:440:9 */

type Sigqueue_t = Sigqueue /* siginfo.h:447:3 */

//  indication whether to queue the signal or not

// Duplicated in <sys/ucontext.h> as a result of XPG4v2 requirements

type K_sigset_t = struct{ F__sigbits [3]uint32 } /* signal.h:73:3 */

// The signal handler routine can have either one or three arguments.
// Existing C code has used either form so not specifing the arguments
// neatly finesses the problem.  C++ doesn't accept this.  To C++
// "(*sa_handler)()" indicates a routine with no arguments (ANSI C would
// specify this as "(*sa_handler)(void)").  One or the other form must be
// used for C++ and the only logical choice is "(*sa_handler)(int)" to allow
// the SIG_* defines to work.  "(*sa_sigaction)(int, siginfo_t *, void *)"
// can be used for the three argument form.

// Note: storage overlap by sa_handler and sa_sigaction
type Sigaction = struct {
	Fsa_flags    int32
	F__ccgo_pad1 [4]byte
	F_funcptr    struct{ F_handler uintptr }
	Fsa_mask     Sigset_t
} /* signal.h:89:1 */

// this is only valid for SIGCLD

// non-conformant ANSI compilation

// definitions for the sa_flags field

// this is only valid for SIGCLD

// use of these symbols by applications is injurious
//	to binary compatibility

// Duplicated in <sys/ucontext.h> as a result of XPG4v2 requirements.
type Sigaltstack = struct {
	Fss_sp       uintptr
	Fss_size     uint64
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
} /* signal.h:176:9 */

// this is only valid for SIGCLD

// non-conformant ANSI compilation

// definitions for the sa_flags field

// this is only valid for SIGCLD

// use of these symbols by applications is injurious
//	to binary compatibility

// Duplicated in <sys/ucontext.h> as a result of XPG4v2 requirements.
type Stack_t = Sigaltstack /* signal.h:183:3 */

// signotify id used only by libc for mq_notify()/aio_notify()
type Signotify_id = struct {
	Fsn_pid   int32
	Fsn_index int32
	Fsn_pad   int32
} /* signal.h:205:9 */

// signotify id used only by libc for mq_notify()/aio_notify()
type Signotify_id_t = Signotify_id /* signal.h:209:3 */

// Command codes for sig_notify call

// Added as per XPG4v2
type Sigstack = struct {
	Fss_sp       uintptr
	Fss_onstack  int32
	F__ccgo_pad1 [4]byte
} /* signal.h:235:1 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015, Joyent, Inc.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//	This file defines the data needed to specify a set of
//	processes.  These types are used by the sigsend, sigsendset,
//	priocntl, priocntlset, waitid, evexit, and evexitset system
//	calls.

// The following defines the values for an identifier type.  It
// specifies the interpretation of an id value.  An idtype and
// id together define a simple set of processes.
type Idtype_t = uint32 /* procset.h:80:3 */

// The following defines the operations which can be performed to
// combine two simple sets of processes to form another set of
// processes.
type Idop_t = uint32 /* procset.h:102:3 */

// The following structure is used to define a set of processes.
// The set is defined in terms of two simple sets of processes
// and an operator which operates on these two operand sets.
type Procset = struct {
	Fp_op      uint32
	Fp_lidtype uint32
	Fp_lid     int32
	Fp_ridtype uint32
	Fp_rid     int32
} /* procset.h:110:9 */

// The following structure is used to define a set of processes.
// The set is defined in terms of two simple sets of processes
// and an operator which operates on these two operand sets.
type Procset_t = Procset /* procset.h:125:3 */

var _ int8 /* gen.c:2:13: */
