// Code generated by 'ccgo limits/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o limits/limits_illumos_amd64.go -pkgname limits', DO NOT EDIT.

package limits

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ARG_MAX                              = 2096640                       // limits.h:73:1:
	BC_BASE_MAX                          = 99                            // limits.h:175:1:
	BC_DIM_MAX                           = 2048                          // limits.h:176:1:
	BC_SCALE_MAX                         = 99                            // limits.h:177:1:
	BC_STRING_MAX                        = 1000                          // limits.h:178:1:
	CHARCLASS_NAME_MAX                   = 14                            // limits.h:206:1:
	CHAR_BIT                             = 8                             // limits.h:64:1:
	CHAR_MAX                             = 127                           // limits.h:99:1:
	CHAR_MIN                             = -128                          // limits.h:97:1:
	CHILD_MAX                            = 25                            // limits.h:272:1:
	COLL_WEIGHTS_MAX                     = 10                            // limits.h:179:1:
	DBL_DIG                              = 15                            // limits.h:226:1:
	DBL_MAX                              = 1.7976931348623157081452e+308 // limits.h:227:1:
	DBL_MIN                              = 2.2250738585072013830903e-308 // limits.h:236:1:
	EXPR_NEST_MAX                        = 32                            // limits.h:180:1:
	FCHR_MAX                             = 1048576                       // limits.h:251:1:
	FLT_DIG                              = 6                             // limits.h:229:1:
	FLT_MAX                              = 3.4028234663852885981170e+38  // limits.h:230:1:
	FLT_MIN                              = 1.1754943508222875079688e-38  // limits.h:238:1:
	INT16_MAX                            = 32767                         // int_limits.h:93:1:
	INT16_MIN                            = -32768                        // int_limits.h:211:1:
	INT32_MAX                            = 2147483647                    // int_limits.h:94:1:
	INT32_MIN                            = -2147483648                   // int_limits.h:212:1:
	INT64_MAX                            = 9223372036854775807           // int_limits.h:96:1:
	INT64_MIN                            = -9223372036854775808          // int_limits.h:214:1:
	INT8_MAX                             = 127                           // int_limits.h:92:1:
	INT8_MIN                             = -128                          // int_limits.h:210:1:
	INTMAX_MAX                           = 9223372036854775807           // int_limits.h:111:1:
	INTMAX_MIN                           = -9223372036854775808          // int_limits.h:220:1:
	INTPTR_MAX                           = 9223372036854775807           // int_limits.h:157:1:
	INTPTR_MIN                           = -9223372036854775808          // int_limits.h:241:1:
	INT_FAST16_MAX                       = 2147483647                    // int_limits.h:137:1:
	INT_FAST16_MIN                       = -2147483648                   // int_limits.h:233:1:
	INT_FAST32_MAX                       = 2147483647                    // int_limits.h:138:1:
	INT_FAST32_MIN                       = -2147483648                   // int_limits.h:234:1:
	INT_FAST64_MAX                       = 9223372036854775807           // int_limits.h:140:1:
	INT_FAST64_MIN                       = -9223372036854775808          // int_limits.h:236:1:
	INT_FAST8_MAX                        = 127                           // int_limits.h:136:1:
	INT_FAST8_MIN                        = -128                          // int_limits.h:232:1:
	INT_LEAST16_MAX                      = 32767                         // int_limits.h:123:1:
	INT_LEAST16_MIN                      = -32768                        // int_limits.h:226:1:
	INT_LEAST32_MAX                      = 2147483647                    // int_limits.h:124:1:
	INT_LEAST32_MIN                      = -2147483648                   // int_limits.h:227:1:
	INT_LEAST64_MAX                      = 9223372036854775807           // int_limits.h:126:1:
	INT_LEAST64_MIN                      = -9223372036854775808          // int_limits.h:229:1:
	INT_LEAST8_MAX                       = 127                           // int_limits.h:122:1:
	INT_LEAST8_MIN                       = -128                          // int_limits.h:225:1:
	INT_MAX                              = 2147483647                    // limits.h:120:1:
	INT_MIN                              = -2147483648                   // limits.h:118:1:
	IOV_MAX                              = 1024                          // limits.h:22:1:
	LINE_MAX                             = 2048                          // limits.h:181:1:
	LLONG_MAX                            = 9223372036854775807           // limits.h:142:1:
	LLONG_MIN                            = -9223372036854775808          // limits.h:140:1:
	LOGIN_NAME_MAX                       = 33                            // limits.h:306:1:
	LOGIN_NAME_MAX_TRAD                  = 9                             // limits.h:308:1:
	LOGNAME_MAX                          = 32                            // limits.h:303:1:
	LOGNAME_MAX_TRAD                     = 8                             // limits.h:307:1:
	LONG_BIT                             = 64                            // limits.h:219:1:
	LONG_LONG_MAX                        = 9223372036854775807           // limits.h:154:1:
	LONG_LONG_MIN                        = -9223372036854775808          // limits.h:152:1:
	LONG_MAX                             = 9223372036854775807           // limits.h:131:1:
	LONG_MIN                             = -9223372036854775808          // limits.h:129:1:
	MAX_CANON                            = 256                           // limits.h:84:1:
	MAX_INPUT                            = 512                           // limits.h:88:1:
	MB_LEN_MAX                           = 5                             // limits_iso.h:58:1:
	NAME_MAX                             = 255                           // limits.h:270:1:
	NGROUPS_MAX                          = 16                            // limits.h:91:1:
	NL_ARGMAX                            = 9                             // limits.h:208:1:
	NL_LANGMAX                           = 14                            // limits.h:210:1:
	NL_MSGMAX                            = 32767                         // limits.h:211:1:
	NL_NMAX                              = 1                             // limits.h:212:1:
	NL_SETMAX                            = 255                           // limits.h:213:1:
	NL_TEXTMAX                           = 2048                          // limits.h:214:1:
	NZERO                                = 20                            // limits.h:215:1:
	OPEN_MAX                             = 256                           // limits.h:274:1:
	PASS_MAX                             = 256                           // limits.h:203:1:
	PATH_MAX                             = 1024                          // limits.h:94:1:
	PID_MAX                              = 999999                        // limits.h:252:1:
	PIPE_BUF                             = 5120                          // limits.h:99:1:
	PIPE_MAX                             = 5120                          // limits.h:277:1:
	PTRDIFF_MAX                          = 9223372036854775807           // int_limits.h:166:1:
	PTRDIFF_MIN                          = -9223372036854775808          // int_limits.h:248:1:
	RE_DUP_MAX                           = 255                           // limits.h:183:1:
	SCHAR_MAX                            = 127                           // limits.h:75:1:
	SCHAR_MIN                            = -128                          // limits.h:73:1:
	SHRT_MAX                             = 32767                         // limits.h:106:1:
	SHRT_MIN                             = -32768                        // limits.h:104:1:
	SIG_ATOMIC_MAX                       = 2147483647                    // int_limits.h:186:1:
	SIG_ATOMIC_MIN                       = -2147483648                   // int_limits.h:255:1:
	SIZE_MAX                             = 18446744073709551615          // int_limits.h:179:1:
	SSIZE_MAX                            = 9223372036854775807           // limits.h:63:1:
	STD_BLK                              = 1024                          // limits.h:279:1:
	SYMLINK_MAX                          = 1024                          // limits.h:97:1:
	SYSPID_MAX                           = 1                             // limits.h:282:1:
	SYS_NMLN                             = 257                           // limits.h:285:1:
	TMP_MAX                              = 17576                         // limits.h:102:1:
	TTYNAME_MAX                          = 128                           // limits.h:310:1:
	UCHAR_MAX                            = 255                           // limits.h:82:1:
	UID_MAX                              = 2147483647                    // limits.h:280:1:
	UINT16_MAX                           = 65535                         // int_limits.h:102:1:
	UINT32_MAX                           = 4294967295                    // int_limits.h:103:1:
	UINT64_MAX                           = 18446744073709551615          // int_limits.h:105:1:
	UINT8_MAX                            = 255                           // int_limits.h:101:1:
	UINTMAX_MAX                          = 18446744073709551615          // int_limits.h:117:1:
	UINTPTR_MAX                          = 18446744073709551615          // int_limits.h:158:1:
	UINT_FAST16_MAX                      = 4294967295                    // int_limits.h:144:1:
	UINT_FAST32_MAX                      = 4294967295                    // int_limits.h:145:1:
	UINT_FAST64_MAX                      = 18446744073709551615          // int_limits.h:147:1:
	UINT_FAST8_MAX                       = 255                           // int_limits.h:143:1:
	UINT_LEAST16_MAX                     = 65535                         // int_limits.h:130:1:
	UINT_LEAST32_MAX                     = 4294967295                    // int_limits.h:131:1:
	UINT_LEAST64_MAX                     = 18446744073709551615          // int_limits.h:133:1:
	UINT_LEAST8_MAX                      = 255                           // int_limits.h:129:1:
	UINT_MAX                             = 4294967295                    // limits.h:124:1:
	ULLONG_MAX                           = 18446744073709551615          // limits.h:146:1:
	ULONG_LONG_MAX                       = 18446744073709551615          // limits.h:158:1:
	ULONG_MAX                            = 18446744073709551615          // limits.h:135:1:
	USHRT_MAX                            = 65535                         // limits.h:113:1:
	USI_MAX                              = 4294967295                    // limits.h:281:1:
	WCHAR_MAX                            = 2147483647                    // int_limits.h:195:1:
	WCHAR_MIN                            = -2147483648                   // int_limits.h:264:1:
	WINT_MAX                             = 2147483647                    // int_limits.h:200:1:
	WINT_MIN                             = -2147483648                   // int_limits.h:269:1:
	WORD_BIT                             = 32                            // limits.h:217:1:
	X_ALIGNMENT_REQUIRED                 = 1                             // isa_defs.h:262:1:
	X_ARG_MAX32                          = 1048320                       // limits.h:70:1:
	X_ARG_MAX64                          = 2096640                       // limits.h:71:1:
	X_BIT_FIELDS_LTOH                    = 0                             // isa_defs.h:245:1:
	X_BOOL_ALIGNMENT                     = 1                             // isa_defs.h:248:1:
	X_CHAR_ALIGNMENT                     = 1                             // isa_defs.h:249:1:
	X_CHAR_IS_SIGNED                     = 0                             // isa_defs.h:247:1:
	X_CLOCK_T                            = 0                             // limits.h:291:1:
	X_DMA_USES_PHYSADDR                  = 0                             // isa_defs.h:281:1:
	X_DONT_USE_1275_GENERIC_NAMES        = 0                             // isa_defs.h:287:1:
	X_DOUBLE_ALIGNMENT                   = 8                             // isa_defs.h:256:1:
	X_DOUBLE_COMPLEX_ALIGNMENT           = 8                             // isa_defs.h:257:1:
	X_DTRACE_VERSION                     = 1                             // feature_tests.h:490:1:
	X_FILE_OFFSET_BITS                   = 64                            // <builtin>:25:1:
	X_FIRMWARE_NEEDS_FDISK               = 0                             // isa_defs.h:282:1:
	X_FLOAT_ALIGNMENT                    = 4                             // isa_defs.h:252:1:
	X_FLOAT_COMPLEX_ALIGNMENT            = 4                             // isa_defs.h:253:1:
	X_GCC_LIMITS_H_                      = 0                             // limits.h:30:1:
	X_HAVE_CPUID_INSN                    = 0                             // isa_defs.h:288:1:
	X_IEEE_754                           = 0                             // isa_defs.h:246:1:
	X_INT_ALIGNMENT                      = 4                             // isa_defs.h:251:1:
	X_ISO_CPP_14882_1998                 = 0                             // feature_tests.h:466:1:
	X_ISO_C_9899_1999                    = 0                             // feature_tests.h:472:1:
	X_ISO_C_9899_2011                    = 0                             // feature_tests.h:478:1:
	X_ISO_LIMITS_ISO_H                   = 0                             // limits_iso.h:44:1:
	X_LARGEFILE64_SOURCE                 = 1                             // feature_tests.h:231:1:
	X_LARGEFILE_SOURCE                   = 1                             // feature_tests.h:235:1:
	X_LIMITS_H                           = 0                             // limits.h:36:1:
	X_LIMITS_H___                        = 0                             // limits.h:60:1:
	X_LITTLE_ENDIAN                      = 0                             // isa_defs.h:242:1:
	X_LONGLONG_TYPE                      = 0                             // feature_tests.h:412:1:
	X_LONG_ALIGNMENT                     = 8                             // isa_defs.h:254:1:
	X_LONG_DOUBLE_ALIGNMENT              = 16                            // isa_defs.h:258:1:
	X_LONG_DOUBLE_COMPLEX_ALIGNMENT      = 16                            // isa_defs.h:259:1:
	X_LONG_LONG_ALIGNMENT                = 8                             // isa_defs.h:255:1:
	X_LONG_LONG_ALIGNMENT_32             = 4                             // isa_defs.h:268:1:
	X_LONG_LONG_LTOH                     = 0                             // isa_defs.h:244:1:
	X_LP64                               = 1                             // <predefined>:286:1:
	X_MAX_ALIGNMENT                      = 16                            // isa_defs.h:261:1:
	X_MULTI_DATAMODEL                    = 0                             // isa_defs.h:279:1:
	X_NORETURN_KYWD                      = 0                             // feature_tests.h:448:1:
	X_PASS_MAX                           = 256                           // limits.h:198:1:
	X_PASS_MAX_XPG                       = 8                             // limits.h:197:1:
	X_POINTER_ALIGNMENT                  = 8                             // isa_defs.h:260:1:
	X_POSIX2_BC_BASE_MAX                 = 99                            // limits.h:164:1:
	X_POSIX2_BC_DIM_MAX                  = 2048                          // limits.h:165:1:
	X_POSIX2_BC_SCALE_MAX                = 99                            // limits.h:166:1:
	X_POSIX2_BC_STRING_MAX               = 1000                          // limits.h:167:1:
	X_POSIX2_CHARCLASS_NAME_MAX          = 14                            // limits.h:173:1:
	X_POSIX2_COLL_WEIGHTS_MAX            = 2                             // limits.h:168:1:
	X_POSIX2_EXPR_NEST_MAX               = 32                            // limits.h:169:1:
	X_POSIX2_LINE_MAX                    = 2048                          // limits.h:170:1:
	X_POSIX2_RE_DUP_MAX                  = 255                           // limits.h:171:1:
	X_POSIX_AIO_LISTIO_MAX               = 2                             // limits.h:110:1:
	X_POSIX_AIO_MAX                      = 1                             // limits.h:111:1:
	X_POSIX_ARG_MAX                      = 4096                          // limits.h:112:1:
	X_POSIX_CHILD_MAX                    = 6                             // limits.h:116:1:
	X_POSIX_CLOCKRES_MIN                 = 20000000                      // limits.h:118:1:
	X_POSIX_DELAYTIMER_MAX               = 32                            // limits.h:119:1:
	X_POSIX_HOST_NAME_MAX                = 255                           // limits.h:155:1:
	X_POSIX_LINK_MAX                     = 8                             // limits.h:120:1:
	X_POSIX_LOGIN_NAME_MAX               = 9                             // limits.h:149:1:
	X_POSIX_MAX_CANON                    = 255                           // limits.h:121:1:
	X_POSIX_MAX_INPUT                    = 255                           // limits.h:122:1:
	X_POSIX_MQ_OPEN_MAX                  = 8                             // limits.h:123:1:
	X_POSIX_MQ_PRIO_MAX                  = 32                            // limits.h:124:1:
	X_POSIX_NAME_MAX                     = 14                            // limits.h:125:1:
	X_POSIX_NGROUPS_MAX                  = 0                             // limits.h:131:1:
	X_POSIX_OPEN_MAX                     = 16                            // limits.h:132:1:
	X_POSIX_PATH_MAX                     = 255                           // limits.h:133:1:
	X_POSIX_PIPE_BUF                     = 512                           // limits.h:135:1:
	X_POSIX_RE_DUP_MAX                   = 255                           // limits.h:156:1:
	X_POSIX_RTSIG_MAX                    = 8                             // limits.h:136:1:
	X_POSIX_SEM_NSEMS_MAX                = 256                           // limits.h:137:1:
	X_POSIX_SEM_VALUE_MAX                = 32767                         // limits.h:138:1:
	X_POSIX_SIGQUEUE_MAX                 = 32                            // limits.h:139:1:
	X_POSIX_SSIZE_MAX                    = 32767                         // limits.h:140:1:
	X_POSIX_STREAM_MAX                   = 8                             // limits.h:141:1:
	X_POSIX_SYMLINK_MAX                  = 255                           // limits.h:157:1:
	X_POSIX_SYMLOOP_MAX                  = 8                             // limits.h:158:1:
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS = 4                             // limits.h:150:1:
	X_POSIX_THREAD_KEYS_MAX              = 128                           // limits.h:151:1:
	X_POSIX_THREAD_THREADS_MAX           = 64                            // limits.h:152:1:
	X_POSIX_TIMER_MAX                    = 32                            // limits.h:142:1:
	X_POSIX_TTY_NAME_MAX                 = 9                             // limits.h:153:1:
	X_POSIX_TZNAME_MAX                   = 3                             // limits.h:146:1:
	X_PSM_MODULES                        = 0                             // isa_defs.h:284:1:
	X_RESTRICT_KYWD                      = 0                             // feature_tests.h:435:1:
	X_RTC_CONFIG                         = 0                             // isa_defs.h:285:1:
	X_SHORT_ALIGNMENT                    = 2                             // isa_defs.h:250:1:
	X_SOFT_HOSTID                        = 0                             // isa_defs.h:286:1:
	X_STACK_GROWS_DOWNWARD               = 0                             // isa_defs.h:243:1:
	X_STDC_C11                           = 0                             // feature_tests.h:165:1:
	X_STDC_C99                           = 0                             // feature_tests.h:169:1:
	X_SUNOS_VTOC_16                      = 0                             // isa_defs.h:280:1:
	X_SYS_CCOMPILE_H                     = 0                             // ccompile.h:32:1:
	X_SYS_FEATURE_TESTS_H                = 0                             // feature_tests.h:41:1:
	X_SYS_INT_LIMITS_H                   = 0                             // int_limits.h:39:1:
	X_SYS_ISA_DEFS_H                     = 0                             // isa_defs.h:30:1:
	X_SYS_LIMITS_H                       = 0                             // limits.h:16:1:
	X_XOPEN_IOV_MAX                      = 16                            // limits.h:244:1:
	X_XOPEN_NAME_MAX                     = 255                           // limits.h:245:1:
	X_XOPEN_PATH_MAX                     = 1024                          // limits.h:246:1:
	X_XOPEN_VERSION                      = 3                             // feature_tests.h:392:1:
	Sun                                  = 1                             // <predefined>:172:1:
	Unix                                 = 1                             // <predefined>:175:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// Copyright (C) 1992-2022 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// This administrivia gets added to the beginning of limits.h
//    if the system has its own version of limits.h.

// We use _GCC_LIMITS_H_ because we want this not to match
//    any macros that the system's limits.h uses for its own purposes.

// Use "..." so that we find syslimits.h only in this same directory.
// syslimits.h stands for the system's own limits.h file.
//    If we can use it ok unmodified, then we install this text.
//    If fixincludes fixes it, then the fixed version is installed
//    instead of this text.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright (c) 2013 Gary Mills
// Copyright 2017 RackTop Systems.
//
// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 Joyent, Inc.  All rights reserved.

//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 EveryCity Ltd. All rights reserved.
// Copyright 2019 Joyent, Inc.

// This file contains definitions designed to enable different compilers
// to be used harmoniously on Solaris systems.

// Allow for version tests for compiler bugs and features.

// analogous to lint's PRINTFLIKEn

// Handle the kernel printf routines that can take '%b' too

// This one's pretty obvious -- the function never returns

// The function is 'extern inline' and expects GNU C89 behaviour, not C99
// behaviour.
//
// Should only be used on 'extern inline' definitions for GCC.

// The function has control flow such that it may return multiple times (in
// the manner of setjmp or vfork)

// This is an appropriate label for functions that do not
// modify their arguments, e.g. strlen()

// This is a stronger form of __pure__. Can be used for functions
// that do not modify their arguments and don't depend on global
// memory.

// This attribute, attached to a variable, means that the variable is meant to
// be possibly unused. GCC will not produce a warning for this variable.

// Shorthand versions for readability

// In release build, disable warnings about variables
// which are used only for debugging.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// This header file serves to group a set of well known defines and to
// set these for each instruction set architecture.  These defines may
// be divided into two groups;  characteristics of the processor and
// implementation choices for Solaris on a processor.
//
// Processor Characteristics:
//
// _LITTLE_ENDIAN / _BIG_ENDIAN:
//	The natural byte order of the processor.  A pointer to an int points
//	to the least/most significant byte of that int.
//
// _STACK_GROWS_UPWARD / _STACK_GROWS_DOWNWARD:
//	The processor specific direction of stack growth.  A push onto the
//	stack increases/decreases the stack pointer, so it stores data at
//	successively higher/lower addresses.  (Stackless machines ignored
//	without regrets).
//
// _LONG_LONG_HTOL / _LONG_LONG_LTOH:
//	A pointer to a long long points to the most/least significant long
//	within that long long.
//
// _BIT_FIELDS_HTOL / _BIT_FIELDS_LTOH:
//	The C compiler assigns bit fields from the high/low to the low/high end
//	of an int (most to least significant vs. least to most significant).
//
// _IEEE_754:
//	The processor (or supported implementations of the processor)
//	supports the ieee-754 floating point standard.  No other floating
//	point standards are supported (or significant).  Any other supported
//	floating point formats are expected to be cased on the ISA processor
//	symbol.
//
// _CHAR_IS_UNSIGNED / _CHAR_IS_SIGNED:
//	The C Compiler implements objects of type `char' as `unsigned' or
//	`signed' respectively.  This is really an implementation choice of
//	the compiler writer, but it is specified in the ABI and tends to
//	be uniform across compilers for an instruction set architecture.
//	Hence, it has the properties of a processor characteristic.
//
// _CHAR_ALIGNMENT / _SHORT_ALIGNMENT / _INT_ALIGNMENT / _LONG_ALIGNMENT /
// _LONG_LONG_ALIGNMENT / _DOUBLE_ALIGNMENT / _LONG_DOUBLE_ALIGNMENT /
// _POINTER_ALIGNMENT / _FLOAT_ALIGNMENT:
//	The ABI defines alignment requirements of each of the primitive
//	object types.  Some, if not all, may be hardware requirements as
// 	well.  The values are expressed in "byte-alignment" units.
//
// _MAX_ALIGNMENT:
//	The most stringent alignment requirement as specified by the ABI.
//	Equal to the maximum of all the above _XXX_ALIGNMENT values.
//
// _MAX_ALIGNMENT_TYPE:
// 	The name of the C type that has the value descried in _MAX_ALIGNMENT.
//
// _ALIGNMENT_REQUIRED:
//	True or false (1 or 0) whether or not the hardware requires the ABI
//	alignment.
//
// _LONG_LONG_ALIGNMENT_32
//	The 32-bit ABI supported by a 64-bit kernel may have different
//	alignment requirements for primitive object types.  The value of this
//	identifier is expressed in "byte-alignment" units.
//
// _HAVE_CPUID_INSN
//	This indicates that the architecture supports the 'cpuid'
//	instruction as defined by Intel.  (Intel allows other vendors
//	to extend the instruction for their own purposes.)
//
//
// Implementation Choices:
//
// _ILP32 / _LP64:
//	This specifies the compiler data type implementation as specified in
//	the relevant ABI.  The choice between these is strongly influenced
//	by the underlying hardware, but is not absolutely tied to it.
//	Currently only two data type models are supported:
//
//	_ILP32:
//		Int/Long/Pointer are 32 bits.  This is the historical UNIX
//		and Solaris implementation.  Due to its historical standing,
//		this is the default case.
//
//	_LP64:
//		Long/Pointer are 64 bits, Int is 32 bits.  This is the chosen
//		implementation for 64-bit ABIs such as SPARC V9.
//
//	_I32LPx:
//		A compilation environment where 'int' is 32-bit, and
//		longs and pointers are simply the same size.
//
//	In all cases, Char is 8 bits and Short is 16 bits.
//
// _SUNOS_VTOC_8 / _SUNOS_VTOC_16 / _SVR4_VTOC_16:
//	This specifies the form of the disk VTOC (or label):
//
//	_SUNOS_VTOC_8:
//		This is a VTOC form which is upwardly compatible with the
//		SunOS 4.x disk label and allows 8 partitions per disk.
//
//	_SUNOS_VTOC_16:
//		In this format the incore vtoc image matches the ondisk
//		version.  It allows 16 slices per disk, and is not
//		compatible with the SunOS 4.x disk label.
//
//	Note that these are not the only two VTOC forms possible and
//	additional forms may be added.  One possible form would be the
//	SVr4 VTOC form.  The symbol for that is reserved now, although
//	it is not implemented.
//
//	_SVR4_VTOC_16:
//		This VTOC form is compatible with the System V Release 4
//		VTOC (as implemented on the SVr4 Intel and 3b ports) with
//		16 partitions per disk.
//
//
// _DMA_USES_PHYSADDR / _DMA_USES_VIRTADDR
//	This describes the type of addresses used by system DMA:
//
//	_DMA_USES_PHYSADDR:
//		This type of DMA, used in the x86 implementation,
//		requires physical addresses for DMA buffers.  The 24-bit
//		addresses used by some legacy boards is the source of the
//		"low-memory" (<16MB) requirement for some devices using DMA.
//
//	_DMA_USES_VIRTADDR:
//		This method of DMA allows the use of virtual addresses for
//		DMA transfers.
//
// _FIRMWARE_NEEDS_FDISK / _NO_FDISK_PRESENT
//      This indicates the presence/absence of an fdisk table.
//
//      _FIRMWARE_NEEDS_FDISK
//              The fdisk table is required by system firmware.  If present,
//              it allows a disk to be subdivided into multiple fdisk
//              partitions, each of which is equivalent to a separate,
//              virtual disk.  This enables the co-existence of multiple
//              operating systems on a shared hard disk.
//
//      _NO_FDISK_PRESENT
//              If the fdisk table is absent, it is assumed that the entire
//              media is allocated for a single operating system.
//
// _HAVE_TEM_FIRMWARE
//	Defined if this architecture has the (fallback) option of
//	using prom_* calls for doing I/O if a suitable kernel driver
//	is not available to do it.
//
// _DONT_USE_1275_GENERIC_NAMES
//		Controls whether or not device tree node names should
//		comply with the IEEE 1275 "Generic Names" Recommended
//		Practice. With _DONT_USE_GENERIC_NAMES, device-specific
//		names identifying the particular device will be used.
//
// __i386_COMPAT
//	This indicates whether the i386 ABI is supported as a *non-native*
//	mode for the platform.  When this symbol is defined:
//	-	32-bit xstat-style system calls are enabled
//	-	32-bit xmknod-style system calls are enabled
//	-	32-bit system calls use i386 sizes -and- alignments
//
//	Note that this is NOT defined for the i386 native environment!
//
// __x86
//	This is ONLY a synonym for defined(__i386) || defined(__amd64)
//	which is useful only insofar as these two architectures share
//	common attributes.  Analogous to __sparc.
//
// _PSM_MODULES
//	This indicates whether or not the implementation uses PSM
//	modules for processor support, reading /etc/mach from inside
//	the kernel to extract a list.
//
// _RTC_CONFIG
//	This indicates whether or not the implementation uses /etc/rtc_config
//	to configure the real-time clock in the kernel.
//
// _UNIX_KRTLD
//	This indicates that the implementation uses a dynamically
//	linked unix + krtld to form the core kernel image at boot
//	time, or (in the absence of this symbol) a prelinked kernel image.
//
// _OBP
//	This indicates the firmware interface is OBP.
//
// _SOFT_HOSTID
//	This indicates that the implementation obtains the hostid
//	from the file /etc/hostid, rather than from hardware.

// The following set of definitions characterize Solaris on AMD's
// 64-bit systems.

// Define the appropriate "processor characteristics"

// Different alignment constraints for the i386 ABI in compatibility mode

// Define the appropriate "implementation choices".

// The feature test macro __i386 is generic for all processors implementing
// the Intel 386 instruction set or a superset of it.  Specifically, this
// includes all members of the 386, 486, and Pentium family of processors.

// Values of _POSIX_C_SOURCE
//
//		undefined   not a POSIX compilation
//		1	    POSIX.1-1990 compilation
//		2	    POSIX.2-1992 compilation
//		199309L	    POSIX.1b-1993 compilation (Real Time)
//		199506L	    POSIX.1c-1995 compilation (POSIX Threads)
//		200112L	    POSIX.1-2001 compilation (Austin Group Revision)
//		200809L     POSIX.1-2008 compilation

// The feature test macros __XOPEN_OR_POSIX, _STRICT_STDC, _STRICT_SYMBOLS,
// and _STDC_C99 are Sun implementation specific macros created in order to
// compress common standards specified feature test macros for easier reading.
// These macros should not be used by the application developer as
// unexpected results may occur. Instead, the user should reference
// standards(7) for correct usage of the standards feature test macros.
//
// __XOPEN_OR_POSIX     Used in cases where a symbol is defined by both
//                      X/Open or POSIX or in the negative, when neither
//                      X/Open or POSIX defines a symbol.
//
// _STRICT_STDC         __STDC__ is specified by the C Standards and defined
//                      by the compiler. For Sun compilers the value of
//                      __STDC__ is either 1, 0, or not defined based on the
//                      compilation mode (see cc(1)). When the value of
//                      __STDC__ is 1 and in the absence of any other feature
//                      test macros, the namespace available to the application
//                      is limited to only those symbols defined by the C
//                      Standard. _STRICT_STDC provides a more readable means
//                      of identifying symbols defined by the standard, or in
//                      the negative, symbols that are extensions to the C
//                      Standard. See additional comments for GNU C differences.
//
// _STDC_C99            __STDC_VERSION__ is specified by the C standards and
//                      defined by the compiler and indicates the version of
//                      the C standard. A value of 199901L indicates a
//                      compiler that complies with ISO/IEC 9899:1999, other-
//                      wise known as the C99 standard.
//
// _STDC_C11		Like _STDC_C99 except that the value of __STDC_VERSION__
//                      is 201112L indicating a compiler that compiles with
//                      ISO/IEC 9899:2011, otherwise known as the C11 standard.
//
// _STRICT_SYMBOLS	Used in cases where symbol visibility is restricted
//                      by the standards, and the user has not explicitly
//                      relaxed the strictness via __EXTENSIONS__.

// ISO/IEC 9899:1990 and it's revisions, ISO/IEC 9899:1999 and ISO/IEC
// 99899:2011 specify the following predefined macro name:
//
// __STDC__	The integer constant 1, intended to indicate a conforming
//		implementation.
//
// Furthermore, a strictly conforming program shall use only those features
// of the language and library specified in these standards. A conforming
// implementation shall accept any strictly conforming program.
//
// Based on these requirements, Sun's C compiler defines __STDC__ to 1 for
// strictly conforming environments and __STDC__ to 0 for environments that
// use ANSI C semantics but allow extensions to the C standard. For non-ANSI
// C semantics, Sun's C compiler does not define __STDC__.
//
// The GNU C project interpretation is that __STDC__ should always be defined
// to 1 for compilation modes that accept ANSI C syntax regardless of whether
// or not extensions to the C standard are used. Violations of conforming
// behavior are conditionally flagged as warnings via the use of the
// -pedantic option. In addition to defining __STDC__ to 1, the GNU C
// compiler also defines __STRICT_ANSI__ as a means of specifying strictly
// conforming environments using the -ansi or -std=<standard> options.
//
// In the absence of any other compiler options, Sun and GNU set the value
// of __STDC__ as follows when using the following options:
//
//				Value of __STDC__  __STRICT_ANSI__
//
// cc -Xa (default)			0	      undefined
// cc -Xt (transitional)		0             undefined
// cc -Xc (strictly conforming)		1	      undefined
// cc -Xs (K&R C)		    undefined	      undefined
//
// gcc (default)			1	      undefined
// gcc -ansi, -std={c89, c99,...)	1               defined
// gcc -traditional (K&R)	    undefined	      undefined
//
// The default compilation modes for Sun C compilers versus GNU C compilers
// results in a differing value for __STDC__ which results in a more
// restricted namespace when using Sun compilers. To allow both GNU and Sun
// interpretations to peacefully co-exist, we use the following Sun
// implementation _STRICT_STDC_ macro:

// Compiler complies with ISO/IEC 9899:1999 or ISO/IEC 9989:2011

// Use strict symbol visibility.

// This is a variant of _STRICT_SYMBOLS that is meant to cover headers that are
// governed by POSIX, but have not been governed by ISO C. One can go two ways
// on what should happen if an application actively includes (not transitively)
// a header that isn't part of the ISO C spec, we opt to say that if someone has
// gone out of there way then they're doing it for a reason and that is an act
// of non-compliance and therefore it's not up to us to hide away every symbol.
//
// In general, prefer using _STRICT_SYMBOLS, but this is here in particular for
// cases where in the past we have only used a POSIX related check and we don't
// wish to make something stricter. Often applications are relying on the
// ability to, or more realistically unwittingly, have _STRICT_STDC declared and
// still use these interfaces.

// Large file interfaces:
//
//	_LARGEFILE_SOURCE
//		1		large file-related additions to POSIX
//				interfaces requested (fseeko, etc.)
//	_LARGEFILE64_SOURCE
//		1		transitional large-file-related interfaces
//				requested (seek64, stat64, etc.)
//
// The corresponding announcement macros are respectively:
//	_LFS_LARGEFILE
//	_LFS64_LARGEFILE
// (These are set in <unistd.h>.)
//
// Requesting _LARGEFILE64_SOURCE implies requesting _LARGEFILE_SOURCE as
// well.
//
// The large file interfaces are made visible regardless of the initial values
// of the feature test macros under certain circumstances:
//    -	If no explicit standards-conforming environment is requested (neither
//	of _POSIX_SOURCE nor _XOPEN_SOURCE is defined and the value of
//	__STDC__ does not imply standards conformance).
//    -	Extended system interfaces are explicitly requested (__EXTENSIONS__
//	is defined).
//    -	Access to in-kernel interfaces is requested (_KERNEL or _KMEMUSER is
//	defined).  (Note that this dependency is an artifact of the current
//	kernel implementation and may change in future releases.)

// Large file compilation environment control:
//
// The setting of _FILE_OFFSET_BITS controls the size of various file-related
// types and governs the mapping between file-related source function symbol
// names and the corresponding binary entry points.
//
// In the 32-bit environment, the default value is 32; if not set, set it to
// the default here, to simplify tests in other headers.
//
// In the 64-bit compilation environment, the only value allowed is 64.

// Use of _XOPEN_SOURCE
//
// The following X/Open specifications are supported:
//
// X/Open Portability Guide, Issue 3 (XPG3)
// X/Open CAE Specification, Issue 4 (XPG4)
// X/Open CAE Specification, Issue 4, Version 2 (XPG4v2)
// X/Open CAE Specification, Issue 5 (XPG5)
// Open Group Technical Standard, Issue 6 (XPG6), also referred to as
//    IEEE Std. 1003.1-2001 and ISO/IEC 9945:2002.
// Open Group Technical Standard, Issue 7 (XPG7), also referred to as
//    IEEE Std. 1003.1-2008 and ISO/IEC 9945:2009.
//
// XPG4v2 is also referred to as UNIX 95 (SUS or SUSv1).
// XPG5 is also referred to as UNIX 98 or the Single Unix Specification,
//     Version 2 (SUSv2)
// XPG6 is the result of a merge of the X/Open and POSIX specifications
//     and as such is also referred to as IEEE Std. 1003.1-2001 in
//     addition to UNIX 03 and SUSv3.
// XPG7 is also referred to as UNIX 08 and SUSv4.
//
// When writing a conforming X/Open application, as per the specification
// requirements, the appropriate feature test macros must be defined at
// compile time. These are as follows. For more info, see standards(7).
//
// Feature Test Macro				     Specification
// ------------------------------------------------  -------------
// _XOPEN_SOURCE                                         XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4                   XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1           XPG4v2
// _XOPEN_SOURCE = 500                                   XPG5
// _XOPEN_SOURCE = 600  (or POSIX_C_SOURCE=200112L)      XPG6
// _XOPEN_SOURCE = 700  (or POSIX_C_SOURCE=200809L)      XPG7
//
// In order to simplify the guards within the headers, the following
// implementation private test macros have been created. Applications
// must NOT use these private test macros as unexpected results will
// occur.
//
// Note that in general, the use of these private macros is cumulative.
// For example, the use of _XPG3 with no other restrictions on the X/Open
// namespace will make the symbols visible for XPG3 through XPG6
// compilation environments. The use of _XPG4_2 with no other X/Open
// namespace restrictions indicates that the symbols were introduced in
// XPG4v2 and are therefore visible for XPG4v2 through XPG6 compilation
// environments, but not for XPG3 or XPG4 compilation environments.
//
// _XPG3    X/Open Portability Guide, Issue 3 (XPG3)
// _XPG4    X/Open CAE Specification, Issue 4 (XPG4)
// _XPG4_2  X/Open CAE Specification, Issue 4, Version 2 (XPG4v2/UNIX 95/SUS)
// _XPG5    X/Open CAE Specification, Issue 5 (XPG5/UNIX 98/SUSv2)
// _XPG6    Open Group Technical Standard, Issue 6 (XPG6/UNIX 03/SUSv3)
// _XPG7    Open Group Technical Standard, Issue 7 (XPG7/UNIX 08/SUSv4)

// X/Open Portability Guide, Issue 3

// _XOPEN_VERSION is defined by the X/Open specifications and is not
// normally defined by the application, except in the case of an XPG4
// application.  On the implementation side, _XOPEN_VERSION defined with
// the value of 3 indicates an XPG3 application. _XOPEN_VERSION defined
// with the value of 4 indicates an XPG4 or XPG4v2 (UNIX 95) application.
// _XOPEN_VERSION  defined with a value of 500 indicates an XPG5 (UNIX 98)
// application and with a value of 600 indicates an XPG6 (UNIX 03)
// application and with a value of 700 indicates an XPG7 (UNIX 08).
// The appropriate version is determined by the use of the
// feature test macros described earlier.  The value of _XOPEN_VERSION
// defaults to 3 otherwise indicating support for XPG3 applications.

// ANSI C and ISO 9899:1990 say the type long long doesn't exist in strictly
// conforming environments.  ISO 9899:1999 says it does.
//
// The presence of _LONGLONG_TYPE says "long long exists" which is therefore
// defined in all but strictly conforming environments that disallow it.

// The following macro defines a value for the ISO C99 restrict
// keyword so that _RESTRICT_KYWD resolves to "restrict" if
// an ISO C99 compiler is used, "__restrict" for c++ and "" (null string)
// if any other compiler is used. This allows for the use of single
// prototype declarations regardless of compiler version.

// The following macro defines a value for the ISO C11 _Noreturn
// keyword so that _NORETURN_KYWD resolves to "_Noreturn" if
// an ISO C11 compiler is used and "" (null string) if any other
// compiler is used. This allows for the use of single prototype
// declarations regardless of compiler version.

// ISO/IEC 9899:2011 Annex K

// The following macro indicates header support for the ANSI C++
// standard.  The ISO/IEC designation for this is ISO/IEC FDIS 14882.

// The following macro indicates header support for the C99 standard,
// ISO/IEC 9899:1999, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011 Annex K, Programming Languages - C.

// The following macro indicates header support for DTrace. The value is an
// integer that corresponds to the major version number for DTrace.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2003 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// An application should not include this header directly.  Instead it
// should be included only through the inclusion of other Sun headers.
//
// The contents of this header is limited to identifiers specified in the
// C Standard.  Any new identifiers specified in future amendments to the
// C Standard must be placed in this header.  If these new identifiers
// are required to also be in the C++ Standard "std" namespace, then for
// anything other than macro definitions, corresponding "using" directives
// must also be added to <limits.h>.

// Sizes of integral types

// min value of a "long int"
// max value of a "long int"
// max value of "unsigned long int"
// min value of a long long
// max value of a long long
// max value of "unsigned long long

// This file and its contents are supplied under the terms of the
// Common Development and Distribution License ("CDDL"), version 1.0.
// You may only use this file in accordance with the terms of version
// 1.0 of the CDDL.
//
// A full copy of the text of the CDDL should have accompanied this
// source.  A copy of the CDDL is also available via the Internet at
// http://www.illumos.org/license/CDDL.
// Copyright 2015 Joyent, Inc.  All rights reserved.

// Include fixed width type limits as proposed by the ISO/JTC1/SC22/WG14 C
// committee's working draft for the revision of the current ISO C standard,
// ISO/IEC 9899:1990 Programming language - C.  These are not currently
// required by any standard but constitute a useful, general purpose set
// of type definitions and limits which is namespace clean with respect to
// all standards.
//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/int_limits.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_limits.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> as defined in the ISO C standard, ISO/IEC 9899:1999
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<limits.h>		This nested inclusion is disabled for strictly
//				ANSI-C conforming compilations.  The *_MIN
//				definitions are not visible to POSIX or XPG
//				conforming applications (due to what may be
//				a bug in the specification - this is under
//				investigation)
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Limits
//
// The following define the limits for the types defined in <sys/int_types.h>.
//
// INTMAX_MIN (minimum value of the largest supported signed integer type),
// INTMAX_MAX (maximum value of the largest supported signed integer type),
// and UINTMAX_MAX (maximum value of the largest supported unsigned integer
// type) can be set to implementation defined limits.
//
// NOTE : A programmer can test to see whether an implementation supports
// a particular size of integer by testing if the macro that gives the
// maximum for that datatype is defined. For example, if #ifdef UINT64_MAX
// tests false, the implementation does not support unsigned 64 bit integers.
//
// The type of these macros is intentionally unspecified.
//
// The types int8_t, int_least8_t, and int_fast8_t are not defined for ISAs
// where the ABI specifies "char" as unsigned when the translation mode is
// not ANSI-C.

// The following 2 macros are provided for testing whether the types
// intptr_t and uintptr_t (integers large enough to hold a void *) are
// defined in this header. They are needed in case the architecture can't
// represent a pointer in any standard integral type.

// Maximum limits of ptrdiff_t defined in <sys/types.h>

// Maximum value of a "size_t".  SIZE_MAX was previously defined
// in <limits.h>, however, the standards specify it be defined
// in <stdint.h>. The <stdint.h> headers includes this header as
// does <limits.h>. The value of SIZE_MAX should not deviate
// from the value of ULONG_MAX defined <sys/types.h>.

// Maximum limit of sig_atomic_t defined in <sys/types.h>

// Maximum limit of wchar_t. The WCHAR_* macros are also
// defined in <iso/wchar_iso.h>, but inclusion of that header
// will break ISO/IEC C namespace.

// Maximum limit of wint_t

// It is probably a bug in the POSIX specification (IEEE-1003.1-1990) that
// when including <limits.h> that the suffix _MAX is reserved but not the
// suffix _MIN.  However, until that issue is resolved....

// Minimum value of a pointer-holding signed integer type

// Minimum limits of ptrdiff_t defined in <sys/types.h>

// Minimum limit of sig_atomic_t defined in <sys/types.h>

// Minimum limit of wchar_t. The WCHAR_* macros are also
// defined in <iso/wchar_iso.h>, but inclusion of that header
// will break ISO/IEC C namespace.

// Minimum limit of wint_t

// ARG_MAX is calculated as follows:
// NCARGS - space for other stuff on initial stack
// like aux vectors, saved registers, etc..

// These two symbols have their historical values, the actual buffer is
// larger.

// POSIX conformant definitions - An implementation may define
// other symbols which reflect the actual implementation. Alternate
// definitions may not be as restrictive as the POSIX definitions.
// POSIX.1c conformant
// UNIX 03 conformant

// POSIX.2 and XPG4-XSH4 conformant definitions

// UNIX 03 conformant

// For dual definitions for PASS_MAX and sysconf.c

// NLS printf() and scanf()

// Marked as LEGACY in SUSv2 and removed in UNIX 03
// of a double
// of a "float"

// Marked as LEGACY in SUSv1 and removed in SUSv2
// of a double
// of a float

// POSIX 1003.1a, section 2.9.5, table 2-5 contains [NAME_MAX] and the
// related text states:
//
// A definition of one of the values from Table 2-5 shall be omitted from the
// <limits.h> on specific implementations where the corresponding value is
// equal to or greater than the stated minimum, but where the value can vary
// depending on the file to which it is applied. The actual value supported for
// a specific pathname shall be provided by the pathconf() (5.7.1) function.
//
// This is clear that any machine supporting multiple file system types
// and/or a network should not include this define, regardless of protection
// by the _POSIX_SOURCE and _POSIX_C_SOURCE flags. We chose to ignore that
// and provide it anyway for compatibility with other platforms that don't
// follow the spec as precisely as they should. Its usage is discouraged.

type Clock_t = int64 /* limits.h:292:14 */ // System Private interface to sysconf()

// Increased for illumos

// Copyright (C) 1991-2022 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// Number of bits in a `char'.

// Maximum length of a multibyte character.

// Minimum and maximum values a `signed char' can hold.

// Maximum value an `unsigned char' can hold.  (Minimum is 0).

// Minimum and maximum values a `char' can hold.

// Minimum and maximum values a `signed short int' can hold.

// Maximum value an `unsigned short int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed int' can hold.

// Maximum value an `unsigned int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long int' can hold.
//    (Same as `int').

// Maximum value an `unsigned long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// This administrivia gets added to the end of limits.h
//    if the system has its own version of limits.h.

var _ int8 /* gen.c:2:13: */
