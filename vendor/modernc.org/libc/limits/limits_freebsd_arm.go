// Code generated by 'ccgo limits/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o limits/limits_freebsd_arm.go -pkgname limits', DO NOT EDIT.

package limits

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ARG_MAX                              = 262144               // syslimits.h:54:1:
	BC_BASE_MAX                          = 99                   // limits.h:65:1:
	BC_DIM_MAX                           = 2048                 // limits.h:66:1:
	BC_SCALE_MAX                         = 99                   // limits.h:67:1:
	BC_STRING_MAX                        = 1000                 // limits.h:68:1:
	CHARCLASS_NAME_MAX                   = 14                   // limits.h:69:1:
	CHAR_BIT                             = 8                    // limits.h:40:1:
	CHAR_MAX                             = 255                  // limits.h:48:1:
	CHAR_MIN                             = 0                    // limits.h:49:1:
	CHILD_MAX                            = 40                   // syslimits.h:57:1:
	COLL_WEIGHTS_MAX                     = 10                   // limits.h:70:1:
	EXPR_NEST_MAX                        = 32                   // limits.h:71:1:
	GID_MAX                              = 4294967295           // limits.h:85:1:
	INT_MAX                              = 2147483647           // limits.h:60:1:
	INT_MIN                              = -2147483648          // limits.h:61:1:
	IOV_MAX                              = 1024                 // syslimits.h:70:1:
	LINE_MAX                             = 2048                 // limits.h:72:1:
	LLONG_MAX                            = 9223372036854775807  // limits.h:69:1:
	LLONG_MIN                            = -9223372036854775808 // limits.h:70:1:
	LONG_BIT                             = 32                   // limits.h:94:1:
	LONG_MAX                             = 2147483647           // limits.h:64:1:
	LONG_MIN                             = -2147483648          // limits.h:65:1:
	MAX_CANON                            = 255                  // syslimits.h:59:1:
	MAX_INPUT                            = 255                  // syslimits.h:60:1:
	MB_LEN_MAX                           = 6                    // limits.h:141:1:
	MQ_PRIO_MAX                          = 64                   // limits.h:99:1:
	NAME_MAX                             = 255                  // syslimits.h:61:1:
	NGROUPS_MAX                          = 1023                 // syslimits.h:63:1:
	NL_ARGMAX                            = 4096                 // limits.h:125:1:
	NL_LANGMAX                           = 31                   // limits.h:137:1:
	NL_MSGMAX                            = 32767                // limits.h:126:1:
	NL_NMAX                              = 1                    // limits.h:138:1:
	NL_SETMAX                            = 255                  // limits.h:127:1:
	NL_TEXTMAX                           = 2048                 // limits.h:128:1:
	OFF_MAX                              = 9223372036854775807  // limits.h:80:1:
	OFF_MIN                              = -9223372036854775808 // limits.h:81:1:
	OPEN_MAX                             = 64                   // syslimits.h:66:1:
	PASS_MAX                             = 128                  // limits.h:135:1:
	PATH_MAX                             = 1024                 // syslimits.h:68:1:
	PIPE_BUF                             = 512                  // syslimits.h:69:1:
	QUAD_MAX                             = 9223372036854775807  // limits.h:89:1:
	QUAD_MIN                             = -9223372036854775808 // limits.h:90:1:
	RE_DUP_MAX                           = 255                  // limits.h:73:1:
	SCHAR_MAX                            = 127                  // limits.h:42:1:
	SCHAR_MIN                            = -128                 // limits.h:43:1:
	SHRT_MAX                             = 32767                // limits.h:56:1:
	SHRT_MIN                             = -32768               // limits.h:57:1:
	SIZE_T_MAX                           = 4294967295           // limits.h:78:1:
	SSIZE_MAX                            = 2147483647           // limits.h:74:1:
	UCHAR_MAX                            = 255                  // limits.h:45:1:
	UID_MAX                              = 4294967295           // limits.h:86:1:
	UINT_MAX                             = 4294967295           // limits.h:59:1:
	ULLONG_MAX                           = 18446744073709551615 // limits.h:68:1:
	ULONG_MAX                            = 4294967295           // limits.h:63:1:
	UQUAD_MAX                            = 18446744073709551615 // limits.h:88:1:
	USHRT_MAX                            = 65535                // limits.h:55:1:
	WORD_BIT                             = 32                   // limits.h:95:1:
	X_FILE_OFFSET_BITS                   = 64                   // <builtin>:25:1:
	X_ILP32                              = 1                    // <predefined>:1:1:
	X_LIMITS_H_                          = 0                    // limits.h:36:1:
	X_MACHINE__LIMITS_H_                 = 0                    // _limits.h:36:1:
	X_Nonnull                            = 0                    // cdefs.h:790:1:
	X_Null_unspecified                   = 0                    // cdefs.h:792:1:
	X_Nullable                           = 0                    // cdefs.h:791:1:
	X_POSIX2_BC_BASE_MAX                 = 99                   // limits.h:75:1:
	X_POSIX2_BC_DIM_MAX                  = 2048                 // limits.h:76:1:
	X_POSIX2_BC_SCALE_MAX                = 99                   // limits.h:77:1:
	X_POSIX2_BC_STRING_MAX               = 1000                 // limits.h:78:1:
	X_POSIX2_CHARCLASS_NAME_MAX          = 14                   // limits.h:79:1:
	X_POSIX2_COLL_WEIGHTS_MAX            = 2                    // limits.h:80:1:
	X_POSIX2_EQUIV_CLASS_MAX             = 2                    // limits.h:81:1:
	X_POSIX2_EXPR_NEST_MAX               = 32                   // limits.h:82:1:
	X_POSIX2_LINE_MAX                    = 2048                 // limits.h:83:1:
	X_POSIX2_RE_DUP_MAX                  = 255                  // limits.h:84:1:
	X_POSIX_AIO_LISTIO_MAX               = 2                    // limits.h:89:1:
	X_POSIX_AIO_MAX                      = 1                    // limits.h:90:1:
	X_POSIX_ARG_MAX                      = 4096                 // limits.h:41:1:
	X_POSIX_CHILD_MAX                    = 25                   // limits.h:51:1:
	X_POSIX_CLOCKRES_MIN                 = 20000000             // limits.h:100:1:
	X_POSIX_DELAYTIMER_MAX               = 32                   // limits.h:91:1:
	X_POSIX_HOST_NAME_MAX                = 255                  // limits.h:110:1:
	X_POSIX_LINK_MAX                     = 8                    // limits.h:42:1:
	X_POSIX_LOGIN_NAME_MAX               = 9                    // limits.h:111:1:
	X_POSIX_MAX_CANON                    = 255                  // limits.h:43:1:
	X_POSIX_MAX_INPUT                    = 255                  // limits.h:44:1:
	X_POSIX_MQ_OPEN_MAX                  = 8                    // limits.h:92:1:
	X_POSIX_MQ_PRIO_MAX                  = 32                   // limits.h:93:1:
	X_POSIX_NAME_MAX                     = 14                   // limits.h:45:1:
	X_POSIX_NGROUPS_MAX                  = 8                    // limits.h:52:1:
	X_POSIX_OPEN_MAX                     = 20                   // limits.h:53:1:
	X_POSIX_PATH_MAX                     = 256                  // limits.h:54:1:
	X_POSIX_PIPE_BUF                     = 512                  // limits.h:46:1:
	X_POSIX_RE_DUP_MAX                   = 255                  // limits.h:121:1:
	X_POSIX_RTSIG_MAX                    = 8                    // limits.h:94:1:
	X_POSIX_SEM_NSEMS_MAX                = 256                  // limits.h:95:1:
	X_POSIX_SEM_VALUE_MAX                = 32767                // limits.h:96:1:
	X_POSIX_SIGQUEUE_MAX                 = 32                   // limits.h:97:1:
	X_POSIX_SSIZE_MAX                    = 32767                // limits.h:47:1:
	X_POSIX_SS_REPL_MAX                  = 4                    // limits.h:112:1:
	X_POSIX_STREAM_MAX                   = 8                    // limits.h:48:1:
	X_POSIX_SYMLINK_MAX                  = 255                  // limits.h:113:1:
	X_POSIX_SYMLOOP_MAX                  = 8                    // limits.h:114:1:
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS = 4                    // limits.h:104:1:
	X_POSIX_THREAD_KEYS_MAX              = 128                  // limits.h:105:1:
	X_POSIX_THREAD_THREADS_MAX           = 64                   // limits.h:106:1:
	X_POSIX_TIMER_MAX                    = 32                   // limits.h:98:1:
	X_POSIX_TRACE_EVENT_NAME_MAX         = 30                   // limits.h:115:1:
	X_POSIX_TRACE_NAME_MAX               = 8                    // limits.h:116:1:
	X_POSIX_TRACE_SYS_MAX                = 8                    // limits.h:117:1:
	X_POSIX_TRACE_USER_EVENT_MAX         = 32                   // limits.h:118:1:
	X_POSIX_TTY_NAME_MAX                 = 9                    // limits.h:119:1:
	X_POSIX_TZNAME_MAX                   = 6                    // limits.h:55:1:
	X_SYS_CDEFS_H_                       = 0                    // cdefs.h:39:1:
	X_SYS_LIMITS_H_                      = 0                    // limits.h:35:1:
	X_SYS_SYSLIMITS_H_                   = 0                    // syslimits.h:36:1:
	X_XOPEN_IOV_MAX                      = 16                   // limits.h:132:1:
	X_XOPEN_NAME_MAX                     = 255                  // limits.h:133:1:
	X_XOPEN_PATH_MAX                     = 1024                 // limits.h:134:1:
	Unix                                 = 1                    // <predefined>:367:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = uint32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	8.2 (Berkeley) 1/4/94
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// Testing against Clang-specific extensions.

// This code has been put in place to help reduce the addition of
// compiler specific defines in FreeBSD code.  It helps to aid in
// having a compiler-agnostic source tree.

// Compiler memory barriers, specific to gcc and clang.

// XXX: if __GNUC__ >= 2: not tested everywhere originally, where replaced

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky to use if it must work in non-ANSI
// mode -- there must be no spaces between its arguments, and for nested
// __CONCAT's, all the __CONCAT's must be at the left.  __CONCAT can also
// concatenate double-quoted strings produced by the __STRING macro, but
// this only works with ANSI C.
//
// __XSTRING is like __STRING, but it expands any macros in its argument
// first.  It is only available with ANSI C.

// Compiler-dependent macros to help declare dead (non-returning) and
// pure (no side effects) functions, and unused variables.  They are
// null except for versions of gcc that are known to support the features
// properly (old versions of gcc-2 supported the dead and pure features
// in a different (wrong) way).  If we do not provide an implementation
// for a given compiler, let the compile fail if it is told to use
// a feature that we cannot live without.

// Keywords added in C11.

// Emulation of C11 _Generic().  Unlike the previously defined C11
// keywords, it is not possible to implement this using exactly the same
// syntax.  Therefore implement something similar under the name
// __generic().  Unlike _Generic(), this macro can only distinguish
// between a single type, so it requires nested invocations to
// distinguish multiple cases.

// C99 Static array indices in function parameter declarations.  Syntax such as:
// void bar(int myArray[static 10]);
// is allowed in C99 but not in C++.  Define __min_size appropriately so
// headers using it can be compiled in either language.  Use like this:
// void bar(int myArray[__min_size(10)]);

// XXX: should use `#if __STDC_VERSION__ < 199901'.

// C++11 exposes a load of C99 stuff

// GCC 2.95 provides `__restrict' as an extension to C90 to support the
// C99-specific `restrict' type qualifier.  We happen to use `__restrict' as
// a way to define the `restrict' type qualifier without disturbing older
// software that is unaware of C99 keywords.

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// We define this here since <stddef.h>, <sys/queue.h>, and <sys/types.h>
// require it.

// Given the pointer x to the member m of the struct s, return
// a pointer to the containing structure.  When using GCC, we first
// assign pointer x to a local variable, to check that its type is
// compatible with member m.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly (old versions of gcc-2
// didn't permit keeping the keywords out of the application namespace).

// Compiler-dependent macros that rely on FreeBSD-specific extensions.

// Embed the rcs id of a source file in the resulting library.  Note that in
// more recent ELF binutils, we use .ident allowing the ID to be stripped.
// Usage:
//	__FBSDID("$FreeBSD$");

// -
// The following definitions are an extension of the behavior originally
// implemented in <sys/_posix.h>, but with a different level of granularity.
// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1		1003.1-1990
//  _POSIX_C_SOURCE == 2		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309		1003.1b-1993
//  _POSIX_C_SOURCE == 199506		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112		1003.1-2001
//  _POSIX_C_SOURCE == 200809		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.
//
// Our macros begin with two underscores to avoid namespace screwage.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.
// -
// Deal with _ANSI_SOURCE:
// If it is defined, and no other compilation environment is explicitly
// requested, then define our internal feature-test macros to zero.  This
// makes no difference to the preprocessor (undefined symbols in preprocessing
// expressions are defined to have value zero), but makes it more convenient for
// a test program to print out the values.
//
// If a program mistakenly defines _ANSI_SOURCE and some other macro such as
// _POSIX_C_SOURCE, we will assume that it wants the broader compilation
// environment (and in fact we will never get here).

// User override __EXT1_VISIBLE

// Old versions of GCC use non-standard ARM arch symbols; acle-compat.h
// translates them to __ARM_ARCH and the modern feature symbols defined by ARM.

// Nullability qualifiers: currently only supported by Clang.

// Type Safety Checking
//
// Clang provides additional attributes to enable checking type safety
// properties that cannot be enforced by the C type system.

// Lock annotations.
//
// Clang provides support for doing basic thread-safety tests at
// compile-time, by marking which locks will/should be held when
// entering/leaving a functions.
//
// Furthermore, it is also possible to annotate variables and structure
// members to enforce that they are only accessed when certain locks are
// held.

// Structure implements a lock.

// Function acquires an exclusive or shared lock.

// Function attempts to acquire an exclusive or shared lock.

// Function releases a lock.

// Function asserts that an exclusive or shared lock is held.

// Function requires that an exclusive or shared lock is or is not held.

// Function should not be analyzed.

// Function or variable should not be sanitized, e.g., by AddressSanitizer.
// GCC has the nosanitize attribute, but as a function attribute only, and
// warns on use as a variable attribute.

// Guard variables and structure members by lock.

// Alignment builtins for better type checking and improved code generation.
// Provide fallback versions for other compilers (GCC/Clang < 10):

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	8.3 (Berkeley) 1/4/94
// $FreeBSD$

// According to ANSI (section 2.2.4.2), the values below must be usable by
// #if preprocessing directives.  Additionally, the expression must have the
// same type as would an expression that is an object of the corresponding
// type converted according to the integral promotions.  The subtraction for
// INT_MIN, etc., is so the value is not unsigned; e.g., 0x80000000 is an
// unsigned int for 32-bit two's complement ANSI compilers (section 3.1.3.2).

// max value for an unsigned long long

// Quads and long longs are the same size.  Ensure they stay in sync.

// Minimum signal stack size.

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)syslimits.h	8.1 (Berkeley) 6/2/93
// $FreeBSD$

// Do not add any new variables here.  (See the comment at the end of
// the file for why.)

// We leave the following values undefined to force applications to either
// assume conservative values or call sysconf() to get the current value.
//
// HOST_NAME_MAX
//
// (We should do this for most of the values currently defined here,
// but many programs are not prepared to deal with this yet.)

var _ uint8 /* gen.c:2:13: */
