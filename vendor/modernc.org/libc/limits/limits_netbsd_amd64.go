// Code generated by 'ccgo limits/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o limits/limits_netbsd_amd64.go -pkgname limits', DO NOT EDIT.

package limits

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ARG_MAX                              = 262144               // syslimits.h:45:1:
	BC_BASE_MAX                          = 2147483647           // syslimits.h:63:1:
	BC_DIM_MAX                           = 65535                // syslimits.h:64:1:
	BC_SCALE_MAX                         = 2147483647           // syslimits.h:65:1:
	BC_STRING_MAX                        = 2147483647           // syslimits.h:66:1:
	CHARCLASS_NAME_MAX                   = 14                   // limits.h:125:1:
	CHAR_BIT                             = 8                    // limits.h:41:1:
	CHAR_MAX                             = 127                  // limits.h:163:1:
	CHAR_MIN                             = -128                 // limits.h:162:1:
	CHILD_MAX                            = 160                  // syslimits.h:47:1:
	COLL_WEIGHTS_MAX                     = 2                    // syslimits.h:67:1:
	DBL_DIG                              = 15                   // limits.h:85:1:
	EXPR_NEST_MAX                        = 32                   // syslimits.h:68:1:
	FLT_DIG                              = 6                    // limits.h:89:1:
	GID_MAX                              = 2147483647           // syslimits.h:49:1:
	INT_MAX                              = 0x7fffffff           // limits.h:52:1:
	INT_MIN                              = -2147483648          // limits.h:53:1:
	IOV_MAX                              = 1024                 // syslimits.h:84:1:
	LINE_MAX                             = 2048                 // syslimits.h:69:1:
	LINK_MAX                             = 32767                // syslimits.h:50:1:
	LLONG_MAX                            = 0x7fffffffffffffff   // limits.h:62:1:
	LLONG_MIN                            = -9223372036854775808 // limits.h:63:1:
	LOGIN_NAME_MAX                       = 17                   // syslimits.h:77:1:
	LONG_BIT                             = 64                   // limits.h:82:1:
	LONG_MAX                             = 0x7fffffffffffffff   // limits.h:56:1:
	LONG_MIN                             = -9223372036854775808 // limits.h:57:1:
	MAX_CANON                            = 255                  // syslimits.h:51:1:
	MAX_INPUT                            = 255                  // syslimits.h:52:1:
	MB_LEN_MAX                           = 32                   // limits.h:145:1:
	NAME_MAX                             = 511                  // syslimits.h:53:1:
	NGROUPS_MAX                          = 16                   // syslimits.h:55:1:
	NL_ARGMAX                            = 9                    // limits.h:126:1:
	NL_LANGMAX                           = 14                   // limits.h:127:1:
	NL_MSGMAX                            = 32767                // limits.h:128:1:
	NL_NMAX                              = 1                    // limits.h:129:1:
	NL_SETMAX                            = 255                  // limits.h:130:1:
	NL_TEXTMAX                           = 2048                 // limits.h:131:1:
	NZERO                                = 20                   // syslimits.h:85:1:
	OPEN_MAX                             = 128                  // syslimits.h:58:1:
	PASS_MAX                             = 128                  // limits.h:123:1:
	PATH_MAX                             = 1024                 // syslimits.h:60:1:
	PIPE_BUF                             = 512                  // syslimits.h:61:1:
	PTHREAD_DESTRUCTOR_ITERATIONS        = 4                    // limits.h:90:1:
	PTHREAD_KEYS_MAX                     = 256                  // limits.h:91:1:
	PTHREAD_THREADS_MAX                  = 64                   // limits.h:93:1:
	QUAD_MAX                             = 0x7fffffffffffffff   // limits.h:75:1:
	QUAD_MIN                             = -9223372036854775808 // limits.h:76:1:
	RE_DUP_MAX                           = 255                  // syslimits.h:70:1:
	SCHAR_MAX                            = 0x7f                 // limits.h:44:1:
	SCHAR_MIN                            = -128                 // limits.h:45:1:
	SHRT_MAX                             = 0x7fff               // limits.h:48:1:
	SHRT_MIN                             = -32768               // limits.h:49:1:
	SIZE_T_MAX                           = 18446744073709551615 // limits.h:72:1:
	SSIZE_MAX                            = 9223372036854775807  // limits.h:68:1:
	SSIZE_MIN                            = -9223372036854775808 // limits.h:71:1:
	TMP_MAX                              = 308915776            // limits.h:139:1:
	UCHAR_MAX                            = 0xff                 // limits.h:43:1:
	UID_MAX                              = 2147483647           // syslimits.h:56:1:
	UINT_MAX                             = 0xffffffff           // limits.h:51:1:
	ULLONG_MAX                           = 0xffffffffffffffff   // limits.h:61:1:
	ULONG_MAX                            = 0xffffffffffffffff   // limits.h:55:1:
	UQUAD_MAX                            = 0xffffffffffffffff   // limits.h:74:1:
	USHRT_MAX                            = 0xffff               // limits.h:47:1:
	WORD_BIT                             = 32                   // limits.h:83:1:
	X_FILE_OFFSET_BITS                   = 64                   // <builtin>:25:1:
	X_GETGR_R_SIZE_MAX                   = 1024                 // limits.h:134:1:
	X_GETPW_R_SIZE_MAX                   = 1024                 // limits.h:135:1:
	X_LIMITS_H_                          = 0                    // limits.h:35:1:
	X_LP64                               = 1                    // <predefined>:268:1:
	X_NETBSD_SOURCE                      = 1                    // featuretest.h:70:1:
	X_POSIX2_BC_BASE_MAX                 = 99                   // limits.h:103:1:
	X_POSIX2_BC_DIM_MAX                  = 2048                 // limits.h:104:1:
	X_POSIX2_BC_SCALE_MAX                = 99                   // limits.h:105:1:
	X_POSIX2_BC_STRING_MAX               = 1000                 // limits.h:106:1:
	X_POSIX2_CHARCLASS_NAME_MAX          = 14                   // limits.h:107:1:
	X_POSIX2_COLL_WEIGHTS_MAX            = 2                    // limits.h:108:1:
	X_POSIX2_EXPR_NEST_MAX               = 32                   // limits.h:109:1:
	X_POSIX2_LINE_MAX                    = 2048                 // limits.h:110:1:
	X_POSIX2_RE_DUP_MAX                  = 255                  // limits.h:111:1:
	X_POSIX_AIO_LISTIO_MAX               = 2                    // limits.h:41:1:
	X_POSIX_AIO_MAX                      = 1                    // limits.h:42:1:
	X_POSIX_ARG_MAX                      = 4096                 // limits.h:43:1:
	X_POSIX_CHILD_MAX                    = 25                   // limits.h:44:1:
	X_POSIX_DELAYTIMER_MAX               = 32                   // limits.h:99:1:
	X_POSIX_HOST_NAME_MAX                = 255                  // limits.h:45:1:
	X_POSIX_LINK_MAX                     = 8                    // limits.h:46:1:
	X_POSIX_LOGIN_NAME_MAX               = 9                    // limits.h:47:1:
	X_POSIX_MAX_CANON                    = 255                  // limits.h:48:1:
	X_POSIX_MAX_INPUT                    = 255                  // limits.h:49:1:
	X_POSIX_MQ_OPEN_MAX                  = 8                    // limits.h:50:1:
	X_POSIX_MQ_PRIO_MAX                  = 32                   // limits.h:51:1:
	X_POSIX_NAME_MAX                     = 14                   // limits.h:52:1:
	X_POSIX_NGROUPS_MAX                  = 8                    // limits.h:53:1:
	X_POSIX_OPEN_MAX                     = 20                   // limits.h:54:1:
	X_POSIX_PATH_MAX                     = 256                  // limits.h:55:1:
	X_POSIX_PIPE_BUF                     = 512                  // limits.h:56:1:
	X_POSIX_REALTIME_SIGNALS             = 200112               // limits.h:98:1:
	X_POSIX_RE_DUP_MAX                   = 255                  // limits.h:57:1:
	X_POSIX_SEM_NSEMS_MAX                = 256                  // limits.h:96:1:
	X_POSIX_SIGQUEUE_MAX                 = 32                   // limits.h:97:1:
	X_POSIX_SSIZE_MAX                    = 32767                // limits.h:58:1:
	X_POSIX_STREAM_MAX                   = 8                    // limits.h:59:1:
	X_POSIX_SYMLINK_MAX                  = 255                  // limits.h:60:1:
	X_POSIX_SYMLOOP_MAX                  = 8                    // limits.h:61:1:
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS = 4                    // limits.h:81:1:
	X_POSIX_THREAD_KEYS_MAX              = 128                  // limits.h:82:1:
	X_POSIX_THREAD_THREADS_MAX           = 64                   // limits.h:83:1:
	X_POSIX_TIMER_MAX                    = 32                   // limits.h:95:1:
	X_POSIX_TTY_NAME_MAX                 = 9                    // limits.h:100:1:
	X_POSIX_TZNAME_MAX                   = 6                    // limits.h:101:1:
	X_SYS_SYSLIMITS_H_                   = 0                    // syslimits.h:35:1:
	X_X86_64_LIMITS_H_                   = 0                    // limits.h:35:1:
	X_XOPEN_IOV_MAX                      = 16                   // limits.h:119:1:
	X_XOPEN_NAME_MAX                     = 256                  // limits.h:120:1:
	X_XOPEN_PATH_MAX                     = 1024                 // limits.h:121:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$NetBSD: limits.h,v 1.40 2016/08/04 06:43:43 christos Exp $

// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	8.2 (Berkeley) 1/4/94

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// We have not implemented these yet
//
// _POSIX_THREAD_ATTR_STACKADDR
// _POSIX_THREAD_ATTR_STACKSIZE
// _POSIX_THREAD_CPUTIME
// _POSIX_THREAD_PRIORITY_SCHEDULING
// _POSIX_THREAD_PRIO_INHERIT
// _POSIX_THREAD_PRIO_PROTECT
// _POSIX_THREAD_PROCESS_SHARED
// _POSIX_THREAD_SAFE_FUNCTIONS
// _POSIX_THREAD_SPORADIC_SERVER

// The following 3 are defined in
// Open Group Base Specifications Issue 7

// These are the correct names, defined in terms of the above
// except for PTHREAD_KEYS_MAX which is bigger than standard
// mandated minimum value _POSIX_THREAD_KEYS_MAX.
// Not yet: PTHREAD_STACK_MIN

// X/Open CAE Specifications,
// adopted in IEEE Std 1003.1-2001 XSI.

// IEEE Std 1003.1-2001 TSF

// Always ensure that this is consistent with <stdio.h>

// X/Open Extended API set 2 (a.k.a. C063)
// This hides unimplemented functions from GNU configure until
// we are done implementing them.

//	$NetBSD: limits.h,v 1.15 2019/01/21 20:22:48 dholland Exp $

// Copyright (c) 1988 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	7.2 (Berkeley) 6/28/90

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: syslimits.h,v 1.28 2015/08/21 07:19:39 uebayasi Exp $

// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)syslimits.h	8.1 (Berkeley) 6/2/93

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// kept in sync with MAXNAMLEN

// IEEE Std 1003.1c-95, adopted in X/Open CAE Specification Issue 5 Version 2

// X/Open CAE Specification Issue 5 Version 2

var _ int8 /* gen.c:2:13: */
