// Code generated by 'ccgo termios/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o termios/termios_freebsd_386.go -pkgname termios', DO NOT EDIT.

package termios

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ALTWERASE            = 0x00000200 // _termios.h:171:1:
	B0                   = 0          // _termios.h:186:1:
	B1000000             = 1000000    // _termios.h:213:1:
	B110                 = 110        // _termios.h:189:1:
	B115200              = 115200     // _termios.h:208:1:
	B1200                = 1200       // _termios.h:195:1:
	B134                 = 134        // _termios.h:190:1:
	B14400               = 14400      // _termios.h:204:1:
	B150                 = 150        // _termios.h:191:1:
	B1500000             = 1500000    // _termios.h:214:1:
	B1800                = 1800       // _termios.h:196:1:
	B19200               = 19200      // _termios.h:200:1:
	B200                 = 200        // _termios.h:192:1:
	B2000000             = 2000000    // _termios.h:215:1:
	B230400              = 230400     // _termios.h:209:1:
	B2400                = 2400       // _termios.h:197:1:
	B2500000             = 2500000    // _termios.h:216:1:
	B28800               = 28800      // _termios.h:205:1:
	B300                 = 300        // _termios.h:193:1:
	B3000000             = 3000000    // _termios.h:217:1:
	B3500000             = 3500000    // _termios.h:218:1:
	B38400               = 38400      // _termios.h:201:1:
	B4000000             = 4000000    // _termios.h:219:1:
	B460800              = 460800     // _termios.h:210:1:
	B4800                = 4800       // _termios.h:198:1:
	B50                  = 50         // _termios.h:187:1:
	B500000              = 500000     // _termios.h:211:1:
	B57600               = 57600      // _termios.h:206:1:
	B600                 = 600        // _termios.h:194:1:
	B7200                = 7200       // _termios.h:203:1:
	B75                  = 75         // _termios.h:188:1:
	B76800               = 76800      // _termios.h:207:1:
	B921600              = 921600     // _termios.h:212:1:
	B9600                = 9600       // _termios.h:199:1:
	BRKINT               = 0x00000002 // _termios.h:86:1:
	CBRK                 = 255        // ttydefaults.h:89:1:
	CCAR_OFLOW           = 0x00100000 // _termios.h:145:1:
	CCTS_OFLOW           = 0x00010000 // _termios.h:140:1:
	CDISCARD             = 15         // ttydefaults.h:84:1:
	CDSR_OFLOW           = 0x00080000 // _termios.h:144:1:
	CDSUSP               = 25         // ttydefaults.h:80:1:
	CDTR_IFLOW           = 0x00040000 // _termios.h:143:1:
	CEOF                 = 4          // ttydefaults.h:69:1:
	CEOL                 = 0xff       // ttydefaults.h:70:1:
	CEOT                 = 4          // ttydefaults.h:87:1:
	CERASE               = 127        // ttydefaults.h:71:1:
	CERASE2              = 8          // ttydefaults.h:72:1:
	CFLUSH               = 15         // ttydefaults.h:91:1:
	CIGNORE              = 0x00000001 // _termios.h:126:1:
	CINTR                = 3          // ttydefaults.h:73:1:
	CKILL                = 21         // ttydefaults.h:75:1:
	CLNEXT               = 22         // ttydefaults.h:83:1:
	CLOCAL               = 0x00008000 // _termios.h:138:1:
	CMIN                 = 1          // ttydefaults.h:76:1:
	CNO_RTSDTR           = 0x00200000 // _termios.h:146:1:
	CQUIT                = 28         // ttydefaults.h:77:1:
	CREAD                = 0x00000800 // _termios.h:134:1:
	CREPRINT             = 18         // ttydefaults.h:86:1:
	CRPRNT               = 18         // ttydefaults.h:90:1:
	CRTSCTS              = 196608     // _termios.h:141:1:
	CRTS_IFLOW           = 0x00020000 // _termios.h:142:1:
	CS5                  = 0x00000000 // _termios.h:129:1:
	CS6                  = 0x00000100 // _termios.h:130:1:
	CS7                  = 0x00000200 // _termios.h:131:1:
	CS8                  = 0x00000300 // _termios.h:132:1:
	CSIZE                = 0x00000300 // _termios.h:128:1:
	CSTART               = 17         // ttydefaults.h:81:1:
	CSTATUS              = 20         // ttydefaults.h:74:1:
	CSTOP                = 19         // ttydefaults.h:82:1:
	CSTOPB               = 0x00000400 // _termios.h:133:1:
	CSUSP                = 26         // ttydefaults.h:78:1:
	CTIME                = 0          // ttydefaults.h:79:1:
	CWERASE              = 23         // ttydefaults.h:85:1:
	ECHO                 = 0x00000008 // _termios.h:162:1:
	ECHOCTL              = 0x00000040 // _termios.h:166:1:
	ECHOE                = 0x00000002 // _termios.h:160:1:
	ECHOK                = 0x00000004 // _termios.h:161:1:
	ECHOKE               = 0x00000001 // _termios.h:158:1:
	ECHONL               = 0x00000010 // _termios.h:163:1:
	ECHOPRT              = 0x00000020 // _termios.h:165:1:
	EXTA                 = 19200      // _termios.h:220:1:
	EXTB                 = 38400      // _termios.h:221:1:
	EXTPROC              = 0x00000800 // _termios.h:174:1:
	FLUSHO               = 0x00800000 // _termios.h:177:1:
	H4DISC               = 7          // ttycom.h:135:1:
	HUPCL                = 0x00004000 // _termios.h:137:1:
	ICANON               = 0x00000100 // _termios.h:169:1:
	ICRNL                = 0x00000100 // _termios.h:93:1:
	IEXTEN               = 0x00000400 // _termios.h:173:1:
	IGNBRK               = 0x00000001 // _termios.h:85:1:
	IGNCR                = 0x00000080 // _termios.h:92:1:
	IGNPAR               = 0x00000004 // _termios.h:87:1:
	IMAXBEL              = 0x00002000 // _termios.h:100:1:
	INLCR                = 0x00000040 // _termios.h:91:1:
	INPCK                = 0x00000010 // _termios.h:89:1:
	IOCPARM_MASK         = 8191       // ioccom.h:49:1:
	IOCPARM_MAX          = 8192       // ioccom.h:54:1:
	IOCPARM_SHIFT        = 13         // ioccom.h:48:1:
	IOC_DIRMASK          = 3758096384 // ioccom.h:60:1:
	IOC_IN               = 0x80000000 // ioccom.h:58:1:
	IOC_INOUT            = 3221225472 // ioccom.h:59:1:
	IOC_OUT              = 0x40000000 // ioccom.h:57:1:
	IOC_VOID             = 0x20000000 // ioccom.h:56:1:
	ISIG                 = 0x00000080 // _termios.h:168:1:
	ISTRIP               = 0x00000020 // _termios.h:90:1:
	IXANY                = 0x00000800 // _termios.h:97:1:
	IXOFF                = 0x00000400 // _termios.h:95:1:
	IXON                 = 0x00000200 // _termios.h:94:1:
	MDMBUF               = 1048576    // termios.h:52:1:
	NCCS                 = 20         // _termios.h:78:1:
	NETGRAPHDISC         = 6          // ttycom.h:134:1:
	NOFLSH               = 0x80000000 // _termios.h:181:1:
	NOKERNINFO           = 0x02000000 // _termios.h:178:1:
	OCRNL                = 0x00000010 // _termios.h:117:1:
	ONLCR                = 0x00000002 // _termios.h:108:1:
	ONLRET               = 0x00000040 // _termios.h:119:1:
	ONOCR                = 0x00000020 // _termios.h:118:1:
	ONOEOT               = 0x00000008 // _termios.h:114:1:
	OPOST                = 0x00000001 // _termios.h:106:1:
	OXTABS               = 4          // termios.h:51:1:
	PARENB               = 0x00001000 // _termios.h:135:1:
	PARMRK               = 0x00000008 // _termios.h:88:1:
	PARODD               = 0x00002000 // _termios.h:136:1:
	PENDIN               = 0x20000000 // _termios.h:179:1:
	PPPDISC              = 5          // ttycom.h:133:1:
	SLIPDISC             = 4          // ttycom.h:132:1:
	TAB0                 = 0x00000000 // _termios.h:112:1:
	TAB3                 = 0x00000004 // _termios.h:113:1:
	TABDLY               = 0x00000004 // _termios.h:111:1:
	TCIFLUSH             = 1          // termios.h:69:1:
	TCIOFF               = 3          // termios.h:74:1:
	TCIOFLUSH            = 3          // termios.h:71:1:
	TCION                = 4          // termios.h:75:1:
	TCOFLUSH             = 2          // termios.h:70:1:
	TCOOFF               = 1          // termios.h:72:1:
	TCOON                = 2          // termios.h:73:1:
	TCSADRAIN            = 1          // termios.h:63:1:
	TCSAFLUSH            = 2          // termios.h:64:1:
	TCSANOW              = 0          // termios.h:62:1:
	TCSASOFT             = 0x10       // termios.h:66:1:
	TIOCM_CAR            = 64         // ttycom.h:103:1:
	TIOCM_CD             = 64         // ttycom.h:102:1:
	TIOCM_CTS            = 0040       // ttycom.h:98:1:
	TIOCM_DCD            = 0100       // ttycom.h:99:1:
	TIOCM_DSR            = 0400       // ttycom.h:101:1:
	TIOCM_DTR            = 0002       // ttycom.h:94:1:
	TIOCM_LE             = 0001       // ttycom.h:93:1:
	TIOCM_RI             = 0200       // ttycom.h:100:1:
	TIOCM_RNG            = 128        // ttycom.h:104:1:
	TIOCM_RTS            = 0004       // ttycom.h:95:1:
	TIOCM_SR             = 0020       // ttycom.h:97:1:
	TIOCM_ST             = 0010       // ttycom.h:96:1:
	TIOCPKT_DATA         = 0x00       // ttycom.h:111:1:
	TIOCPKT_DOSTOP       = 0x20       // ttycom.h:117:1:
	TIOCPKT_FLUSHREAD    = 0x01       // ttycom.h:112:1:
	TIOCPKT_FLUSHWRITE   = 0x02       // ttycom.h:113:1:
	TIOCPKT_IOCTL        = 0x40       // ttycom.h:118:1:
	TIOCPKT_NOSTOP       = 0x10       // ttycom.h:116:1:
	TIOCPKT_START        = 0x08       // ttycom.h:115:1:
	TIOCPKT_STOP         = 0x04       // ttycom.h:114:1:
	TOSTOP               = 0x00400000 // _termios.h:175:1:
	TTYDEF_CFLAG         = 19200      // ttydefaults.h:55:1:
	TTYDEF_IFLAG         = 11010      // ttydefaults.h:49:1:
	TTYDEF_LFLAG         = 1483       // ttydefaults.h:54:1:
	TTYDEF_LFLAG_ECHO    = 1483       // ttydefaults.h:52:1:
	TTYDEF_LFLAG_NOECHO  = 1408       // ttydefaults.h:51:1:
	TTYDEF_OFLAG         = 3          // ttydefaults.h:50:1:
	TTYDEF_SPEED         = 9600       // ttydefaults.h:56:1:
	TTYDISC              = 0          // ttycom.h:131:1:
	VDISCARD             = 15         // _termios.h:70:1:
	VDSUSP               = 11         // _termios.h:64:1:
	VEOF                 = 0          // _termios.h:45:1:
	VEOL                 = 1          // _termios.h:46:1:
	VEOL2                = 2          // _termios.h:48:1:
	VERASE               = 3          // _termios.h:50:1:
	VERASE2              = 7          // _termios.h:57:1:
	VINTR                = 8          // _termios.h:60:1:
	VKILL                = 5          // _termios.h:54:1:
	VLNEXT               = 14         // _termios.h:69:1:
	VMIN                 = 16         // _termios.h:72:1:
	VQUIT                = 9          // _termios.h:61:1:
	VREPRINT             = 6          // _termios.h:56:1:
	VSTART               = 12         // _termios.h:66:1:
	VSTATUS              = 18         // _termios.h:75:1:
	VSTOP                = 13         // _termios.h:67:1:
	VSUSP                = 10         // _termios.h:62:1:
	VTIME                = 17         // _termios.h:73:1:
	VWERASE              = 4          // _termios.h:52:1:
	X_FILE_OFFSET_BITS   = 64         // <builtin>:25:1:
	X_ILP32              = 1          // <predefined>:1:1:
	X_MACHINE__LIMITS_H_ = 0          // _limits.h:36:1:
	X_MACHINE__TYPES_H_  = 0          // _types.h:42:1:
	X_Nonnull            = 0          // cdefs.h:790:1:
	X_Null_unspecified   = 0          // cdefs.h:792:1:
	X_Nullable           = 0          // cdefs.h:791:1:
	X_PID_T_DECLARED     = 0          // termios.h:47:1:
	X_POSIX_VDISABLE     = 0xff       // _termios.h:80:1:
	X_SYS_CDEFS_H_       = 0          // cdefs.h:39:1:
	X_SYS_IOCCOM_H_      = 0          // ioccom.h:36:1:
	X_SYS_TTYCOM_H_      = 0          // ttycom.h:41:1:
	X_SYS_TTYDEFAULTS_H_ = 0          // ttydefaults.h:44:1:
	X_SYS__TERMIOS_H_    = 0          // _termios.h:36:1:
	X_SYS__TYPES_H_      = 0          // _types.h:32:1:
	X_SYS__WINSIZE_H_    = 0          // _winsize.h:36:1:
	X_TERMIOS_H_         = 0          // termios.h:36:1:
	I386                 = 1          // <predefined>:335:1:
	Unix                 = 1          // <predefined>:336:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)termios.h	8.3 (Berkeley) 3/28/94
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// Testing against Clang-specific extensions.

// This code has been put in place to help reduce the addition of
// compiler specific defines in FreeBSD code.  It helps to aid in
// having a compiler-agnostic source tree.

// Compiler memory barriers, specific to gcc and clang.

// XXX: if __GNUC__ >= 2: not tested everywhere originally, where replaced

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky to use if it must work in non-ANSI
// mode -- there must be no spaces between its arguments, and for nested
// __CONCAT's, all the __CONCAT's must be at the left.  __CONCAT can also
// concatenate double-quoted strings produced by the __STRING macro, but
// this only works with ANSI C.
//
// __XSTRING is like __STRING, but it expands any macros in its argument
// first.  It is only available with ANSI C.

// Compiler-dependent macros to help declare dead (non-returning) and
// pure (no side effects) functions, and unused variables.  They are
// null except for versions of gcc that are known to support the features
// properly (old versions of gcc-2 supported the dead and pure features
// in a different (wrong) way).  If we do not provide an implementation
// for a given compiler, let the compile fail if it is told to use
// a feature that we cannot live without.

// Keywords added in C11.

// Emulation of C11 _Generic().  Unlike the previously defined C11
// keywords, it is not possible to implement this using exactly the same
// syntax.  Therefore implement something similar under the name
// __generic().  Unlike _Generic(), this macro can only distinguish
// between a single type, so it requires nested invocations to
// distinguish multiple cases.

// C99 Static array indices in function parameter declarations.  Syntax such as:
// void bar(int myArray[static 10]);
// is allowed in C99 but not in C++.  Define __min_size appropriately so
// headers using it can be compiled in either language.  Use like this:
// void bar(int myArray[__min_size(10)]);

// XXX: should use `#if __STDC_VERSION__ < 199901'.

// C++11 exposes a load of C99 stuff

// GCC 2.95 provides `__restrict' as an extension to C90 to support the
// C99-specific `restrict' type qualifier.  We happen to use `__restrict' as
// a way to define the `restrict' type qualifier without disturbing older
// software that is unaware of C99 keywords.

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// We define this here since <stddef.h>, <sys/queue.h>, and <sys/types.h>
// require it.

// Given the pointer x to the member m of the struct s, return
// a pointer to the containing structure.  When using GCC, we first
// assign pointer x to a local variable, to check that its type is
// compatible with member m.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly (old versions of gcc-2
// didn't permit keeping the keywords out of the application namespace).

// Compiler-dependent macros that rely on FreeBSD-specific extensions.

// Embed the rcs id of a source file in the resulting library.  Note that in
// more recent ELF binutils, we use .ident allowing the ID to be stripped.
// Usage:
//	__FBSDID("$FreeBSD$");

// -
// The following definitions are an extension of the behavior originally
// implemented in <sys/_posix.h>, but with a different level of granularity.
// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1		1003.1-1990
//  _POSIX_C_SOURCE == 2		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309		1003.1b-1993
//  _POSIX_C_SOURCE == 199506		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112		1003.1-2001
//  _POSIX_C_SOURCE == 200809		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.
//
// Our macros begin with two underscores to avoid namespace screwage.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.
// -
// Deal with _ANSI_SOURCE:
// If it is defined, and no other compilation environment is explicitly
// requested, then define our internal feature-test macros to zero.  This
// makes no difference to the preprocessor (undefined symbols in preprocessing
// expressions are defined to have value zero), but makes it more convenient for
// a test program to print out the values.
//
// If a program mistakenly defines _ANSI_SOURCE and some other macro such as
// _POSIX_C_SOURCE, we will assume that it wants the broader compilation
// environment (and in fact we will never get here).

// User override __EXT1_VISIBLE

// Old versions of GCC use non-standard ARM arch symbols; acle-compat.h
// translates them to __ARM_ARCH and the modern feature symbols defined by ARM.

// Nullability qualifiers: currently only supported by Clang.

// Type Safety Checking
//
// Clang provides additional attributes to enable checking type safety
// properties that cannot be enforced by the C type system.

// Lock annotations.
//
// Clang provides support for doing basic thread-safety tests at
// compile-time, by marking which locks will/should be held when
// entering/leaving a functions.
//
// Furthermore, it is also possible to annotate variables and structure
// members to enforce that they are only accessed when certain locks are
// held.

// Structure implements a lock.

// Function acquires an exclusive or shared lock.

// Function attempts to acquire an exclusive or shared lock.

// Function releases a lock.

// Function asserts that an exclusive or shared lock is held.

// Function requires that an exclusive or shared lock is or is not held.

// Function should not be analyzed.

// Function or variable should not be sanitized, e.g., by AddressSanitizer.
// GCC has the nosanitize attribute, but as a function attribute only, and
// warns on use as a variable attribute.

// Guard variables and structure members by lock.

// Alignment builtins for better type checking and improved code generation.
// Provide fallback versions for other compilers (GCC/Clang < 10):

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)termios.h	8.3 (Berkeley) 3/28/94
// $FreeBSD$

// Special Control Characters
//
// Index into c_cc[] character array.
//
//	Name	     Subscript	Enabled by
//			7	   ex-spare 1
//			19	   spare 2

// Input flags - software input processing

// Output flags - software output processing

// Control flags - hardware control of terminal

// "Local" flags - dumping ground for other state
//
// Warning: some flags in this structure begin with
// the letter "I" and look like they belong in the
// input flag.

// Standard speeds

type Tcflag_t = uint32 /* _termios.h:224:22 */
type Cc_t = uint8      /* _termios.h:225:23 */
type Speed_t = uint32  /* _termios.h:226:22 */

type Termios = struct {
	Fc_iflag  Tcflag_t
	Fc_oflag  Tcflag_t
	Fc_cflag  Tcflag_t
	Fc_lflag  Tcflag_t
	Fc_cc     [20]Cc_t
	Fc_ispeed Speed_t
	Fc_ospeed Speed_t
} /* _termios.h:228:1 */

// -
// SPDX-License-Identifier: BSD-2-Clause-FreeBSD
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// -
// This file is in the public domain.
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-4-Clause
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	From: @(#)ansi.h	8.2 (Berkeley) 1/4/94
//	From: @(#)types.h	8.3 (Berkeley) 1/5/94
// $FreeBSD$

// -
// This file is in the public domain.
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	8.3 (Berkeley) 1/4/94
// $FreeBSD$

// According to ANSI (section 2.2.4.2), the values below must be usable by
// #if preprocessing directives.  Additionally, the expression must have the
// same type as would an expression that is an object of the corresponding
// type converted according to the integral promotions.  The subtraction for
// INT_MIN, etc., is so the value is not unsigned; e.g., 0x80000000 is an
// unsigned int for 32-bit two's complement ANSI compilers (section 3.1.3.2).

// max value for an unsigned long long

// Minimum signal stack size.

// Basic types upon which most other types are built.
type X__int8_t = int8     /* _types.h:55:22 */
type X__uint8_t = uint8   /* _types.h:56:24 */
type X__int16_t = int16   /* _types.h:57:17 */
type X__uint16_t = uint16 /* _types.h:58:25 */
type X__int32_t = int32   /* _types.h:59:15 */
type X__uint32_t = uint32 /* _types.h:60:23 */

type X__int64_t = int64 /* _types.h:66:20 */

type X__uint64_t = uint64 /* _types.h:68:28 */

// Standard type definitions.
type X__clock_t = uint32             /* _types.h:84:23 */
type X__critical_t = X__int32_t      /* _types.h:85:19 */
type X__double_t = float64           /* _types.h:87:21 */
type X__float_t = float64            /* _types.h:88:21 */
type X__intfptr_t = X__int32_t       /* _types.h:90:19 */
type X__intptr_t = X__int32_t        /* _types.h:91:19 */
type X__intmax_t = X__int64_t        /* _types.h:93:19 */
type X__int_fast8_t = X__int32_t     /* _types.h:94:19 */
type X__int_fast16_t = X__int32_t    /* _types.h:95:19 */
type X__int_fast32_t = X__int32_t    /* _types.h:96:19 */
type X__int_fast64_t = X__int64_t    /* _types.h:97:19 */
type X__int_least8_t = X__int8_t     /* _types.h:98:18 */
type X__int_least16_t = X__int16_t   /* _types.h:99:19 */
type X__int_least32_t = X__int32_t   /* _types.h:100:19 */
type X__int_least64_t = X__int64_t   /* _types.h:101:19 */
type X__ptrdiff_t = X__int32_t       /* _types.h:112:19 */
type X__register_t = X__int32_t      /* _types.h:113:19 */
type X__segsz_t = X__int32_t         /* _types.h:114:19 */
type X__size_t = X__uint32_t         /* _types.h:115:20 */
type X__ssize_t = X__int32_t         /* _types.h:116:19 */
type X__time_t = X__int32_t          /* _types.h:117:19 */
type X__uintfptr_t = X__uint32_t     /* _types.h:118:20 */
type X__uintptr_t = X__uint32_t      /* _types.h:119:20 */
type X__uintmax_t = X__uint64_t      /* _types.h:121:20 */
type X__uint_fast8_t = X__uint32_t   /* _types.h:122:20 */
type X__uint_fast16_t = X__uint32_t  /* _types.h:123:20 */
type X__uint_fast32_t = X__uint32_t  /* _types.h:124:20 */
type X__uint_fast64_t = X__uint64_t  /* _types.h:125:20 */
type X__uint_least8_t = X__uint8_t   /* _types.h:126:19 */
type X__uint_least16_t = X__uint16_t /* _types.h:127:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:128:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:129:20 */
type X__u_register_t = X__uint32_t   /* _types.h:136:20 */
type X__vm_offset_t = X__uint32_t    /* _types.h:137:20 */
type X__vm_paddr_t = X__uint64_t     /* _types.h:138:20 */
type X__vm_size_t = X__uint32_t      /* _types.h:139:20 */
type X___wchar_t = int32             /* _types.h:141:14 */

// Standard type definitions.
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // file block size
type X__blkcnt_t = X__int64_t    /* _types.h:41:19 */ // file block count
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // clock_gettime()...
type X__fflags_t = X__uint32_t   /* _types.h:43:20 */ // file flags
type X__fsblkcnt_t = X__uint64_t /* _types.h:44:20 */
type X__fsfilcnt_t = X__uint64_t /* _types.h:45:20 */
type X__gid_t = X__uint32_t      /* _types.h:46:20 */
type X__id_t = X__int64_t        /* _types.h:47:19 */ // can hold a gid_t, pid_t, or uid_t
type X__ino_t = X__uint64_t      /* _types.h:48:20 */ // inode number
type X__key_t = int32            /* _types.h:49:15 */ // IPC key (for Sys V IPC)
type X__lwpid_t = X__int32_t     /* _types.h:50:19 */ // Thread ID (a.k.a. LWP)
type X__mode_t = X__uint16_t     /* _types.h:51:20 */ // permissions
type X__accmode_t = int32        /* _types.h:52:14 */ // access permissions
type X__nl_item = int32          /* _types.h:53:14 */
type X__nlink_t = X__uint64_t    /* _types.h:54:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:55:19 */ // file offset
type X__off64_t = X__int64_t     /* _types.h:56:19 */ // file offset (alias)
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process [group]
type X__rlim_t = X__int64_t      /* _types.h:58:19 */ // resource limit - intentionally
// signed, because of legacy code
// that uses -1 for RLIM_INFINITY
type X__sa_family_t = X__uint8_t /* _types.h:61:19 */
type X__socklen_t = X__uint32_t  /* _types.h:62:20 */
type X__suseconds_t = int32      /* _types.h:63:15 */ // microseconds (signed)
type X__timer_t = uintptr        /* _types.h:64:24 */ // timer_gettime()...
type X__mqd_t = uintptr          /* _types.h:65:21 */ // mq_open()...
type X__uid_t = X__uint32_t      /* _types.h:66:20 */
type X__useconds_t = uint32      /* _types.h:67:22 */ // microseconds (unsigned)
type X__cpuwhich_t = int32       /* _types.h:68:14 */ // which parameter for cpuset.
type X__cpulevel_t = int32       /* _types.h:69:14 */ // level parameter for cpuset.
type X__cpusetid_t = int32       /* _types.h:70:14 */ // cpuset identifier.
type X__daddr_t = X__int64_t     /* _types.h:71:19 */ // bwrite(3), FIOBMAP2, etc

// Unusual type definitions.
// rune_t is declared to be an “int” instead of the more natural
// “unsigned long” or “long”.  Two things are happening here.  It is not
// unsigned so that EOF (-1) can be naturally assigned to it and used.  Also,
// it looks like 10646 will be a 31 bit standard.  This means that if your
// ints cannot hold 32 bits, you will be in trouble.  The reason an int was
// chosen over a long is that the is*() and to*() routines take ints (says
// ANSI C), but they use __ct_rune_t instead of int.
//
// NOTE: rune_t is not covered by ANSI nor other standards, and should not
// be instantiated outside of lib/libc/locale.  Use wchar_t.  wint_t and
// rune_t must be the same type.  Also, wint_t should be able to hold all
// members of the largest character set plus one extra value (WEOF), and
// must be at least 16 bits.
type X__ct_rune_t = int32     /* _types.h:91:14 */ // arg type for ctype funcs
type X__rune_t = X__ct_rune_t /* _types.h:92:21 */ // rune_t (see above)
type X__wint_t = X__ct_rune_t /* _types.h:93:21 */ // wint_t (see above)

// Clang already provides these types as built-ins, but only in C++ mode.
type X__char16_t = X__uint_least16_t /* _types.h:97:26 */
type X__char32_t = X__uint_least32_t /* _types.h:98:26 */
// In C++11, char16_t and char32_t are built-in types.

type X__max_align_t = struct {
	F__max_align1 int64
	F__max_align2 float64
} /* _types.h:111:3 */

type X__dev_t = X__uint64_t /* _types.h:113:20 */ // device number

type X__fixpt_t = X__uint32_t /* _types.h:115:20 */ // fixed point number

// mbstate_t is an opaque object to keep conversion state during multibyte
// stream conversions.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint32
	F__mbstate8  [128]int8
} /* _types.h:124:3 */

type X__rman_res_t = X__uintmax_t /* _types.h:126:25 */

// Types for varargs. These are all provided by builtin types these
// days, so centralize their definition.
type X__va_list = X__builtin_va_list /* _types.h:133:27 */ // internally known to gcc
type X__gnuc_va_list = X__va_list    /* _types.h:140:20 */ // compatibility w/GNU headers

// When the following macro is defined, the system uses 64-bit inode numbers.
// Programs can use this to avoid including <sys/param.h>, with its associated
// namespace pollution.

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ttycom.h	8.1 (Berkeley) 3/28/94
// $FreeBSD$

// Window/terminal size structure.  This information is stored by the kernel
// in order to provide a consistent interface, but is not used by the kernel.
type Winsize = struct {
	Fws_row    uint16
	Fws_col    uint16
	Fws_xpixel uint16
	Fws_ypixel uint16
} /* _winsize.h:42:1 */

type Pid_t = X__pid_t /* termios.h:46:18 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1989, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ttycom.h	8.1 (Berkeley) 3/28/94
// $FreeBSD$

// Tty ioctl's except for those supported only for backwards compatibility
// with the old tty driver.

// 0-2 compat
// 3-7 unused
// 8-10 compat
// 11-12 unused
// 17-18 compat
// 23-25 unused
// 29-85 unused
// 88 unused
// 89-91 conflicts: tun and tap
// 92-93 tun and tap
// 94-97 conflicts: tun and tap
// 100 unused
// 105 unused
// 116-117 compat
// 124-127 compat

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1982, 1986, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ttydefaults.h	8.4 (Berkeley) 1/21/94
// $FreeBSD$

// System wide defaults for terminal state.

// Defaults on "first" open.

// Control Character Defaults
// XXX: A lot of code uses lowercase characters, but control-character
// conversion is actually only valid when applied to uppercase
// characters. We just treat lowercase characters as if they were
// inserted as uppercase.
// compat

// PROTECTED INCLUSION ENDS HERE

// #define TTYDEFCHARS to include an array of default control characters.
var _ int8 /* gen.c:2:13: */
