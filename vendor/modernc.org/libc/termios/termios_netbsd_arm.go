// Code generated by 'ccgo termios/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o termios/termios_netbsd_arm.go -pkgname termios', DO NOT EDIT.

package termios

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ALTWERASE                 = 0x00000200 // termios.h:174:1:
	ARG_MAX                   = 262144     // syslimits.h:45:1:
	B0                        = 0          // termios.h:215:1:
	B1000000                  = 1000000    // termios.h:242:1:
	B110                      = 110        // termios.h:218:1:
	B115200                   = 115200     // termios.h:237:1:
	B1200                     = 1200       // termios.h:224:1:
	B134                      = 134        // termios.h:219:1:
	B14400                    = 14400      // termios.h:233:1:
	B150                      = 150        // termios.h:220:1:
	B1500000                  = 1500000    // termios.h:243:1:
	B1800                     = 1800       // termios.h:225:1:
	B19200                    = 19200      // termios.h:229:1:
	B200                      = 200        // termios.h:221:1:
	B2000000                  = 2000000    // termios.h:244:1:
	B230400                   = 230400     // termios.h:238:1:
	B2400                     = 2400       // termios.h:226:1:
	B2500000                  = 2500000    // termios.h:245:1:
	B28800                    = 28800      // termios.h:234:1:
	B300                      = 300        // termios.h:222:1:
	B3000000                  = 3000000    // termios.h:246:1:
	B3500000                  = 3500000    // termios.h:247:1:
	B38400                    = 38400      // termios.h:230:1:
	B4000000                  = 4000000    // termios.h:248:1:
	B460800                   = 460800     // termios.h:239:1:
	B4800                     = 4800       // termios.h:227:1:
	B50                       = 50         // termios.h:216:1:
	B500000                   = 500000     // termios.h:240:1:
	B57600                    = 57600      // termios.h:235:1:
	B600                      = 600        // termios.h:223:1:
	B7200                     = 7200       // termios.h:232:1:
	B75                       = 75         // termios.h:217:1:
	B76800                    = 76800      // termios.h:236:1:
	B921600                   = 921600     // termios.h:241:1:
	B9600                     = 9600       // termios.h:228:1:
	BC_DIM_MAX                = 65535      // syslimits.h:64:1:
	BRKINT                    = 0x00000002 // termios.h:91:1:
	CCTS_OFLOW                = 65536      // termios.h:145:1:
	CDISCARD                  = 15         // ttydefaults.h:72:1:
	CDSUSP                    = 25         // ttydefaults.h:68:1:
	CDTRCTS                   = 0x00020000 // termios.h:146:1:
	CEOF                      = 4          // ttydefaults.h:58:1:
	CEOT                      = 4          // ttydefaults.h:75:1:
	CERASE                    = 0177       // ttydefaults.h:60:1:
	CFLUSH                    = 15         // ttydefaults.h:79:1:
	CHILD_MAX                 = 160        // syslimits.h:47:1:
	CHWFLOW                   = 1245184    // termios.h:148:1:
	CIGNORE                   = 0x00000001 // termios.h:129:1:
	CINTR                     = 3          // ttydefaults.h:61:1:
	CKILL                     = 21         // ttydefaults.h:63:1:
	CLNEXT                    = 22         // ttydefaults.h:71:1:
	CLOCAL                    = 0x00008000 // termios.h:141:1:
	CMIN                      = 1          // ttydefaults.h:64:1:
	COLL_WEIGHTS_MAX          = 2          // syslimits.h:67:1:
	CQUIT                     = 034        // ttydefaults.h:65:1:
	CREAD                     = 0x00000800 // termios.h:137:1:
	CREPRINT                  = 18         // ttydefaults.h:74:1:
	CRPRNT                    = 18         // ttydefaults.h:78:1:
	CRTSCTS                   = 0x00010000 // termios.h:143:1:
	CRTS_IFLOW                = 65536      // termios.h:144:1:
	CS5                       = 0x00000000 // termios.h:132:1:
	CS6                       = 0x00000100 // termios.h:133:1:
	CS7                       = 0x00000200 // termios.h:134:1:
	CS8                       = 0x00000300 // termios.h:135:1:
	CSIZE                     = 0x00000300 // termios.h:131:1:
	CSTART                    = 17         // ttydefaults.h:69:1:
	CSTATUS                   = 20         // ttydefaults.h:62:1:
	CSTOP                     = 19         // ttydefaults.h:70:1:
	CSTOPB                    = 0x00000400 // termios.h:136:1:
	CSUSP                     = 26         // ttydefaults.h:66:1:
	CTIME                     = 0          // ttydefaults.h:67:1:
	CWERASE                   = 23         // ttydefaults.h:73:1:
	ECHO                      = 0x00000008 // termios.h:165:1:
	ECHOCTL                   = 0x00000040 // termios.h:169:1:
	ECHOE                     = 0x00000002 // termios.h:163:1:
	ECHOK                     = 0x00000004 // termios.h:164:1:
	ECHOKE                    = 0x00000001 // termios.h:161:1:
	ECHONL                    = 0x00000010 // termios.h:166:1:
	ECHOPRT                   = 0x00000020 // termios.h:168:1:
	EXPR_NEST_MAX             = 32         // syslimits.h:68:1:
	EXTA                      = 19200      // termios.h:249:1:
	EXTB                      = 38400      // termios.h:250:1:
	EXTPROC                   = 0x00000800 // termios.h:178:1:
	FLUSHO                    = 0x00800000 // termios.h:182:1:
	GID_MAX                   = 2147483647 // syslimits.h:49:1:
	HDLCDISC                  = 9          // ttycom.h:177:1:
	HUPCL                     = 0x00004000 // termios.h:140:1:
	ICANON                    = 0x00000100 // termios.h:172:1:
	ICRNL                     = 0x00000100 // termios.h:98:1:
	IEXTEN                    = 0x00000400 // termios.h:176:1:
	IGNBRK                    = 0x00000001 // termios.h:90:1:
	IGNCR                     = 0x00000080 // termios.h:97:1:
	IGNPAR                    = 0x00000004 // termios.h:92:1:
	IMAXBEL                   = 0x00002000 // termios.h:105:1:
	INLCR                     = 0x00000040 // termios.h:96:1:
	INPCK                     = 0x00000010 // termios.h:94:1:
	IOCGROUP_SHIFT            = 8          // ioccom.h:49:1:
	IOCPARM_MASK              = 0x1fff     // ioccom.h:47:1:
	IOCPARM_SHIFT             = 16         // ioccom.h:48:1:
	IOV_MAX                   = 1024       // syslimits.h:84:1:
	ISIG                      = 0x00000080 // termios.h:171:1:
	ISTRIP                    = 0x00000020 // termios.h:95:1:
	IXANY                     = 0x00000800 // termios.h:102:1:
	IXOFF                     = 0x00000400 // termios.h:100:1:
	IXON                      = 0x00000200 // termios.h:99:1:
	LINE_MAX                  = 2048       // syslimits.h:69:1:
	LINK_MAX                  = 32767      // syslimits.h:50:1:
	LOGIN_NAME_MAX            = 17         // syslimits.h:77:1:
	MAX_CANON                 = 255        // syslimits.h:51:1:
	MAX_INPUT                 = 255        // syslimits.h:52:1:
	MDMBUF                    = 0x00100000 // termios.h:147:1:
	NAME_MAX                  = 511        // syslimits.h:53:1:
	NCCS                      = 20         // termios.h:79:1:
	NGROUPS_MAX               = 16         // syslimits.h:55:1:
	NOFLSH                    = 0x80000000 // termios.h:186:1:
	NOKERNINFO                = 0x02000000 // termios.h:183:1:
	NZERO                     = 20         // syslimits.h:85:1:
	OCRNL                     = 0x00000010 // termios.h:120:1:
	ONLCR                     = 0x00000002 // termios.h:113:1:
	ONLRET                    = 0x00000040 // termios.h:122:1:
	ONOCR                     = 0x00000020 // termios.h:121:1:
	ONOEOT                    = 0x00000008 // termios.h:117:1:
	OPEN_MAX                  = 128        // syslimits.h:58:1:
	OPOST                     = 0x00000001 // termios.h:111:1:
	OXTABS                    = 0x00000004 // termios.h:116:1:
	PARENB                    = 0x00001000 // termios.h:138:1:
	PARMRK                    = 0x00000008 // termios.h:93:1:
	PARODD                    = 0x00002000 // termios.h:139:1:
	PATH_MAX                  = 1024       // syslimits.h:60:1:
	PENDIN                    = 0x20000000 // termios.h:184:1:
	PIPE_BUF                  = 512        // syslimits.h:61:1:
	PPPDISC                   = 5          // ttycom.h:175:1:
	RE_DUP_MAX                = 255        // syslimits.h:70:1:
	SLIPDISC                  = 4          // ttycom.h:174:1:
	STRIPDISC                 = 6          // ttycom.h:176:1:
	TABLDISC                  = 3          // ttycom.h:173:1:
	TCIFLUSH                  = 1          // termios.h:255:1:
	TCIOFF                    = 3          // termios.h:260:1:
	TCIOFLUSH                 = 3          // termios.h:257:1:
	TCION                     = 4          // termios.h:261:1:
	TCOFLUSH                  = 2          // termios.h:256:1:
	TCOOFF                    = 1          // termios.h:258:1:
	TCOON                     = 2          // termios.h:259:1:
	TCSADRAIN                 = 1          // termios.h:206:1:
	TCSAFLUSH                 = 2          // termios.h:207:1:
	TCSANOW                   = 0          // termios.h:205:1:
	TCSASOFT                  = 0x10       // termios.h:209:1:
	TIOCFLAG_CDTRCTS          = 0x10       // ttycom.h:158:1:
	TIOCFLAG_CLOCAL           = 0x02       // ttycom.h:155:1:
	TIOCFLAG_CRTSCTS          = 0x04       // ttycom.h:156:1:
	TIOCFLAG_MDMBUF           = 0x08       // ttycom.h:157:1:
	TIOCFLAG_SOFTCAR          = 0x01       // ttycom.h:154:1:
	TIOCM_CAR                 = 0100       // ttycom.h:89:1:
	TIOCM_CD                  = 64         // ttycom.h:90:1:
	TIOCM_CTS                 = 0040       // ttycom.h:88:1:
	TIOCM_DSR                 = 0400       // ttycom.h:93:1:
	TIOCM_DTR                 = 0002       // ttycom.h:84:1:
	TIOCM_LE                  = 0001       // ttycom.h:83:1:
	TIOCM_RI                  = 128        // ttycom.h:92:1:
	TIOCM_RNG                 = 0200       // ttycom.h:91:1:
	TIOCM_RTS                 = 0004       // ttycom.h:85:1:
	TIOCM_SR                  = 0020       // ttycom.h:87:1:
	TIOCM_ST                  = 0010       // ttycom.h:86:1:
	TIOCPKT_DATA              = 0x00       // ttycom.h:126:1:
	TIOCPKT_DOSTOP            = 0x20       // ttycom.h:132:1:
	TIOCPKT_FLUSHREAD         = 0x01       // ttycom.h:127:1:
	TIOCPKT_FLUSHWRITE        = 0x02       // ttycom.h:128:1:
	TIOCPKT_IOCTL             = 0x40       // ttycom.h:133:1:
	TIOCPKT_NOSTOP            = 0x10       // ttycom.h:131:1:
	TIOCPKT_START             = 0x08       // ttycom.h:130:1:
	TIOCPKT_STOP              = 0x04       // ttycom.h:129:1:
	TOSTOP                    = 0x00400000 // termios.h:180:1:
	TTLINEDNAMELEN            = 32         // ttycom.h:110:1:
	TTYDEF_CFLAG              = 19200      // ttydefaults.h:51:1:
	TTYDEF_IFLAG              = 11010      // ttydefaults.h:48:1:
	TTYDEF_LFLAG              = 1483       // ttydefaults.h:50:1:
	TTYDEF_OFLAG              = 7          // ttydefaults.h:49:1:
	TTYDEF_SPEED              = 9600       // ttydefaults.h:52:1:
	TTYDISC                   = 0          // ttycom.h:172:1:
	UID_MAX                   = 2147483647 // syslimits.h:56:1:
	VDISCARD                  = 15         // termios.h:71:1:
	VDSUSP                    = 11         // termios.h:65:1:
	VEOF                      = 0          // termios.h:47:1:
	VEOL                      = 1          // termios.h:48:1:
	VEOL2                     = 2          // termios.h:50:1:
	VERASE                    = 3          // termios.h:52:1:
	VINTR                     = 8          // termios.h:61:1:
	VKILL                     = 5          // termios.h:56:1:
	VLNEXT                    = 14         // termios.h:70:1:
	VMIN                      = 16         // termios.h:73:1:
	VQUIT                     = 9          // termios.h:62:1:
	VREPRINT                  = 6          // termios.h:58:1:
	VSTART                    = 12         // termios.h:67:1:
	VSTATUS                   = 18         // termios.h:76:1:
	VSTOP                     = 13         // termios.h:68:1:
	VSUSP                     = 10         // termios.h:63:1:
	VTIME                     = 17         // termios.h:74:1:
	VWERASE                   = 4          // termios.h:54:1:
	X_ARM_ARCH_4T             = 0          // cdefs.h:44:1:
	X_ARM_ARCH_5              = 0          // cdefs.h:40:1:
	X_ARM_ARCH_5T             = 0          // cdefs.h:36:1:
	X_ARM_ARCH_6              = 0          // cdefs.h:31:1:
	X_ARM_ARCH_7              = 0          // cdefs.h:20:1:
	X_ARM_ARCH_DWORD_OK       = 0          // cdefs.h:51:1:
	X_ARM_ARCH_T2             = 0          // cdefs.h:24:1:
	X_ARM_CDEFS_H_            = 0          // cdefs.h:4:1:
	X_ARM_INT_TYPES_H_        = 0          // int_types.h:33:1:
	X_FILE_OFFSET_BITS        = 64         // <builtin>:25:1:
	X_NETBSD_SOURCE           = 1          // featuretest.h:70:1:
	X_NETBSD_SYS_TTYCOM_H_    = 0          // ttycom.h:65:1:
	X_PATH_PTMDEV             = "/dev/ptm" // ttycom.h:81:1:
	X_POSIX_SYS_TTYCOM_H_     = 0          // ttycom.h:40:1:
	X_SYS_ANSI_H_             = 0          // ansi.h:33:1:
	X_SYS_CDEFS_ELF_H_        = 0          // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_            = 0          // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_      = 0          // common_ansi.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_ = 0          // common_int_types.h:33:1:
	X_SYS_IOCCOM_H_           = 0          // ioccom.h:35:1:
	X_SYS_SYSLIMITS_H_        = 0          // syslimits.h:35:1:
	X_SYS_TERMIOS_H_          = 0          // termios.h:35:1:
	X_SYS_TTYDEFAULTS_H_      = 0          // ttydefaults.h:43:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// return true if value 'a' fits in type 't'

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// ******** Integer types capable of holding object pointers

type X__intptr_t = int32   /* common_int_types.h:58:27 */
type X__uintptr_t = uint32 /* common_int_types.h:59:26 */

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Special Control Characters
//
// Index into c_cc[] character array.
//
//	Name	     Subscript	Enabled by
//			7	   spare 1
//			19	   spare 2

// Input flags - software input processing

// Output flags - software output processing

// Control flags - hardware control of terminal

// "Local" flags - dumping ground for other state
//
// Warning: some flags in this structure begin with
// the letter "I" and look like they belong in the
// input flag.

type Tcflag_t = uint32 /* termios.h:188:22 */
type Cc_t = uint8      /* termios.h:189:23 */
type Speed_t = uint32  /* termios.h:190:22 */

type Termios = struct {
	Fc_iflag  Tcflag_t
	Fc_oflag  Tcflag_t
	Fc_cflag  Tcflag_t
	Fc_lflag  Tcflag_t
	Fc_cc     [20]Cc_t
	Fc_ispeed int32
	Fc_ospeed int32
} /* termios.h:192:1 */

// Commands passed to tcsetattr() for setting the termios structure.

// Standard speeds

type Pid_t = X__pid_t /* termios.h:265:18 */

// Include tty ioctl's that aren't just for backwards compatibility
// with the old tty driver.  These ioctl definitions were previously
// in <sys/ioctl.h>.   Most of this appears only when _NETBSD_SOURCE
// is defined, but (at least) struct winsize has been made standard,
// and needs to be visible here (as well as via the old <sys/ioctl.h>.)
//	$NetBSD: ttycom.h,v 1.21 2017/10/25 06:32:59 kre Exp $

// -
// Copyright (c) 1982, 1986, 1990, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ttycom.h	8.1 (Berkeley) 3/28/94

//	$NetBSD: syslimits.h,v 1.28 2015/08/21 07:19:39 uebayasi Exp $

// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)syslimits.h	8.1 (Berkeley) 6/2/93

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// kept in sync with MAXNAMLEN

// IEEE Std 1003.1c-95, adopted in X/Open CAE Specification Issue 5 Version 2

// X/Open CAE Specification Issue 5 Version 2

//	$NetBSD: ioccom.h,v 1.13 2019/05/26 10:21:33 hannken Exp $

// -
// Copyright (c) 1982, 1986, 1990, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ioccom.h	8.3 (Berkeley) 1/9/95

// Ioctl's have the command encoded in the lower word, and the size of
// any in or out parameters in the upper word.  The high 3 bits of the
// upper word are used to encode the in/out status of the parameter.
//
//	 31 29 28                     16 15            8 7             0
//	+---------------------------------------------------------------+
//	| I/O | Parameter Length        | Command Group | Command       |
//	+---------------------------------------------------------------+

// no parameters
// copy parameters out
// copy parameters in
// copy parameters in and out
// mask for IN/OUT/VOID

// this should be _IORW, but stdio got there first

// Tty ioctl's except for those supported only for backwards compatibility
// with the old tty driver.

// Window/terminal size structure.  This information is stored by the kernel
// in order to provide a consistent interface, but is not used by the kernel.
type Winsize = struct {
	Fws_row    uint16
	Fws_col    uint16
	Fws_xpixel uint16
	Fws_ypixel uint16
} /* ttycom.h:54:1 */

// The following are not exposed when imported via <termios.h>
// when _POSIX_SOURCE (et.al.) is defined (and hence _NETBSD_SOURCE
// is not, unless that is added manually.)

// ptmget, for /dev/ptm pty getting ioctl TIOCPTMGET, and for TIOCPTSNAME
type Ptmget = struct {
	Fcfd int32
	Fsfd int32
	Fcn  [1024]uint8
	Fsn  [1024]uint8
} /* ttycom.h:74:1 */

// 8-10 compat
// 15 unused
// 17-18 compat

// This is the maximum length of a line discipline's name.
type Linedn_t = [32]uint8 /* ttycom.h:111:14 */

// END OF PROTECTED INCLUDE.

//	$NetBSD: ttydefaults.h,v 1.16 2008/05/24 14:06:39 yamt Exp $

// -
// Copyright (c) 1982, 1986, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)ttydefaults.h	8.4 (Berkeley) 1/21/94

// System wide defaults for terminal state.

// Defaults on "first" open.

// Control Character Defaults
// compat

// PROTECTED INCLUSION ENDS HERE

// #define TTYDEFCHARS to include an array of default control characters.
var _ uint8 /* gen.c:2:13: */
