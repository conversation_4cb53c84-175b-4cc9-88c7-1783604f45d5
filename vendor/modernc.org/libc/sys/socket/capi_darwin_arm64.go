// Code generated by 'ccgo sys/socket/gen.c -crt-import-path  -export-defines  -export-enums  -export-externs X -export-fields F -export-structs  -export-typedefs  -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_darwin_arm64.go -pkgname socket', DO NOT EDIT.

package socket

var CAPI = map[string]struct{}{
	"__darwin_check_fd_set":          {},
	"__darwin_check_fd_set_overflow": {},
	"__darwin_fd_clr":                {},
	"__darwin_fd_isset":              {},
	"__darwin_fd_set":                {},
}
