// Code generated by 'ccgo sys/socket/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_freebsd_arm.go -pkgname socket', DO NOT EDIT.

package socket

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_APPLETALK               = 16         // socket.h:240:1:
	AF_ARP                     = 35         // socket.h:264:1:
	AF_ATM                     = 30         // socket.h:257:1:
	AF_BLUETOOTH               = 36         // socket.h:265:1:
	AF_CCITT                   = 10         // socket.h:234:1:
	AF_CHAOS                   = 5          // socket.h:228:1:
	AF_CNT                     = 21         // socket.h:245:1:
	AF_COIP                    = 20         // socket.h:244:1:
	AF_DATAKIT                 = 9          // socket.h:233:1:
	AF_DECnet                  = 12         // socket.h:236:1:
	AF_DLI                     = 13         // socket.h:237:1:
	AF_E164                    = 26         // socket.h:251:1:
	AF_ECMA                    = 8          // socket.h:232:1:
	AF_HYLINK                  = 15         // socket.h:239:1:
	AF_HYPERV                  = 43         // socket.h:269:1:
	AF_IEEE80211               = 37         // socket.h:266:1:
	AF_IMPLINK                 = 3          // socket.h:226:1:
	AF_INET                    = 2          // socket.h:224:1:
	AF_INET6                   = 28         // socket.h:254:1:
	AF_INET6_SDP               = 42         // socket.h:268:1:
	AF_INET_SDP                = 40         // socket.h:267:1:
	AF_IPX                     = 23         // socket.h:247:1:
	AF_ISDN                    = 26         // socket.h:250:1:
	AF_ISO                     = 7          // socket.h:230:1:
	AF_LAT                     = 14         // socket.h:238:1:
	AF_LINK                    = 18         // socket.h:242:1:
	AF_LOCAL                   = 1          // socket.h:221:1:
	AF_MAX                     = 43         // socket.h:270:1:
	AF_NATM                    = 29         // socket.h:256:1:
	AF_NETBIOS                 = 6          // socket.h:229:1:
	AF_NETGRAPH                = 32         // socket.h:261:1:
	AF_OSI                     = 7          // socket.h:231:1:
	AF_PUP                     = 4          // socket.h:227:1:
	AF_ROUTE                   = 17         // socket.h:241:1:
	AF_SCLUSTER                = 34         // socket.h:263:1:
	AF_SIP                     = 24         // socket.h:248:1:
	AF_SLOW                    = 33         // socket.h:262:1:
	AF_SNA                     = 11         // socket.h:235:1:
	AF_UNIX                    = 1          // socket.h:223:1:
	AF_UNSPEC                  = 0          // socket.h:219:1:
	AF_VENDOR00                = 39         // socket.h:276:1:
	AF_VENDOR01                = 41         // socket.h:277:1:
	AF_VENDOR03                = 45         // socket.h:278:1:
	AF_VENDOR04                = 47         // socket.h:279:1:
	AF_VENDOR05                = 49         // socket.h:280:1:
	AF_VENDOR06                = 51         // socket.h:281:1:
	AF_VENDOR07                = 53         // socket.h:282:1:
	AF_VENDOR08                = 55         // socket.h:283:1:
	AF_VENDOR09                = 57         // socket.h:284:1:
	AF_VENDOR10                = 59         // socket.h:285:1:
	AF_VENDOR11                = 61         // socket.h:286:1:
	AF_VENDOR12                = 63         // socket.h:287:1:
	AF_VENDOR13                = 65         // socket.h:288:1:
	AF_VENDOR14                = 67         // socket.h:289:1:
	AF_VENDOR15                = 69         // socket.h:290:1:
	AF_VENDOR16                = 71         // socket.h:291:1:
	AF_VENDOR17                = 73         // socket.h:292:1:
	AF_VENDOR18                = 75         // socket.h:293:1:
	AF_VENDOR19                = 77         // socket.h:294:1:
	AF_VENDOR20                = 79         // socket.h:295:1:
	AF_VENDOR21                = 81         // socket.h:296:1:
	AF_VENDOR22                = 83         // socket.h:297:1:
	AF_VENDOR23                = 85         // socket.h:298:1:
	AF_VENDOR24                = 87         // socket.h:299:1:
	AF_VENDOR25                = 89         // socket.h:300:1:
	AF_VENDOR26                = 91         // socket.h:301:1:
	AF_VENDOR27                = 93         // socket.h:302:1:
	AF_VENDOR28                = 95         // socket.h:303:1:
	AF_VENDOR29                = 97         // socket.h:304:1:
	AF_VENDOR30                = 99         // socket.h:305:1:
	AF_VENDOR31                = 101        // socket.h:306:1:
	AF_VENDOR32                = 103        // socket.h:307:1:
	AF_VENDOR33                = 105        // socket.h:308:1:
	AF_VENDOR34                = 107        // socket.h:309:1:
	AF_VENDOR35                = 109        // socket.h:310:1:
	AF_VENDOR36                = 111        // socket.h:311:1:
	AF_VENDOR37                = 113        // socket.h:312:1:
	AF_VENDOR38                = 115        // socket.h:313:1:
	AF_VENDOR39                = 117        // socket.h:314:1:
	AF_VENDOR40                = 119        // socket.h:315:1:
	AF_VENDOR41                = 121        // socket.h:316:1:
	AF_VENDOR42                = 123        // socket.h:317:1:
	AF_VENDOR43                = 125        // socket.h:318:1:
	AF_VENDOR44                = 127        // socket.h:319:1:
	AF_VENDOR45                = 129        // socket.h:320:1:
	AF_VENDOR46                = 131        // socket.h:321:1:
	AF_VENDOR47                = 133        // socket.h:322:1:
	CMGROUP_MAX                = 16         // socket.h:495:1:
	MSG_CMSG_CLOEXEC           = 0x00040000 // socket.h:468:1:
	MSG_COMPAT                 = 0x00008000 // socket.h:459:1:
	MSG_CTRUNC                 = 0x00000020 // socket.h:448:1:
	MSG_DONTROUTE              = 0x00000004 // socket.h:445:1:
	MSG_DONTWAIT               = 0x00000080 // socket.h:451:1:
	MSG_EOF                    = 0x00000100 // socket.h:452:1:
	MSG_EOR                    = 0x00000008 // socket.h:446:1:
	MSG_NBIO                   = 0x00004000 // socket.h:458:1:
	MSG_NOSIGNAL               = 0x00020000 // socket.h:465:1:
	MSG_NOTIFICATION           = 0x00002000 // socket.h:457:1:
	MSG_OOB                    = 0x00000001 // socket.h:443:1:
	MSG_PEEK                   = 0x00000002 // socket.h:444:1:
	MSG_TRUNC                  = 0x00000010 // socket.h:447:1:
	MSG_WAITALL                = 0x00000040 // socket.h:449:1:
	MSG_WAITFORONE             = 0x00080000 // socket.h:469:1:
	NET_RT_DUMP                = 1          // socket.h:414:1:
	NET_RT_FLAGS               = 2          // socket.h:415:1:
	NET_RT_IFLIST              = 3          // socket.h:416:1:
	NET_RT_IFLISTL             = 5          // socket.h:418:1:
	NET_RT_IFMALIST            = 4          // socket.h:417:1:
	NET_RT_NHGRP               = 7          // socket.h:421:1:
	NET_RT_NHOP                = 6          // socket.h:420:1:
	PF_APPLETALK               = 16         // socket.h:371:1:
	PF_ARP                     = 35         // socket.h:389:1:
	PF_ATM                     = 30         // socket.h:385:1:
	PF_BLUETOOTH               = 36         // socket.h:390:1:
	PF_CCITT                   = 10         // socket.h:365:1:
	PF_CHAOS                   = 5          // socket.h:359:1:
	PF_CNT                     = 21         // socket.h:376:1:
	PF_COIP                    = 20         // socket.h:375:1:
	PF_DATAKIT                 = 9          // socket.h:364:1:
	PF_DECnet                  = 12         // socket.h:367:1:
	PF_DLI                     = 13         // socket.h:368:1:
	PF_ECMA                    = 8          // socket.h:363:1:
	PF_HYLINK                  = 15         // socket.h:370:1:
	PF_IEEE80211               = 37         // socket.h:391:1:
	PF_IMPLINK                 = 3          // socket.h:357:1:
	PF_INET                    = 2          // socket.h:356:1:
	PF_INET6                   = 28         // socket.h:383:1:
	PF_INET6_SDP               = 42         // socket.h:393:1:
	PF_INET_SDP                = 40         // socket.h:392:1:
	PF_IPX                     = 23         // socket.h:378:1:
	PF_ISDN                    = 26         // socket.h:381:1:
	PF_ISO                     = 7          // socket.h:361:1:
	PF_KEY                     = 27         // socket.h:382:1:
	PF_LAT                     = 14         // socket.h:369:1:
	PF_LINK                    = 18         // socket.h:373:1:
	PF_LOCAL                   = 1          // socket.h:354:1:
	PF_MAX                     = 43         // socket.h:395:1:
	PF_NATM                    = 29         // socket.h:384:1:
	PF_NETBIOS                 = 6          // socket.h:360:1:
	PF_NETGRAPH                = 32         // socket.h:386:1:
	PF_OSI                     = 7          // socket.h:362:1:
	PF_PIP                     = 25         // socket.h:380:1:
	PF_PUP                     = 4          // socket.h:358:1:
	PF_ROUTE                   = 17         // socket.h:372:1:
	PF_RTIP                    = 22         // socket.h:379:1:
	PF_SCLUSTER                = 34         // socket.h:388:1:
	PF_SIP                     = 24         // socket.h:377:1:
	PF_SLOW                    = 33         // socket.h:387:1:
	PF_SNA                     = 11         // socket.h:366:1:
	PF_UNIX                    = 1          // socket.h:355:1:
	PF_UNSPEC                  = 0          // socket.h:353:1:
	PF_XTP                     = 19         // socket.h:374:1:
	PRU_FLUSH_RD               = 0          // socket.h:636:1:
	PRU_FLUSH_RDWR             = 2          // socket.h:638:1:
	PRU_FLUSH_WR               = 1          // socket.h:637:1:
	SCM_BINTIME                = 0x04       // socket.h:587:1:
	SCM_CREDS                  = 0x03       // socket.h:586:1:
	SCM_CREDS2                 = 0x08       // socket.h:591:1:
	SCM_MONOTONIC              = 0x06       // socket.h:589:1:
	SCM_REALTIME               = 0x05       // socket.h:588:1:
	SCM_RIGHTS                 = 0x01       // socket.h:583:1:
	SCM_TIMESTAMP              = 0x02       // socket.h:585:1:
	SCM_TIME_INFO              = 0x07       // socket.h:590:1:
	SF_MNOWAIT                 = 0x00000002 // socket.h:656:1:
	SF_NOCACHE                 = 0x00000010 // socket.h:659:1:
	SF_NODISKIO                = 0x00000001 // socket.h:655:1:
	SF_SYNC                    = 0x00000004 // socket.h:657:1:
	SF_USER_READAHEAD          = 0x00000008 // socket.h:658:1:
	SHUT_RD                    = 0          // socket.h:629:1:
	SHUT_RDWR                  = 2          // socket.h:631:1:
	SHUT_WR                    = 1          // socket.h:630:1:
	SOCK_CLOEXEC               = 0x10000000 // socket.h:114:1:
	SOCK_DGRAM                 = 2          // socket.h:103:1:
	SOCK_MAXADDRLEN            = 255        // socket.h:335:1:
	SOCK_NONBLOCK              = 0x20000000 // socket.h:115:1:
	SOCK_RAW                   = 3          // socket.h:104:1:
	SOCK_RDM                   = 4          // socket.h:106:1:
	SOCK_SEQPACKET             = 5          // socket.h:108:1:
	SOCK_STREAM                = 1          // socket.h:102:1:
	SOL_SOCKET                 = 0xffff     // socket.h:214:1:
	SOMAXCONN                  = 128        // socket.h:427:1:
	SO_ACCEPTCONN              = 0x00000002 // socket.h:130:1:
	SO_ACCEPTFILTER            = 0x00001000 // socket.h:144:1:
	SO_BINTIME                 = 0x00002000 // socket.h:145:1:
	SO_BROADCAST               = 0x00000020 // socket.h:134:1:
	SO_DEBUG                   = 0x00000001 // socket.h:129:1:
	SO_DOMAIN                  = 0x1019     // socket.h:175:1:
	SO_DONTROUTE               = 0x00000010 // socket.h:133:1:
	SO_ERROR                   = 0x1007     // socket.h:161:1:
	SO_KEEPALIVE               = 0x00000008 // socket.h:132:1:
	SO_LABEL                   = 0x1009     // socket.h:164:1:
	SO_LINGER                  = 0x00000080 // socket.h:138:1:
	SO_LISTENINCQLEN           = 0x1013     // socket.h:168:1:
	SO_LISTENQLEN              = 0x1012     // socket.h:167:1:
	SO_LISTENQLIMIT            = 0x1011     // socket.h:166:1:
	SO_MAX_PACING_RATE         = 0x1018     // socket.h:174:1:
	SO_NOSIGPIPE               = 0x00000800 // socket.h:143:1:
	SO_NO_DDP                  = 0x00008000 // socket.h:148:1:
	SO_NO_OFFLOAD              = 0x00004000 // socket.h:147:1:
	SO_OOBINLINE               = 0x00000100 // socket.h:139:1:
	SO_PEERLABEL               = 0x1010     // socket.h:165:1:
	SO_PROTOCOL                = 0x1016     // socket.h:171:1:
	SO_PROTOTYPE               = 4118       // socket.h:172:1:
	SO_RCVBUF                  = 0x1002     // socket.h:156:1:
	SO_RCVLOWAT                = 0x1004     // socket.h:158:1:
	SO_RCVTIMEO                = 0x1006     // socket.h:160:1:
	SO_RERROR                  = 0x00020000 // socket.h:150:1:
	SO_REUSEADDR               = 0x00000004 // socket.h:131:1:
	SO_REUSEPORT               = 0x00000200 // socket.h:141:1:
	SO_REUSEPORT_LB            = 0x00010000 // socket.h:149:1:
	SO_SETFIB                  = 0x1014     // socket.h:169:1:
	SO_SNDBUF                  = 0x1001     // socket.h:155:1:
	SO_SNDLOWAT                = 0x1003     // socket.h:157:1:
	SO_SNDTIMEO                = 0x1005     // socket.h:159:1:
	SO_TIMESTAMP               = 0x00000400 // socket.h:142:1:
	SO_TS_BINTIME              = 1          // socket.h:180:1:
	SO_TS_CLOCK                = 0x1017     // socket.h:173:1:
	SO_TS_CLOCK_MAX            = 3          // socket.h:184:1:
	SO_TS_DEFAULT              = 0          // socket.h:183:1:
	SO_TS_MONOTONIC            = 3          // socket.h:182:1:
	SO_TS_REALTIME             = 2          // socket.h:181:1:
	SO_TS_REALTIME_MICRO       = 0          // socket.h:179:1:
	SO_TYPE                    = 0x1008     // socket.h:162:1:
	SO_USELOOPBACK             = 0x00000040 // socket.h:136:1:
	SO_USER_COOKIE             = 0x1015     // socket.h:170:1:
	SO_VENDOR                  = 0x80000000 // socket.h:193:1:
	ST_INFO_HW                 = 0x0001     // socket.h:599:1:
	ST_INFO_HW_HPREC           = 0x0002     // socket.h:600:1:
	X_ARM_INCLUDE__ALIGN_H_    = 0          // _align.h:44:1:
	X_FILE_OFFSET_BITS         = 64         // <builtin>:25:1:
	X_GID_T_DECLARED           = 0          // socket.h:53:1:
	X_ILP32                    = 1          // <predefined>:1:1:
	X_MACHINE__TYPES_H_        = 0          // _types.h:42:1:
	X_Nonnull                  = 0          // cdefs.h:790:1:
	X_Null_unspecified         = 0          // cdefs.h:792:1:
	X_Nullable                 = 0          // cdefs.h:791:1:
	X_OFF_T_DECLARED           = 0          // socket.h:58:1:
	X_PID_T_DECLARED           = 0          // socket.h:63:1:
	X_SA_FAMILY_T_DECLARED     = 0          // socket.h:69:1:
	X_SIZE_T_DECLARED          = 0          // _iovec.h:42:1:
	X_SOCKLEN_T_DECLARED       = 0          // socket.h:74:1:
	X_SSIZE_T_DECLARED         = 0          // socket.h:79:1:
	X_SS_MAXSIZE               = 128        // _sockaddr_storage.h:41:1:
	X_SYS_CDEFS_H_             = 0          // cdefs.h:39:1:
	X_SYS_SOCKET_H_            = 0          // socket.h:36:1:
	X_SYS__IOVEC_H_            = 0          // _iovec.h:36:1:
	X_SYS__SOCKADDR_STORAGE_H_ = 0          // _sockaddr_storage.h:36:1:
	X_SYS__TYPES_H_            = 0          // _types.h:32:1:
	X_UID_T_DECLARED           = 0          // socket.h:85:1:
	X_UINT32_T_DECLARED        = 0          // socket.h:91:1:
	X_UINTPTR_T_DECLARED       = 0          // socket.h:96:1:
	Pseudo_AF_HDRCMPLT         = 31         // socket.h:258:1:
	Pseudo_AF_KEY              = 27         // socket.h:252:1:
	Pseudo_AF_PIP              = 25         // socket.h:249:1:
	Pseudo_AF_RTIP             = 22         // socket.h:246:1:
	Pseudo_AF_XTP              = 19         // socket.h:243:1:
	Unix                       = 1          // <predefined>:367:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = uint32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1982, 1985, 1986, 1988, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)socket.h	8.4 (Berkeley) 2/21/94
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// Testing against Clang-specific extensions.

// This code has been put in place to help reduce the addition of
// compiler specific defines in FreeBSD code.  It helps to aid in
// having a compiler-agnostic source tree.

// Compiler memory barriers, specific to gcc and clang.

// XXX: if __GNUC__ >= 2: not tested everywhere originally, where replaced

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky to use if it must work in non-ANSI
// mode -- there must be no spaces between its arguments, and for nested
// __CONCAT's, all the __CONCAT's must be at the left.  __CONCAT can also
// concatenate double-quoted strings produced by the __STRING macro, but
// this only works with ANSI C.
//
// __XSTRING is like __STRING, but it expands any macros in its argument
// first.  It is only available with ANSI C.

// Compiler-dependent macros to help declare dead (non-returning) and
// pure (no side effects) functions, and unused variables.  They are
// null except for versions of gcc that are known to support the features
// properly (old versions of gcc-2 supported the dead and pure features
// in a different (wrong) way).  If we do not provide an implementation
// for a given compiler, let the compile fail if it is told to use
// a feature that we cannot live without.

// Keywords added in C11.

// Emulation of C11 _Generic().  Unlike the previously defined C11
// keywords, it is not possible to implement this using exactly the same
// syntax.  Therefore implement something similar under the name
// __generic().  Unlike _Generic(), this macro can only distinguish
// between a single type, so it requires nested invocations to
// distinguish multiple cases.

// C99 Static array indices in function parameter declarations.  Syntax such as:
// void bar(int myArray[static 10]);
// is allowed in C99 but not in C++.  Define __min_size appropriately so
// headers using it can be compiled in either language.  Use like this:
// void bar(int myArray[__min_size(10)]);

// XXX: should use `#if __STDC_VERSION__ < 199901'.

// C++11 exposes a load of C99 stuff

// GCC 2.95 provides `__restrict' as an extension to C90 to support the
// C99-specific `restrict' type qualifier.  We happen to use `__restrict' as
// a way to define the `restrict' type qualifier without disturbing older
// software that is unaware of C99 keywords.

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// We define this here since <stddef.h>, <sys/queue.h>, and <sys/types.h>
// require it.

// Given the pointer x to the member m of the struct s, return
// a pointer to the containing structure.  When using GCC, we first
// assign pointer x to a local variable, to check that its type is
// compatible with member m.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly (old versions of gcc-2
// didn't permit keeping the keywords out of the application namespace).

// Compiler-dependent macros that rely on FreeBSD-specific extensions.

// Embed the rcs id of a source file in the resulting library.  Note that in
// more recent ELF binutils, we use .ident allowing the ID to be stripped.
// Usage:
//	__FBSDID("$FreeBSD$");

// -
// The following definitions are an extension of the behavior originally
// implemented in <sys/_posix.h>, but with a different level of granularity.
// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1		1003.1-1990
//  _POSIX_C_SOURCE == 2		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309		1003.1b-1993
//  _POSIX_C_SOURCE == 199506		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112		1003.1-2001
//  _POSIX_C_SOURCE == 200809		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.
//
// Our macros begin with two underscores to avoid namespace screwage.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.
// -
// Deal with _ANSI_SOURCE:
// If it is defined, and no other compilation environment is explicitly
// requested, then define our internal feature-test macros to zero.  This
// makes no difference to the preprocessor (undefined symbols in preprocessing
// expressions are defined to have value zero), but makes it more convenient for
// a test program to print out the values.
//
// If a program mistakenly defines _ANSI_SOURCE and some other macro such as
// _POSIX_C_SOURCE, we will assume that it wants the broader compilation
// environment (and in fact we will never get here).

// User override __EXT1_VISIBLE

// Old versions of GCC use non-standard ARM arch symbols; acle-compat.h
// translates them to __ARM_ARCH and the modern feature symbols defined by ARM.

// Nullability qualifiers: currently only supported by Clang.

// Type Safety Checking
//
// Clang provides additional attributes to enable checking type safety
// properties that cannot be enforced by the C type system.

// Lock annotations.
//
// Clang provides support for doing basic thread-safety tests at
// compile-time, by marking which locks will/should be held when
// entering/leaving a functions.
//
// Furthermore, it is also possible to annotate variables and structure
// members to enforce that they are only accessed when certain locks are
// held.

// Structure implements a lock.

// Function acquires an exclusive or shared lock.

// Function attempts to acquire an exclusive or shared lock.

// Function releases a lock.

// Function asserts that an exclusive or shared lock is held.

// Function requires that an exclusive or shared lock is or is not held.

// Function should not be analyzed.

// Function or variable should not be sanitized, e.g., by AddressSanitizer.
// GCC has the nosanitize attribute, but as a function attribute only, and
// warns on use as a variable attribute.

// Guard variables and structure members by lock.

// Alignment builtins for better type checking and improved code generation.
// Provide fallback versions for other compilers (GCC/Clang < 10):

// -
// SPDX-License-Identifier: BSD-2-Clause-FreeBSD
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-4-Clause
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	From: @(#)ansi.h	8.2 (Berkeley) 1/4/94
//	From: @(#)types.h	8.3 (Berkeley) 1/5/94
// $FreeBSD$

// Basic types upon which most other types are built.
type X__int8_t = int8     /* _types.h:51:22 */
type X__uint8_t = uint8   /* _types.h:52:24 */
type X__int16_t = int16   /* _types.h:53:17 */
type X__uint16_t = uint16 /* _types.h:54:25 */
type X__int32_t = int32   /* _types.h:55:15 */
type X__uint32_t = uint32 /* _types.h:56:23 */

// LONGLONG
type X__int64_t = int64 /* _types.h:61:20 */

// LONGLONG
type X__uint64_t = uint64 /* _types.h:66:28 */

// Standard type definitions.
type X__clock_t = X__uint32_t        /* _types.h:71:20 */ // clock()...
type X__critical_t = X__int32_t      /* _types.h:72:19 */
type X__double_t = float64           /* _types.h:74:17 */
type X__float_t = float32            /* _types.h:75:16 */
type X__intfptr_t = X__int32_t       /* _types.h:77:19 */
type X__intmax_t = X__int64_t        /* _types.h:78:19 */
type X__intptr_t = X__int32_t        /* _types.h:79:19 */
type X__int_fast8_t = X__int32_t     /* _types.h:80:19 */
type X__int_fast16_t = X__int32_t    /* _types.h:81:19 */
type X__int_fast32_t = X__int32_t    /* _types.h:82:19 */
type X__int_fast64_t = X__int64_t    /* _types.h:83:19 */
type X__int_least8_t = X__int8_t     /* _types.h:84:18 */
type X__int_least16_t = X__int16_t   /* _types.h:85:19 */
type X__int_least32_t = X__int32_t   /* _types.h:86:19 */
type X__int_least64_t = X__int64_t   /* _types.h:87:19 */
type X__ptrdiff_t = X__int32_t       /* _types.h:88:19 */ // ptr1 - ptr2
type X__register_t = X__int32_t      /* _types.h:89:19 */
type X__segsz_t = X__int32_t         /* _types.h:90:19 */ // segment size (in pages)
type X__size_t = X__uint32_t         /* _types.h:91:20 */ // sizeof()
type X__ssize_t = X__int32_t         /* _types.h:92:19 */ // byte count or error
type X__time_t = X__int64_t          /* _types.h:93:19 */ // time()...
type X__uintfptr_t = X__uint32_t     /* _types.h:94:20 */
type X__uintmax_t = X__uint64_t      /* _types.h:95:20 */
type X__uintptr_t = X__uint32_t      /* _types.h:96:20 */
type X__uint_fast8_t = X__uint32_t   /* _types.h:97:20 */
type X__uint_fast16_t = X__uint32_t  /* _types.h:98:20 */
type X__uint_fast32_t = X__uint32_t  /* _types.h:99:20 */
type X__uint_fast64_t = X__uint64_t  /* _types.h:100:20 */
type X__uint_least8_t = X__uint8_t   /* _types.h:101:19 */
type X__uint_least16_t = X__uint16_t /* _types.h:102:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:103:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:104:20 */
type X__u_register_t = X__uint32_t   /* _types.h:105:20 */
type X__vm_offset_t = X__uint32_t    /* _types.h:106:20 */
type X__vm_paddr_t = X__uint32_t     /* _types.h:107:20 */
type X__vm_size_t = X__uint32_t      /* _types.h:108:20 */

type X___wchar_t = uint32 /* _types.h:110:22 */

// Standard type definitions.
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // file block size
type X__blkcnt_t = X__int64_t    /* _types.h:41:19 */ // file block count
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // clock_gettime()...
type X__fflags_t = X__uint32_t   /* _types.h:43:20 */ // file flags
type X__fsblkcnt_t = X__uint64_t /* _types.h:44:20 */
type X__fsfilcnt_t = X__uint64_t /* _types.h:45:20 */
type X__gid_t = X__uint32_t      /* _types.h:46:20 */
type X__id_t = X__int64_t        /* _types.h:47:19 */ // can hold a gid_t, pid_t, or uid_t
type X__ino_t = X__uint64_t      /* _types.h:48:20 */ // inode number
type X__key_t = int32            /* _types.h:49:15 */ // IPC key (for Sys V IPC)
type X__lwpid_t = X__int32_t     /* _types.h:50:19 */ // Thread ID (a.k.a. LWP)
type X__mode_t = X__uint16_t     /* _types.h:51:20 */ // permissions
type X__accmode_t = int32        /* _types.h:52:14 */ // access permissions
type X__nl_item = int32          /* _types.h:53:14 */
type X__nlink_t = X__uint64_t    /* _types.h:54:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:55:19 */ // file offset
type X__off64_t = X__int64_t     /* _types.h:56:19 */ // file offset (alias)
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process [group]
type X__rlim_t = X__int64_t      /* _types.h:58:19 */ // resource limit - intentionally
// signed, because of legacy code
// that uses -1 for RLIM_INFINITY
type X__sa_family_t = X__uint8_t /* _types.h:61:19 */
type X__socklen_t = X__uint32_t  /* _types.h:62:20 */
type X__suseconds_t = int32      /* _types.h:63:15 */ // microseconds (signed)
type X__timer_t = uintptr        /* _types.h:64:24 */ // timer_gettime()...
type X__mqd_t = uintptr          /* _types.h:65:21 */ // mq_open()...
type X__uid_t = X__uint32_t      /* _types.h:66:20 */
type X__useconds_t = uint32      /* _types.h:67:22 */ // microseconds (unsigned)
type X__cpuwhich_t = int32       /* _types.h:68:14 */ // which parameter for cpuset.
type X__cpulevel_t = int32       /* _types.h:69:14 */ // level parameter for cpuset.
type X__cpusetid_t = int32       /* _types.h:70:14 */ // cpuset identifier.
type X__daddr_t = X__int64_t     /* _types.h:71:19 */ // bwrite(3), FIOBMAP2, etc

// Unusual type definitions.
// rune_t is declared to be an “int” instead of the more natural
// “unsigned long” or “long”.  Two things are happening here.  It is not
// unsigned so that EOF (-1) can be naturally assigned to it and used.  Also,
// it looks like 10646 will be a 31 bit standard.  This means that if your
// ints cannot hold 32 bits, you will be in trouble.  The reason an int was
// chosen over a long is that the is*() and to*() routines take ints (says
// ANSI C), but they use __ct_rune_t instead of int.
//
// NOTE: rune_t is not covered by ANSI nor other standards, and should not
// be instantiated outside of lib/libc/locale.  Use wchar_t.  wint_t and
// rune_t must be the same type.  Also, wint_t should be able to hold all
// members of the largest character set plus one extra value (WEOF), and
// must be at least 16 bits.
type X__ct_rune_t = int32     /* _types.h:91:14 */ // arg type for ctype funcs
type X__rune_t = X__ct_rune_t /* _types.h:92:21 */ // rune_t (see above)
type X__wint_t = X__ct_rune_t /* _types.h:93:21 */ // wint_t (see above)

// Clang already provides these types as built-ins, but only in C++ mode.
type X__char16_t = X__uint_least16_t /* _types.h:97:26 */
type X__char32_t = X__uint_least32_t /* _types.h:98:26 */
// In C++11, char16_t and char32_t are built-in types.

type X__max_align_t = struct {
	F__max_align1 int64
	F__max_align2 float64
} /* _types.h:111:3 */

type X__dev_t = X__uint64_t /* _types.h:113:20 */ // device number

type X__fixpt_t = X__uint32_t /* _types.h:115:20 */ // fixed point number

// mbstate_t is an opaque object to keep conversion state during multibyte
// stream conversions.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]uint8
} /* _types.h:124:3 */

type X__rman_res_t = X__uintmax_t /* _types.h:126:25 */

// Types for varargs. These are all provided by builtin types these
// days, so centralize their definition.
type X__va_list = X__builtin_va_list /* _types.h:133:27 */ // internally known to gcc
type X__gnuc_va_list = X__va_list    /* _types.h:140:20 */

type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  Size_t
} /* _iovec.h:45:1 */

// -
// SPDX-License-Identifier: BSD-4-Clause
//
// Copyright (c) 2001 David E. O'Brien
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// William Jolitz.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)param.h	5.8 (Berkeley) 6/28/91
// $FreeBSD$

// Round p (pointer or byte index) up to the hardware-required alignment which
// is sufficient for any data type, pointer or numeric.  The resulting type
// is equivelent to arm's uintptr_t (but is purposely spelled "unsigned" here).

// Definitions related to sockets: types, address families, options.

// Data types.
type Gid_t = X__gid_t /* socket.h:52:18 */

type Off_t = X__off_t /* socket.h:57:18 */

type Pid_t = X__pid_t /* socket.h:62:18 */

type Sa_family_t = X__sa_family_t /* socket.h:68:23 */

type Socklen_t = X__socklen_t /* socket.h:73:21 */

type Ssize_t = X__ssize_t /* socket.h:78:19 */

type Uid_t = X__uid_t /* socket.h:84:18 */

type Uint32_t = X__uint32_t /* socket.h:90:20 */

type Uintptr_t = X__uintptr_t /* socket.h:95:21 */

// Types

// Creation flags, OR'ed into socket() and socketpair() type argument.

// Option flags per-socket.

// Additional options, not kept in so_options.

// Space reserved for new socket options added by third-party vendors.
// This range applies to all socket option levels.  New socket options
// in FreeBSD should always use an option value less than SO_VENDOR.

// Structure used for manipulating linger option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:199:1 */

type Accept_filter_arg = struct {
	Faf_name [16]uint8
	Faf_arg  [240]uint8
} /* socket.h:205:1 */

// Level number for (get/set)sockopt() to apply to socket itself.

// Address families.
// When allocating a new AF_ constant, please only allocate
// even numbered constants for FreeBSD until 134 as odd numbered AF_
// constants 39-133 are now reserved for vendors.

// Structure used by kernel to store most
// addresses.
type Sockaddr = struct {
	Fsa_len    uint8
	Fsa_family Sa_family_t
	Fsa_data   [14]uint8
} /* socket.h:329:1 */

// Structure used by kernel to pass protocol
// information in raw sockets.
type Sockproto = struct {
	Fsp_family   uint16
	Fsp_protocol uint16
} /* socket.h:341:1 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1982, 1985, 1986, 1988, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)socket.h	8.4 (Berkeley) 2/21/94
// $FreeBSD$

// RFC 2553: protocol-independent placeholder for socket addresses

type Sockaddr_storage = struct {
	Fss_len     uint8
	Fss_family  Sa_family_t
	F__ss_pad1  [6]uint8
	F__ss_align X__int64_t
	F__ss_pad2  [112]uint8
} /* _sockaddr_storage.h:48:1 */

// Protocol families, same as address families for now.

// Definitions for network related sysctl, CTL_NET.
//
// Second level is protocol family.
// Third level is protocol number.
//
// Further levels are defined by the individual families.

// PF_ROUTE - Routing table
//
// Three additional levels are defined:
//	Fourth: address family, 0 is wildcard
//	Fifth: type of info, defined below
//	Sixth: flag(s) to mask with for NET_RT_FLAGS

// Maximum queue length specifiable by listen.

// Message header for recvmsg and sendmsg calls.
// Used value-result for recvmsg, value only for sendmsg.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    Socklen_t
	Fmsg_iov        uintptr
	Fmsg_iovlen     int32
	Fmsg_control    uintptr
	Fmsg_controllen Socklen_t
	Fmsg_flags      int32
} /* socket.h:433:1 */

//			 0x00000200	   unused
//			 0x00000400	   unused
//			 0x00000800	   unused
//			 0x00001000	   unused

// Header for ancillary data objects in msg_control buffer.
// Used for additional information with/about a datagram
// not expressible by flags.  The format is a sequence
// of message elements headed by cmsghdr structures.
type Cmsghdr = struct {
	Fcmsg_len   Socklen_t
	Fcmsg_level int32
	Fcmsg_type  int32
} /* socket.h:482:1 */

// While we may have more groups than this, the cmsgcred struct must
// be able to fit in an mbuf and we have historically supported a
// maximum of 16 groups.

// Credentials structure, used to verify the identity of a peer
// process that has sent us a message. This is allocated by the
// peer process but filled in by the kernel. This prevents the
// peer from lying about its identity. (Note that cmcred_groups[0]
// is the effective GID.)
type Cmsgcred = struct {
	Fcmcred_pid     Pid_t
	Fcmcred_uid     Uid_t
	Fcmcred_euid    Uid_t
	Fcmcred_gid     Gid_t
	Fcmcred_ngroups int16
	F__ccgo_pad1    [2]byte
	Fcmcred_groups  [16]Gid_t
} /* socket.h:504:1 */

// Socket credentials (LOCAL_CREDS).
type Sockcred = struct {
	Fsc_uid     Uid_t
	Fsc_euid    Uid_t
	Fsc_gid     Gid_t
	Fsc_egid    Gid_t
	Fsc_ngroups int32
	Fsc_groups  [1]Gid_t
} /* socket.h:516:1 */

// Compute size of a sockcred structure with groups.

// Socket credentials (LOCAL_CREDS_PERSISTENT).
type Sockcred2 = struct {
	Fsc_version int32
	Fsc_pid     Pid_t
	Fsc_uid     Uid_t
	Fsc_euid    Uid_t
	Fsc_gid     Gid_t
	Fsc_egid    Gid_t
	Fsc_ngroups int32
	Fsc_groups  [1]Gid_t
} /* socket.h:534:1 */

// given pointer to struct cmsghdr, return pointer to data

// given pointer to struct cmsghdr, return pointer to next cmsghdr

// RFC 2292 requires to check msg_controllen, in case that the kernel returns
// an empty list for some reasons.

// RFC 2292 additions

// "Socket"-level control message types:

type Sock_timestamp_info = struct {
	Fst_info_flags X__uint32_t
	Fst_info_pad0  X__uint32_t
	Fst_info_rsv   [7]X__uint64_t
} /* socket.h:593:1 */

// 4.3 compat sockaddr, move to compat file later
type Osockaddr = struct {
	Fsa_family uint16
	Fsa_data   [14]uint8
} /* socket.h:608:1 */

// 4.3-compat message header (move to compat file later).
type Omsghdr = struct {
	Fmsg_name         uintptr
	Fmsg_namelen      int32
	Fmsg_iov          uintptr
	Fmsg_iovlen       int32
	Fmsg_accrights    uintptr
	Fmsg_accrightslen int32
} /* socket.h:616:1 */

// howto arguments for shutdown(2), specified by Posix.1g.

// for SCTP
// we cheat and use the SHUT_XX defines for these

// sendfile(2) header/trailer struct
type Sf_hdtr = struct {
	Fheaders  uintptr
	Fhdr_cnt  int32
	Ftrailers uintptr
	Ftrl_cnt  int32
} /* socket.h:645:1 */

// Sendfile-specific flag(s)

// Sendmmsg/recvmmsg specific structure(s)
type Mmsghdr = struct {
	Fmsg_hdr struct {
		Fmsg_name       uintptr
		Fmsg_namelen    Socklen_t
		Fmsg_iov        uintptr
		Fmsg_iovlen     int32
		Fmsg_control    uintptr
		Fmsg_controllen Socklen_t
		Fmsg_flags      int32
	}
	Fmsg_len Ssize_t
} /* socket.h:669:1 */

var _ uint8 /* gen.c:2:13: */
