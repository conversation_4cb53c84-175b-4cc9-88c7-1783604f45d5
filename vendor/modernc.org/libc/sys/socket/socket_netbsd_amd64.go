// Code generated by 'ccgo sys/socket/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_netbsd_amd64.go -pkgname socket', DO NOT EDIT.

package socket

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_APPLETALK              = 16         // socket.h:218:1:
	AF_ARP                    = 28         // socket.h:237:1:
	AF_BLUETOOTH              = 31         // socket.h:243:1:
	AF_CAN                    = 35         // socket.h:247:1:
	AF_CCITT                  = 10         // socket.h:212:1:
	AF_CHAOS                  = 5          // socket.h:206:1:
	AF_CNT                    = 21         // socket.h:225:1:
	AF_COIP                   = 20         // socket.h:224:1:
	AF_DATAKIT                = 9          // socket.h:211:1:
	AF_DECnet                 = 12         // socket.h:214:1:
	AF_DLI                    = 13         // socket.h:215:1:
	AF_E164                   = 26         // socket.h:235:1:
	AF_ECMA                   = 8          // socket.h:210:1:
	AF_ETHER                  = 36         // socket.h:248:1:
	AF_HYLINK                 = 15         // socket.h:217:1:
	AF_IEEE80211              = 32         // socket.h:244:1:
	AF_IMPLINK                = 3          // socket.h:204:1:
	AF_INET                   = 2          // socket.h:203:1:
	AF_INET6                  = 24         // socket.h:230:1:
	AF_IPX                    = 23         // socket.h:229:1:
	AF_ISDN                   = 26         // socket.h:234:1:
	AF_ISO                    = 7          // socket.h:208:1:
	AF_LAT                    = 14         // socket.h:216:1:
	AF_LINK                   = 18         // socket.h:220:1:
	AF_LOCAL                  = 1          // socket.h:201:1:
	AF_MAX                    = 37         // socket.h:249:1:
	AF_MPLS                   = 33         // socket.h:245:1:
	AF_NATM                   = 27         // socket.h:236:1:
	AF_NS                     = 6          // socket.h:207:1:
	AF_OROUTE                 = 17         // socket.h:219:1:
	AF_OSI                    = 7          // socket.h:209:1:
	AF_PUP                    = 4          // socket.h:205:1:
	AF_ROUTE                  = 34         // socket.h:246:1:
	AF_SNA                    = 11         // socket.h:213:1:
	AF_UNIX                   = 1          // socket.h:202:1:
	AF_UNSPEC                 = 0          // socket.h:200:1:
	MSG_BCAST                 = 0x0100     // socket.h:497:1:
	MSG_CMSG_CLOEXEC          = 0x0800     // socket.h:501:1:
	MSG_CONTROLMBUF           = 0x2000000  // socket.h:515:1:
	MSG_CTRUNC                = 0x0020     // socket.h:494:1:
	MSG_DONTROUTE             = 0x0004     // socket.h:491:1:
	MSG_DONTWAIT              = 0x0080     // socket.h:496:1:
	MSG_EOR                   = 0x0008     // socket.h:492:1:
	MSG_IOVUSRSPACE           = 0x4000000  // socket.h:516:1:
	MSG_LENUSRSPACE           = 0x8000000  // socket.h:517:1:
	MSG_MCAST                 = 0x0200     // socket.h:498:1:
	MSG_NAMEMBUF              = 0x1000000  // socket.h:514:1:
	MSG_NBIO                  = 0x1000     // socket.h:502:1:
	MSG_NOSIGNAL              = 0x0400     // socket.h:499:1:
	MSG_NOTIFICATION          = 0x4000     // socket.h:504:1:
	MSG_OOB                   = 0x0001     // socket.h:489:1:
	MSG_PEEK                  = 0x0002     // socket.h:490:1:
	MSG_TRUNC                 = 0x0010     // socket.h:493:1:
	MSG_USERFLAGS             = 0x0ffffff  // socket.h:513:1:
	MSG_WAITALL               = 0x0040     // socket.h:495:1:
	MSG_WAITFORONE            = 0x2000     // socket.h:503:1:
	NET_RT_DUMP               = 1          // socket.h:457:1:
	NET_RT_FLAGS              = 2          // socket.h:458:1:
	NET_RT_IFLIST             = 6          // socket.h:462:1:
	NET_RT_OIFLIST            = 5          // socket.h:461:1:
	NET_RT_OOIFLIST           = 4          // socket.h:460:1:
	NET_RT_OOOIFLIST          = 3          // socket.h:459:1:
	PCB_ALL                   = 0          // socket.h:444:1:
	PCB_SLOP                  = 20         // socket.h:443:1:
	PF_APPLETALK              = 16         // socket.h:334:1:
	PF_ARP                    = 28         // socket.h:351:1:
	PF_BLUETOOTH              = 31         // socket.h:355:1:
	PF_CAN                    = 35         // socket.h:358:1:
	PF_CCITT                  = 10         // socket.h:328:1:
	PF_CHAOS                  = 5          // socket.h:322:1:
	PF_CNT                    = 21         // socket.h:341:1:
	PF_COIP                   = 20         // socket.h:340:1:
	PF_DATAKIT                = 9          // socket.h:327:1:
	PF_DECnet                 = 12         // socket.h:330:1:
	PF_DLI                    = 13         // socket.h:331:1:
	PF_E164                   = 26         // socket.h:349:1:
	PF_ECMA                   = 8          // socket.h:326:1:
	PF_ETHER                  = 36         // socket.h:359:1:
	PF_HYLINK                 = 15         // socket.h:333:1:
	PF_IMPLINK                = 3          // socket.h:320:1:
	PF_INET                   = 2          // socket.h:319:1:
	PF_INET6                  = 24         // socket.h:342:1:
	PF_IPX                    = 23         // socket.h:343:1:
	PF_ISDN                   = 26         // socket.h:348:1:
	PF_ISO                    = 7          // socket.h:324:1:
	PF_KEY                    = 29         // socket.h:353:1:
	PF_LAT                    = 14         // socket.h:332:1:
	PF_LINK                   = 18         // socket.h:336:1:
	PF_LOCAL                  = 1          // socket.h:317:1:
	PF_MAX                    = 37         // socket.h:361:1:
	PF_MPLS                   = 33         // socket.h:356:1:
	PF_NATM                   = 27         // socket.h:350:1:
	PF_NS                     = 6          // socket.h:323:1:
	PF_OROUTE                 = 17         // socket.h:335:1:
	PF_OSI                    = 7          // socket.h:325:1:
	PF_PIP                    = 25         // socket.h:346:1:
	PF_PUP                    = 4          // socket.h:321:1:
	PF_ROUTE                  = 34         // socket.h:357:1:
	PF_RTIP                   = 22         // socket.h:345:1:
	PF_SNA                    = 11         // socket.h:329:1:
	PF_UNIX                   = 1          // socket.h:318:1:
	PF_UNSPEC                 = 0          // socket.h:316:1:
	PF_XTP                    = 19         // socket.h:338:1:
	SCM_CREDS                 = 0x10       // socket.h:581:1:
	SCM_RIGHTS                = 0x01       // socket.h:576:1:
	SCM_TIMESTAMP             = 0x08       // socket.h:580:1:
	SHUT_RD                   = 0          // socket.h:587:1:
	SHUT_RDWR                 = 2          // socket.h:589:1:
	SHUT_WR                   = 1          // socket.h:588:1:
	SOCK_CLOEXEC              = 0x10000000 // socket.h:113:1:
	SOCK_CONN_DGRAM           = 6          // socket.h:110:1:
	SOCK_DCCP                 = 6          // socket.h:111:1:
	SOCK_DGRAM                = 2          // socket.h:106:1:
	SOCK_FLAGS_MASK           = 0xf0000000 // socket.h:116:1:
	SOCK_NONBLOCK             = 0x20000000 // socket.h:114:1:
	SOCK_NOSIGPIPE            = 0x40000000 // socket.h:115:1:
	SOCK_RAW                  = 3          // socket.h:107:1:
	SOCK_RDM                  = 4          // socket.h:108:1:
	SOCK_SEQPACKET            = 5          // socket.h:109:1:
	SOCK_STREAM               = 1          // socket.h:105:1:
	SOL_SOCKET                = 0xffff     // socket.h:195:1:
	SOMAXCONN                 = 128        // socket.h:470:1:
	SO_ACCEPTCONN             = 0x0002     // socket.h:122:1:
	SO_ACCEPTFILTER           = 0x1000     // socket.h:133:1:
	SO_BROADCAST              = 0x0020     // socket.h:126:1:
	SO_DEBUG                  = 0x0001     // socket.h:121:1:
	SO_DEFOPTS                = 27645      // socket.h:138:1:
	SO_DONTROUTE              = 0x0010     // socket.h:125:1:
	SO_ERROR                  = 0x1007     // socket.h:169:1:
	SO_KEEPALIVE              = 0x0008     // socket.h:124:1:
	SO_LINGER                 = 0x0080     // socket.h:128:1:
	SO_NOHEADER               = 0x100a     // socket.h:173:1:
	SO_NOSIGPIPE              = 0x0800     // socket.h:132:1:
	SO_OOBINLINE              = 0x0100     // socket.h:129:1:
	SO_OVERFLOWED             = 0x1009     // socket.h:171:1:
	SO_RCVBUF                 = 0x1002     // socket.h:164:1:
	SO_RCVLOWAT               = 0x1004     // socket.h:166:1:
	SO_RCVTIMEO               = 0x100c     // socket.h:178:1:
	SO_RERROR                 = 0x4000     // socket.h:135:1:
	SO_REUSEADDR              = 0x0004     // socket.h:123:1:
	SO_REUSEPORT              = 0x0200     // socket.h:130:1:
	SO_SNDBUF                 = 0x1001     // socket.h:163:1:
	SO_SNDLOWAT               = 0x1003     // socket.h:165:1:
	SO_SNDTIMEO               = 0x100b     // socket.h:177:1:
	SO_TIMESTAMP              = 0x2000     // socket.h:134:1:
	SO_TYPE                   = 0x1008     // socket.h:170:1:
	SO_USELOOPBACK            = 0x0040     // socket.h:127:1:
	UIO_MAXIOV                = 1024       // uio.h:97:1:
	X_AMD64_INT_TYPES_H_      = 0          // int_types.h:35:1:
	X_FILE_OFFSET_BITS        = 64         // <builtin>:25:1:
	X_LP64                    = 1          // <predefined>:268:1:
	X_NETBSD_SOURCE           = 1          // featuretest.h:70:1:
	X_SS_MAXSIZE              = 128        // socket.h:294:1:
	X_SYS_ANSI_H_             = 0          // ansi.h:33:1:
	X_SYS_CDEFS_ELF_H_        = 0          // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_            = 0          // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_      = 0          // common_ansi.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_ = 0          // common_int_types.h:33:1:
	X_SYS_SIGTYPES_H_         = 0          // sigtypes.h:40:1:
	X_SYS_SOCKET_H_           = 0          // socket.h:64:1:
	X_SYS_UIO_H_              = 0          // uio.h:35:1:
	X_X86_64_CDEFS_H_         = 0          // cdefs.h:4:1:
	Pseudo_AF_HDRCMPLT        = 30         // socket.h:240:1:
	Pseudo_AF_KEY             = 29         // socket.h:239:1:
	Pseudo_AF_PIP             = 25         // socket.h:232:1:
	Pseudo_AF_RTIP            = 22         // socket.h:227:1:
	Pseudo_AF_XTP             = 19         // socket.h:222:1:
)

// file offset

const ( /* uio.h:69:1: */
	UIO_READ  = 0
	UIO_WRITE = 1
)

// Segment flag values.
const ( /* uio.h:72:1: */
	UIO_USERSPACE = 0 // from user data space
	UIO_SYSSPACE  = 1
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// return true if value 'a' fits in type 't'

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// ******** Integer types capable of holding object pointers

type X__intptr_t = int64   /* common_int_types.h:58:27 */
type X__uintptr_t = uint64 /* common_int_types.h:59:26 */

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */

type Sa_family_t = X__sa_family_t /* socket.h:78:23 */

type Socklen_t = X__socklen_t /* socket.h:83:21 */

type Ssize_t = int64 /* socket.h:95:23 */

//	$NetBSD: uio.h,v 1.36 2011/07/27 13:20:07 uebayasi Exp $

// Copyright (c) 1982, 1986, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)uio.h	8.5 (Berkeley) 2/22/94

//	$NetBSD: ansi.h,v 1.11 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  Size_t
} /* uio.h:56:1 */

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Off_t = X__off_t /* uio.h:65:18 */

//	$NetBSD: sigtypes.h,v 1.11 2017/01/12 18:29:14 christos Exp $

// Copyright (c) 1982, 1986, 1989, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)signal.h	8.4 (Berkeley) 5/4/95

// This header file defines various signal-related types.  We also keep
// the macros to manipulate sigset_t here, to encapsulate knowledge of
// its internals.

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: ansi.h,v 1.11 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Sigset_t = struct{ F__bits [4]X__uint32_t } /* sigtypes.h:62:3 */

// Macro for manipulating signal masks.

type Sigaltstack = struct {
	Fss_sp       uintptr
	Fss_size     Size_t
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
} /* sigtypes.h:108:9 */

// Macro for manipulating signal masks.

type Stack_t = Sigaltstack /* sigtypes.h:116:3 */

// Socket types.

// Option flags per-socket.
// 	SO_OTIMESTAMP	0x0400

// Allowed default option flags

// Additional options, not kept in so_options.
// SO_OSNDTIMEO		0x1005
// SO_ORCVTIMEO		0x1006

// Structure used for manipulating linger option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:182:1 */

type Accept_filter_arg = struct {
	Faf_name [16]int8
	Faf_arg  [240]int8
} /* socket.h:187:1 */

// Level number for (get/set)sockopt() to apply to socket itself.

// Address families.

// Structure used by kernel to store most
// addresses.
type Sockaddr = struct {
	Fsa_len    X__uint8_t
	Fsa_family X__sa_family_t
	Fsa_data   [14]int8
} /* socket.h:255:1 */

// RFC 2553: protocol-independent placeholder for socket addresses

type Sockaddr_storage = struct {
	Fss_len     X__uint8_t
	Fss_family  X__sa_family_t
	F__ss_pad1  [6]int8
	F__ss_align X__int64_t
	F__ss_pad2  [112]int8
} /* socket.h:301:1 */

// Protocol families, same as address families for now.

type Pid_t = X__pid_t /* socket.h:366:18 */ // process id

type Gid_t = X__gid_t /* socket.h:371:18 */ // group id

type Uid_t = X__uid_t /* socket.h:376:18 */ // user id

// Socket credentials.
type Sockcred = struct {
	Fsc_pid     X__pid_t
	Fsc_uid     X__uid_t
	Fsc_euid    X__uid_t
	Fsc_gid     X__gid_t
	Fsc_egid    X__gid_t
	Fsc_ngroups int32
	Fsc_groups  [1]X__gid_t
} /* socket.h:383:1 */

// Compute size of a sockcred structure with groups.

// Definition for CTL_NET PCB fetching sysctls
type Kinfo_pcb = struct {
	Fki_pcbaddr  X__uint64_t
	Fki_ppcbaddr X__uint64_t
	Fki_sockaddr X__uint64_t
	Fki_family   X__uint32_t
	Fki_type     X__uint32_t
	Fki_protocol X__uint32_t
	Fki_pflags   X__uint32_t
	Fki_sostate  X__uint32_t
	Fki_prstate  X__uint32_t
	Fki_tstate   X__int32_t
	Fki_tflags   X__uint32_t
	Fki_rcvq     X__uint64_t
	Fki_sndq     X__uint64_t
	Fki_s        struct {
		F_kis_src struct {
			Fsa_len    X__uint8_t
			Fsa_family X__sa_family_t
			Fsa_data   [14]int8
		}
		F__ccgo_pad1 [248]byte
	}
	Fki_d struct {
		F_kid_dst struct {
			Fsa_len    X__uint8_t
			Fsa_family X__sa_family_t
			Fsa_data   [14]int8
		}
		F__ccgo_pad1 [248]byte
	}
	Fki_inode   X__uint64_t
	Fki_vnode   X__uint64_t
	Fki_conn    X__uint64_t
	Fki_refs    X__uint64_t
	Fki_nextref X__uint64_t
} /* socket.h:404:1 */

// PF_ROUTE - Routing table
//
// Three additional levels are defined:
//	Fourth: address family, 0 is wildcard
//	Fifth: type of info, defined below
//	Sixth: flag(s) to mask with for NET_RT_FLAGS

// Maximum queue length specifiable by listen(2).

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Message header for recvmsg and sendmsg calls.
// Used value-result for recvmsg, value only for sendmsg.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    X__socklen_t
	F__ccgo_pad1    [4]byte
	Fmsg_iov        uintptr
	Fmsg_iovlen     int32
	F__ccgo_pad2    [4]byte
	Fmsg_control    uintptr
	Fmsg_controllen X__socklen_t
	Fmsg_flags      int32
} /* socket.h:479:1 */

type Mmsghdr = struct {
	Fmsg_hdr struct {
		Fmsg_name       uintptr
		Fmsg_namelen    X__socklen_t
		F__ccgo_pad1    [4]byte
		Fmsg_iov        uintptr
		Fmsg_iovlen     int32
		F__ccgo_pad2    [4]byte
		Fmsg_control    uintptr
		Fmsg_controllen X__socklen_t
		Fmsg_flags      int32
	}
	Fmsg_len     uint32
	F__ccgo_pad1 [4]byte
} /* socket.h:506:1 */

// Extra flags used internally only

// Header for ancillary data objects in msg_control buffer.
// Used for additional information with/about a datagram
// not expressible by flags.  The format is a sequence
// of message elements headed by cmsghdr structures.
type Cmsghdr = struct {
	Fcmsg_len   X__socklen_t
	Fcmsg_level int32
	Fcmsg_type  int32
} /* socket.h:525:1 */

var _ int8 /* gen.c:2:13: */
