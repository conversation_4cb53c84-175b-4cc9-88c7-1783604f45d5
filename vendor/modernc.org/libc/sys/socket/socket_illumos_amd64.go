// Code generated by 'ccgo sys/socket/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_illumos_amd64.go -pkgname socket', DO NOT EDIT.

package socket

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_802                               = 18                 // socket.h:296:1:
	AF_APPLETALK                         = 16                 // socket.h:294:1:
	AF_CCITT                             = 10                 // socket.h:288:1:
	AF_CHAOS                             = 5                  // socket.h:283:1:
	AF_DATAKIT                           = 9                  // socket.h:287:1:
	AF_DECnet                            = 12                 // socket.h:290:1:
	AF_DLI                               = 13                 // socket.h:291:1:
	AF_ECMA                              = 8                  // socket.h:286:1:
	AF_FILE                              = 1                  // socket.h:279:1:
	AF_GOSIP                             = 22                 // socket.h:300:1:
	AF_HYLINK                            = 15                 // socket.h:293:1:
	AF_IMPLINK                           = 3                  // socket.h:281:1:
	AF_INET                              = 2                  // socket.h:280:1:
	AF_INET6                             = 26                 // socket.h:304:1:
	AF_INET_OFFLOAD                      = 30                 // socket.h:308:1:
	AF_IPX                               = 23                 // socket.h:301:1:
	AF_KEY                               = 27                 // socket.h:305:1:
	AF_LAT                               = 14                 // socket.h:292:1:
	AF_LINK                              = 25                 // socket.h:303:1:
	AF_LOCAL                             = 1                  // socket.h:278:1:
	AF_LX_NETLINK                        = 33                 // socket.h:311:1:
	AF_MAX                               = 33                 // socket.h:313:1:
	AF_NBS                               = 7                  // socket.h:285:1:
	AF_NCA                               = 28                 // socket.h:306:1:
	AF_NIT                               = 17                 // socket.h:295:1:
	AF_NS                                = 6                  // socket.h:284:1:
	AF_OSI                               = 19                 // socket.h:297:1:
	AF_OSINET                            = 21                 // socket.h:299:1:
	AF_PACKET                            = 32                 // socket.h:310:1:
	AF_POLICY                            = 29                 // socket.h:307:1:
	AF_PUP                               = 4                  // socket.h:282:1:
	AF_ROUTE                             = 24                 // socket.h:302:1:
	AF_SNA                               = 11                 // socket.h:289:1:
	AF_TRILL                             = 31                 // socket.h:309:1:
	AF_UNIX                              = 1                  // socket.h:277:1:
	AF_UNSPEC                            = 0                  // socket.h:276:1:
	AF_X25                               = 20                 // socket.h:298:1:
	CANBSIZ                              = 256                // param.h:91:1:
	CDLIMIT                              = 2048               // param.h:164:1:
	CLOCKS_PER_SEC                       = 1000000            // time_iso.h:78:1:
	CLOCK_HIGHRES                        = 4                  // time_impl.h:126:1:
	CLOCK_MONOTONIC                      = 4                  // time_impl.h:124:1:
	CLOCK_PROCESS_CPUTIME_ID             = 5                  // time_impl.h:125:1:
	CLOCK_PROF                           = 2                  // time_impl.h:127:1:
	CLOCK_REALTIME                       = 3                  // time_impl.h:123:1:
	CLOCK_THREAD_CPUTIME_ID              = 2                  // time_impl.h:122:1:
	CLOCK_VIRTUAL                        = 1                  // time_impl.h:121:1:
	CMASK                                = 022                // param.h:163:1:
	DEFAULT_JUMPPID                      = 0                  // param.h:120:1:
	DEFAULT_MAXPID                       = 30000              // param.h:119:1:
	DEV_BSHIFT                           = 9                  // param.h:251:1:
	DEV_BSIZE                            = 512                // param.h:250:1:
	DST_AUST                             = 2                  // time.h:115:1:
	DST_AUSTALT                          = 10                 // time.h:123:1:
	DST_CAN                              = 6                  // time.h:119:1:
	DST_EET                              = 5                  // time.h:118:1:
	DST_GB                               = 7                  // time.h:120:1:
	DST_MET                              = 4                  // time.h:117:1:
	DST_NONE                             = 0                  // time.h:113:1:
	DST_RUM                              = 8                  // time.h:121:1:
	DST_TUR                              = 9                  // time.h:122:1:
	DST_USA                              = 1                  // time.h:114:1:
	DST_WET                              = 3                  // time.h:116:1:
	FD_SETSIZE                           = 65536              // select.h:88:1:
	FILF_AUTO                            = 0x2                // socket.h:232:1:
	FILF_BYPASS                          = 0x4                // socket.h:233:1:
	FILF_PROG                            = 0x1                // socket.h:231:1:
	FILNAME_MAX                          = 32                 // socket.h:221:1:
	FIL_ATTACH                           = 0x1                // socket.h:217:1:
	FIL_DETACH                           = 0x2                // socket.h:218:1:
	FIL_LIST                             = 0x3                // socket.h:219:1:
	FSCALE                               = 256                // param.h:304:1:
	FSHIFT                               = 8                  // param.h:303:1:
	GID_NETADM                           = 65                 // param.h:100:1:
	GID_NOBODY                           = 60001              // param.h:95:1:
	GID_UNKNOWN                          = 96                 // param.h:97:1:
	IMPLINK_HIGHEXPER                    = 158                // in.h:284:1:
	IMPLINK_IP                           = 155                // in.h:282:1:
	IMPLINK_LOWEXPER                     = 156                // in.h:283:1:
	INADDR_6TO4RRANYCAST                 = 0xc0586301         // in.h:392:1:
	INADDR_ALLHOSTS_GROUP                = 0xe0000001         // in.h:372:1:
	INADDR_ALLRPTS_GROUP                 = 0xe0000016         // in.h:374:1:
	INADDR_ALLRTRS_GROUP                 = 0xe0000002         // in.h:373:1:
	INADDR_ANY                           = 0x00000000         // in.h:366:1:
	INADDR_BROADCAST                     = 0xffffffff         // in.h:368:1:
	INADDR_LOOPBACK                      = 0x7F000001         // in.h:367:1:
	INADDR_MAX_LOCAL_GROUP               = 0xe00000ff         // in.h:375:1:
	INADDR_NONE                          = 0xffffffff         // in.h:369:1:
	INADDR_UNSPEC_GROUP                  = 0xe0000000         // in.h:371:1:
	INET6_ADDRSTRLEN                     = 46                 // in.h:1212:1:
	INET_ADDRSTRLEN                      = 16                 // in.h:1210:1:
	IN_AUTOCONF_MASK                     = 0xffff0000         // in.h:379:1:
	IN_AUTOCONF_NET                      = 0xa9fe0000         // in.h:378:1:
	IN_CLASSA_HOST                       = 0x00ffffff         // in.h:332:1:
	IN_CLASSA_MAX                        = 128                // in.h:333:1:
	IN_CLASSA_NET                        = 0xff000000         // in.h:330:1:
	IN_CLASSA_NSHIFT                     = 24                 // in.h:331:1:
	IN_CLASSB_HOST                       = 0x0000ffff         // in.h:338:1:
	IN_CLASSB_MAX                        = 65536              // in.h:339:1:
	IN_CLASSB_NET                        = 0xffff0000         // in.h:336:1:
	IN_CLASSB_NSHIFT                     = 16                 // in.h:337:1:
	IN_CLASSC_HOST                       = 0x000000ff         // in.h:344:1:
	IN_CLASSC_NET                        = 0xffffff00         // in.h:342:1:
	IN_CLASSC_NSHIFT                     = 8                  // in.h:343:1:
	IN_CLASSD_HOST                       = 0x0fffffff         // in.h:349:1:
	IN_CLASSD_NET                        = 0xf0000000         // in.h:347:1:
	IN_CLASSD_NSHIFT                     = 28                 // in.h:348:1:
	IN_CLASSE_NET                        = 0xffffffff         // in.h:352:1:
	IN_LOOPBACKNET                       = 127                // in.h:395:1:
	IN_PRIVATE12_MASK                    = 0xfff00000         // in.h:383:1:
	IN_PRIVATE12_NET                     = 0xac100000         // in.h:382:1:
	IN_PRIVATE16_MASK                    = 0xffff0000         // in.h:385:1:
	IN_PRIVATE16_NET                     = 0xc0a80000         // in.h:384:1:
	IN_PRIVATE8_MASK                     = 0xff000000         // in.h:381:1:
	IN_PRIVATE8_NET                      = 0x0a000000         // in.h:380:1:
	IPPORT_BIFFUDP                       = 512                // in.h:250:1:
	IPPORT_BOOTPC                        = 68                 // in.h:214:1:
	IPPORT_BOOTPS                        = 67                 // in.h:213:1:
	IPPORT_CHARGEN                       = 19                 // in.h:199:1:
	IPPORT_CMDSERVER                     = 514                // in.h:243:1:
	IPPORT_DAYTIME                       = 13                 // in.h:197:1:
	IPPORT_DHCPV6C                       = 546                // in.h:260:1:
	IPPORT_DHCPV6S                       = 547                // in.h:261:1:
	IPPORT_DISCARD                       = 9                  // in.h:195:1:
	IPPORT_DOMAIN                        = 53                 // in.h:206:1:
	IPPORT_ECHO                          = 7                  // in.h:194:1:
	IPPORT_EFSSERVER                     = 520                // in.h:245:1:
	IPPORT_EXECSERVER                    = 512                // in.h:241:1:
	IPPORT_FINGER                        = 79                 // in.h:217:1:
	IPPORT_FTP                           = 21                 // in.h:200:1:
	IPPORT_HTTP                          = 80                 // in.h:218:1:
	IPPORT_HTTP_ALT                      = 8080               // in.h:219:1:
	IPPORT_IKE                           = 500                // in.h:235:1:
	IPPORT_IKE_NATT                      = 4500               // in.h:236:1:
	IPPORT_LDAP                          = 389                // in.h:226:1:
	IPPORT_LOGINSERVER                   = 513                // in.h:242:1:
	IPPORT_MDNS                          = 5353               // in.h:207:1:
	IPPORT_MIP                           = 434                // in.h:228:1:
	IPPORT_MTP                           = 57                 // in.h:208:1:
	IPPORT_NAMESERVER                    = 42                 // in.h:204:1:
	IPPORT_NETBIOS_DGM                   = 138                // in.h:224:1:
	IPPORT_NETBIOS_NS                    = 137                // in.h:223:1:
	IPPORT_NETBIOS_SSN                   = 139                // in.h:225:1:
	IPPORT_NETSTAT                       = 15                 // in.h:198:1:
	IPPORT_NTP                           = 123                // in.h:222:1:
	IPPORT_PRINTER                       = 515                // in.h:244:1:
	IPPORT_RESERVED                      = 1024               // in.h:271:1:
	IPPORT_RIPNG                         = 521                // in.h:255:1:
	IPPORT_RJE                           = 77                 // in.h:216:1:
	IPPORT_ROUTESERVER                   = 520                // in.h:254:1:
	IPPORT_SLP                           = 427                // in.h:227:1:
	IPPORT_SMB                           = 445                // in.h:229:1:
	IPPORT_SMTP                          = 25                 // in.h:202:1:
	IPPORT_SOCKS                         = 1080               // in.h:263:1:
	IPPORT_SUPDUP                        = 95                 // in.h:221:1:
	IPPORT_SYSLOG                        = 514                // in.h:252:1:
	IPPORT_SYSTAT                        = 11                 // in.h:196:1:
	IPPORT_TALK                          = 517                // in.h:253:1:
	IPPORT_TELNET                        = 23                 // in.h:201:1:
	IPPORT_TFTP                          = 69                 // in.h:215:1:
	IPPORT_TIMESERVER                    = 37                 // in.h:203:1:
	IPPORT_TTYLINK                       = 87                 // in.h:220:1:
	IPPORT_USERRESERVED                  = 5000               // in.h:272:1:
	IPPORT_VXLAN                         = 4789               // in.h:230:1:
	IPPORT_WHOIS                         = 43                 // in.h:205:1:
	IPPORT_WHOSERVER                     = 513                // in.h:251:1:
	IPPROTO_AH                           = 51                 // in.h:169:1:
	IPPROTO_DSTOPTS                      = 60                 // in.h:172:1:
	IPPROTO_EGP                          = 8                  // in.h:160:1:
	IPPROTO_ENCAP                        = 4                  // in.h:158:1:
	IPPROTO_EON                          = 80                 // in.h:175:1:
	IPPROTO_ESP                          = 50                 // in.h:168:1:
	IPPROTO_FRAGMENT                     = 44                 // in.h:166:1:
	IPPROTO_GGP                          = 3                  // in.h:157:1:
	IPPROTO_HELLO                        = 63                 // in.h:173:1:
	IPPROTO_HOPOPTS                      = 0                  // in.h:154:1:
	IPPROTO_ICMP                         = 1                  // in.h:155:1:
	IPPROTO_ICMPV6                       = 58                 // in.h:170:1:
	IPPROTO_IDP                          = 22                 // in.h:163:1:
	IPPROTO_IGMP                         = 2                  // in.h:156:1:
	IPPROTO_IP                           = 0                  // in.h:153:1:
	IPPROTO_IPV6                         = 41                 // in.h:164:1:
	IPPROTO_MAX                          = 256                // in.h:182:1:
	IPPROTO_ND                           = 77                 // in.h:174:1:
	IPPROTO_NONE                         = 59                 // in.h:171:1:
	IPPROTO_OSPF                         = 89                 // in.h:176:1:
	IPPROTO_PIM                          = 103                // in.h:177:1:
	IPPROTO_PUP                          = 12                 // in.h:161:1:
	IPPROTO_RAW                          = 255                // in.h:181:1:
	IPPROTO_ROUTING                      = 43                 // in.h:165:1:
	IPPROTO_RSVP                         = 46                 // in.h:167:1:
	IPPROTO_SCTP                         = 132                // in.h:178:1:
	IPPROTO_TCP                          = 6                  // in.h:159:1:
	IPPROTO_UDP                          = 17                 // in.h:162:1:
	IPSEC_PREF_NEVER                     = 0x01               // in.h:941:1:
	IPSEC_PREF_REQUIRED                  = 0x02               // in.h:942:1:
	IPSEC_PREF_UNIQUE                    = 0x04               // in.h:943:1:
	IPV6_ADD_MEMBERSHIP                  = 0x9                // in.h:1224:1:
	IPV6_BOUND_IF                        = 0x41               // in.h:1307:1:
	IPV6_CHECKSUM                        = 0x18               // in.h:1257:1:
	IPV6_DONTFRAG                        = 0x21               // in.h:1260:1:
	IPV6_DROP_MEMBERSHIP                 = 0xa                // in.h:1226:1:
	IPV6_DSTOPTS                         = 0xf                // in.h:1234:1:
	IPV6_FLOWINFO_FLOWLABEL              = 0xffff0f00         // in.h:447:1:
	IPV6_FLOWINFO_TCLASS                 = 0x0000f00f         // in.h:448:1:
	IPV6_HOPLIMIT                        = 0xc                // in.h:1231:1:
	IPV6_HOPOPTS                         = 0xe                // in.h:1233:1:
	IPV6_JOIN_GROUP                      = 0x9                // in.h:1202:1:
	IPV6_LEAVE_GROUP                     = 0xa                // in.h:1204:1:
	IPV6_MULTICAST_HOPS                  = 0x7                // in.h:1196:1:
	IPV6_MULTICAST_IF                    = 0x6                // in.h:1193:1:
	IPV6_MULTICAST_LOOP                  = 0x8                // in.h:1199:1:
	IPV6_NEXTHOP                         = 0xd                // in.h:1232:1:
	IPV6_PAD1_OPT                        = 0                  // in.h:1314:1:
	IPV6_PATHMTU                         = 0x25               // in.h:1264:1:
	IPV6_PKTINFO                         = 0xb                // in.h:1229:1:
	IPV6_PREFER_SRC_CGA                  = 0x00000020         // in.h:1289:1:
	IPV6_PREFER_SRC_CGADEFAULT           = 16                 // in.h:1296:1:
	IPV6_PREFER_SRC_CGAMASK              = 48                 // in.h:1295:1:
	IPV6_PREFER_SRC_COA                  = 0x00000002         // in.h:1285:1:
	IPV6_PREFER_SRC_DEFAULT              = 21                 // in.h:1301:1:
	IPV6_PREFER_SRC_HOME                 = 0x00000001         // in.h:1284:1:
	IPV6_PREFER_SRC_MASK                 = 63                 // in.h:1298:1:
	IPV6_PREFER_SRC_MIPDEFAULT           = 1                  // in.h:1292:1:
	IPV6_PREFER_SRC_MIPMASK              = 3                  // in.h:1291:1:
	IPV6_PREFER_SRC_NONCGA               = 0x00000010         // in.h:1288:1:
	IPV6_PREFER_SRC_PUBLIC               = 0x00000004         // in.h:1286:1:
	IPV6_PREFER_SRC_TMP                  = 0x00000008         // in.h:1287:1:
	IPV6_PREFER_SRC_TMPDEFAULT           = 4                  // in.h:1294:1:
	IPV6_PREFER_SRC_TMPMASK              = 12                 // in.h:1293:1:
	IPV6_RECVDSTOPTS                     = 0x28               // in.h:1271:1:
	IPV6_RECVHOPLIMIT                    = 0x13               // in.h:1240:1:
	IPV6_RECVHOPOPTS                     = 0x14               // in.h:1241:1:
	IPV6_RECVPATHMTU                     = 0x24               // in.h:1263:1:
	IPV6_RECVPKTINFO                     = 0x12               // in.h:1239:1:
	IPV6_RECVRTHDR                       = 0x16               // in.h:1249:1:
	IPV6_RECVRTHDRDSTOPTS                = 0x17               // in.h:1255:1:
	IPV6_RECVTCLASS                      = 0x19               // in.h:1258:1:
	IPV6_RTHDR                           = 0x10               // in.h:1236:1:
	IPV6_RTHDRDSTOPTS                    = 0x11               // in.h:1237:1:
	IPV6_RTHDR_TYPE_0                    = 0                  // in.h:1152:1:
	IPV6_SEC_OPT                         = 0x22               // in.h:1261:1:
	IPV6_SRC_PREFERENCES                 = 0x23               // in.h:1262:1:
	IPV6_TCLASS                          = 0x26               // in.h:1265:1:
	IPV6_UNICAST_HOPS                    = 0x5                // in.h:1190:1:
	IPV6_UNSPEC_SRC                      = 0x42               // in.h:1308:1:
	IPV6_USE_MIN_MTU                     = 0x20               // in.h:1259:1:
	IPV6_V6ONLY                          = 0x27               // in.h:1266:1:
	IP_ADD_MEMBERSHIP                    = 0x13               // in.h:921:1:
	IP_ADD_SOURCE_MEMBERSHIP             = 0x17               // in.h:925:1:
	IP_BLOCK_SOURCE                      = 0x15               // in.h:923:1:
	IP_BOUND_IF                          = 0x41               // in.h:976:1:
	IP_BROADCAST                         = 0x106              // in.h:994:1:
	IP_BROADCAST_TTL                     = 0x43               // in.h:978:1:
	IP_DEFAULT_MULTICAST_LOOP            = 1                  // in.h:1009:1:
	IP_DEFAULT_MULTICAST_TTL             = 1                  // in.h:1008:1:
	IP_DHCPINIT_IF                       = 0x45               // in.h:980:1:
	IP_DONTFRAG                          = 0x1b               // in.h:934:1:
	IP_DONTROUTE                         = 0x105              // in.h:990:1:
	IP_DROP_MEMBERSHIP                   = 0x14               // in.h:922:1:
	IP_DROP_SOURCE_MEMBERSHIP            = 0x18               // in.h:926:1:
	IP_HDRINCL                           = 2                  // in.h:899:1:
	IP_MULTICAST_IF                      = 0x10               // in.h:918:1:
	IP_MULTICAST_LOOP                    = 0x12               // in.h:920:1:
	IP_MULTICAST_TTL                     = 0x11               // in.h:919:1:
	IP_NEXTHOP                           = 0x19               // in.h:927:1:
	IP_OPTIONS                           = 1                  // in.h:896:1:
	IP_PKTINFO                           = 0x1a               // in.h:932:1:
	IP_RECVDSTADDR                       = 0x7                // in.h:911:1:
	IP_RECVIF                            = 0x9                // in.h:913:1:
	IP_RECVOPTS                          = 0x5                // in.h:909:1:
	IP_RECVPKTINFO                       = 0x1a               // in.h:933:1:
	IP_RECVRETOPTS                       = 0x6                // in.h:910:1:
	IP_RECVSLLA                          = 0xa                // in.h:914:1:
	IP_RECVTOS                           = 0xc                // in.h:916:1:
	IP_RECVTTL                           = 0xb                // in.h:915:1:
	IP_RETOPTS                           = 0x8                // in.h:912:1:
	IP_REUSEADDR                         = 0x104              // in.h:986:1:
	IP_SEC_OPT                           = 0x22               // in.h:940:1:
	IP_TOS                               = 3                  // in.h:902:1:
	IP_TTL                               = 4                  // in.h:906:1:
	IP_UNBLOCK_SOURCE                    = 0x16               // in.h:924:1:
	IP_UNSPEC_SRC                        = 0x42               // in.h:977:1:
	ITIMER_PROF                          = 2                  // time.h:201:1:
	ITIMER_REAL                          = 0                  // time.h:199:1:
	ITIMER_REALPROF                      = 3                  // time.h:204:1:
	ITIMER_VIRTUAL                       = 1                  // time.h:200:1:
	LINUX_SLL_BROADCAST                  = 1                  // socket_impl.h:122:1:
	LINUX_SLL_HOST                       = 0                  // socket_impl.h:121:1:
	LINUX_SLL_MULTICAST                  = 2                  // socket_impl.h:123:1:
	LINUX_SLL_OTHERHOST                  = 3                  // socket_impl.h:124:1:
	LINUX_SLL_OUTGOING                   = 4                  // socket_impl.h:125:1:
	MAXBSIZE                             = 8192               // param.h:249:1:
	MAXFRAG                              = 8                  // param.h:252:1:
	MAXLINK                              = 32767              // param.h:126:1:
	MAXLINKNAMELEN                       = 32                 // param.h:209:1:
	MAXNAMELEN                           = 256                // param.h:202:1:
	MAXOFFSET_T                          = 0x7fffffffffffffff // param.h:258:1:
	MAXOFF_T                             = 0x7fffffffffffffff // param.h:257:1:
	MAXPATHLEN                           = 1024               // param.h:199:1:
	MAXPROJID                            = 2147483647         // param.h:125:1:
	MAXSYMLINKS                          = 20                 // param.h:201:1:
	MAXUID                               = 2147483647         // param.h:123:1:
	MAX_CANON                            = 256                // param.h:89:1:
	MAX_INPUT                            = 512                // param.h:86:1:
	MCAST_BLOCK_SOURCE                   = 0x2b               // in.h:1278:1:
	MCAST_EXCLUDE                        = 2                  // in.h:1122:1:
	MCAST_INCLUDE                        = 1                  // in.h:1121:1:
	MCAST_JOIN_GROUP                     = 0x29               // in.h:1276:1:
	MCAST_JOIN_SOURCE_GROUP              = 0x2d               // in.h:1280:1:
	MCAST_LEAVE_GROUP                    = 0x2a               // in.h:1277:1:
	MCAST_LEAVE_SOURCE_GROUP             = 0x2e               // in.h:1281:1:
	MCAST_UNBLOCK_SOURCE                 = 0x2c               // in.h:1279:1:
	MICROSEC                             = 1000000            // time.h:246:1:
	MILLISEC                             = 1000               // time.h:245:1:
	MINEPHUID                            = 0x80000000         // param.h:128:1:
	MSG_CTRUNC                           = 0x10               // socket.h:429:1:
	MSG_DONTROUTE                        = 0x4                // socket.h:427:1:
	MSG_DONTWAIT                         = 0x80               // socket.h:432:1:
	MSG_DUPCTRL                          = 0x800              // socket.h:435:1:
	MSG_EOR                              = 0x8                // socket.h:428:1:
	MSG_MAXIOVLEN                        = 16                 // socket.h:440:1:
	MSG_NOSIGNAL                         = 0x200              // socket.h:434:1:
	MSG_NOTIFICATION                     = 0x100              // socket.h:433:1:
	MSG_OOB                              = 0x1                // socket.h:425:1:
	MSG_PEEK                             = 0x2                // socket.h:426:1:
	MSG_TRUNC                            = 0x20               // socket.h:430:1:
	MSG_WAITALL                          = 0x40               // socket.h:431:1:
	MSG_XPG4_2                           = 0x8000             // socket.h:437:1:
	NADDR                                = 13                 // param.h:212:1:
	NANOSEC                              = 1000000000         // time.h:247:1:
	NBBY                                 = 8                  // select.h:103:1:
	NBPS                                 = 0x20000            // param.h:165:1:
	NBPSCTR                              = 512                // param.h:166:1:
	NCARGS                               = 2097152            // param.h:294:1:
	NCARGS32                             = 0x100000           // param.h:291:1:
	NCARGS64                             = 0x200000           // param.h:292:1:
	NC_APPLETALK                         = "appletalk"        // netconfig.h:108:1:
	NC_BROADCAST                         = 02                 // netconfig.h:85:1:
	NC_CCITT                             = "ccitt"            // netconfig.h:102:1:
	NC_CHAOS                             = "chaos"            // netconfig.h:97:1:
	NC_DATAKIT                           = "datakit"          // netconfig.h:101:1:
	NC_DECNET                            = "decnet"           // netconfig.h:104:1:
	NC_DLI                               = "dli"              // netconfig.h:105:1:
	NC_ECMA                              = "ecma"             // netconfig.h:100:1:
	NC_GOSIP                             = "gosip"            // netconfig.h:114:1:
	NC_HYLINK                            = "hylink"           // netconfig.h:107:1:
	NC_IBTF                              = "ibtf"             // netconfig.h:138:1:
	NC_ICMP                              = "icmp"             // netconfig.h:132:1:
	NC_IEEE802                           = "ieee802"          // netconfig.h:110:1:
	NC_IMPLINK                           = "implink"          // netconfig.h:95:1:
	NC_INET                              = "inet"             // netconfig.h:93:1:
	NC_INET6                             = "inet6"            // netconfig.h:94:1:
	NC_KDAPL                             = "kdapl"            // netconfig.h:139:1:
	NC_KVIPL                             = "kvipl"            // netconfig.h:137:1:
	NC_LAT                               = "lat"              // netconfig.h:106:1:
	NC_LOOPBACK                          = "loopback"         // netconfig.h:92:1:
	NC_NBS                               = "nbs"              // netconfig.h:99:1:
	NC_NIT                               = "nit"              // netconfig.h:109:1:
	NC_NOFLAG                            = 00                 // netconfig.h:83:1:
	NC_NOPROTO                           = "-"                // netconfig.h:129:1:
	NC_NOPROTOFMLY                       = "-"                // netconfig.h:91:1:
	NC_NS                                = "ns"               // netconfig.h:98:1:
	NC_OSI                               = "osi"              // netconfig.h:111:1:
	NC_OSINET                            = "osinet"           // netconfig.h:113:1:
	NC_PUP                               = "pup"              // netconfig.h:96:1:
	NC_RDMA                              = "rdma"             // netconfig.h:123:1:
	NC_SNA                               = "sna"              // netconfig.h:103:1:
	NC_TCP                               = "tcp"              // netconfig.h:130:1:
	NC_TPI_CLTS                          = 1                  // netconfig.h:65:1:
	NC_TPI_COTS                          = 2                  // netconfig.h:66:1:
	NC_TPI_COTS_ORD                      = 3                  // netconfig.h:67:1:
	NC_TPI_RAW                           = 4                  // netconfig.h:68:1:
	NC_TPI_RDMA                          = 5                  // netconfig.h:77:1:
	NC_UDP                               = "udp"              // netconfig.h:131:1:
	NC_VISIBLE                           = 01                 // netconfig.h:84:1:
	NC_X25                               = "x25"              // netconfig.h:112:1:
	NETCONFIG                            = "/etc/netconfig"   // netconfig.h:41:1:
	NETPATH                              = "NETPATH"          // netconfig.h:42:1:
	NGROUPS_MAX_DEFAULT                  = 16                 // param.h:148:1:
	NGROUPS_OLDMAX                       = 32                 // param.h:143:1:
	NGROUPS_UMAX                         = 1024               // param.h:142:1:
	NGROUPS_UMIN                         = 0                  // param.h:141:1:
	NMOUNT                               = 40                 // param.h:130:1:
	NOFILE                               = 20                 // param.h:132:1:
	NZERO                                = 20                 // param.h:153:1:
	PF_802                               = 18                 // socket.h:338:1:
	PF_APPLETALK                         = 16                 // socket.h:336:1:
	PF_CCITT                             = 10                 // socket.h:330:1:
	PF_CHAOS                             = 5                  // socket.h:325:1:
	PF_DATAKIT                           = 9                  // socket.h:329:1:
	PF_DECnet                            = 12                 // socket.h:332:1:
	PF_DLI                               = 13                 // socket.h:333:1:
	PF_ECMA                              = 8                  // socket.h:328:1:
	PF_FILE                              = 1                  // socket.h:321:1:
	PF_GOSIP                             = 22                 // socket.h:342:1:
	PF_HYLINK                            = 15                 // socket.h:335:1:
	PF_IMPLINK                           = 3                  // socket.h:323:1:
	PF_INET                              = 2                  // socket.h:322:1:
	PF_INET6                             = 26                 // socket.h:346:1:
	PF_INET_OFFLOAD                      = 30                 // socket.h:350:1:
	PF_IPX                               = 23                 // socket.h:343:1:
	PF_KEY                               = 27                 // socket.h:347:1:
	PF_LAT                               = 14                 // socket.h:334:1:
	PF_LINK                              = 25                 // socket.h:345:1:
	PF_LOCAL                             = 1                  // socket.h:320:1:
	PF_LX_NETLINK                        = 33                 // socket.h:353:1:
	PF_MAX                               = 33                 // socket.h:355:1:
	PF_NBS                               = 7                  // socket.h:327:1:
	PF_NCA                               = 28                 // socket.h:348:1:
	PF_NIT                               = 17                 // socket.h:337:1:
	PF_NS                                = 6                  // socket.h:326:1:
	PF_OSI                               = 19                 // socket.h:339:1:
	PF_OSINET                            = 21                 // socket.h:341:1:
	PF_PACKET                            = 32                 // socket.h:352:1:
	PF_POLICY                            = 29                 // socket.h:349:1:
	PF_PUP                               = 4                  // socket.h:324:1:
	PF_ROUTE                             = 24                 // socket.h:344:1:
	PF_SNA                               = 11                 // socket.h:331:1:
	PF_TRILL                             = 31                 // socket.h:351:1:
	PF_UNIX                              = 1                  // socket.h:319:1:
	PF_UNSPEC                            = 0                  // socket.h:318:1:
	PF_X25                               = 20                 // socket.h:340:1:
	PIPE_BUF                             = 5120               // param.h:221:1:
	PIPE_MAX                             = 5120               // param.h:225:1:
	POLLERR                              = 0x0008             // poll.h:74:1:
	POLLET                               = 0x2000             // poll.h:84:1:
	POLLHUP                              = 0x0010             // poll.h:75:1:
	POLLIN                               = 0x0001             // poll.h:59:1:
	POLLNORM                             = 64                 // poll.h:68:1:
	POLLNVAL                             = 0x0020             // poll.h:76:1:
	POLLONESHOT                          = 0x1000             // poll.h:83:1:
	POLLOUT                              = 0x0004             // poll.h:61:1:
	POLLPRI                              = 0x0002             // poll.h:60:1:
	POLLRDBAND                           = 0x0080             // poll.h:64:1:
	POLLRDHUP                            = 0x4000             // poll.h:66:1:
	POLLRDNORM                           = 0x0040             // poll.h:62:1:
	POLLREMOVE                           = 0x0800             // poll.h:82:1:
	POLLWRBAND                           = 0x0100             // poll.h:65:1:
	POLLWRNORM                           = 4                  // poll.h:63:1:
	PREMOTE                              = 39                 // param.h:185:1:
	PROTO_SDP                            = 257                // in.h:185:1:
	P_MYID                               = -1                 // types.h:632:1:
	REG_LABEL_BP                         = 2                  // machtypes.h:44:1:
	REG_LABEL_MAX                        = 8                  // machtypes.h:51:1:
	REG_LABEL_PC                         = 0                  // machtypes.h:42:1:
	REG_LABEL_R12                        = 4                  // machtypes.h:47:1:
	REG_LABEL_R13                        = 5                  // machtypes.h:48:1:
	REG_LABEL_R14                        = 6                  // machtypes.h:49:1:
	REG_LABEL_R15                        = 7                  // machtypes.h:50:1:
	REG_LABEL_RBX                        = 3                  // machtypes.h:46:1:
	REG_LABEL_SP                         = 1                  // machtypes.h:43:1:
	SCM_RIGHTS                           = 0x1010             // socket.h:197:1:
	SCM_TIMESTAMP                        = 4115               // socket.h:201:1:
	SCM_UCRED                            = 0x1012             // socket.h:199:1:
	SCTRSHFT                             = 9                  // param.h:168:1:
	SEC                                  = 1                  // time.h:244:1:
	SHUT_RD                              = 0                  // socket.h:458:1:
	SHUT_RDWR                            = 2                  // socket.h:460:1:
	SHUT_WR                              = 1                  // socket.h:459:1:
	SOCK_CLOEXEC                         = 0x080000           // socket.h:127:1:
	SOCK_DGRAM                           = 1                  // socket.h:113:1:
	SOCK_NDELAY                          = 0x200000           // socket.h:129:1:
	SOCK_NONBLOCK                        = 0x100000           // socket.h:128:1:
	SOCK_RAW                             = 4                  // socket.h:114:1:
	SOCK_RDM                             = 5                  // socket.h:120:1:
	SOCK_SEQPACKET                       = 6                  // socket.h:121:1:
	SOCK_STREAM                          = 2                  // socket.h:112:1:
	SOCK_TYPE_MASK                       = 0xffff             // socket.h:122:1:
	SOL_FILTER                           = 0xfffc             // socket.h:267:1:
	SOL_PACKET                           = 0xfffd             // socket.h:266:1:
	SOL_ROUTE                            = 0xfffe             // socket.h:264:1:
	SOL_SOCKET                           = 0xffff             // socket.h:262:1:
	SOMAXCONN                            = 128                // socket.h:360:1:
	SO_ACCEPTCONN                        = 0x0002             // socket.h:135:1:
	SO_ALLZONES                          = 0x1014             // socket.h:202:1:
	SO_ANON_MLP                          = 0x100a             // socket.h:191:1:
	SO_ATTACH_FILTER                     = 0x40000001         // socket.h:157:1:
	SO_BROADCAST                         = 0x0020             // socket.h:139:1:
	SO_DEBUG                             = 0x0001             // socket.h:134:1:
	SO_DETACH_FILTER                     = 0x40000002         // socket.h:158:1:
	SO_DGRAM_ERRIND                      = 0x0200             // socket.h:143:1:
	SO_DOMAIN                            = 0x100c             // socket.h:193:1:
	SO_DONTLINGER                        = -129               // socket.h:177:1:
	SO_DONTROUTE                         = 0x0010             // socket.h:138:1:
	SO_ERROR                             = 0x1007             // socket.h:188:1:
	SO_EXCLBIND                          = 0x1015             // socket.h:203:1:
	SO_KEEPALIVE                         = 0x0008             // socket.h:137:1:
	SO_LINGER                            = 0x0080             // socket.h:141:1:
	SO_MAC_EXEMPT                        = 0x100b             // socket.h:192:1:
	SO_MAC_IMPLICIT                      = 0x1016             // socket.h:204:1:
	SO_OOBINLINE                         = 0x0100             // socket.h:142:1:
	SO_PROTOTYPE                         = 0x1009             // socket.h:190:1:
	SO_RCVBUF                            = 0x1002             // socket.h:183:1:
	SO_RCVLOWAT                          = 0x1004             // socket.h:185:1:
	SO_RCVPSH                            = 0x100d             // socket.h:194:1:
	SO_RCVTIMEO                          = 0x1006             // socket.h:187:1:
	SO_RECVUCRED                         = 0x0400             // socket.h:144:1:
	SO_REUSEADDR                         = 0x0004             // socket.h:136:1:
	SO_SECATTR                           = 0x1011             // socket.h:198:1:
	SO_SNDBUF                            = 0x1001             // socket.h:182:1:
	SO_SNDLOWAT                          = 0x1003             // socket.h:184:1:
	SO_SNDTIMEO                          = 0x1005             // socket.h:186:1:
	SO_TIMESTAMP                         = 0x1013             // socket.h:200:1:
	SO_TYPE                              = 0x1008             // socket.h:189:1:
	SO_USELOOPBACK                       = 0x0040             // socket.h:140:1:
	SO_VRRP                              = 0x1017             // socket.h:205:1:
	SYSNAME                              = 9                  // param.h:184:1:
	TIMER_ABSTIME                        = 0x1                // time_impl.h:134:1:
	TIMER_RELTIME                        = 0x0                // time_impl.h:133:1:
	TIME_UTC                             = 0x1                // time.h:306:1:
	TYPICALMAXPATHLEN                    = 64                 // param.h:200:1:
	UBSIZE                               = 512                // param.h:167:1:
	UID_DLADM                            = 15                 // param.h:98:1:
	UID_NETADM                           = 16                 // param.h:99:1:
	UID_NOACCESS                         = 60002              // param.h:101:1:
	UID_NOBODY                           = 60001              // param.h:94:1:
	UID_UNKNOWN                          = 96                 // param.h:96:1:
	UIOA_ALLOC                           = 0x0001             // uio.h:194:1:
	UIOA_CLR                             = -16                // uio.h:199:1:
	UIOA_ENABLED                         = 0x0004             // uio.h:196:1:
	UIOA_FINI                            = 0x0008             // uio.h:197:1:
	UIOA_INIT                            = 0x0002             // uio.h:195:1:
	UIOA_IOV_MAX                         = 16                 // uio.h:112:1:
	UIOA_POLL                            = 0x0010             // uio.h:201:1:
	UIO_ASYNC                            = 0x0002             // uio.h:237:1:
	UIO_COPY_CACHED                      = 0x0001             // uio.h:235:1:
	UIO_COPY_DEFAULT                     = 0x0000             // uio.h:234:1:
	UIO_XUIO                             = 0x0004             // uio.h:238:1:
	X_ACL_ACE_ENABLED                    = 0x2                // unistd.h:349:1:
	X_ACL_ACLENT_ENABLED                 = 0x1                // unistd.h:348:1:
	X_ALIGNMENT_REQUIRED                 = 1                  // isa_defs.h:262:1:
	X_BIT_FIELDS_LTOH                    = 0                  // isa_defs.h:245:1:
	X_BOOL_ALIGNMENT                     = 1                  // isa_defs.h:248:1:
	X_CASE_INSENSITIVE                   = 0x2                // unistd.h:342:1:
	X_CASE_SENSITIVE                     = 0x1                // unistd.h:341:1:
	X_CHAR_ALIGNMENT                     = 1                  // isa_defs.h:249:1:
	X_CHAR_IS_SIGNED                     = 0                  // isa_defs.h:247:1:
	X_CLOCKID_T                          = 0                  // types.h:568:1:
	X_CLOCK_T                            = 0                  // types.h:563:1:
	X_COND_MAGIC                         = 0x4356             // types.h:426:1:
	X_CS_LFS64_CFLAGS                    = 72                 // unistd.h:61:1:
	X_CS_LFS64_LDFLAGS                   = 73                 // unistd.h:62:1:
	X_CS_LFS64_LIBS                      = 74                 // unistd.h:63:1:
	X_CS_LFS64_LINTFLAGS                 = 75                 // unistd.h:64:1:
	X_CS_LFS_CFLAGS                      = 68                 // unistd.h:56:1:
	X_CS_LFS_LDFLAGS                     = 69                 // unistd.h:57:1:
	X_CS_LFS_LIBS                        = 70                 // unistd.h:58:1:
	X_CS_LFS_LINTFLAGS                   = 71                 // unistd.h:59:1:
	X_CS_PATH                            = 65                 // unistd.h:50:1:
	X_CS_POSIX_V6_ILP32_OFF32_CFLAGS     = 800                // unistd.h:85:1:
	X_CS_POSIX_V6_ILP32_OFF32_LDFLAGS    = 801                // unistd.h:86:1:
	X_CS_POSIX_V6_ILP32_OFF32_LIBS       = 802                // unistd.h:87:1:
	X_CS_POSIX_V6_ILP32_OFF32_LINTFLAGS  = 803                // unistd.h:88:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_CFLAGS    = 804                // unistd.h:89:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LDFLAGS   = 805                // unistd.h:90:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LIBS      = 806                // unistd.h:91:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LINTFLAGS = 807                // unistd.h:92:1:
	X_CS_POSIX_V6_LP64_OFF64_CFLAGS      = 808                // unistd.h:93:1:
	X_CS_POSIX_V6_LP64_OFF64_LDFLAGS     = 809                // unistd.h:94:1:
	X_CS_POSIX_V6_LP64_OFF64_LIBS        = 810                // unistd.h:95:1:
	X_CS_POSIX_V6_LP64_OFF64_LINTFLAGS   = 811                // unistd.h:96:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_CFLAGS    = 812                // unistd.h:97:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LDFLAGS   = 813                // unistd.h:98:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LIBS      = 814                // unistd.h:99:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LINTFLAGS = 815                // unistd.h:100:1:
	X_CS_POSIX_V6_WIDTH_RESTRICTED_ENVS  = 816                // unistd.h:101:1:
	X_CS_XBS5_ILP32_OFF32_CFLAGS         = 700                // unistd.h:67:1:
	X_CS_XBS5_ILP32_OFF32_LDFLAGS        = 701                // unistd.h:68:1:
	X_CS_XBS5_ILP32_OFF32_LIBS           = 702                // unistd.h:69:1:
	X_CS_XBS5_ILP32_OFF32_LINTFLAGS      = 703                // unistd.h:70:1:
	X_CS_XBS5_ILP32_OFFBIG_CFLAGS        = 705                // unistd.h:71:1:
	X_CS_XBS5_ILP32_OFFBIG_LDFLAGS       = 706                // unistd.h:72:1:
	X_CS_XBS5_ILP32_OFFBIG_LIBS          = 707                // unistd.h:73:1:
	X_CS_XBS5_ILP32_OFFBIG_LINTFLAGS     = 708                // unistd.h:74:1:
	X_CS_XBS5_LP64_OFF64_CFLAGS          = 709                // unistd.h:75:1:
	X_CS_XBS5_LP64_OFF64_LDFLAGS         = 710                // unistd.h:76:1:
	X_CS_XBS5_LP64_OFF64_LIBS            = 711                // unistd.h:77:1:
	X_CS_XBS5_LP64_OFF64_LINTFLAGS       = 712                // unistd.h:78:1:
	X_CS_XBS5_LPBIG_OFFBIG_CFLAGS        = 713                // unistd.h:79:1:
	X_CS_XBS5_LPBIG_OFFBIG_LDFLAGS       = 714                // unistd.h:80:1:
	X_CS_XBS5_LPBIG_OFFBIG_LIBS          = 715                // unistd.h:81:1:
	X_CS_XBS5_LPBIG_OFFBIG_LINTFLAGS     = 716                // unistd.h:82:1:
	X_DMA_USES_PHYSADDR                  = 0                  // isa_defs.h:281:1:
	X_DONT_USE_1275_GENERIC_NAMES        = 0                  // isa_defs.h:287:1:
	X_DOUBLE_ALIGNMENT                   = 8                  // isa_defs.h:256:1:
	X_DOUBLE_COMPLEX_ALIGNMENT           = 8                  // isa_defs.h:257:1:
	X_DTRACE_VERSION                     = 1                  // feature_tests.h:490:1:
	X_FILE_OFFSET_BITS                   = 64                 // <builtin>:25:1:
	X_FIRMWARE_NEEDS_FDISK               = 0                  // isa_defs.h:282:1:
	X_FLOAT_ALIGNMENT                    = 4                  // isa_defs.h:252:1:
	X_FLOAT_COMPLEX_ALIGNMENT            = 4                  // isa_defs.h:253:1:
	X_HAVE_CPUID_INSN                    = 0                  // isa_defs.h:288:1:
	X_IEEE_754                           = 0                  // isa_defs.h:246:1:
	X_INT64_TYPE                         = 0                  // int_types.h:82:1:
	X_INT_ALIGNMENT                      = 4                  // isa_defs.h:251:1:
	X_IN_ADDR_T                          = 0                  // byteorder.h:78:1:
	X_IN_PORT_T                          = 0                  // byteorder.h:73:1:
	X_IPADDR_T                           = 0                  // in.h:98:1:
	X_ISO_CPP_14882_1998                 = 0                  // feature_tests.h:466:1:
	X_ISO_C_9899_1999                    = 0                  // feature_tests.h:472:1:
	X_ISO_C_9899_2011                    = 0                  // feature_tests.h:478:1:
	X_ISO_TIME_ISO_H                     = 0                  // time_iso.h:46:1:
	X_LARGEFILE64_SOURCE                 = 1                  // feature_tests.h:231:1:
	X_LARGEFILE_SOURCE                   = 1                  // feature_tests.h:235:1:
	X_LITTLE_ENDIAN                      = 0                  // isa_defs.h:242:1:
	X_LOCALE_T                           = 0                  // time.h:291:1:
	X_LONGLONG_TYPE                      = 0                  // feature_tests.h:412:1:
	X_LONG_ALIGNMENT                     = 8                  // isa_defs.h:254:1:
	X_LONG_DOUBLE_ALIGNMENT              = 16                 // isa_defs.h:258:1:
	X_LONG_DOUBLE_COMPLEX_ALIGNMENT      = 16                 // isa_defs.h:259:1:
	X_LONG_LONG_ALIGNMENT                = 8                  // isa_defs.h:255:1:
	X_LONG_LONG_ALIGNMENT_32             = 4                  // isa_defs.h:268:1:
	X_LONG_LONG_LTOH                     = 0                  // isa_defs.h:244:1:
	X_LP64                               = 1                  // <predefined>:286:1:
	X_MAX_ALIGNMENT                      = 16                 // isa_defs.h:261:1:
	X_MULTI_DATAMODEL                    = 0                  // isa_defs.h:279:1:
	X_MUTEX_MAGIC                        = 0x4d58             // types.h:424:1:
	X_NBBY                               = 8                  // select.h:100:1:
	X_NETINET_IN_H                       = 0                  // in.h:33:1:
	X_NET_IF_DL_H                        = 0                  // if_dl.h:39:1:
	X_NORETURN_KYWD                      = 0                  // feature_tests.h:448:1:
	X_OFF_T                              = 0                  // types.h:142:1:
	X_OLD_IPV6_RECVDSTOPTS               = 0x15               // in.h:1247:1:
	X_PC_2_SYMLINKS                      = 19                 // unistd.h:309:1:
	X_PC_ACCESS_FILTERING                = 25                 // unistd.h:315:1:
	X_PC_ACL_ENABLED                     = 20                 // unistd.h:310:1:
	X_PC_ALLOC_SIZE_MIN                  = 13                 // unistd.h:303:1:
	X_PC_ASYNC_IO                        = 10                 // unistd.h:299:1:
	X_PC_CASE_BEHAVIOR                   = 22                 // unistd.h:312:1:
	X_PC_CHOWN_RESTRICTED                = 9                  // unistd.h:297:1:
	X_PC_FILESIZEBITS                    = 67                 // unistd.h:325:1:
	X_PC_LAST                            = 101                // unistd.h:336:1:
	X_PC_LINK_MAX                        = 1                  // unistd.h:289:1:
	X_PC_MAX_CANON                       = 2                  // unistd.h:290:1:
	X_PC_MAX_INPUT                       = 3                  // unistd.h:291:1:
	X_PC_MIN_HOLE_SIZE                   = 21                 // unistd.h:311:1:
	X_PC_NAME_MAX                        = 4                  // unistd.h:292:1:
	X_PC_NO_TRUNC                        = 7                  // unistd.h:295:1:
	X_PC_PATH_MAX                        = 5                  // unistd.h:293:1:
	X_PC_PIPE_BUF                        = 6                  // unistd.h:294:1:
	X_PC_PRIO_IO                         = 11                 // unistd.h:300:1:
	X_PC_REC_INCR_XFER_SIZE              = 14                 // unistd.h:304:1:
	X_PC_REC_MAX_XFER_SIZE               = 15                 // unistd.h:305:1:
	X_PC_REC_MIN_XFER_SIZE               = 16                 // unistd.h:306:1:
	X_PC_REC_XFER_ALIGN                  = 17                 // unistd.h:307:1:
	X_PC_SATTR_ENABLED                   = 23                 // unistd.h:313:1:
	X_PC_SATTR_EXISTS                    = 24                 // unistd.h:314:1:
	X_PC_SYMLINK_MAX                     = 18                 // unistd.h:308:1:
	X_PC_SYNC_IO                         = 12                 // unistd.h:301:1:
	X_PC_TIMESTAMP_RESOLUTION            = 26                 // unistd.h:317:1:
	X_PC_VDISABLE                        = 8                  // unistd.h:296:1:
	X_PC_XATTR_ENABLED                   = 100                // unistd.h:330:1:
	X_PC_XATTR_EXISTS                    = 101                // unistd.h:331:1:
	X_POINTER_ALIGNMENT                  = 8                  // isa_defs.h:260:1:
	X_POSIX2_CHAR_TERM                   = 1                  // unistd.h:391:1:
	X_POSIX2_C_BIND                      = 1                  // unistd.h:401:1:
	X_POSIX2_C_DEV                       = 1                  // unistd.h:402:1:
	X_POSIX2_C_VERSION                   = 199209             // unistd.h:376:1:
	X_POSIX2_FORT_RUN                    = 1                  // unistd.h:403:1:
	X_POSIX2_LOCALEDEF                   = 1                  // unistd.h:404:1:
	X_POSIX2_SW_DEV                      = 1                  // unistd.h:405:1:
	X_POSIX2_UPE                         = 1                  // unistd.h:406:1:
	X_POSIX2_VERSION                     = 199209             // unistd.h:363:1:
	X_POSIX_REGEXP                       = 1                  // unistd.h:410:1:
	X_POSIX_SHELL                        = 1                  // unistd.h:411:1:
	X_POSIX_VDISABLE                     = 0                  // param.h:70:1:
	X_POSIX_VERSION                      = 199506             // unistd.h:355:1:
	X_PSM_MODULES                        = 0                  // isa_defs.h:284:1:
	X_PTRDIFF_T                          = 0                  // types.h:112:1:
	X_RESTRICT_KYWD                      = 0                  // feature_tests.h:435:1:
	X_RTC_CONFIG                         = 0                  // isa_defs.h:285:1:
	X_RWL_MAGIC                          = 0x5257             // types.h:427:1:
	X_SA_FAMILY_T                        = 0                  // socket_impl.h:42:1:
	X_SC_2_CHAR_TERM                     = 66                 // unistd.h:175:1:
	X_SC_2_C_BIND                        = 45                 // unistd.h:153:1:
	X_SC_2_C_DEV                         = 46                 // unistd.h:154:1:
	X_SC_2_C_VERSION                     = 47                 // unistd.h:155:1:
	X_SC_2_FORT_DEV                      = 48                 // unistd.h:156:1:
	X_SC_2_FORT_RUN                      = 49                 // unistd.h:157:1:
	X_SC_2_LOCALEDEF                     = 50                 // unistd.h:158:1:
	X_SC_2_PBS                           = 724                // unistd.h:246:1:
	X_SC_2_PBS_ACCOUNTING                = 725                // unistd.h:247:1:
	X_SC_2_PBS_CHECKPOINT                = 726                // unistd.h:248:1:
	X_SC_2_PBS_LOCATE                    = 728                // unistd.h:249:1:
	X_SC_2_PBS_MESSAGE                   = 729                // unistd.h:250:1:
	X_SC_2_PBS_TRACK                     = 730                // unistd.h:251:1:
	X_SC_2_SW_DEV                        = 51                 // unistd.h:159:1:
	X_SC_2_UPE                           = 52                 // unistd.h:160:1:
	X_SC_2_VERSION                       = 53                 // unistd.h:161:1:
	X_SC_ADVISORY_INFO                   = 731                // unistd.h:252:1:
	X_SC_AIO_LISTIO_MAX                  = 18                 // unistd.h:125:1:
	X_SC_AIO_MAX                         = 19                 // unistd.h:126:1:
	X_SC_AIO_PRIO_DELTA_MAX              = 20                 // unistd.h:127:1:
	X_SC_ARG_MAX                         = 1                  // unistd.h:106:1:
	X_SC_ASYNCHRONOUS_IO                 = 21                 // unistd.h:128:1:
	X_SC_ATEXIT_MAX                      = 76                 // unistd.h:179:1:
	X_SC_AVPHYS_PAGES                    = 501                // unistd.h:190:1:
	X_SC_BARRIERS                        = 732                // unistd.h:253:1:
	X_SC_BC_BASE_MAX                     = 54                 // unistd.h:162:1:
	X_SC_BC_DIM_MAX                      = 55                 // unistd.h:163:1:
	X_SC_BC_SCALE_MAX                    = 56                 // unistd.h:164:1:
	X_SC_BC_STRING_MAX                   = 57                 // unistd.h:165:1:
	X_SC_CHILD_MAX                       = 2                  // unistd.h:107:1:
	X_SC_CLK_TCK                         = 3                  // unistd.h:108:1:
	X_SC_CLOCK_SELECTION                 = 733                // unistd.h:254:1:
	X_SC_COHER_BLKSZ                     = 503                // unistd.h:196:1:
	X_SC_COLL_WEIGHTS_MAX                = 58                 // unistd.h:166:1:
	X_SC_CPUID_MAX                       = 517                // unistd.h:211:1:
	X_SC_CPUTIME                         = 734                // unistd.h:255:1:
	X_SC_DCACHE_ASSOC                    = 513                // unistd.h:206:1:
	X_SC_DCACHE_BLKSZ                    = 510                // unistd.h:203:1:
	X_SC_DCACHE_LINESZ                   = 508                // unistd.h:201:1:
	X_SC_DCACHE_SZ                       = 506                // unistd.h:199:1:
	X_SC_DCACHE_TBLKSZ                   = 511                // unistd.h:204:1:
	X_SC_DELAYTIMER_MAX                  = 22                 // unistd.h:129:1:
	X_SC_EPHID_MAX                       = 518                // unistd.h:212:1:
	X_SC_EXPR_NEST_MAX                   = 59                 // unistd.h:167:1:
	X_SC_FSYNC                           = 23                 // unistd.h:130:1:
	X_SC_GETGR_R_SIZE_MAX                = 569                // unistd.h:220:1:
	X_SC_GETPW_R_SIZE_MAX                = 570                // unistd.h:221:1:
	X_SC_HOST_NAME_MAX                   = 735                // unistd.h:256:1:
	X_SC_ICACHE_ASSOC                    = 512                // unistd.h:205:1:
	X_SC_ICACHE_BLKSZ                    = 509                // unistd.h:202:1:
	X_SC_ICACHE_LINESZ                   = 507                // unistd.h:200:1:
	X_SC_ICACHE_SZ                       = 505                // unistd.h:198:1:
	X_SC_IOV_MAX                         = 77                 // unistd.h:180:1:
	X_SC_IPV6                            = 762                // unistd.h:283:1:
	X_SC_JOB_CONTROL                     = 6                  // unistd.h:111:1:
	X_SC_LINE_MAX                        = 60                 // unistd.h:168:1:
	X_SC_LOGIN_NAME_MAX                  = 571                // unistd.h:222:1:
	X_SC_LOGNAME_MAX                     = 10                 // unistd.h:116:1:
	X_SC_MAPPED_FILES                    = 24                 // unistd.h:131:1:
	X_SC_MAXPID                          = 514                // unistd.h:208:1:
	X_SC_MEMLOCK                         = 25                 // unistd.h:132:1:
	X_SC_MEMLOCK_RANGE                   = 26                 // unistd.h:133:1:
	X_SC_MEMORY_PROTECTION               = 27                 // unistd.h:134:1:
	X_SC_MESSAGE_PASSING                 = 28                 // unistd.h:135:1:
	X_SC_MONOTONIC_CLOCK                 = 736                // unistd.h:257:1:
	X_SC_MQ_OPEN_MAX                     = 29                 // unistd.h:136:1:
	X_SC_MQ_PRIO_MAX                     = 30                 // unistd.h:137:1:
	X_SC_NGROUPS_MAX                     = 4                  // unistd.h:109:1:
	X_SC_NPROCESSORS_CONF                = 14                 // unistd.h:120:1:
	X_SC_NPROCESSORS_MAX                 = 516                // unistd.h:210:1:
	X_SC_NPROCESSORS_ONLN                = 15                 // unistd.h:121:1:
	X_SC_OPEN_MAX                        = 5                  // unistd.h:110:1:
	X_SC_PAGESIZE                        = 11                 // unistd.h:117:1:
	X_SC_PAGE_SIZE                       = 11                 // unistd.h:182:1:
	X_SC_PASS_MAX                        = 9                  // unistd.h:115:1:
	X_SC_PHYS_PAGES                      = 500                // unistd.h:189:1:
	X_SC_PRIORITIZED_IO                  = 31                 // unistd.h:138:1:
	X_SC_PRIORITY_SCHEDULING             = 32                 // unistd.h:139:1:
	X_SC_RAW_SOCKETS                     = 763                // unistd.h:284:1:
	X_SC_READER_WRITER_LOCKS             = 737                // unistd.h:258:1:
	X_SC_REALTIME_SIGNALS                = 33                 // unistd.h:140:1:
	X_SC_REGEXP                          = 738                // unistd.h:259:1:
	X_SC_RE_DUP_MAX                      = 61                 // unistd.h:169:1:
	X_SC_RTSIG_MAX                       = 34                 // unistd.h:141:1:
	X_SC_SAVED_IDS                       = 7                  // unistd.h:112:1:
	X_SC_SEMAPHORES                      = 35                 // unistd.h:142:1:
	X_SC_SEM_NSEMS_MAX                   = 36                 // unistd.h:143:1:
	X_SC_SEM_VALUE_MAX                   = 37                 // unistd.h:144:1:
	X_SC_SHARED_MEMORY_OBJECTS           = 38                 // unistd.h:145:1:
	X_SC_SHELL                           = 739                // unistd.h:260:1:
	X_SC_SIGQUEUE_MAX                    = 39                 // unistd.h:146:1:
	X_SC_SIGRT_MAX                       = 41                 // unistd.h:148:1:
	X_SC_SIGRT_MIN                       = 40                 // unistd.h:147:1:
	X_SC_SPAWN                           = 740                // unistd.h:261:1:
	X_SC_SPIN_LOCKS                      = 741                // unistd.h:262:1:
	X_SC_SPLIT_CACHE                     = 504                // unistd.h:197:1:
	X_SC_SPORADIC_SERVER                 = 742                // unistd.h:263:1:
	X_SC_SS_REPL_MAX                     = 743                // unistd.h:264:1:
	X_SC_STACK_PROT                      = 515                // unistd.h:209:1:
	X_SC_STREAM_MAX                      = 16                 // unistd.h:122:1:
	X_SC_SYMLOOP_MAX                     = 744                // unistd.h:265:1:
	X_SC_SYNCHRONIZED_IO                 = 42                 // unistd.h:149:1:
	X_SC_THREADS                         = 576                // unistd.h:227:1:
	X_SC_THREAD_ATTR_STACKADDR           = 577                // unistd.h:228:1:
	X_SC_THREAD_ATTR_STACKSIZE           = 578                // unistd.h:229:1:
	X_SC_THREAD_CPUTIME                  = 745                // unistd.h:266:1:
	X_SC_THREAD_DESTRUCTOR_ITERATIONS    = 568                // unistd.h:219:1:
	X_SC_THREAD_KEYS_MAX                 = 572                // unistd.h:223:1:
	X_SC_THREAD_PRIORITY_SCHEDULING      = 579                // unistd.h:230:1:
	X_SC_THREAD_PRIO_INHERIT             = 580                // unistd.h:231:1:
	X_SC_THREAD_PRIO_PROTECT             = 581                // unistd.h:232:1:
	X_SC_THREAD_PROCESS_SHARED           = 582                // unistd.h:233:1:
	X_SC_THREAD_SAFE_FUNCTIONS           = 583                // unistd.h:234:1:
	X_SC_THREAD_SPORADIC_SERVER          = 746                // unistd.h:267:1:
	X_SC_THREAD_STACK_MIN                = 573                // unistd.h:224:1:
	X_SC_THREAD_THREADS_MAX              = 574                // unistd.h:225:1:
	X_SC_TIMEOUTS                        = 747                // unistd.h:268:1:
	X_SC_TIMERS                          = 43                 // unistd.h:150:1:
	X_SC_TIMER_MAX                       = 44                 // unistd.h:151:1:
	X_SC_TRACE                           = 748                // unistd.h:269:1:
	X_SC_TRACE_EVENT_FILTER              = 749                // unistd.h:270:1:
	X_SC_TRACE_EVENT_NAME_MAX            = 750                // unistd.h:271:1:
	X_SC_TRACE_INHERIT                   = 751                // unistd.h:272:1:
	X_SC_TRACE_LOG                       = 752                // unistd.h:273:1:
	X_SC_TRACE_NAME_MAX                  = 753                // unistd.h:274:1:
	X_SC_TRACE_SYS_MAX                   = 754                // unistd.h:275:1:
	X_SC_TRACE_USER_EVENT_MAX            = 755                // unistd.h:276:1:
	X_SC_TTY_NAME_MAX                    = 575                // unistd.h:226:1:
	X_SC_TYPED_MEMORY_OBJECTS            = 756                // unistd.h:277:1:
	X_SC_TZNAME_MAX                      = 17                 // unistd.h:123:1:
	X_SC_T_IOV_MAX                       = 79                 // unistd.h:186:1:
	X_SC_UADDR_MAX                       = 519                // unistd.h:213:1:
	X_SC_V6_ILP32_OFF32                  = 757                // unistd.h:278:1:
	X_SC_V6_ILP32_OFFBIG                 = 758                // unistd.h:279:1:
	X_SC_V6_LP64_OFF64                   = 759                // unistd.h:280:1:
	X_SC_V6_LPBIG_OFFBIG                 = 760                // unistd.h:281:1:
	X_SC_VERSION                         = 8                  // unistd.h:113:1:
	X_SC_XBS5_ILP32_OFF32                = 720                // unistd.h:240:1:
	X_SC_XBS5_ILP32_OFFBIG               = 721                // unistd.h:241:1:
	X_SC_XBS5_LP64_OFF64                 = 722                // unistd.h:242:1:
	X_SC_XBS5_LPBIG_OFFBIG               = 723                // unistd.h:243:1:
	X_SC_XOPEN_CRYPT                     = 62                 // unistd.h:170:1:
	X_SC_XOPEN_ENH_I18N                  = 63                 // unistd.h:171:1:
	X_SC_XOPEN_LEGACY                    = 717                // unistd.h:237:1:
	X_SC_XOPEN_REALTIME                  = 718                // unistd.h:238:1:
	X_SC_XOPEN_REALTIME_THREADS          = 719                // unistd.h:239:1:
	X_SC_XOPEN_SHM                       = 64                 // unistd.h:172:1:
	X_SC_XOPEN_STREAMS                   = 761                // unistd.h:282:1:
	X_SC_XOPEN_UNIX                      = 78                 // unistd.h:181:1:
	X_SC_XOPEN_VERSION                   = 12                 // unistd.h:118:1:
	X_SC_XOPEN_XCU_VERSION               = 67                 // unistd.h:176:1:
	X_SEMA_MAGIC                         = 0x534d             // types.h:425:1:
	X_SHORT_ALIGNMENT                    = 2                  // isa_defs.h:250:1:
	X_SIGEVENT                           = 0                  // time.h:132:1:
	X_SIGSET_T                           = 0                  // select.h:73:1:
	X_SIGVAL                             = 0                  // time.h:124:1:
	X_SIZE_T                             = 0                  // types.h:540:1:
	X_SOCKLEN_T                          = 0                  // in.h:48:1:
	X_SOFT_HOSTID                        = 0                  // isa_defs.h:286:1:
	X_SSIZE_T                            = 0                  // types.h:549:1:
	X_SS_MAXSIZE                         = 256                // socket_impl.h:70:1:
	X_STACK_GROWS_DOWNWARD               = 0                  // isa_defs.h:243:1:
	X_STDC_C11                           = 0                  // feature_tests.h:165:1:
	X_STDC_C99                           = 0                  // feature_tests.h:169:1:
	X_SUNOS_VTOC_16                      = 0                  // isa_defs.h:280:1:
	X_SUSECONDS_T                        = 0                  // types.h:343:1:
	X_SYS_BYTEORDER_H                    = 0                  // byteorder.h:41:1:
	X_SYS_CCOMPILE_H                     = 0                  // ccompile.h:32:1:
	X_SYS_CRED_H                         = 0                  // cred.h:35:1:
	X_SYS_FEATURE_TESTS_H                = 0                  // feature_tests.h:41:1:
	X_SYS_INT_TYPES_H                    = 0                  // int_types.h:30:1:
	X_SYS_ISA_DEFS_H                     = 0                  // isa_defs.h:30:1:
	X_SYS_MACHTYPES_H                    = 0                  // machtypes.h:27:1:
	X_SYS_NETCONFIG_H                    = 0                  // netconfig.h:35:1:
	X_SYS_NULL_H                         = 0                  // null.h:17:1:
	X_SYS_PARAM_H                        = 0                  // param.h:41:1:
	X_SYS_POLL_H                         = 0                  // poll.h:38:1:
	X_SYS_SELECT_H                       = 0                  // select.h:45:1:
	X_SYS_SOCKET_H                       = 0                  // socket.h:47:1:
	X_SYS_SOCKET_IMPL_H                  = 0                  // socket_impl.h:35:1:
	X_SYS_TIME_H                         = 0                  // time.h:27:1:
	X_SYS_TIME_IMPL_H                    = 0                  // time_impl.h:38:1:
	X_SYS_TYPES_H                        = 0                  // types.h:35:1:
	X_SYS_UIO_H                          = 0                  // uio.h:45:1:
	X_SYS_UNISTD_H                       = 0                  // unistd.h:40:1:
	X_SYS_UN_H                           = 0                  // un.h:39:1:
	X_TIMER_T                            = 0                  // types.h:573:1:
	X_TIME_H                             = 0                  // time.h:37:1:
	X_TIME_T                             = 0                  // types.h:558:1:
	X_TTY_BUFSIZ                         = 2048               // param.h:75:1:
	X_UID_T                              = 0                  // types.h:400:1:
	X_XOPEN_ENH_I18N                     = 1                  // unistd.h:389:1:
	X_XOPEN_REALTIME                     = 1                  // unistd.h:388:1:
	X_XOPEN_SHM                          = 1                  // unistd.h:390:1:
	X_XOPEN_STREAMS                      = 1                  // unistd.h:412:1:
	X_XOPEN_UNIX                         = 0                  // unistd.h:382:1:
	X_XOPEN_VERSION                      = 3                  // feature_tests.h:392:1:
	X_XOPEN_XCU_VERSION                  = 4                  // unistd.h:385:1:
	X_XOPEN_XPG3                         = 0                  // unistd.h:380:1:
	X_XOPEN_XPG4                         = 0                  // unistd.h:381:1:
	Sun                                  = 1                  // <predefined>:172:1:
	Unix                                 = 1                  // <predefined>:175:1:
)

// used for block sizes

// The boolean_t type has had a varied amount of exposure over the years in
// terms of how its enumeration constants have been exposed. In particular, it
// originally used the __XOPEN_OR_POSIX macro to determine whether to prefix the
// B_TRUE and B_FALSE with an underscore. This check never included the
// question of if we were in a strict ANSI C environment or whether extensions
// were defined.
//
// Compilers such as clang started defaulting to always including an
// XOPEN_SOURCE declaration on behalf of users, but also noted __EXTENSIONS__.
// This would lead most software that had used the non-underscore versions to
// need it. As such, we have adjusted the non-strict XOPEN environment to retain
// its old behavior so as to minimize namespace pollution; however, we instead
// include both variants of the definitions in the generally visible version
// allowing software written in either world to hopefully end up in a good
// place.
//
// This isn't perfect, but should hopefully minimize the pain for folks actually
// trying to build software.
const ( /* types.h:215:1: */
	B_FALSE   = 0
	B_TRUE    = 1
	X_B_FALSE = 0
	X_B_TRUE  = 1
)

// uio extensions
//
// PSARC 2009/478: Copy Reduction Interfaces
const ( /* uio.h:146:1: */
	UIOTYPE_ASYNCIO  = 0
	UIOTYPE_ZEROCOPY = 1
	UIOTYPE_PEEKSIZE = 2
)

// I/O direction.
const ( /* uio.h:220:1: */
	UIO_READ  = 0
	UIO_WRITE = 1
)

// Segment flag values.
const ( /* uio.h:93:1: */
	UIO_USERSPACE  = 0
	UIO_SYSSPACE   = 1
	UIO_USERISPACE = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright (c) 1989, 2010, Oracle and/or its affiliates. All rights reserved.
// Copyright 2015, Joyent, Inc. All rights reserved.
// Copyright 2022 Garrett D'Amore
//

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// Copyright (c) 2013, OmniTI Computer Consulting, Inc. All rights reserved.

// Copyright (c) 2014, Joyent, Inc. All rights reserved.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 EveryCity Ltd. All rights reserved.
// Copyright 2019 Joyent, Inc.

// This file contains definitions designed to enable different compilers
// to be used harmoniously on Solaris systems.

// Allow for version tests for compiler bugs and features.

// analogous to lint's PRINTFLIKEn

// Handle the kernel printf routines that can take '%b' too

// This one's pretty obvious -- the function never returns

// The function is 'extern inline' and expects GNU C89 behaviour, not C99
// behaviour.
//
// Should only be used on 'extern inline' definitions for GCC.

// The function has control flow such that it may return multiple times (in
// the manner of setjmp or vfork)

// This is an appropriate label for functions that do not
// modify their arguments, e.g. strlen()

// This is a stronger form of __pure__. Can be used for functions
// that do not modify their arguments and don't depend on global
// memory.

// This attribute, attached to a variable, means that the variable is meant to
// be possibly unused. GCC will not produce a warning for this variable.

// Shorthand versions for readability

// In release build, disable warnings about variables
// which are used only for debugging.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// This header file serves to group a set of well known defines and to
// set these for each instruction set architecture.  These defines may
// be divided into two groups;  characteristics of the processor and
// implementation choices for Solaris on a processor.
//
// Processor Characteristics:
//
// _LITTLE_ENDIAN / _BIG_ENDIAN:
//	The natural byte order of the processor.  A pointer to an int points
//	to the least/most significant byte of that int.
//
// _STACK_GROWS_UPWARD / _STACK_GROWS_DOWNWARD:
//	The processor specific direction of stack growth.  A push onto the
//	stack increases/decreases the stack pointer, so it stores data at
//	successively higher/lower addresses.  (Stackless machines ignored
//	without regrets).
//
// _LONG_LONG_HTOL / _LONG_LONG_LTOH:
//	A pointer to a long long points to the most/least significant long
//	within that long long.
//
// _BIT_FIELDS_HTOL / _BIT_FIELDS_LTOH:
//	The C compiler assigns bit fields from the high/low to the low/high end
//	of an int (most to least significant vs. least to most significant).
//
// _IEEE_754:
//	The processor (or supported implementations of the processor)
//	supports the ieee-754 floating point standard.  No other floating
//	point standards are supported (or significant).  Any other supported
//	floating point formats are expected to be cased on the ISA processor
//	symbol.
//
// _CHAR_IS_UNSIGNED / _CHAR_IS_SIGNED:
//	The C Compiler implements objects of type `char' as `unsigned' or
//	`signed' respectively.  This is really an implementation choice of
//	the compiler writer, but it is specified in the ABI and tends to
//	be uniform across compilers for an instruction set architecture.
//	Hence, it has the properties of a processor characteristic.
//
// _CHAR_ALIGNMENT / _SHORT_ALIGNMENT / _INT_ALIGNMENT / _LONG_ALIGNMENT /
// _LONG_LONG_ALIGNMENT / _DOUBLE_ALIGNMENT / _LONG_DOUBLE_ALIGNMENT /
// _POINTER_ALIGNMENT / _FLOAT_ALIGNMENT:
//	The ABI defines alignment requirements of each of the primitive
//	object types.  Some, if not all, may be hardware requirements as
// 	well.  The values are expressed in "byte-alignment" units.
//
// _MAX_ALIGNMENT:
//	The most stringent alignment requirement as specified by the ABI.
//	Equal to the maximum of all the above _XXX_ALIGNMENT values.
//
// _MAX_ALIGNMENT_TYPE:
// 	The name of the C type that has the value descried in _MAX_ALIGNMENT.
//
// _ALIGNMENT_REQUIRED:
//	True or false (1 or 0) whether or not the hardware requires the ABI
//	alignment.
//
// _LONG_LONG_ALIGNMENT_32
//	The 32-bit ABI supported by a 64-bit kernel may have different
//	alignment requirements for primitive object types.  The value of this
//	identifier is expressed in "byte-alignment" units.
//
// _HAVE_CPUID_INSN
//	This indicates that the architecture supports the 'cpuid'
//	instruction as defined by Intel.  (Intel allows other vendors
//	to extend the instruction for their own purposes.)
//
//
// Implementation Choices:
//
// _ILP32 / _LP64:
//	This specifies the compiler data type implementation as specified in
//	the relevant ABI.  The choice between these is strongly influenced
//	by the underlying hardware, but is not absolutely tied to it.
//	Currently only two data type models are supported:
//
//	_ILP32:
//		Int/Long/Pointer are 32 bits.  This is the historical UNIX
//		and Solaris implementation.  Due to its historical standing,
//		this is the default case.
//
//	_LP64:
//		Long/Pointer are 64 bits, Int is 32 bits.  This is the chosen
//		implementation for 64-bit ABIs such as SPARC V9.
//
//	_I32LPx:
//		A compilation environment where 'int' is 32-bit, and
//		longs and pointers are simply the same size.
//
//	In all cases, Char is 8 bits and Short is 16 bits.
//
// _SUNOS_VTOC_8 / _SUNOS_VTOC_16 / _SVR4_VTOC_16:
//	This specifies the form of the disk VTOC (or label):
//
//	_SUNOS_VTOC_8:
//		This is a VTOC form which is upwardly compatible with the
//		SunOS 4.x disk label and allows 8 partitions per disk.
//
//	_SUNOS_VTOC_16:
//		In this format the incore vtoc image matches the ondisk
//		version.  It allows 16 slices per disk, and is not
//		compatible with the SunOS 4.x disk label.
//
//	Note that these are not the only two VTOC forms possible and
//	additional forms may be added.  One possible form would be the
//	SVr4 VTOC form.  The symbol for that is reserved now, although
//	it is not implemented.
//
//	_SVR4_VTOC_16:
//		This VTOC form is compatible with the System V Release 4
//		VTOC (as implemented on the SVr4 Intel and 3b ports) with
//		16 partitions per disk.
//
//
// _DMA_USES_PHYSADDR / _DMA_USES_VIRTADDR
//	This describes the type of addresses used by system DMA:
//
//	_DMA_USES_PHYSADDR:
//		This type of DMA, used in the x86 implementation,
//		requires physical addresses for DMA buffers.  The 24-bit
//		addresses used by some legacy boards is the source of the
//		"low-memory" (<16MB) requirement for some devices using DMA.
//
//	_DMA_USES_VIRTADDR:
//		This method of DMA allows the use of virtual addresses for
//		DMA transfers.
//
// _FIRMWARE_NEEDS_FDISK / _NO_FDISK_PRESENT
//      This indicates the presence/absence of an fdisk table.
//
//      _FIRMWARE_NEEDS_FDISK
//              The fdisk table is required by system firmware.  If present,
//              it allows a disk to be subdivided into multiple fdisk
//              partitions, each of which is equivalent to a separate,
//              virtual disk.  This enables the co-existence of multiple
//              operating systems on a shared hard disk.
//
//      _NO_FDISK_PRESENT
//              If the fdisk table is absent, it is assumed that the entire
//              media is allocated for a single operating system.
//
// _HAVE_TEM_FIRMWARE
//	Defined if this architecture has the (fallback) option of
//	using prom_* calls for doing I/O if a suitable kernel driver
//	is not available to do it.
//
// _DONT_USE_1275_GENERIC_NAMES
//		Controls whether or not device tree node names should
//		comply with the IEEE 1275 "Generic Names" Recommended
//		Practice. With _DONT_USE_GENERIC_NAMES, device-specific
//		names identifying the particular device will be used.
//
// __i386_COMPAT
//	This indicates whether the i386 ABI is supported as a *non-native*
//	mode for the platform.  When this symbol is defined:
//	-	32-bit xstat-style system calls are enabled
//	-	32-bit xmknod-style system calls are enabled
//	-	32-bit system calls use i386 sizes -and- alignments
//
//	Note that this is NOT defined for the i386 native environment!
//
// __x86
//	This is ONLY a synonym for defined(__i386) || defined(__amd64)
//	which is useful only insofar as these two architectures share
//	common attributes.  Analogous to __sparc.
//
// _PSM_MODULES
//	This indicates whether or not the implementation uses PSM
//	modules for processor support, reading /etc/mach from inside
//	the kernel to extract a list.
//
// _RTC_CONFIG
//	This indicates whether or not the implementation uses /etc/rtc_config
//	to configure the real-time clock in the kernel.
//
// _UNIX_KRTLD
//	This indicates that the implementation uses a dynamically
//	linked unix + krtld to form the core kernel image at boot
//	time, or (in the absence of this symbol) a prelinked kernel image.
//
// _OBP
//	This indicates the firmware interface is OBP.
//
// _SOFT_HOSTID
//	This indicates that the implementation obtains the hostid
//	from the file /etc/hostid, rather than from hardware.

// The following set of definitions characterize Solaris on AMD's
// 64-bit systems.

// Define the appropriate "processor characteristics"

// Different alignment constraints for the i386 ABI in compatibility mode

// Define the appropriate "implementation choices".

// The feature test macro __i386 is generic for all processors implementing
// the Intel 386 instruction set or a superset of it.  Specifically, this
// includes all members of the 386, 486, and Pentium family of processors.

// Values of _POSIX_C_SOURCE
//
//		undefined   not a POSIX compilation
//		1	    POSIX.1-1990 compilation
//		2	    POSIX.2-1992 compilation
//		199309L	    POSIX.1b-1993 compilation (Real Time)
//		199506L	    POSIX.1c-1995 compilation (POSIX Threads)
//		200112L	    POSIX.1-2001 compilation (Austin Group Revision)
//		200809L     POSIX.1-2008 compilation

// The feature test macros __XOPEN_OR_POSIX, _STRICT_STDC, _STRICT_SYMBOLS,
// and _STDC_C99 are Sun implementation specific macros created in order to
// compress common standards specified feature test macros for easier reading.
// These macros should not be used by the application developer as
// unexpected results may occur. Instead, the user should reference
// standards(7) for correct usage of the standards feature test macros.
//
// __XOPEN_OR_POSIX     Used in cases where a symbol is defined by both
//                      X/Open or POSIX or in the negative, when neither
//                      X/Open or POSIX defines a symbol.
//
// _STRICT_STDC         __STDC__ is specified by the C Standards and defined
//                      by the compiler. For Sun compilers the value of
//                      __STDC__ is either 1, 0, or not defined based on the
//                      compilation mode (see cc(1)). When the value of
//                      __STDC__ is 1 and in the absence of any other feature
//                      test macros, the namespace available to the application
//                      is limited to only those symbols defined by the C
//                      Standard. _STRICT_STDC provides a more readable means
//                      of identifying symbols defined by the standard, or in
//                      the negative, symbols that are extensions to the C
//                      Standard. See additional comments for GNU C differences.
//
// _STDC_C99            __STDC_VERSION__ is specified by the C standards and
//                      defined by the compiler and indicates the version of
//                      the C standard. A value of 199901L indicates a
//                      compiler that complies with ISO/IEC 9899:1999, other-
//                      wise known as the C99 standard.
//
// _STDC_C11		Like _STDC_C99 except that the value of __STDC_VERSION__
//                      is 201112L indicating a compiler that compiles with
//                      ISO/IEC 9899:2011, otherwise known as the C11 standard.
//
// _STRICT_SYMBOLS	Used in cases where symbol visibility is restricted
//                      by the standards, and the user has not explicitly
//                      relaxed the strictness via __EXTENSIONS__.

// ISO/IEC 9899:1990 and it's revisions, ISO/IEC 9899:1999 and ISO/IEC
// 99899:2011 specify the following predefined macro name:
//
// __STDC__	The integer constant 1, intended to indicate a conforming
//		implementation.
//
// Furthermore, a strictly conforming program shall use only those features
// of the language and library specified in these standards. A conforming
// implementation shall accept any strictly conforming program.
//
// Based on these requirements, Sun's C compiler defines __STDC__ to 1 for
// strictly conforming environments and __STDC__ to 0 for environments that
// use ANSI C semantics but allow extensions to the C standard. For non-ANSI
// C semantics, Sun's C compiler does not define __STDC__.
//
// The GNU C project interpretation is that __STDC__ should always be defined
// to 1 for compilation modes that accept ANSI C syntax regardless of whether
// or not extensions to the C standard are used. Violations of conforming
// behavior are conditionally flagged as warnings via the use of the
// -pedantic option. In addition to defining __STDC__ to 1, the GNU C
// compiler also defines __STRICT_ANSI__ as a means of specifying strictly
// conforming environments using the -ansi or -std=<standard> options.
//
// In the absence of any other compiler options, Sun and GNU set the value
// of __STDC__ as follows when using the following options:
//
//				Value of __STDC__  __STRICT_ANSI__
//
// cc -Xa (default)			0	      undefined
// cc -Xt (transitional)		0             undefined
// cc -Xc (strictly conforming)		1	      undefined
// cc -Xs (K&R C)		    undefined	      undefined
//
// gcc (default)			1	      undefined
// gcc -ansi, -std={c89, c99,...)	1               defined
// gcc -traditional (K&R)	    undefined	      undefined
//
// The default compilation modes for Sun C compilers versus GNU C compilers
// results in a differing value for __STDC__ which results in a more
// restricted namespace when using Sun compilers. To allow both GNU and Sun
// interpretations to peacefully co-exist, we use the following Sun
// implementation _STRICT_STDC_ macro:

// Compiler complies with ISO/IEC 9899:1999 or ISO/IEC 9989:2011

// Use strict symbol visibility.

// This is a variant of _STRICT_SYMBOLS that is meant to cover headers that are
// governed by POSIX, but have not been governed by ISO C. One can go two ways
// on what should happen if an application actively includes (not transitively)
// a header that isn't part of the ISO C spec, we opt to say that if someone has
// gone out of there way then they're doing it for a reason and that is an act
// of non-compliance and therefore it's not up to us to hide away every symbol.
//
// In general, prefer using _STRICT_SYMBOLS, but this is here in particular for
// cases where in the past we have only used a POSIX related check and we don't
// wish to make something stricter. Often applications are relying on the
// ability to, or more realistically unwittingly, have _STRICT_STDC declared and
// still use these interfaces.

// Large file interfaces:
//
//	_LARGEFILE_SOURCE
//		1		large file-related additions to POSIX
//				interfaces requested (fseeko, etc.)
//	_LARGEFILE64_SOURCE
//		1		transitional large-file-related interfaces
//				requested (seek64, stat64, etc.)
//
// The corresponding announcement macros are respectively:
//	_LFS_LARGEFILE
//	_LFS64_LARGEFILE
// (These are set in <unistd.h>.)
//
// Requesting _LARGEFILE64_SOURCE implies requesting _LARGEFILE_SOURCE as
// well.
//
// The large file interfaces are made visible regardless of the initial values
// of the feature test macros under certain circumstances:
//    -	If no explicit standards-conforming environment is requested (neither
//	of _POSIX_SOURCE nor _XOPEN_SOURCE is defined and the value of
//	__STDC__ does not imply standards conformance).
//    -	Extended system interfaces are explicitly requested (__EXTENSIONS__
//	is defined).
//    -	Access to in-kernel interfaces is requested (_KERNEL or _KMEMUSER is
//	defined).  (Note that this dependency is an artifact of the current
//	kernel implementation and may change in future releases.)

// Large file compilation environment control:
//
// The setting of _FILE_OFFSET_BITS controls the size of various file-related
// types and governs the mapping between file-related source function symbol
// names and the corresponding binary entry points.
//
// In the 32-bit environment, the default value is 32; if not set, set it to
// the default here, to simplify tests in other headers.
//
// In the 64-bit compilation environment, the only value allowed is 64.

// Use of _XOPEN_SOURCE
//
// The following X/Open specifications are supported:
//
// X/Open Portability Guide, Issue 3 (XPG3)
// X/Open CAE Specification, Issue 4 (XPG4)
// X/Open CAE Specification, Issue 4, Version 2 (XPG4v2)
// X/Open CAE Specification, Issue 5 (XPG5)
// Open Group Technical Standard, Issue 6 (XPG6), also referred to as
//    IEEE Std. 1003.1-2001 and ISO/IEC 9945:2002.
// Open Group Technical Standard, Issue 7 (XPG7), also referred to as
//    IEEE Std. 1003.1-2008 and ISO/IEC 9945:2009.
//
// XPG4v2 is also referred to as UNIX 95 (SUS or SUSv1).
// XPG5 is also referred to as UNIX 98 or the Single Unix Specification,
//     Version 2 (SUSv2)
// XPG6 is the result of a merge of the X/Open and POSIX specifications
//     and as such is also referred to as IEEE Std. 1003.1-2001 in
//     addition to UNIX 03 and SUSv3.
// XPG7 is also referred to as UNIX 08 and SUSv4.
//
// When writing a conforming X/Open application, as per the specification
// requirements, the appropriate feature test macros must be defined at
// compile time. These are as follows. For more info, see standards(7).
//
// Feature Test Macro				     Specification
// ------------------------------------------------  -------------
// _XOPEN_SOURCE                                         XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4                   XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1           XPG4v2
// _XOPEN_SOURCE = 500                                   XPG5
// _XOPEN_SOURCE = 600  (or POSIX_C_SOURCE=200112L)      XPG6
// _XOPEN_SOURCE = 700  (or POSIX_C_SOURCE=200809L)      XPG7
//
// In order to simplify the guards within the headers, the following
// implementation private test macros have been created. Applications
// must NOT use these private test macros as unexpected results will
// occur.
//
// Note that in general, the use of these private macros is cumulative.
// For example, the use of _XPG3 with no other restrictions on the X/Open
// namespace will make the symbols visible for XPG3 through XPG6
// compilation environments. The use of _XPG4_2 with no other X/Open
// namespace restrictions indicates that the symbols were introduced in
// XPG4v2 and are therefore visible for XPG4v2 through XPG6 compilation
// environments, but not for XPG3 or XPG4 compilation environments.
//
// _XPG3    X/Open Portability Guide, Issue 3 (XPG3)
// _XPG4    X/Open CAE Specification, Issue 4 (XPG4)
// _XPG4_2  X/Open CAE Specification, Issue 4, Version 2 (XPG4v2/UNIX 95/SUS)
// _XPG5    X/Open CAE Specification, Issue 5 (XPG5/UNIX 98/SUSv2)
// _XPG6    Open Group Technical Standard, Issue 6 (XPG6/UNIX 03/SUSv3)
// _XPG7    Open Group Technical Standard, Issue 7 (XPG7/UNIX 08/SUSv4)

// X/Open Portability Guide, Issue 3

// _XOPEN_VERSION is defined by the X/Open specifications and is not
// normally defined by the application, except in the case of an XPG4
// application.  On the implementation side, _XOPEN_VERSION defined with
// the value of 3 indicates an XPG3 application. _XOPEN_VERSION defined
// with the value of 4 indicates an XPG4 or XPG4v2 (UNIX 95) application.
// _XOPEN_VERSION  defined with a value of 500 indicates an XPG5 (UNIX 98)
// application and with a value of 600 indicates an XPG6 (UNIX 03)
// application and with a value of 700 indicates an XPG7 (UNIX 08).
// The appropriate version is determined by the use of the
// feature test macros described earlier.  The value of _XOPEN_VERSION
// defaults to 3 otherwise indicating support for XPG3 applications.

// ANSI C and ISO 9899:1990 say the type long long doesn't exist in strictly
// conforming environments.  ISO 9899:1999 says it does.
//
// The presence of _LONGLONG_TYPE says "long long exists" which is therefore
// defined in all but strictly conforming environments that disallow it.

// The following macro defines a value for the ISO C99 restrict
// keyword so that _RESTRICT_KYWD resolves to "restrict" if
// an ISO C99 compiler is used, "__restrict" for c++ and "" (null string)
// if any other compiler is used. This allows for the use of single
// prototype declarations regardless of compiler version.

// The following macro defines a value for the ISO C11 _Noreturn
// keyword so that _NORETURN_KYWD resolves to "_Noreturn" if
// an ISO C11 compiler is used and "" (null string) if any other
// compiler is used. This allows for the use of single prototype
// declarations regardless of compiler version.

// ISO/IEC 9899:2011 Annex K

// The following macro indicates header support for the ANSI C++
// standard.  The ISO/IEC designation for this is ISO/IEC FDIS 14882.

// The following macro indicates header support for the C99 standard,
// ISO/IEC 9899:1999, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011 Annex K, Programming Languages - C.

// The following macro indicates header support for DTrace. The value is an
// integer that corresponds to the major version number for DTrace.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// Machine dependent definitions moved to <sys/machtypes.h>.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Machine dependent types:
//
//	intel ia32 Version

type X_label_t = struct{ Fval [8]int64 } /* machtypes.h:59:9 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright (c) 1989, 2010, Oracle and/or its affiliates. All rights reserved.
// Copyright 2015, Joyent, Inc. All rights reserved.
// Copyright 2022 Garrett D'Amore
//

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// Copyright (c) 2013, OmniTI Computer Consulting, Inc. All rights reserved.

// Copyright (c) 2014, Joyent, Inc. All rights reserved.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 EveryCity Ltd. All rights reserved.
// Copyright 2019 Joyent, Inc.

// This file contains definitions designed to enable different compilers
// to be used harmoniously on Solaris systems.

// Allow for version tests for compiler bugs and features.

// analogous to lint's PRINTFLIKEn

// Handle the kernel printf routines that can take '%b' too

// This one's pretty obvious -- the function never returns

// The function is 'extern inline' and expects GNU C89 behaviour, not C99
// behaviour.
//
// Should only be used on 'extern inline' definitions for GCC.

// The function has control flow such that it may return multiple times (in
// the manner of setjmp or vfork)

// This is an appropriate label for functions that do not
// modify their arguments, e.g. strlen()

// This is a stronger form of __pure__. Can be used for functions
// that do not modify their arguments and don't depend on global
// memory.

// This attribute, attached to a variable, means that the variable is meant to
// be possibly unused. GCC will not produce a warning for this variable.

// Shorthand versions for readability

// In release build, disable warnings about variables
// which are used only for debugging.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// This header file serves to group a set of well known defines and to
// set these for each instruction set architecture.  These defines may
// be divided into two groups;  characteristics of the processor and
// implementation choices for Solaris on a processor.
//
// Processor Characteristics:
//
// _LITTLE_ENDIAN / _BIG_ENDIAN:
//	The natural byte order of the processor.  A pointer to an int points
//	to the least/most significant byte of that int.
//
// _STACK_GROWS_UPWARD / _STACK_GROWS_DOWNWARD:
//	The processor specific direction of stack growth.  A push onto the
//	stack increases/decreases the stack pointer, so it stores data at
//	successively higher/lower addresses.  (Stackless machines ignored
//	without regrets).
//
// _LONG_LONG_HTOL / _LONG_LONG_LTOH:
//	A pointer to a long long points to the most/least significant long
//	within that long long.
//
// _BIT_FIELDS_HTOL / _BIT_FIELDS_LTOH:
//	The C compiler assigns bit fields from the high/low to the low/high end
//	of an int (most to least significant vs. least to most significant).
//
// _IEEE_754:
//	The processor (or supported implementations of the processor)
//	supports the ieee-754 floating point standard.  No other floating
//	point standards are supported (or significant).  Any other supported
//	floating point formats are expected to be cased on the ISA processor
//	symbol.
//
// _CHAR_IS_UNSIGNED / _CHAR_IS_SIGNED:
//	The C Compiler implements objects of type `char' as `unsigned' or
//	`signed' respectively.  This is really an implementation choice of
//	the compiler writer, but it is specified in the ABI and tends to
//	be uniform across compilers for an instruction set architecture.
//	Hence, it has the properties of a processor characteristic.
//
// _CHAR_ALIGNMENT / _SHORT_ALIGNMENT / _INT_ALIGNMENT / _LONG_ALIGNMENT /
// _LONG_LONG_ALIGNMENT / _DOUBLE_ALIGNMENT / _LONG_DOUBLE_ALIGNMENT /
// _POINTER_ALIGNMENT / _FLOAT_ALIGNMENT:
//	The ABI defines alignment requirements of each of the primitive
//	object types.  Some, if not all, may be hardware requirements as
// 	well.  The values are expressed in "byte-alignment" units.
//
// _MAX_ALIGNMENT:
//	The most stringent alignment requirement as specified by the ABI.
//	Equal to the maximum of all the above _XXX_ALIGNMENT values.
//
// _MAX_ALIGNMENT_TYPE:
// 	The name of the C type that has the value descried in _MAX_ALIGNMENT.
//
// _ALIGNMENT_REQUIRED:
//	True or false (1 or 0) whether or not the hardware requires the ABI
//	alignment.
//
// _LONG_LONG_ALIGNMENT_32
//	The 32-bit ABI supported by a 64-bit kernel may have different
//	alignment requirements for primitive object types.  The value of this
//	identifier is expressed in "byte-alignment" units.
//
// _HAVE_CPUID_INSN
//	This indicates that the architecture supports the 'cpuid'
//	instruction as defined by Intel.  (Intel allows other vendors
//	to extend the instruction for their own purposes.)
//
//
// Implementation Choices:
//
// _ILP32 / _LP64:
//	This specifies the compiler data type implementation as specified in
//	the relevant ABI.  The choice between these is strongly influenced
//	by the underlying hardware, but is not absolutely tied to it.
//	Currently only two data type models are supported:
//
//	_ILP32:
//		Int/Long/Pointer are 32 bits.  This is the historical UNIX
//		and Solaris implementation.  Due to its historical standing,
//		this is the default case.
//
//	_LP64:
//		Long/Pointer are 64 bits, Int is 32 bits.  This is the chosen
//		implementation for 64-bit ABIs such as SPARC V9.
//
//	_I32LPx:
//		A compilation environment where 'int' is 32-bit, and
//		longs and pointers are simply the same size.
//
//	In all cases, Char is 8 bits and Short is 16 bits.
//
// _SUNOS_VTOC_8 / _SUNOS_VTOC_16 / _SVR4_VTOC_16:
//	This specifies the form of the disk VTOC (or label):
//
//	_SUNOS_VTOC_8:
//		This is a VTOC form which is upwardly compatible with the
//		SunOS 4.x disk label and allows 8 partitions per disk.
//
//	_SUNOS_VTOC_16:
//		In this format the incore vtoc image matches the ondisk
//		version.  It allows 16 slices per disk, and is not
//		compatible with the SunOS 4.x disk label.
//
//	Note that these are not the only two VTOC forms possible and
//	additional forms may be added.  One possible form would be the
//	SVr4 VTOC form.  The symbol for that is reserved now, although
//	it is not implemented.
//
//	_SVR4_VTOC_16:
//		This VTOC form is compatible with the System V Release 4
//		VTOC (as implemented on the SVr4 Intel and 3b ports) with
//		16 partitions per disk.
//
//
// _DMA_USES_PHYSADDR / _DMA_USES_VIRTADDR
//	This describes the type of addresses used by system DMA:
//
//	_DMA_USES_PHYSADDR:
//		This type of DMA, used in the x86 implementation,
//		requires physical addresses for DMA buffers.  The 24-bit
//		addresses used by some legacy boards is the source of the
//		"low-memory" (<16MB) requirement for some devices using DMA.
//
//	_DMA_USES_VIRTADDR:
//		This method of DMA allows the use of virtual addresses for
//		DMA transfers.
//
// _FIRMWARE_NEEDS_FDISK / _NO_FDISK_PRESENT
//      This indicates the presence/absence of an fdisk table.
//
//      _FIRMWARE_NEEDS_FDISK
//              The fdisk table is required by system firmware.  If present,
//              it allows a disk to be subdivided into multiple fdisk
//              partitions, each of which is equivalent to a separate,
//              virtual disk.  This enables the co-existence of multiple
//              operating systems on a shared hard disk.
//
//      _NO_FDISK_PRESENT
//              If the fdisk table is absent, it is assumed that the entire
//              media is allocated for a single operating system.
//
// _HAVE_TEM_FIRMWARE
//	Defined if this architecture has the (fallback) option of
//	using prom_* calls for doing I/O if a suitable kernel driver
//	is not available to do it.
//
// _DONT_USE_1275_GENERIC_NAMES
//		Controls whether or not device tree node names should
//		comply with the IEEE 1275 "Generic Names" Recommended
//		Practice. With _DONT_USE_GENERIC_NAMES, device-specific
//		names identifying the particular device will be used.
//
// __i386_COMPAT
//	This indicates whether the i386 ABI is supported as a *non-native*
//	mode for the platform.  When this symbol is defined:
//	-	32-bit xstat-style system calls are enabled
//	-	32-bit xmknod-style system calls are enabled
//	-	32-bit system calls use i386 sizes -and- alignments
//
//	Note that this is NOT defined for the i386 native environment!
//
// __x86
//	This is ONLY a synonym for defined(__i386) || defined(__amd64)
//	which is useful only insofar as these two architectures share
//	common attributes.  Analogous to __sparc.
//
// _PSM_MODULES
//	This indicates whether or not the implementation uses PSM
//	modules for processor support, reading /etc/mach from inside
//	the kernel to extract a list.
//
// _RTC_CONFIG
//	This indicates whether or not the implementation uses /etc/rtc_config
//	to configure the real-time clock in the kernel.
//
// _UNIX_KRTLD
//	This indicates that the implementation uses a dynamically
//	linked unix + krtld to form the core kernel image at boot
//	time, or (in the absence of this symbol) a prelinked kernel image.
//
// _OBP
//	This indicates the firmware interface is OBP.
//
// _SOFT_HOSTID
//	This indicates that the implementation obtains the hostid
//	from the file /etc/hostid, rather than from hardware.

// The following set of definitions characterize Solaris on AMD's
// 64-bit systems.

// Define the appropriate "processor characteristics"

// Different alignment constraints for the i386 ABI in compatibility mode

// Define the appropriate "implementation choices".

// The feature test macro __i386 is generic for all processors implementing
// the Intel 386 instruction set or a superset of it.  Specifically, this
// includes all members of the 386, 486, and Pentium family of processors.

// Values of _POSIX_C_SOURCE
//
//		undefined   not a POSIX compilation
//		1	    POSIX.1-1990 compilation
//		2	    POSIX.2-1992 compilation
//		199309L	    POSIX.1b-1993 compilation (Real Time)
//		199506L	    POSIX.1c-1995 compilation (POSIX Threads)
//		200112L	    POSIX.1-2001 compilation (Austin Group Revision)
//		200809L     POSIX.1-2008 compilation

// The feature test macros __XOPEN_OR_POSIX, _STRICT_STDC, _STRICT_SYMBOLS,
// and _STDC_C99 are Sun implementation specific macros created in order to
// compress common standards specified feature test macros for easier reading.
// These macros should not be used by the application developer as
// unexpected results may occur. Instead, the user should reference
// standards(7) for correct usage of the standards feature test macros.
//
// __XOPEN_OR_POSIX     Used in cases where a symbol is defined by both
//                      X/Open or POSIX or in the negative, when neither
//                      X/Open or POSIX defines a symbol.
//
// _STRICT_STDC         __STDC__ is specified by the C Standards and defined
//                      by the compiler. For Sun compilers the value of
//                      __STDC__ is either 1, 0, or not defined based on the
//                      compilation mode (see cc(1)). When the value of
//                      __STDC__ is 1 and in the absence of any other feature
//                      test macros, the namespace available to the application
//                      is limited to only those symbols defined by the C
//                      Standard. _STRICT_STDC provides a more readable means
//                      of identifying symbols defined by the standard, or in
//                      the negative, symbols that are extensions to the C
//                      Standard. See additional comments for GNU C differences.
//
// _STDC_C99            __STDC_VERSION__ is specified by the C standards and
//                      defined by the compiler and indicates the version of
//                      the C standard. A value of 199901L indicates a
//                      compiler that complies with ISO/IEC 9899:1999, other-
//                      wise known as the C99 standard.
//
// _STDC_C11		Like _STDC_C99 except that the value of __STDC_VERSION__
//                      is 201112L indicating a compiler that compiles with
//                      ISO/IEC 9899:2011, otherwise known as the C11 standard.
//
// _STRICT_SYMBOLS	Used in cases where symbol visibility is restricted
//                      by the standards, and the user has not explicitly
//                      relaxed the strictness via __EXTENSIONS__.

// ISO/IEC 9899:1990 and it's revisions, ISO/IEC 9899:1999 and ISO/IEC
// 99899:2011 specify the following predefined macro name:
//
// __STDC__	The integer constant 1, intended to indicate a conforming
//		implementation.
//
// Furthermore, a strictly conforming program shall use only those features
// of the language and library specified in these standards. A conforming
// implementation shall accept any strictly conforming program.
//
// Based on these requirements, Sun's C compiler defines __STDC__ to 1 for
// strictly conforming environments and __STDC__ to 0 for environments that
// use ANSI C semantics but allow extensions to the C standard. For non-ANSI
// C semantics, Sun's C compiler does not define __STDC__.
//
// The GNU C project interpretation is that __STDC__ should always be defined
// to 1 for compilation modes that accept ANSI C syntax regardless of whether
// or not extensions to the C standard are used. Violations of conforming
// behavior are conditionally flagged as warnings via the use of the
// -pedantic option. In addition to defining __STDC__ to 1, the GNU C
// compiler also defines __STRICT_ANSI__ as a means of specifying strictly
// conforming environments using the -ansi or -std=<standard> options.
//
// In the absence of any other compiler options, Sun and GNU set the value
// of __STDC__ as follows when using the following options:
//
//				Value of __STDC__  __STRICT_ANSI__
//
// cc -Xa (default)			0	      undefined
// cc -Xt (transitional)		0             undefined
// cc -Xc (strictly conforming)		1	      undefined
// cc -Xs (K&R C)		    undefined	      undefined
//
// gcc (default)			1	      undefined
// gcc -ansi, -std={c89, c99,...)	1               defined
// gcc -traditional (K&R)	    undefined	      undefined
//
// The default compilation modes for Sun C compilers versus GNU C compilers
// results in a differing value for __STDC__ which results in a more
// restricted namespace when using Sun compilers. To allow both GNU and Sun
// interpretations to peacefully co-exist, we use the following Sun
// implementation _STRICT_STDC_ macro:

// Compiler complies with ISO/IEC 9899:1999 or ISO/IEC 9989:2011

// Use strict symbol visibility.

// This is a variant of _STRICT_SYMBOLS that is meant to cover headers that are
// governed by POSIX, but have not been governed by ISO C. One can go two ways
// on what should happen if an application actively includes (not transitively)
// a header that isn't part of the ISO C spec, we opt to say that if someone has
// gone out of there way then they're doing it for a reason and that is an act
// of non-compliance and therefore it's not up to us to hide away every symbol.
//
// In general, prefer using _STRICT_SYMBOLS, but this is here in particular for
// cases where in the past we have only used a POSIX related check and we don't
// wish to make something stricter. Often applications are relying on the
// ability to, or more realistically unwittingly, have _STRICT_STDC declared and
// still use these interfaces.

// Large file interfaces:
//
//	_LARGEFILE_SOURCE
//		1		large file-related additions to POSIX
//				interfaces requested (fseeko, etc.)
//	_LARGEFILE64_SOURCE
//		1		transitional large-file-related interfaces
//				requested (seek64, stat64, etc.)
//
// The corresponding announcement macros are respectively:
//	_LFS_LARGEFILE
//	_LFS64_LARGEFILE
// (These are set in <unistd.h>.)
//
// Requesting _LARGEFILE64_SOURCE implies requesting _LARGEFILE_SOURCE as
// well.
//
// The large file interfaces are made visible regardless of the initial values
// of the feature test macros under certain circumstances:
//    -	If no explicit standards-conforming environment is requested (neither
//	of _POSIX_SOURCE nor _XOPEN_SOURCE is defined and the value of
//	__STDC__ does not imply standards conformance).
//    -	Extended system interfaces are explicitly requested (__EXTENSIONS__
//	is defined).
//    -	Access to in-kernel interfaces is requested (_KERNEL or _KMEMUSER is
//	defined).  (Note that this dependency is an artifact of the current
//	kernel implementation and may change in future releases.)

// Large file compilation environment control:
//
// The setting of _FILE_OFFSET_BITS controls the size of various file-related
// types and governs the mapping between file-related source function symbol
// names and the corresponding binary entry points.
//
// In the 32-bit environment, the default value is 32; if not set, set it to
// the default here, to simplify tests in other headers.
//
// In the 64-bit compilation environment, the only value allowed is 64.

// Use of _XOPEN_SOURCE
//
// The following X/Open specifications are supported:
//
// X/Open Portability Guide, Issue 3 (XPG3)
// X/Open CAE Specification, Issue 4 (XPG4)
// X/Open CAE Specification, Issue 4, Version 2 (XPG4v2)
// X/Open CAE Specification, Issue 5 (XPG5)
// Open Group Technical Standard, Issue 6 (XPG6), also referred to as
//    IEEE Std. 1003.1-2001 and ISO/IEC 9945:2002.
// Open Group Technical Standard, Issue 7 (XPG7), also referred to as
//    IEEE Std. 1003.1-2008 and ISO/IEC 9945:2009.
//
// XPG4v2 is also referred to as UNIX 95 (SUS or SUSv1).
// XPG5 is also referred to as UNIX 98 or the Single Unix Specification,
//     Version 2 (SUSv2)
// XPG6 is the result of a merge of the X/Open and POSIX specifications
//     and as such is also referred to as IEEE Std. 1003.1-2001 in
//     addition to UNIX 03 and SUSv3.
// XPG7 is also referred to as UNIX 08 and SUSv4.
//
// When writing a conforming X/Open application, as per the specification
// requirements, the appropriate feature test macros must be defined at
// compile time. These are as follows. For more info, see standards(7).
//
// Feature Test Macro				     Specification
// ------------------------------------------------  -------------
// _XOPEN_SOURCE                                         XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4                   XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1           XPG4v2
// _XOPEN_SOURCE = 500                                   XPG5
// _XOPEN_SOURCE = 600  (or POSIX_C_SOURCE=200112L)      XPG6
// _XOPEN_SOURCE = 700  (or POSIX_C_SOURCE=200809L)      XPG7
//
// In order to simplify the guards within the headers, the following
// implementation private test macros have been created. Applications
// must NOT use these private test macros as unexpected results will
// occur.
//
// Note that in general, the use of these private macros is cumulative.
// For example, the use of _XPG3 with no other restrictions on the X/Open
// namespace will make the symbols visible for XPG3 through XPG6
// compilation environments. The use of _XPG4_2 with no other X/Open
// namespace restrictions indicates that the symbols were introduced in
// XPG4v2 and are therefore visible for XPG4v2 through XPG6 compilation
// environments, but not for XPG3 or XPG4 compilation environments.
//
// _XPG3    X/Open Portability Guide, Issue 3 (XPG3)
// _XPG4    X/Open CAE Specification, Issue 4 (XPG4)
// _XPG4_2  X/Open CAE Specification, Issue 4, Version 2 (XPG4v2/UNIX 95/SUS)
// _XPG5    X/Open CAE Specification, Issue 5 (XPG5/UNIX 98/SUSv2)
// _XPG6    Open Group Technical Standard, Issue 6 (XPG6/UNIX 03/SUSv3)
// _XPG7    Open Group Technical Standard, Issue 7 (XPG7/UNIX 08/SUSv4)

// X/Open Portability Guide, Issue 3

// _XOPEN_VERSION is defined by the X/Open specifications and is not
// normally defined by the application, except in the case of an XPG4
// application.  On the implementation side, _XOPEN_VERSION defined with
// the value of 3 indicates an XPG3 application. _XOPEN_VERSION defined
// with the value of 4 indicates an XPG4 or XPG4v2 (UNIX 95) application.
// _XOPEN_VERSION  defined with a value of 500 indicates an XPG5 (UNIX 98)
// application and with a value of 600 indicates an XPG6 (UNIX 03)
// application and with a value of 700 indicates an XPG7 (UNIX 08).
// The appropriate version is determined by the use of the
// feature test macros described earlier.  The value of _XOPEN_VERSION
// defaults to 3 otherwise indicating support for XPG3 applications.

// ANSI C and ISO 9899:1990 say the type long long doesn't exist in strictly
// conforming environments.  ISO 9899:1999 says it does.
//
// The presence of _LONGLONG_TYPE says "long long exists" which is therefore
// defined in all but strictly conforming environments that disallow it.

// The following macro defines a value for the ISO C99 restrict
// keyword so that _RESTRICT_KYWD resolves to "restrict" if
// an ISO C99 compiler is used, "__restrict" for c++ and "" (null string)
// if any other compiler is used. This allows for the use of single
// prototype declarations regardless of compiler version.

// The following macro defines a value for the ISO C11 _Noreturn
// keyword so that _NORETURN_KYWD resolves to "_Noreturn" if
// an ISO C11 compiler is used and "" (null string) if any other
// compiler is used. This allows for the use of single prototype
// declarations regardless of compiler version.

// ISO/IEC 9899:2011 Annex K

// The following macro indicates header support for the ANSI C++
// standard.  The ISO/IEC designation for this is ISO/IEC FDIS 14882.

// The following macro indicates header support for the C99 standard,
// ISO/IEC 9899:1999, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011, Programming Languages - C.

// The following macro indicates header support for the C11 standard,
// ISO/IEC 9899:2011 Annex K, Programming Languages - C.

// The following macro indicates header support for DTrace. The value is an
// integer that corresponds to the major version number for DTrace.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// Machine dependent definitions moved to <sys/machtypes.h>.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Machine dependent types:
//
//	intel ia32 Version

type Label_t = X_label_t /* machtypes.h:59:54 */

type Lock_t = uint8 /* machtypes.h:63:23 */ // lock work for busy wait

// Include fixed width type declarations proposed by the ISO/JTC1/SC22/WG14 C
// committee's working draft for the revision of the current ISO C standard,
// ISO/IEC 9899:1990 Programming language - C.  These are not currently
// required by any standard but constitute a useful, general purpose set
// of type definitions which is namespace clean with respect to all standards.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file, <sys/int_types.h>, is part of the Sun Microsystems implementation
// of <inttypes.h> defined in the ISO C standard, ISO/IEC 9899:1999
// Programming language - C.
//
// Programs/Modules should not directly include this file.  Access to the
// types defined in this file should be through the inclusion of one of the
// following files:
//
//	<sys/types.h>		Provides only the "_t" types defined in this
//				file which is a subset of the contents of
//				<inttypes.h>.  (This can be appropriate for
//				all programs/modules except those claiming
//				ANSI-C conformance.)
//
//	<sys/inttypes.h>	Provides the Kernel and Driver appropriate
//				components of <inttypes.h>.
//
//	<inttypes.h>		For use by applications.
//
// See these files for more details.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Basic / Extended integer types
//
// The following defines the basic fixed-size integer types.
//
// Implementations are free to typedef them to Standard C integer types or
// extensions that they support. If an implementation does not support one
// of the particular integer data types below, then it should not define the
// typedefs and macros corresponding to that data type.  Note that int8_t
// is not defined in -Xs mode on ISAs for which the ABI specifies "char"
// as an unsigned entity because there is no way to define an eight bit
// signed integral.
type Int8_t = int8   /* int_types.h:75:16 */
type Int16_t = int16 /* int_types.h:79:17 */
type Int32_t = int32 /* int_types.h:80:15 */
type Int64_t = int64 /* int_types.h:83:16 */

type Uint8_t = uint8   /* int_types.h:91:24 */
type Uint16_t = uint16 /* int_types.h:92:25 */
type Uint32_t = uint32 /* int_types.h:93:23 */
type Uint64_t = uint64 /* int_types.h:95:24 */

// intmax_t and uintmax_t are to be the longest (in number of bits) signed
// and unsigned integer types supported by the implementation.
type Intmax_t = int64   /* int_types.h:107:19 */
type Uintmax_t = uint64 /* int_types.h:108:19 */

// intptr_t and uintptr_t are signed and unsigned integer types large enough
// to hold any data pointer; that is, data pointers can be assigned into or
// from these integer types without losing precision.
type Intptr_t = int64   /* int_types.h:120:16 */
type Uintptr_t = uint64 /* int_types.h:121:24 */

// The following define the fastest integer types that can hold the
// specified number of bits.
type Int_fast8_t = int8   /* int_types.h:132:16 */
type Int_fast16_t = int32 /* int_types.h:136:15 */
type Int_fast32_t = int32 /* int_types.h:137:15 */
type Int_fast64_t = int64 /* int_types.h:139:16 */

type Uint_fast8_t = uint8   /* int_types.h:146:24 */
type Uint_fast16_t = uint32 /* int_types.h:147:23 */
type Uint_fast32_t = uint32 /* int_types.h:148:23 */
type Uint_fast64_t = uint64 /* int_types.h:150:24 */

// The following define the smallest integer types that can hold the
// specified number of bits.
type Int_least8_t = int8   /* int_types.h:162:16 */
type Int_least16_t = int16 /* int_types.h:166:17 */
type Int_least32_t = int32 /* int_types.h:167:15 */
type Int_least64_t = int64 /* int_types.h:169:16 */

// If these are changed, please update char16_t and char32_t in head/uchar.h.
type Uint_least8_t = uint8   /* int_types.h:179:24 */
type Uint_least16_t = uint16 /* int_types.h:180:25 */
type Uint_least32_t = uint32 /* int_types.h:181:23 */
type Uint_least64_t = uint64 /* int_types.h:183:24 */

// Strictly conforming ANSI C environments prior to the 1999
// revision of the C Standard (ISO/IEC 9899:1999) do not have
// the long long data type.
type Longlong_t = int64    /* types.h:72:20 */
type U_longlong_t = uint64 /* types.h:73:28 */

// These types (t_{u}scalar_t) exist because the XTI/TPI/DLPI standards had
// to use them instead of int32_t and uint32_t because DEC had
// shipped 64-bit wide.
type T_scalar_t = int32   /* types.h:92:18 */
type T_uscalar_t = uint32 /* types.h:93:18 */

// POSIX Extensions
type Uchar_t = uint8   /* types.h:102:23 */
type Ushort_t = uint16 /* types.h:103:24 */
type Uint_t = uint32   /* types.h:104:22 */
type Ulong_t = uint64  /* types.h:105:23 */

type Caddr_t = uintptr /* types.h:107:15 */ // ?<core address> type
type Daddr_t = int64   /* types.h:108:15 */ // <disk address> type
type Cnt_t = int16     /* types.h:109:16 */ // pointer difference

// VM-related types
type Pfn_t = uint64   /* types.h:123:18 */ // page frame number
type Pgcnt_t = uint64 /* types.h:124:18 */ // number of pages
type Spgcnt_t = int64 /* types.h:125:15 */ // signed number of pages

type Use_t = uint8          /* types.h:127:18 */ // use count for swap.
type Sysid_t = int16        /* types.h:128:16 */
type Index_t = int16        /* types.h:129:16 */
type Timeout_id_t = uintptr /* types.h:130:15 */ // opaque handle from timeout(9F)
type Bufcall_id_t = uintptr /* types.h:131:15 */ // opaque handle from bufcall(9F)

// The size of off_t and related types depends on the setting of
// _FILE_OFFSET_BITS.  (Note that other system headers define other types
// related to those defined here.)
//
// If _LARGEFILE64_SOURCE is defined, variants of these types that are
// explicitly 64 bits wide become available.

type Off_t = int64 /* types.h:145:15 */ // offsets within files

type Off64_t = int64 /* types.h:152:16 */ // offsets within files

type Ino_t = uint64      /* types.h:161:18 */ // expanded inode type
type Blkcnt_t = int64    /* types.h:162:15 */ // count of file blocks
type Fsblkcnt_t = uint64 /* types.h:163:18 */ // count of file system blocks
type Fsfilcnt_t = uint64 /* types.h:164:18 */ // count of files

type Ino64_t = uint64      /* types.h:174:16 */ // expanded inode type
type Blkcnt64_t = int64    /* types.h:175:18 */ // count of file blocks
type Fsblkcnt64_t = uint64 /* types.h:176:20 */ // count of file system blocks
type Fsfilcnt64_t = uint64 /* types.h:177:20 */ // count of files

type Blksize_t = int32 /* types.h:187:14 */ // used for block sizes

// The boolean_t type has had a varied amount of exposure over the years in
// terms of how its enumeration constants have been exposed. In particular, it
// originally used the __XOPEN_OR_POSIX macro to determine whether to prefix the
// B_TRUE and B_FALSE with an underscore. This check never included the
// question of if we were in a strict ANSI C environment or whether extensions
// were defined.
//
// Compilers such as clang started defaulting to always including an
// XOPEN_SOURCE declaration on behalf of users, but also noted __EXTENSIONS__.
// This would lead most software that had used the non-underscore versions to
// need it. As such, we have adjusted the non-strict XOPEN environment to retain
// its old behavior so as to minimize namespace pollution; however, we instead
// include both variants of the definitions in the generally visible version
// allowing software written in either world to hopefully end up in a good
// place.
//
// This isn't perfect, but should hopefully minimize the pain for folks actually
// trying to build software.
type Boolean_t = uint32 /* types.h:215:69 */

// The {u,}pad64_t types can be used in structures such that those structures
// may be accessed by code produced by compilation environments which don't
// support a 64 bit integral datatype.  The intention is not to allow
// use of these fields in such environments, but to maintain the alignment
// and offsets of the structure.
//
// Similar comments for {u,}pad128_t.
//
// Note that these types do NOT generate any stronger alignment constraints
// than those available in the underlying ABI.  See <sys/isa_defs.h>
type Pad64_t = int64   /* types.h:240:18 */
type Upad64_t = uint64 /* types.h:241:18 */

type Pad128_t = struct {
	F_q          float64
	F__ccgo_pad1 [8]byte
} /* types.h:257:3 */

type Upad128_t = struct {
	F_q          float64
	F__ccgo_pad1 [8]byte
} /* types.h:262:3 */

type Offset_t = int64    /* types.h:264:20 */
type U_offset_t = uint64 /* types.h:265:22 */
type Len_t = uint64      /* types.h:266:22 */
type Diskaddr_t = uint64 /* types.h:267:22 */

// Definitions remaining from previous partial support for 64-bit file
// offsets.  This partial support for devices greater than 2gb requires
// compiler support for long long.
type Lloff_t = struct{ F_f int64 } /* types.h:284:3 */

type Lldaddr_t = struct{ F_f int64 } /* types.h:304:3 */

type K_fltset_t = uint32 /* types.h:317:16 */ // kernel fault set type

// The following type is for various kinds of identifiers.  The
// actual type must be the same for all since some system calls
// (such as sigsend) take arguments that may be any of these
// types.  The enumeration type idtype_t defined in sys/procset.h
// is used to indicate what type of id is being specified --
// a process id, process group id, session id, scheduling class id,
// user id, group id, project id, task id or zone id.
type Id_t = int32 /* types.h:329:14 */

type Lgrp_id_t = int32 /* types.h:334:15 */ // lgroup ID

// Type useconds_t is an unsigned integral type capable of storing
// values at least in the range of zero to 1,000,000.
type Useconds_t = uint32 /* types.h:340:17 */ // Time, in microseconds

type Suseconds_t = int64 /* types.h:344:14 */ // signed # of microseconds

// Typedefs for dev_t components.
type Major_t = uint32 /* types.h:351:16 */ // major part of device number
type Minor_t = uint32 /* types.h:352:16 */ // minor part of device number

// The data type of a thread priority.
type Pri_t = int16 /* types.h:361:15 */

// The data type for a CPU flags field.  (Can be extended to larger unsigned
// types, if needed, limited by ability to update atomically.)
type Cpu_flag_t = uint16 /* types.h:367:18 */

// For compatibility reasons the following typedefs (prefixed o_)
// can't grow regardless of the EFT definition. Although,
// applications should not explicitly use these typedefs
// they may be included via a system header definition.
// WARNING: These typedefs may be removed in a future
// release.
//
//	ex. the definitions in s5inode.h (now obsoleted)
//		remained small to preserve compatibility
//		in the S5 file system type.
type O_mode_t = uint16 /* types.h:380:18 */ // old file attribute type
type O_dev_t = int16   /* types.h:381:15 */ // old device type
type O_uid_t = uint16  /* types.h:382:18 */ // old UID type
type O_gid_t = uint16  /* types.h:383:17 */ // old GID type
type O_nlink_t = int16 /* types.h:384:15 */ // old file link type
type O_pid_t = int16   /* types.h:385:15 */ // old process id type
type O_ino_t = uint16  /* types.h:386:18 */ // old inode type

// POSIX and XOPEN Declarations
type Key_t = int32   /* types.h:392:13 */ // IPC key type
type Mode_t = uint32 /* types.h:394:16 */ // file attribute type

type Uid_t = uint32 /* types.h:401:22 */ // UID type

type Gid_t = uint32 /* types.h:404:15 */ // GID type

type Datalink_id_t = uint32 /* types.h:406:18 */
type Vrid_t = uint32        /* types.h:407:18 */

type Taskid_t = int32 /* types.h:409:17 */
type Projid_t = int32 /* types.h:410:17 */
type Poolid_t = int32 /* types.h:411:14 */
type Zoneid_t = int32 /* types.h:412:14 */
type Ctid_t = int32   /* types.h:413:14 */

// POSIX definitions are same as defined in thread.h and synch.h.
// Any changes made to here should be reflected in corresponding
// files as described in comments.
type Pthread_t = uint32     /* types.h:420:16 */ // = thread_t in thread.h
type Pthread_key_t = uint32 /* types.h:421:16 */ // = thread_key_t in thread.h

// "Magic numbers" tagging synchronization object types

type X_pthread_mutex = struct {
	F__pthread_mutex_flags struct {
		F__pthread_mutex_flag1   uint16
		F__pthread_mutex_flag2   uint8
		F__pthread_mutex_ceiling uint8
		F__pthread_mutex_type    uint16
		F__pthread_mutex_magic   uint16
	}
	F__pthread_mutex_lock struct {
		F__ccgo_pad1            [0]uint64
		F__pthread_mutex_lock64 struct{ F__pthread_mutex_pad [8]uint8 }
	}
	F__pthread_mutex_data uint64
} /* types.h:429:9 */

// = thread_key_t in thread.h

// "Magic numbers" tagging synchronization object types

type Pthread_mutex_t = X_pthread_mutex /* types.h:448:3 */

type X_pthread_cond = struct {
	F__pthread_cond_flags struct {
		F__pthread_cond_flag  [4]uint8
		F__pthread_cond_type  uint16
		F__pthread_cond_magic uint16
	}
	F__pthread_cond_data uint64
} /* types.h:450:9 */

type Pthread_cond_t = X_pthread_cond /* types.h:457:3 */

// UNIX 98 Extension
type X_pthread_rwlock = struct {
	F__pthread_rwlock_readers  int32
	F__pthread_rwlock_type     uint16
	F__pthread_rwlock_magic    uint16
	F__pthread_rwlock_mutex    Pthread_mutex_t
	F__pthread_rwlock_readercv Pthread_cond_t
	F__pthread_rwlock_writercv Pthread_cond_t
} /* types.h:462:9 */

// UNIX 98 Extension
type Pthread_rwlock_t = X_pthread_rwlock /* types.h:469:3 */

// SUSV3
type Pthread_barrier_t = struct {
	F__pthread_barrier_count    uint32
	F__pthread_barrier_current  uint32
	F__pthread_barrier_cycle    uint64
	F__pthread_barrier_reserved uint64
	F__pthread_barrier_lock     Pthread_mutex_t
	F__pthread_barrier_cond     Pthread_cond_t
} /* types.h:481:3 */

type Pthread_spinlock_t = Pthread_mutex_t /* types.h:483:25 */

// attributes for threads, dynamically allocated by library
type X_pthread_attr = struct{ F__pthread_attrp uintptr } /* types.h:488:9 */

// attributes for threads, dynamically allocated by library
type Pthread_attr_t = X_pthread_attr /* types.h:490:3 */

// attributes for mutex, dynamically allocated by library
type X_pthread_mutexattr = struct{ F__pthread_mutexattrp uintptr } /* types.h:495:9 */

// attributes for mutex, dynamically allocated by library
type Pthread_mutexattr_t = X_pthread_mutexattr /* types.h:497:3 */

// attributes for cond, dynamically allocated by library
type X_pthread_condattr = struct{ F__pthread_condattrp uintptr } /* types.h:502:9 */

// attributes for cond, dynamically allocated by library
type Pthread_condattr_t = X_pthread_condattr /* types.h:504:3 */

// pthread_once
type X_once = struct{ F__pthread_once_pad [4]uint64 } /* types.h:509:9 */

// pthread_once
type Pthread_once_t = X_once /* types.h:511:3 */

// UNIX 98 Extensions
// attributes for rwlock, dynamically allocated by library
type X_pthread_rwlockattr = struct{ F__pthread_rwlockattrp uintptr } /* types.h:517:9 */

// UNIX 98 Extensions
// attributes for rwlock, dynamically allocated by library
type Pthread_rwlockattr_t = X_pthread_rwlockattr /* types.h:519:3 */

// SUSV3
// attributes for pthread_barrier_t, dynamically allocated by library
type Pthread_barrierattr_t = struct{ F__pthread_barrierattrp uintptr } /* types.h:527:3 */

type Dev_t = uint64 /* types.h:529:17 */ // expanded device type

type Nlink_t = uint32 /* types.h:532:16 */ // file link type
type Pid_t = int32    /* types.h:533:13 */ // size of something in bytes

type Ssize_t = int64 /* types.h:551:14 */ // size of something in bytes or -1

type Time_t = int64 /* types.h:559:15 */ // time of day in seconds

type Clock_t = int64 /* types.h:564:15 */ // relative time in a specified resolution

type Clockid_t = int32 /* types.h:569:13 */ // clock identifier type

type Timer_t = int32 /* types.h:574:13 */ // timer identifier type

// BEGIN CSTYLED
type Unchar = uint8  /* types.h:580:23 */
type Ushort = uint16 /* types.h:581:24 */
type Uint = uint32   /* types.h:582:22 */
type Ulong = uint64  /* types.h:583:23 */
// END CSTYLED

// The following is the value of type id_t to use to indicate the
// caller's current id.  See procset.h for the type idtype_t
// which defines which kind of id is being specified.

// The following value of type pfn_t is used to indicate
// invalid page frame number.

// BEGIN CSTYLED
type U_char = uint8                   /* types.h:650:23 */
type U_short = uint16                 /* types.h:651:24 */
type U_int = uint32                   /* types.h:652:22 */
type U_long = uint64                  /* types.h:653:23 */
type X_quad = struct{ Fval [2]int32 } /* types.h:654:9 */

type Quad_t = X_quad /* types.h:654:38 */ // used by UFS
type Quad = Quad_t   /* types.h:655:17 */ // used by UFS
// END CSTYLED

// Nested include for BSD/sockets source compatibility.
// (The select macros used to be defined here).
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Time expressed in seconds and nanoseconds

type Timespec = struct {
	Ftv_sec  int64
	Ftv_nsec int64
} /* time_impl.h:57:9 */

// used by UFS
// END CSTYLED

// Nested include for BSD/sockets source compatibility.
// (The select macros used to be defined here).
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2005 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Implementation-private.  This header should not be included
// directly by an application.  The application should instead
// include <time.h> which includes this header conditionally
// depending on which feature test macros are defined. By default,
// this header is included by <time.h>.  X/Open and POSIX
// standards requirements result in this header being included
// by <time.h> only under a restricted set of conditions.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Time expressed in seconds and nanoseconds

type Timespec_t = Timespec /* time_impl.h:60:3 */

type Timestruc_t = Timespec /* time_impl.h:81:25 */ // definition per SVr4

// The following has been left in for backward compatibility. Portable
// applications should not use the structure name timestruc.

// Timer specification
type Itimerspec = struct {
	Fit_interval struct {
		Ftv_sec  int64
		Ftv_nsec int64
	}
	Fit_value struct {
		Ftv_sec  int64
		Ftv_nsec int64
	}
} /* time_impl.h:95:9 */

// definition per SVr4

// The following has been left in for backward compatibility. Portable
// applications should not use the structure name timestruc.

// Timer specification
type Itimerspec_t = Itimerspec /* time_impl.h:98:3 */

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright (c) 1982, 1986, 1993 Regents of the University of California.
// All rights reserved.  The Berkeley software License Agreement
// specifies the terms and conditions for redistribution.

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2020 OmniOS Community Edition (OmniOSce) Association.

// Copyright (c) 2013, 2016 by Delphix. All rights reserved.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// Structure returned by gettimeofday(2) system call,
// and used in other calls.

type Timeval = struct {
	Ftv_sec  int64
	Ftv_usec int64
} /* time.h:54:1 */

type Timezone = struct {
	Ftz_minuteswest int32
	Ftz_dsttime     int32
} /* time.h:86:1 */

// Needed for longlong_t type.  Placement of this due to <sys/types.h>
// including <sys/select.h> which relies on the presense of the itimerval
// structure.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// Operations on timevals.

// Names of the interval timers, and structure
// defining a timer setting.
// time and when system is running on
// behalf of the process.
// time profiling of multithreaded
// programs.

type Itimerval = struct {
	Fit_interval struct {
		Ftv_sec  int64
		Ftv_usec int64
	}
	Fit_value struct {
		Ftv_sec  int64
		Ftv_usec int64
	}
} /* time.h:209:1 */

//	Definitions for commonly used resolutions.

// Time expressed as a 64-bit nanosecond counter.
type Hrtime_t = int64 /* time.h:265:20 */

// The inclusion of <time.h> is historical and was added for
// backward compatibility in delta 1.2 when a number of definitions
// were moved out of <sys/time.h>.  More recently, the timespec and
// itimerspec structure definitions, along with the _CLOCK_*, CLOCK_*,
// _TIMER_*, and TIMER_* symbols were moved to <sys/time_impl.h>,
// which is now included by <time.h>.  This change was due to POSIX
// 1003.1b-1993 and X/Open UNIX 98 requirements.  For non-POSIX and
// non-X/Open applications, including this header will still make
// visible these definitions.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2010 Nexenta Systems, Inc.  Al rights reserved.
// Copyright 2016 Joyent, Inc.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1988 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
// Copyright 2014 PALO, Richard.
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// An application should not include this header directly.  Instead it
// should be included only through the inclusion of other Sun headers.
//
// The contents of this header is limited to identifiers specified in the
// C Standard.  Any new identifiers specified in future amendments to the
// C Standard must be placed in this header.  If these new identifiers
// are required to also be in the C++ Standard "std" namespace, then for
// anything other than macro definitions, corresponding "using" directives
// must also be added to <time.h.h>.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// This file and its contents are supplied under the terms of the
// Common Development and Distribution License ("CDDL"), version 1.0.
// You may only use this file in accordance with the terms of version
// 1.0 of the CDDL.
//
// A full copy of the text of the CDDL should have accompanied this
// source.  A copy of the CDDL is also available via the Internet at
// http://www.illumos.org/license/CDDL.

// Copyright 2014-2016 PALO, Richard.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// POSIX.1-2008 requires that the NULL macro be cast to type void *.

type Tm = struct {
	Ftm_sec   int32
	Ftm_min   int32
	Ftm_hour  int32
	Ftm_mday  int32
	Ftm_mon   int32
	Ftm_year  int32
	Ftm_wday  int32
	Ftm_yday  int32
	Ftm_isdst int32
} /* time_iso.h:80:1 */

// Neither X/Open nor POSIX allow the inclusion of <signal.h> for the
// definition of the sigevent structure.  Both require the inclusion
// of <signal.h> and <time.h> when using the timer_create() function.
// However, X/Open also specifies that the sigevent structure be defined
// in <time.h> as described in the header <signal.h>.  This prevents
// compiler warnings for applications that only include <time.h> and not
// also <signal.h>.  The sigval union and the sigevent structure is
// therefore defined both here and in <sys/siginfo.h> which gets included
// via inclusion of <signal.h>.
type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* time.h:125:1 */

type Sigevent = struct {
	Fsigev_notify int32
	Fsigev_signo  int32
	Fsigev_value  struct {
		F__ccgo_pad1 [0]uint64
		Fsival_int   int32
		F__ccgo_pad2 [4]byte
	}
	Fsigev_notify_function   uintptr
	Fsigev_notify_attributes uintptr
	F__sigev_pad2            int32
	F__ccgo_pad1             [4]byte
} /* time.h:133:1 */

type Locale_t = uintptr /* time.h:292:24 */

// The inclusion of <sys/select.h> is needed for the FD_CLR,
// FD_ISSET, FD_SET, and FD_SETSIZE macros as well as the
// select() prototype defined in the XOpen specifications
// beginning with XSH4v2.  Placement required after definition
// for itimerval.
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// The sigset_t type is defined in <sys/signal.h> and duplicated
// in <sys/ucontext.h> as a result of XPG4v2 requirements. XPG6
// now allows the visibility of signal.h in this header, however
// an order of inclusion problem occurs as a result of inclusion
// of <sys/select.h> in <signal.h> under certain conditions.
// Rather than include <sys/signal.h> here, we've duplicated
// the sigset_t type instead. This type is required for the XPG6
// introduced pselect() function also declared in this header.
type Sigset_t = struct{ F__sigbits [4]uint32 } /* select.h:76:3 */

// Select uses bit masks of file descriptors in longs.
// These macros manipulate such bit fields.
// FD_SETSIZE may be defined by the user, but the default here
// should be >= RLIM_FD_MAX.

type Fd_mask = int64  /* select.h:92:14 */
type Fds_mask = int64 /* select.h:94:14 */

//  The value of _NBBY needs to be consistant with the value
//  of NBBY in <sys/param.h>.

type Fd_set1 = struct{ Ffds_bits [1024]int64 } /* select.h:120:9 */

//  The value of _NBBY needs to be consistant with the value
//  of NBBY in <sys/param.h>.

type Fd_set = Fd_set1 /* select.h:125:3 */

// _VOID was defined to be either void or char but this is not
// required because previous SunOS compilers have accepted the void
// type. However, because many system header and source files use the
// void keyword, the volatile keyword, and ANSI C function prototypes,
// non-ANSI compilers cannot compile the system anyway. The _VOID macro
// should therefore not be used and remains for source compatibility
// only.
// CSTYLED

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright (c) 2015, Joyent, Inc.  All rights reserved.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// I/O parameter information.  A uio structure describes the I/O which
// is to be performed by an operation.  Typically the data movement will
// be performed by a routine such as uiomove(), which updates the uio
// structure to reflect what was done.

type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  uint64
} /* uio.h:68:9 */

// _VOID was defined to be either void or char but this is not
// required because previous SunOS compilers have accepted the void
// type. However, because many system header and source files use the
// void keyword, the volatile keyword, and ANSI C function prototypes,
// non-ANSI compilers cannot compile the system anyway. The _VOID macro
// should therefore not be used and remains for source compatibility
// only.
// CSTYLED

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2010 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright (c) 2015, Joyent, Inc.  All rights reserved.

//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// I/O parameter information.  A uio structure describes the I/O which
// is to be performed by an operation.  Typically the data movement will
// be performed by a routine such as uiomove(), which updates the uio
// structure to reflect what was done.

type Iovec_t = Iovec /* uio.h:75:3 */

// Segment flag values.
type Uio_seg_t = uint32 /* uio.h:93:70 */

type Uio = struct {
	Fuio_iov     uintptr
	Fuio_iovcnt  int32
	F__ccgo_pad1 [4]byte
	F_uio_offset Lloff_t
	Fuio_segflg  uint32
	Fuio_fmode   uint16
	Fuio_extflg  uint16
	F_uio_limit  Lloff_t
	Fuio_resid   int64
} /* uio.h:95:9 */

type Uio_t = Uio /* uio.h:104:3 */

// Extended uio_t uioa_t used for asynchronous uio.
//
// Note: UIOA_IOV_MAX is defined and used as it is in "fs/vncalls.c"
//	 as there isn't a formal definition of IOV_MAX for the kernel.

type Uioa_page_s = struct {
	Fuioa_pfncnt int32
	F__ccgo_pad1 [4]byte
	Fuioa_ppp    uintptr
	Fuioa_base   uintptr
	Fuioa_len    uint64
} /* uio.h:114:9 */

// Extended uio_t uioa_t used for asynchronous uio.
//
// Note: UIOA_IOV_MAX is defined and used as it is in "fs/vncalls.c"
//	 as there isn't a formal definition of IOV_MAX for the kernel.

type Uioa_page_t = Uioa_page_s /* uio.h:119:3 */

type Uioa_s = struct {
	Fuio_iov     uintptr
	Fuio_iovcnt  int32
	F__ccgo_pad1 [4]byte
	F_uio_offset Lloff_t
	Fuio_segflg  uint32
	Fuio_fmode   uint16
	Fuio_extflg  uint16
	F_uio_limit  Lloff_t
	Fuio_resid   int64
	Fuioa_state  uint32
	F__ccgo_pad2 [4]byte
	Fuioa_mbytes int64
	Fuioa_lcur   uintptr
	Fuioa_lppp   uintptr
	Fuioa_hwst   [4]uintptr
	Fuioa_locked [16]Uioa_page_t
} /* uio.h:121:9 */

type Uioa_t = Uioa_s /* uio.h:139:3 */

// uio extensions
//
// PSARC 2009/478: Copy Reduction Interfaces
type Xuio_type_t = uint32 /* uio.h:150:3 */

type Xuio = struct {
	Fxu_uio      Uio_t
	Fxu_type     uint32
	F__ccgo_pad1 [4]byte
	Fxu_ext      struct {
		Fxu_aio struct {
			Fxu_a_state  uint32
			F__ccgo_pad1 [4]byte
			Fxu_a_mbytes int64
			Fxu_a_lcur   uintptr
			Fxu_a_lppp   uintptr
			Fxu_a_hwst   [4]uintptr
			Fxu_a_locked [16]Uioa_page_t
		}
	}
} /* uio.h:152:9 */

type Xuio_t = Xuio /* uio.h:189:3 */

// I/O direction.
type Uio_rw_t = uint32 /* uio.h:220:45 */

// uio_extflg: extended flags
//
// NOTE: This flag will be used in uiomove to determine if non-temporal
// access, ie, access bypassing caches, should be used.  Filesystems that
// don't initialize this field could experience suboptimal performance due to
// the random data the field contains.
//
// NOTE: This flag is also used by uioasync callers to pass an extended
// uio_t (uioa_t), to uioasync enabled consumers. Unlike above all
// consumers of a uioa_t require the uio_extflg to be initialized.

// Global uioasync capability shadow state.
type Uioasync_s = struct {
	Fenabled     uint32
	F__ccgo_pad1 [4]byte
	Fmincnt      uint64
} /* uio.h:243:9 */

// uio_extflg: extended flags
//
// NOTE: This flag will be used in uiomove to determine if non-temporal
// access, ie, access bypassing caches, should be used.  Filesystems that
// don't initialize this field could experience suboptimal performance due to
// the random data the field contains.
//
// NOTE: This flag is also used by uioasync callers to pass an extended
// uio_t (uioa_t), to uioasync enabled consumers. Unlike above all
// consumers of a uioa_t require the uio_extflg to be initialized.

// Global uioasync capability shadow state.
type Uioasync_t = Uioasync_s /* uio.h:246:3 */

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Portions of this source code were derived from Berkeley 4.3 BSD
// under license from the Regents of the University of California.

type Sa_family_t = uint16 /* socket_impl.h:43:18 */

// Structure used by kernel to store most
// addresses.
type Sockaddr = struct {
	Fsa_family uint16
	Fsa_data   [14]int8
} /* socket_impl.h:50:1 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright (c) 1996, 2010, Oracle and/or its affiliates. All rights reserved.

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// Definitions for UNIX IPC domain.
type Sockaddr_un = struct {
	Fsun_family uint16
	Fsun_path   [108]int8
} /* un.h:53:1 */

// Copyright 1993-2003 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// from UCB 8.1 (Berkeley) 6/10/93

// A Link-Level Sockaddr may specify the interface in one of two
// ways: either by means of a system-provided index number (computed
// anew and possibly differently on every reboot), or by a human-readable
// string such as "il0" (for managerial convenience).
//
// Census taking actions, such as something akin to SIOCGCONF would return
// both the index and the human name.
//
// High volume transactions (such as giving a link-level ``from'' address
// in a recvfrom or recvmsg call) may be likely only to provide the indexed
// form, (which requires fewer copy operations and less space).
//
// The form and interpretation  of the link-level address is purely a matter
// of convention between the device driver and its consumers; however, it is
// expected that all drivers for an interface of a given if_type will agree.

// Structure of a Link-Level sockaddr:
type Sockaddr_dl = struct {
	Fsdl_family uint16
	Fsdl_index  uint16
	Fsdl_type   uint8
	Fsdl_nlen   uint8
	Fsdl_alen   uint8
	Fsdl_slen   uint8
	Fsdl_data   [244]int8
} /* if_dl.h:68:1 */

// sockaddr_storage:
// Common superset of at least AF_INET, AF_INET6 and AF_LINK sockaddr
// structures. Has sufficient size and alignment for those sockaddrs.

// Desired maximum size, alignment size and related types.

// To represent desired sockaddr max alignment for platform, a
// type is chosen which may depend on implementation platform architecture.
// Type chosen based on alignment size restrictions from <sys/isa_defs.h>.
// We desire to force up to (but no more than) 64-bit (8 byte) alignment,
// on platforms where it is possible to do so. (e.g not possible on ia32).
// For all currently supported platforms by our implementation
// in <sys/isa_defs.h>, (i.e. sparc, sparcv9, ia32, ia64)
// type "double" is suitable for that intent.
//
// Note: Type "double" is chosen over the more obvious integer type int64_t.
//
//	int64_t is not a valid type for strict ANSI/ISO C compilation on ILP32.
type Sockaddr_maxalign_t = float64 /* socket_impl.h:85:17 */

// Definitions used for sockaddr_storage structure paddings design.

type Sockaddr_storage = struct {
	Fss_family uint16
	F_ss_pad1  [6]int8
	F_ss_align float64
	F_ss_pad2  [240]int8
} /* socket_impl.h:96:1 */

// To be compatible with the Linux interfaces used, this structure is
// placed in socket_impl.h so that an include for <sys/socket.h> will
// pickup this structure. This structure is for use with PF_PACKET
// sockets.
type Sockaddr_ll = struct {
	Fsll_family   uint16
	Fsll_protocol uint16
	Fsll_ifindex  int32
	Fsll_hatype   uint16
	Fsll_pkttype  uint8
	Fsll_halen    uint8
	Fsll_addr     [8]uint8
} /* socket_impl.h:111:1 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2015 Joyent, Inc.

type Netconfig = struct {
	Fnc_netid     uintptr
	Fnc_semantics uint32
	Fnc_flag      uint32
	Fnc_protofmly uintptr
	Fnc_proto     uintptr
	Fnc_device    uintptr
	Fnc_nlookups  uint32
	F__ccgo_pad1  [4]byte
	Fnc_lookups   uintptr
	Fnc_unused    [8]uint32
} /* netconfig.h:44:1 */

type NCONF_HANDLE = struct {
	Fnc_head uintptr
	Fnc_curr uintptr
} /* netconfig.h:59:3 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright (c) 1995, 1998 by Sun Microsystems, Inc.
// All rights reserved.

// Copyright 2015, Joyent, Inc.

// Structure of file descriptor/event pairs supplied in
// the poll arrays.
type Pollfd = struct {
	Ffd      int32
	Fevents  int16
	Frevents int16
} /* poll.h:48:9 */

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright (c) 1995, 1998 by Sun Microsystems, Inc.
// All rights reserved.

// Copyright 2015, Joyent, Inc.

// Structure of file descriptor/event pairs supplied in
// the poll arrays.
type Pollfd_t = Pollfd /* poll.h:52:3 */

type Nfds_t = uint64 /* poll.h:54:23 */

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2011 Nexenta Systems, Inc. All rights reserved.
// Copyright 2015, Joyent, Inc.
// Copyright 2020 OmniOS Community Edition (OmniOSce) Association.
// Copyright (c) 1982, 1986 Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms are permitted
// provided that this notice is preserved and that due credit is given
// to the University of California at Berkeley. The name of the University
// may not be used to endorse or promote products derived from this
// software without specific prior written permission. This software
// is provided ``as is'' without express or implied warranty.

// Constants and structures defined by the internet system,
// according to following documents
//
// Internet ASSIGNED NUMBERS (RFC1700) and its successors:
//	http://www.iana.org/assignments/protocol-numbers
//	http://www.iana.org/assignments/port-numbers
// Basic Socket Interface Extensions for IPv6 (RFC2133 and its successors)
//

//  DO NOT EDIT THIS FILE.
//
//     It has been auto-edited by fixincludes from:
//
// 	"/usr/include/sys/feature_tests.h"
//
//     This had to be done to correct non-standard usages in the
//     original, manufacturer supplied header file.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2013 Garrett D'Amore <<EMAIL>>
// Copyright 2016 Joyent, Inc.
// Copyright 2022 Oxide Computer Company
//
// Copyright 2006 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
//	Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
//
// Copyright 2013 Nexenta Systems, Inc.  All rights reserved.
// Copyright 2016 Joyent, Inc.
// Copyright 2021 Oxide Computer Company

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// Portions of this source code were derived from Berkeley 4.3 BSD
// under license from the Regents of the University of California.

// The socklen definitions are reproduced here from sys/socket.h so as to
// not introduce that namespace into existing users of netinet/in.h.
type Socklen_t = uint32 /* in.h:57:18 */

type Psocklen_t = uintptr /* in.h:63:15 */

// Symbols such as htonl() are required to be exposed through this file,
// per XNS Issue 5. This is achieved by inclusion of <sys/byteorder.h>
// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2009 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

//	Copyright (c) 1983, 1984, 1985, 1986, 1987, 1988, 1989 AT&T
//	  All Rights Reserved

// University Copyright- Copyright (c) 1982, 1986, 1988
// The Regents of the University of California
// All Rights Reserved
//
// University Acknowledgment- Portions of this document are derived from
// software developed by the University of California, Berkeley, and its
// contributors.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License (the "License").
// You may not use this file except in compliance with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END

// Copyright 2008 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.
// Copyright 2016 Joyent, Inc.

// CDDL HEADER START
//
// The contents of this file are subject to the terms of the
// Common Development and Distribution License, Version 1.0 only
// (the "License").  You may not use this file except in compliance
// with the License.
//
// You can obtain a copy of the license at usr/src/OPENSOLARIS.LICENSE
// or http://www.opensolaris.org/os/licensing.
// See the License for the specific language governing permissions
// and limitations under the License.
//
// When distributing Covered Code, include this CDDL HEADER in each
// file and include the License file at usr/src/OPENSOLARIS.LICENSE.
// If applicable, add the following below this CDDL HEADER, with the
// fields enclosed by brackets "[]" replaced with your own identifying
// information: Portions Copyright [yyyy] [name of copyright owner]
//
// CDDL HEADER END
// Copyright 2014 Garrett D'Amore <<EMAIL>>
//
// Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
// Use is subject to license terms.

// macros for conversion between host and (internet) network byte order

type In_port_t = uint16 /* byteorder.h:74:18 */

type In_addr_t = uint32 /* byteorder.h:79:18 */

// Macros to reverse byte order

// Macros to convert from a specific byte order to/from native byte order

// Macros to read unaligned values from a specific byte order to
// native byte order

// Macros to write unaligned values from native byte order to a specific byte
// order.

// Note: IPv4 address data structures usage conventions.
// The "in_addr_t" type below (required by Unix standards)
// is NOT a typedef of "struct in_addr" and violates the usual
// conventions where "struct <name>" and <name>_t are corresponding
// typedefs.
// To minimize confusion, kernel data structures/usage prefers use
// of "ipaddr_t" as atomic uint32_t type and avoid using "in_addr_t"
// The user level APIs continue to follow the historic popular
// practice of using "struct in_addr".

type Ipaddr_t = uint32 /* in.h:99:18 */

type In6_addr = struct {
	F_S6_un struct {
		F__ccgo_pad1 [0]uint32
		F_S6_u8      [16]uint8
	}
} /* in.h:104:1 */

type In6_addr_t = In6_addr /* in.h:137:25 */

// Protocols
//
// Some of these constant names are copied for the DTrace IP provider in
// usr/src/lib/libdtrace/common/{ip.d.in, ip.sed.in}, which should be kept
// in sync.
// Transmission Protocol

// Port/socket numbers: network standard functions
//
// Entries should exist here for each port number compiled into an ON
// component, such as snoop.

// Port/socket numbers: host specific functions

// Internet Key Exchange (IKE) ports

// UNIX TCP sockets

// UNIX UDP sockets

// DHCPv6 UDP ports

// Ports < IPPORT_RESERVED are reserved for
// privileged processes (e.g. root).
// Ports > IPPORT_USERRESERVED are reserved
// for servers, not necessarily privileged.

// Link numbers

// IPv4 Internet address
//	This definition contains obsolete fields for compatibility
//	with SunOS 3.x and 4.2bsd.  The presence of subnets renders
//	divisions into fixed fields misleading at best.  New code
//	should use only the s_addr field.

type In_addr = struct {
	FS_un struct {
		F__ccgo_pad1 [0]uint32
		FS_un_b      struct {
			Fs_b1 uint8
			Fs_b2 uint8
			Fs_b3 uint8
			Fs_b4 uint8
		}
	}
} /* in.h:301:1 */

// Definitions of bits in internet address integers.
// On subnets, the decomposition of addresses to host and net parts
// is done according to subnet mask, not the masks here.
//
// Note that with the introduction of CIDR, IN_CLASSA, IN_CLASSB,
// IN_CLASSC, IN_CLASSD and IN_CLASSE macros have become "de-facto
// obsolete". IN_MULTICAST macro should be used to test if a address
// is a multicast address.

// We have removed CLASS E checks from the kernel
// But we preserve these defines for userland in order
// to avoid compile  breakage of some 3rd party piece of software

// Scoped IPv4 prefixes (in host byte-order)

// RFC 3927 IPv4 link local address (i in host byte-order)

// Well known 6to4 Relay Router Anycast address defined in RFC 3068

// Define a macro to stuff the loopback address into an Internet address

// IPv4 Socket address.
type Sockaddr_in = struct {
	Fsin_family uint16
	Fsin_port   uint16
	Fsin_addr   struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fsin_zero [8]int8
} /* in.h:409:1 */

// IPv6 socket address.
type Sockaddr_in6 = struct {
	Fsin6_family   uint16
	Fsin6_port     uint16
	Fsin6_flowinfo uint32
	Fsin6_addr     struct {
		F_S6_un struct {
			F__ccgo_pad1 [0]uint32
			F_S6_u8      [16]uint8
		}
	}
	Fsin6_scope_id uint32
	F__sin6_src_id uint32
} /* in.h:424:1 */

// Macros for accessing the traffic class and flow label fields from
// sin6_flowinfo.
// These are designed to be applied to a 32-bit value.

// masks

// Note: Macros IN6ADDR_ANY_INIT and IN6ADDR_LOOPBACK_INIT are for
// use as RHS of Static initializers of "struct in6_addr" (or in6_addr_t)
// only. They need to be different for User/Kernel versions because union
// component data structure is defined differently (it is identical at
// binary representation level).
//
// const struct in6_addr IN6ADDR_ANY_INIT;
// const struct in6_addr IN6ADDR_LOOPBACK_INIT;

// RFC 2553 specifies the following macros. Their type is defined
// as "int" in the RFC but they only have boolean significance
// (zero or non-zero). For the purposes of our comment notation,
// we assume a hypothetical type "bool" defined as follows to
// write the prototypes assumed for macros in our comments better.
//
// typedef int bool;

// IN6 macros used to test for special IPv6 addresses
// (Mostly from spec)
//
// bool  IN6_IS_ADDR_UNSPECIFIED (const struct in6_addr *);
// bool  IN6_IS_ADDR_LOOPBACK    (const struct in6_addr *);
// bool  IN6_IS_ADDR_MULTICAST   (const struct in6_addr *);
// bool  IN6_IS_ADDR_LINKLOCAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_SITELOCAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_V4MAPPED    (const struct in6_addr *);
// bool  IN6_IS_ADDR_V4MAPPED_ANY(const struct in6_addr *); -- Not from RFC2553
// bool  IN6_IS_ADDR_V4COMPAT    (const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_RESERVED (const struct in6_addr *); -- Not from RFC2553
// bool  IN6_IS_ADDR_MC_NODELOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_LINKLOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_SITELOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_ORGLOCAL (const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_GLOBAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_6TO4	 (const struct in6_addr *); -- Not from RFC2553
// bool  IN6_ARE_6TO4_PREFIX_EQUAL(const struct in6_addr *,
//	     const struct in6_addr *);			    -- Not from RFC2553
// bool  IN6_IS_ADDR_LINKSCOPE	 (const struct in6addr  *); -- Not from RFC2553

// IN6_IS_ADDR_V4MAPPED - A IPv4 mapped INADDR_ANY
// Note: This macro is currently NOT defined in RFC2553 specification
// and not a standard macro that portable applications should use.

// Exclude loopback and unspecified address

// Note:
// IN6_IS_ADDR_MC_RESERVED macro is currently NOT defined in RFC2553
// specification and not a standard macro that portable applications
// should use.

// The IN6_IS_ADDR_MC_SOLICITEDNODE macro is not defined in any standard or
// RFC, and shouldn't be used by portable applications.  It is used to see
// if an address is a solicited-node multicast address, which is prefixed
// with ff02:0:0:0:0:1:ff00::/104.

// Macros to a) test for 6to4 IPv6 address, and b) to test if two
// 6to4 addresses have the same /48 prefix, and, hence, are from the
// same 6to4 site.

// IN6_IS_ADDR_LINKSCOPE
// Identifies an address as being either link-local, link-local multicast or
// node-local multicast.  All types of addresses are considered to be unique
// within the scope of a given link.

// Useful utility macros for operations with IPv6 addresses
// Note: These macros are NOT defined in the RFC2553 or any other
// standard specification and are not standard macros that portable
// applications should use.

// IN6_V4MAPPED_TO_INADDR
// IN6_V4MAPPED_TO_IPADDR
//	Assign a IPv4-Mapped IPv6 address to an IPv4 address.
//	Note: These macros are NOT defined in RFC2553 or any other standard
//	specification and are not macros that portable applications should
//	use.
//
// void IN6_V4MAPPED_TO_INADDR(const in6_addr_t *v6, struct in_addr *v4);
// void IN6_V4MAPPED_TO_IPADDR(const in6_addr_t *v6, ipaddr_t v4);
//

// IN6_INADDR_TO_V4MAPPED
// IN6_IPADDR_TO_V4MAPPED
//	Assign a IPv4 address address to an IPv6 address as a IPv4-mapped
//	address.
//	Note: These macros are NOT defined in RFC2553 or any other standard
//	specification and are not macros that portable applications should
//	use.
//
// void IN6_INADDR_TO_V4MAPPED(const struct in_addr *v4, in6_addr_t *v6);
// void IN6_IPADDR_TO_V4MAPPED(const ipaddr_t v4, in6_addr_t *v6);
//

// IN6_6TO4_TO_V4ADDR
//	Extract the embedded IPv4 address from the prefix to a 6to4 IPv6
//      address.
//	Note: This macro is NOT defined in RFC2553 or any other standard
//	specification and is not a macro that portable applications should
//	use.
//	Note: we don't use the IPADDR form of the macro because we need
//	to do a bytewise copy; the V4ADDR in the 6to4 address is not
//	32-bit aligned.
//
// void IN6_6TO4_TO_V4ADDR(const in6_addr_t *v6, struct in_addr *v4);
//

// IN6_V4ADDR_TO_6TO4
//	Given an IPv4 address and an IPv6 address for output, a 6to4 address
//	will be created from the IPv4 Address.
//	Note:  This method for creating 6to4 addresses is not standardized
//	outside of Solaris.  The newly created 6to4 address will be of the form
//	2002:<V4ADDR>:<SUBNETID>::<HOSTID>, where SUBNETID will equal 0 and
//	HOSTID will equal 1.
//
// void IN6_V4ADDR_TO_6TO4(const struct in_addr *v4, in6_addr_t *v6)
//

// IN6_ARE_ADDR_EQUAL (defined in RFC2292)
//	 Compares if IPv6 addresses are equal.
// Note: Compares in order of high likelyhood of a miss so we minimize
// compares. (Current heuristic order, compare in reverse order of
// uint32_t units)
//
// bool  IN6_ARE_ADDR_EQUAL(const struct in6_addr *,
//			    const struct in6_addr *);

// IN6_ARE_PREFIXEDADDR_EQUAL (not defined in RFCs)
//	Compares if prefixed parts of IPv6 addresses are equal.
//
// uint32_t IN6_MASK_FROM_PREFIX(int, int);
// bool     IN6_ARE_PREFIXEDADDR_EQUAL(const struct in6_addr *,
//				       const struct in6_addr *,
//				       int);

// Options for use with [gs]etsockopt at the IP level.
//
// Note: Some of the IP_ namespace has conflict with and
// and is exposed through <xti.h>. (It also requires exposing
// options not implemented). The options with potential
// for conflicts use #ifndef guards.

// IP_PKTINFO and IP_RECVPKTINFO have same value. Size of argument passed in
// is used to differentiate b/w the two.

// Different preferences that can be requested from IPSEC protocols.
// This can be used with the setsockopt() call to set per socket security
// options. When the application uses per-socket API, we will reflect
// the request on both outbound and inbound packets.

type Ipsec_req = struct {
	Fipsr_ah_req         uint32
	Fipsr_esp_req        uint32
	Fipsr_self_encap_req uint32
	Fipsr_auth_alg       uint8
	Fipsr_esp_alg        uint8
	Fipsr_esp_auth_alg   uint8
	F__ccgo_pad1         [1]byte
} /* in.h:950:9 */

// Macros for accessing the traffic class and flow label fields from
// sin6_flowinfo.
// These are designed to be applied to a 32-bit value.

// masks

// Note: Macros IN6ADDR_ANY_INIT and IN6ADDR_LOOPBACK_INIT are for
// use as RHS of Static initializers of "struct in6_addr" (or in6_addr_t)
// only. They need to be different for User/Kernel versions because union
// component data structure is defined differently (it is identical at
// binary representation level).
//
// const struct in6_addr IN6ADDR_ANY_INIT;
// const struct in6_addr IN6ADDR_LOOPBACK_INIT;

// RFC 2553 specifies the following macros. Their type is defined
// as "int" in the RFC but they only have boolean significance
// (zero or non-zero). For the purposes of our comment notation,
// we assume a hypothetical type "bool" defined as follows to
// write the prototypes assumed for macros in our comments better.
//
// typedef int bool;

// IN6 macros used to test for special IPv6 addresses
// (Mostly from spec)
//
// bool  IN6_IS_ADDR_UNSPECIFIED (const struct in6_addr *);
// bool  IN6_IS_ADDR_LOOPBACK    (const struct in6_addr *);
// bool  IN6_IS_ADDR_MULTICAST   (const struct in6_addr *);
// bool  IN6_IS_ADDR_LINKLOCAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_SITELOCAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_V4MAPPED    (const struct in6_addr *);
// bool  IN6_IS_ADDR_V4MAPPED_ANY(const struct in6_addr *); -- Not from RFC2553
// bool  IN6_IS_ADDR_V4COMPAT    (const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_RESERVED (const struct in6_addr *); -- Not from RFC2553
// bool  IN6_IS_ADDR_MC_NODELOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_LINKLOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_SITELOCAL(const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_ORGLOCAL (const struct in6_addr *);
// bool  IN6_IS_ADDR_MC_GLOBAL   (const struct in6_addr *);
// bool  IN6_IS_ADDR_6TO4	 (const struct in6_addr *); -- Not from RFC2553
// bool  IN6_ARE_6TO4_PREFIX_EQUAL(const struct in6_addr *,
//	     const struct in6_addr *);			    -- Not from RFC2553
// bool  IN6_IS_ADDR_LINKSCOPE	 (const struct in6addr  *); -- Not from RFC2553

// IN6_IS_ADDR_V4MAPPED - A IPv4 mapped INADDR_ANY
// Note: This macro is currently NOT defined in RFC2553 specification
// and not a standard macro that portable applications should use.

// Exclude loopback and unspecified address

// Note:
// IN6_IS_ADDR_MC_RESERVED macro is currently NOT defined in RFC2553
// specification and not a standard macro that portable applications
// should use.

// The IN6_IS_ADDR_MC_SOLICITEDNODE macro is not defined in any standard or
// RFC, and shouldn't be used by portable applications.  It is used to see
// if an address is a solicited-node multicast address, which is prefixed
// with ff02:0:0:0:0:1:ff00::/104.

// Macros to a) test for 6to4 IPv6 address, and b) to test if two
// 6to4 addresses have the same /48 prefix, and, hence, are from the
// same 6to4 site.

// IN6_IS_ADDR_LINKSCOPE
// Identifies an address as being either link-local, link-local multicast or
// node-local multicast.  All types of addresses are considered to be unique
// within the scope of a given link.

// Useful utility macros for operations with IPv6 addresses
// Note: These macros are NOT defined in the RFC2553 or any other
// standard specification and are not standard macros that portable
// applications should use.

// IN6_V4MAPPED_TO_INADDR
// IN6_V4MAPPED_TO_IPADDR
//	Assign a IPv4-Mapped IPv6 address to an IPv4 address.
//	Note: These macros are NOT defined in RFC2553 or any other standard
//	specification and are not macros that portable applications should
//	use.
//
// void IN6_V4MAPPED_TO_INADDR(const in6_addr_t *v6, struct in_addr *v4);
// void IN6_V4MAPPED_TO_IPADDR(const in6_addr_t *v6, ipaddr_t v4);
//

// IN6_INADDR_TO_V4MAPPED
// IN6_IPADDR_TO_V4MAPPED
//	Assign a IPv4 address address to an IPv6 address as a IPv4-mapped
//	address.
//	Note: These macros are NOT defined in RFC2553 or any other standard
//	specification and are not macros that portable applications should
//	use.
//
// void IN6_INADDR_TO_V4MAPPED(const struct in_addr *v4, in6_addr_t *v6);
// void IN6_IPADDR_TO_V4MAPPED(const ipaddr_t v4, in6_addr_t *v6);
//

// IN6_6TO4_TO_V4ADDR
//	Extract the embedded IPv4 address from the prefix to a 6to4 IPv6
//      address.
//	Note: This macro is NOT defined in RFC2553 or any other standard
//	specification and is not a macro that portable applications should
//	use.
//	Note: we don't use the IPADDR form of the macro because we need
//	to do a bytewise copy; the V4ADDR in the 6to4 address is not
//	32-bit aligned.
//
// void IN6_6TO4_TO_V4ADDR(const in6_addr_t *v6, struct in_addr *v4);
//

// IN6_V4ADDR_TO_6TO4
//	Given an IPv4 address and an IPv6 address for output, a 6to4 address
//	will be created from the IPv4 Address.
//	Note:  This method for creating 6to4 addresses is not standardized
//	outside of Solaris.  The newly created 6to4 address will be of the form
//	2002:<V4ADDR>:<SUBNETID>::<HOSTID>, where SUBNETID will equal 0 and
//	HOSTID will equal 1.
//
// void IN6_V4ADDR_TO_6TO4(const struct in_addr *v4, in6_addr_t *v6)
//

// IN6_ARE_ADDR_EQUAL (defined in RFC2292)
//	 Compares if IPv6 addresses are equal.
// Note: Compares in order of high likelyhood of a miss so we minimize
// compares. (Current heuristic order, compare in reverse order of
// uint32_t units)
//
// bool  IN6_ARE_ADDR_EQUAL(const struct in6_addr *,
//			    const struct in6_addr *);

// IN6_ARE_PREFIXEDADDR_EQUAL (not defined in RFCs)
//	Compares if prefixed parts of IPv6 addresses are equal.
//
// uint32_t IN6_MASK_FROM_PREFIX(int, int);
// bool     IN6_ARE_PREFIXEDADDR_EQUAL(const struct in6_addr *,
//				       const struct in6_addr *,
//				       int);

// Options for use with [gs]etsockopt at the IP level.
//
// Note: Some of the IP_ namespace has conflict with and
// and is exposed through <xti.h>. (It also requires exposing
// options not implemented). The options with potential
// for conflicts use #ifndef guards.

// IP_PKTINFO and IP_RECVPKTINFO have same value. Size of argument passed in
// is used to differentiate b/w the two.

// Different preferences that can be requested from IPSEC protocols.
// This can be used with the setsockopt() call to set per socket security
// options. When the application uses per-socket API, we will reflect
// the request on both outbound and inbound packets.

type Ipsec_req_t = Ipsec_req /* in.h:957:3 */

// MCAST_* options are protocol-independent.  The actual definitions
// are with the v6 options below; this comment is here to note the
// namespace usage.
//
// #define	MCAST_JOIN_GROUP	0x29
// #define	MCAST_LEAVE_GROUP	0x2a
// #define	MCAST_BLOCK_SOURCE	0x2b
// #define	MCAST_UNBLOCK_SOURCE	0x2c
// #define	MCAST_JOIN_SOURCE_GROUP	0x2d
// #define	MCAST_LEAVE_SOURCE_GROUP 0x2e

// SunOS private (potentially not portable) IP_ option names
// can be reused		0x44

// Option values and names (when !_XPG5) shared with <xti_inet.h>

// The following option values are reserved by <xti_inet.h>
//
// T_IP_OPTIONS	0x107	 -  IP per-packet options
// T_IP_TOS	0x108	 -  IP per packet type of service

// Default value constants for multicast attributes controlled by
// IP*_MULTICAST_LOOP and IP*_MULTICAST_{TTL,HOPS} options.

// Argument structure for IP_ADD_MEMBERSHIP and IP_DROP_MEMBERSHIP.
type Ip_mreq = struct {
	Fimr_multiaddr struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fimr_interface struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
} /* in.h:1015:1 */

// Argument structure for IP_BLOCK_SOURCE, IP_UNBLOCK_SOURCE,
// IP_ADD_SOURCE_MEMBERSHIP, and IP_DROP_SOURCE_MEMBERSHIP.
type Ip_mreq_source = struct {
	Fimr_multiaddr struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fimr_sourceaddr struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fimr_interface struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
} /* in.h:1024:1 */

// Argument structure for IPV6_JOIN_GROUP and IPV6_LEAVE_GROUP on
// IPv6 addresses.
type Ipv6_mreq = struct {
	Fipv6mr_multiaddr struct {
		F_S6_un struct {
			F__ccgo_pad1 [0]uint32
			F_S6_u8      [16]uint8
		}
	}
	Fipv6mr_interface uint32
} /* in.h:1034:1 */

// Use #pragma pack() construct to force 32-bit alignment on amd64.
// This is needed to keep the structure size and offsets consistent
// between a 32-bit app and the 64-bit amd64 kernel in structures
// where 64-bit alignment would create gaps (in this case, structures
// which have a uint32_t followed by a struct sockaddr_storage).

// Argument structure for MCAST_JOIN_GROUP and MCAST_LEAVE_GROUP.
type Group_req = struct {
	Fgr_interface uint32
	F__ccgo_pad1  [4]byte
	Fgr_group     struct {
		Fss_family uint16
		F_ss_pad1  [6]int8
		F_ss_align float64
		F_ss_pad2  [240]int8
	}
} /* in.h:1053:1 */

// Argument structure for MCAST_BLOCK_SOURCE, MCAST_UNBLOCK_SOURCE,
// MCAST_JOIN_SOURCE_GROUP, MCAST_LEAVE_SOURCE_GROUP.
type Group_source_req = struct {
	Fgsr_interface uint32
	F__ccgo_pad1   [4]byte
	Fgsr_group     struct {
		Fss_family uint16
		F_ss_pad1  [6]int8
		F_ss_align float64
		F_ss_pad2  [240]int8
	}
	Fgsr_source struct {
		Fss_family uint16
		F_ss_pad1  [6]int8
		F_ss_align float64
		F_ss_pad2  [240]int8
	}
} /* in.h:1062:1 */

// Argument for SIOC[GS]MSFILTER ioctls
type Group_filter = struct {
	Fgf_interface uint32
	F__ccgo_pad1  [4]byte
	Fgf_group     struct {
		Fss_family uint16
		F_ss_pad1  [6]int8
		F_ss_align float64
		F_ss_pad2  [240]int8
	}
	Fgf_fmode  uint32
	Fgf_numsrc uint32
	Fgf_slist  [1]struct {
		Fss_family uint16
		F_ss_pad1  [6]int8
		F_ss_align float64
		F_ss_pad2  [240]int8
	}
} /* in.h:1071:1 */

// Argument for SIOC[GS]IPMSFILTER ioctls (IPv4-specific)
type Ip_msfilter = struct {
	Fimsf_multiaddr struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fimsf_interface struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fimsf_fmode  uint32
	Fimsf_numsrc uint32
	Fimsf_slist  [1]struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
} /* in.h:1090:1 */

// Definitions needed for [gs]etsourcefilter(), [gs]etipv4sourcefilter()

// Argument struct for IP_PKTINFO option
type In_pktinfo = struct {
	Fipi_ifindex  uint32
	Fipi_spec_dst struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
	Fipi_addr struct {
		FS_un struct {
			F__ccgo_pad1 [0]uint32
			FS_un_b      struct {
				Fs_b1 uint8
				Fs_b2 uint8
				Fs_b3 uint8
				Fs_b4 uint8
			}
		}
	}
} /* in.h:1127:9 */

// Definitions needed for [gs]etsourcefilter(), [gs]etipv4sourcefilter()

// Argument struct for IP_PKTINFO option
type In_pktinfo_t = In_pktinfo /* in.h:1131:3 */

// Argument struct for IPV6_PKTINFO option
type In6_pktinfo = struct {
	Fipi6_addr struct {
		F_S6_un struct {
			F__ccgo_pad1 [0]uint32
			F_S6_u8      [16]uint8
		}
	}
	Fipi6_ifindex uint32
} /* in.h:1136:1 */

// Argument struct for IPV6_MTUINFO option
type Ip6_mtuinfo = struct {
	Fip6m_addr struct {
		Fsin6_family   uint16
		Fsin6_port     uint16
		Fsin6_flowinfo uint32
		Fsin6_addr     struct {
			F_S6_un struct {
				F__ccgo_pad1 [0]uint32
				F_S6_u8      [16]uint8
			}
		}
		Fsin6_scope_id uint32
		F__sin6_src_id uint32
	}
	Fip6m_mtu uint32
} /* in.h:1144:1 */

// Argument structure for IP_ADD_PROXY_ADDR.
// Note that this is an unstable, experimental interface. It may change
// later. Don't use it unless you know what it is.
type In_prefix_t = struct {
	Fin_prefix_addr In_addr
	Fin_prefix_len  uint32
} /* in.h:1183:3 */

// Definitions related to sockets: types, address families, options.

// Types

// Flags for socket() and accept4()

// Option flags per-socket.

// Socket options are passed using a signed integer, but it is also rare
// for more than one to ever be passed at the same time with setsockopt
// and only one at a time can be retrieved with getsockopt.
//
// Since the lower numbers cannot be renumbered for compatibility reasons,
// it would seem that we need to start a new number space (0x40000000 -
// 0x7fffffff) for those that don't need to be stored as a bit flag
// somewhere. This limits the flag options to 30 but that seems to be
// plenty, anyway. 0x40000000 is reserved for future use.

// N.B.: The following definition is present only for compatibility
// with release 3.0.  It will disappear in later releases.

// Additional options, not kept in so_options.

// "Socket"-level control message types:

// Socket filter options

// Structure returned by FIL_LIST
type Fil_info = struct {
	Ffi_flags int32
	Ffi_pos   int32
	Ffi_name  [32]int8
} /* socket.h:225:1 */

// Structure used for manipulating linger option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:254:1 */

// Levels for (get/set)sockopt() that don't apply to a specific protocol.

// Address families.
//
// Some of these constant names are copied for the DTrace IP provider in
// usr/src/lib/libdtrace/common/{ip.d.in, ip.sed.in}, which should be kept
// in sync.

// Protocol families, same as address families for now.

// Maximum queue length specifiable by listen.

// Message header for recvmsg and sendmsg calls.
type Msghdr = struct {
	Fmsg_name         uintptr
	Fmsg_namelen      uint32
	F__ccgo_pad1      [4]byte
	Fmsg_iov          uintptr
	Fmsg_iovlen       int32
	F__ccgo_pad2      [4]byte
	Fmsg_accrights    uintptr
	Fmsg_accrightslen int32
	F__ccgo_pad3      [4]byte
} /* socket.h:365:1 */

// with left over data

// Obsolete but kept for compilation compatibility. Use IOV_MAX.

// Added for XPGv2 compliance

type Cmsghdr = struct {
	Fcmsg_len   uint32
	Fcmsg_level int32
	Fcmsg_type  int32
} /* socket.h:462:1 */

var _ int8 /* gen.c:2:13: */
