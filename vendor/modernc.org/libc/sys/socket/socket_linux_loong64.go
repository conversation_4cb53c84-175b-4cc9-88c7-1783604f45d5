// Code generated by 'ccgo sys/socket/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_linux_loong64.go -pkgname socket', DO NOT EDIT.

package socket

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_ALG                           = 38            // socket.h:134:1:
	AF_APPLETALK                     = 5             // socket.h:100:1:
	AF_ASH                           = 18            // socket.h:114:1:
	AF_ATMPVC                        = 8             // socket.h:103:1:
	AF_ATMSVC                        = 20            // socket.h:116:1:
	AF_AX25                          = 3             // socket.h:98:1:
	AF_BLUETOOTH                     = 31            // socket.h:127:1:
	AF_BRIDGE                        = 7             // socket.h:102:1:
	AF_CAIF                          = 37            // socket.h:133:1:
	AF_CAN                           = 29            // socket.h:125:1:
	AF_DECnet                        = 12            // socket.h:107:1:
	AF_ECONET                        = 19            // socket.h:115:1:
	AF_FILE                          = 1             // socket.h:96:1:
	AF_IB                            = 27            // socket.h:123:1:
	AF_IEEE802154                    = 36            // socket.h:132:1:
	AF_INET                          = 2             // socket.h:97:1:
	AF_INET6                         = 10            // socket.h:105:1:
	AF_IPX                           = 4             // socket.h:99:1:
	AF_IRDA                          = 23            // socket.h:119:1:
	AF_ISDN                          = 34            // socket.h:130:1:
	AF_IUCV                          = 32            // socket.h:128:1:
	AF_KCM                           = 41            // socket.h:137:1:
	AF_KEY                           = 15            // socket.h:110:1:
	AF_LLC                           = 26            // socket.h:122:1:
	AF_LOCAL                         = 1             // socket.h:94:1:
	AF_MAX                           = 46            // socket.h:142:1:
	AF_MCTP                          = 45            // socket.h:141:1:
	AF_MPLS                          = 28            // socket.h:124:1:
	AF_NETBEUI                       = 13            // socket.h:108:1:
	AF_NETLINK                       = 16            // socket.h:111:1:
	AF_NETROM                        = 6             // socket.h:101:1:
	AF_NFC                           = 39            // socket.h:135:1:
	AF_PACKET                        = 17            // socket.h:113:1:
	AF_PHONET                        = 35            // socket.h:131:1:
	AF_PPPOX                         = 24            // socket.h:120:1:
	AF_QIPCRTR                       = 42            // socket.h:138:1:
	AF_RDS                           = 21            // socket.h:117:1:
	AF_ROSE                          = 11            // socket.h:106:1:
	AF_ROUTE                         = 16            // socket.h:112:1:
	AF_RXRPC                         = 33            // socket.h:129:1:
	AF_SECURITY                      = 14            // socket.h:109:1:
	AF_SMC                           = 43            // socket.h:139:1:
	AF_SNA                           = 22            // socket.h:118:1:
	AF_TIPC                          = 30            // socket.h:126:1:
	AF_UNIX                          = 1             // socket.h:95:1:
	AF_UNSPEC                        = 0             // socket.h:93:1:
	AF_VSOCK                         = 40            // socket.h:136:1:
	AF_WANPIPE                       = 25            // socket.h:121:1:
	AF_X25                           = 9             // socket.h:104:1:
	AF_XDP                           = 44            // socket.h:140:1:
	BIG_ENDIAN                       = 4321          // endian.h:28:1:
	BYTE_ORDER                       = 1234          // endian.h:30:1:
	FD_SETSIZE                       = 1024          // select.h:73:1:
	FIOGETOWN                        = 0x8903        // sockios.h:8:1:
	FIOSETOWN                        = 0x8901        // sockios.h:6:1:
	LITTLE_ENDIAN                    = 1234          // endian.h:27:1:
	PDP_ENDIAN                       = 3412          // endian.h:29:1:
	PF_ALG                           = 38            // socket.h:82:1:
	PF_APPLETALK                     = 5             // socket.h:48:1:
	PF_ASH                           = 18            // socket.h:62:1:
	PF_ATMPVC                        = 8             // socket.h:51:1:
	PF_ATMSVC                        = 20            // socket.h:64:1:
	PF_AX25                          = 3             // socket.h:46:1:
	PF_BLUETOOTH                     = 31            // socket.h:75:1:
	PF_BRIDGE                        = 7             // socket.h:50:1:
	PF_CAIF                          = 37            // socket.h:81:1:
	PF_CAN                           = 29            // socket.h:73:1:
	PF_DECnet                        = 12            // socket.h:55:1:
	PF_ECONET                        = 19            // socket.h:63:1:
	PF_FILE                          = 1             // socket.h:44:1:
	PF_IB                            = 27            // socket.h:71:1:
	PF_IEEE802154                    = 36            // socket.h:80:1:
	PF_INET                          = 2             // socket.h:45:1:
	PF_INET6                         = 10            // socket.h:53:1:
	PF_IPX                           = 4             // socket.h:47:1:
	PF_IRDA                          = 23            // socket.h:67:1:
	PF_ISDN                          = 34            // socket.h:78:1:
	PF_IUCV                          = 32            // socket.h:76:1:
	PF_KCM                           = 41            // socket.h:85:1:
	PF_KEY                           = 15            // socket.h:58:1:
	PF_LLC                           = 26            // socket.h:70:1:
	PF_LOCAL                         = 1             // socket.h:42:1:
	PF_MAX                           = 46            // socket.h:90:1:
	PF_MCTP                          = 45            // socket.h:89:1:
	PF_MPLS                          = 28            // socket.h:72:1:
	PF_NETBEUI                       = 13            // socket.h:56:1:
	PF_NETLINK                       = 16            // socket.h:59:1:
	PF_NETROM                        = 6             // socket.h:49:1:
	PF_NFC                           = 39            // socket.h:83:1:
	PF_PACKET                        = 17            // socket.h:61:1:
	PF_PHONET                        = 35            // socket.h:79:1:
	PF_PPPOX                         = 24            // socket.h:68:1:
	PF_QIPCRTR                       = 42            // socket.h:86:1:
	PF_RDS                           = 21            // socket.h:65:1:
	PF_ROSE                          = 11            // socket.h:54:1:
	PF_ROUTE                         = 16            // socket.h:60:1:
	PF_RXRPC                         = 33            // socket.h:77:1:
	PF_SECURITY                      = 14            // socket.h:57:1:
	PF_SMC                           = 43            // socket.h:87:1:
	PF_SNA                           = 22            // socket.h:66:1:
	PF_TIPC                          = 30            // socket.h:74:1:
	PF_UNIX                          = 1             // socket.h:43:1:
	PF_UNSPEC                        = 0             // socket.h:41:1:
	PF_VSOCK                         = 40            // socket.h:84:1:
	PF_WANPIPE                       = 25            // socket.h:69:1:
	PF_X25                           = 9             // socket.h:52:1:
	PF_XDP                           = 44            // socket.h:88:1:
	SCM_TIMESTAMP                    = 29            // socket.h:156:1:
	SCM_TIMESTAMPING                 = 37            // socket.h:158:1:
	SCM_TIMESTAMPING_OPT_STATS       = 54            // socket.h:90:1:
	SCM_TIMESTAMPING_PKTINFO         = 58            // socket.h:98:1:
	SCM_TIMESTAMPNS                  = 35            // socket.h:157:1:
	SCM_TXTIME                       = 61            // socket.h:105:1:
	SCM_WIFI_STATUS                  = 41            // socket.h:64:1:
	SIOCATMARK                       = 0x8905        // sockios.h:10:1:
	SIOCGPGRP                        = 0x8904        // sockios.h:9:1:
	SIOCGSTAMPNS_OLD                 = 0x8907        // sockios.h:12:1:
	SIOCGSTAMP_OLD                   = 0x8906        // sockios.h:11:1:
	SIOCSPGRP                        = 0x8902        // sockios.h:7:1:
	SOL_AAL                          = 265           // socket.h:153:1:
	SOL_ALG                          = 279           // socket.h:167:1:
	SOL_ATM                          = 264           // socket.h:152:1:
	SOL_BLUETOOTH                    = 274           // socket.h:162:1:
	SOL_CAIF                         = 278           // socket.h:166:1:
	SOL_DCCP                         = 269           // socket.h:157:1:
	SOL_DECNET                       = 261           // socket.h:149:1:
	SOL_IRDA                         = 266           // socket.h:154:1:
	SOL_IUCV                         = 277           // socket.h:165:1:
	SOL_KCM                          = 281           // socket.h:169:1:
	SOL_LLC                          = 268           // socket.h:156:1:
	SOL_MCTP                         = 285           // socket.h:173:1:
	SOL_MPTCP                        = 284           // socket.h:172:1:
	SOL_NETBEUI                      = 267           // socket.h:155:1:
	SOL_NETLINK                      = 270           // socket.h:158:1:
	SOL_NFC                          = 280           // socket.h:168:1:
	SOL_PACKET                       = 263           // socket.h:151:1:
	SOL_PNPIPE                       = 275           // socket.h:163:1:
	SOL_PPPOL2TP                     = 273           // socket.h:161:1:
	SOL_RAW                          = 255           // socket.h:148:1:
	SOL_RDS                          = 276           // socket.h:164:1:
	SOL_RXRPC                        = 272           // socket.h:160:1:
	SOL_SMC                          = 286           // socket.h:174:1:
	SOL_SOCKET                       = 1             // socket.h:9:1:
	SOL_TIPC                         = 271           // socket.h:159:1:
	SOL_TLS                          = 282           // socket.h:170:1:
	SOL_X25                          = 262           // socket.h:150:1:
	SOL_XDP                          = 283           // socket.h:171:1:
	SOMAXCONN                        = 4096          // socket.h:177:1:
	SO_ACCEPTCONN                    = 30            // socket.h:51:1:
	SO_ATTACH_BPF                    = 50            // socket.h:82:1:
	SO_ATTACH_FILTER                 = 26            // socket.h:45:1:
	SO_ATTACH_REUSEPORT_CBPF         = 51            // socket.h:85:1:
	SO_ATTACH_REUSEPORT_EBPF         = 52            // socket.h:86:1:
	SO_BINDTODEVICE                  = 25            // socket.h:42:1:
	SO_BINDTOIFINDEX                 = 62            // socket.h:107:1:
	SO_BPF_EXTENSIONS                = 48            // socket.h:78:1:
	SO_BROADCAST                     = 6             // socket.h:16:1:
	SO_BSDCOMPAT                     = 14            // socket.h:26:1:
	SO_BUF_LOCK                      = 72            // socket.h:127:1:
	SO_BUSY_POLL                     = 46            // socket.h:74:1:
	SO_BUSY_POLL_BUDGET              = 70            // socket.h:123:1:
	SO_CNX_ADVICE                    = 53            // socket.h:88:1:
	SO_COOKIE                        = 57            // socket.h:96:1:
	SO_DEBUG                         = 1             // socket.h:11:1:
	SO_DETACH_BPF                    = 27            // socket.h:83:1:
	SO_DETACH_FILTER                 = 27            // socket.h:46:1:
	SO_DETACH_REUSEPORT_BPF          = 68            // socket.h:120:1:
	SO_DOMAIN                        = 39            // socket.h:59:1:
	SO_DONTROUTE                     = 5             // socket.h:15:1:
	SO_ERROR                         = 4             // socket.h:14:1:
	SO_GET_FILTER                    = 26            // socket.h:47:1:
	SO_INCOMING_CPU                  = 49            // socket.h:80:1:
	SO_INCOMING_NAPI_ID              = 56            // socket.h:94:1:
	SO_KEEPALIVE                     = 9             // socket.h:21:1:
	SO_LINGER                        = 13            // socket.h:25:1:
	SO_LOCK_FILTER                   = 44            // socket.h:70:1:
	SO_MARK                          = 36            // socket.h:56:1:
	SO_MAX_PACING_RATE               = 47            // socket.h:76:1:
	SO_MEMINFO                       = 55            // socket.h:92:1:
	SO_NETNS_COOKIE                  = 71            // socket.h:125:1:
	SO_NOFCS                         = 43            // socket.h:68:1:
	SO_NO_CHECK                      = 11            // socket.h:23:1:
	SO_OOBINLINE                     = 10            // socket.h:22:1:
	SO_PASSCRED                      = 16            // socket.h:29:1:
	SO_PASSPIDFD                     = 76            // socket.h:135:1:
	SO_PASSSEC                       = 34            // socket.h:54:1:
	SO_PEEK_OFF                      = 42            // socket.h:65:1:
	SO_PEERCRED                      = 17            // socket.h:30:1:
	SO_PEERGROUPS                    = 59            // socket.h:100:1:
	SO_PEERNAME                      = 28            // socket.h:49:1:
	SO_PEERPIDFD                     = 77            // socket.h:136:1:
	SO_PEERSEC                       = 31            // socket.h:53:1:
	SO_PREFER_BUSY_POLL              = 69            // socket.h:122:1:
	SO_PRIORITY                      = 12            // socket.h:24:1:
	SO_PROTOCOL                      = 38            // socket.h:58:1:
	SO_RCVBUF                        = 8             // socket.h:18:1:
	SO_RCVBUFFORCE                   = 33            // socket.h:20:1:
	SO_RCVLOWAT                      = 18            // socket.h:31:1:
	SO_RCVMARK                       = 75            // socket.h:133:1:
	SO_RCVTIMEO                      = 20            // socket.h:145:1:
	SO_RCVTIMEO_NEW                  = 66            // socket.h:117:1:
	SO_RCVTIMEO_OLD                  = 20            // socket.h:33:1:
	SO_RESERVE_MEM                   = 73            // socket.h:129:1:
	SO_REUSEADDR                     = 2             // socket.h:12:1:
	SO_REUSEPORT                     = 15            // socket.h:27:1:
	SO_RXQ_OVFL                      = 40            // socket.h:61:1:
	SO_SECURITY_AUTHENTICATION       = 22            // socket.h:38:1:
	SO_SECURITY_ENCRYPTION_NETWORK   = 24            // socket.h:40:1:
	SO_SECURITY_ENCRYPTION_TRANSPORT = 23            // socket.h:39:1:
	SO_SELECT_ERR_QUEUE              = 45            // socket.h:72:1:
	SO_SNDBUF                        = 7             // socket.h:17:1:
	SO_SNDBUFFORCE                   = 32            // socket.h:19:1:
	SO_SNDLOWAT                      = 19            // socket.h:32:1:
	SO_SNDTIMEO                      = 21            // socket.h:146:1:
	SO_SNDTIMEO_NEW                  = 67            // socket.h:118:1:
	SO_SNDTIMEO_OLD                  = 21            // socket.h:34:1:
	SO_TIMESTAMP                     = 29            // socket.h:141:1:
	SO_TIMESTAMPING                  = 37            // socket.h:143:1:
	SO_TIMESTAMPING_NEW              = 65            // socket.h:115:1:
	SO_TIMESTAMPING_OLD              = 37            // socket.h:111:1:
	SO_TIMESTAMPNS                   = 35            // socket.h:142:1:
	SO_TIMESTAMPNS_NEW               = 64            // socket.h:114:1:
	SO_TIMESTAMPNS_OLD               = 35            // socket.h:110:1:
	SO_TIMESTAMP_NEW                 = 63            // socket.h:113:1:
	SO_TIMESTAMP_OLD                 = 29            // socket.h:109:1:
	SO_TXREHASH                      = 74            // socket.h:131:1:
	SO_TXTIME                        = 61            // socket.h:104:1:
	SO_TYPE                          = 3             // socket.h:13:1:
	SO_WIFI_STATUS                   = 41            // socket.h:63:1:
	SO_ZEROCOPY                      = 60            // socket.h:102:1:
	X_ABILP64                        = 3             // <predefined>:377:1:
	X_ATFILE_SOURCE                  = 1             // features.h:353:1:
	X_BITS_ATOMIC_WIDE_COUNTER_H     = 0             // atomic_wide_counter.h:20:1:
	X_BITS_BYTESWAP_H                = 1             // byteswap.h:24:1:
	X_BITS_ENDIANNESS_H              = 1             // endianness.h:2:1:
	X_BITS_ENDIAN_H                  = 1             // endian.h:20:1:
	X_BITS_PTHREADTYPES_ARCH_H       = 1             // pthreadtypes-arch.h:21:1:
	X_BITS_PTHREADTYPES_COMMON_H     = 1             // pthreadtypes.h:20:1:
	X_BITS_SOCKADDR_H                = 1             // sockaddr.h:24:1:
	X_BITS_STDINT_INTN_H             = 1             // stdint-intn.h:20:1:
	X_BITS_TIME64_H                  = 1             // time64.h:24:1:
	X_BITS_TYPESIZES_H               = 1             // typesizes.h:24:1:
	X_BITS_TYPES_H                   = 1             // types.h:24:1:
	X_BITS_UINTN_IDENTITY_H          = 1             // uintn-identity.h:24:1:
	X_BSD_SIZE_T_                    = 0             // stddef.h:193:1:
	X_BSD_SIZE_T_DEFINED_            = 0             // stddef.h:196:1:
	X_DEFAULT_SOURCE                 = 1             // features.h:238:1:
	X_ENDIAN_H                       = 1             // endian.h:19:1:
	X_FEATURES_H                     = 1             // features.h:19:1:
	X_FILE_OFFSET_BITS               = 64            // <builtin>:25:1:
	X_GCC_SIZE_T                     = 0             // stddef.h:200:1:
	X_LINUX_POSIX_TYPES_H            = 0             // posix_types.h:3:1:
	X_LINUX_STDDEF_H                 = 0             // stddef.h:3:1:
	X_LOONGARCH_ARCH                 = "loongarch64" // <predefined>:214:1:
	X_LOONGARCH_ARCH_LOONGARCH64     = 1             // <predefined>:340:1:
	X_LOONGARCH_FPSET                = 32            // <predefined>:265:1:
	X_LOONGARCH_SIM                  = 3             // <predefined>:233:1:
	X_LOONGARCH_SPFPSET              = 32            // <predefined>:88:1:
	X_LOONGARCH_SZINT                = 32            // <predefined>:230:1:
	X_LOONGARCH_SZLONG               = 64            // <predefined>:388:1:
	X_LOONGARCH_SZPTR                = 64            // <predefined>:200:1:
	X_LOONGARCH_TUNE                 = "la464"       // <predefined>:245:1:
	X_LOONGARCH_TUNE_LA464           = 1             // <predefined>:63:1:
	X_LP64                           = 1             // <predefined>:372:1:
	X_POSIX_C_SOURCE                 = 200809        // features.h:292:1:
	X_POSIX_SOURCE                   = 1             // features.h:290:1:
	X_SIZET_                         = 0             // stddef.h:201:1:
	X_SIZE_T                         = 0             // stddef.h:187:1:
	X_SIZE_T_                        = 0             // stddef.h:192:1:
	X_SIZE_T_DECLARED                = 0             // stddef.h:197:1:
	X_SIZE_T_DEFINED                 = 0             // stddef.h:195:1:
	X_SIZE_T_DEFINED_                = 0             // stddef.h:194:1:
	X_SS_SIZE                        = 128           // sockaddr.h:40:1:
	X_STDC_PREDEF_H                  = 1             // <predefined>:223:1:
	X_STRUCT_TIMESPEC                = 1             // struct_timespec.h:3:1:
	X_SYS_CDEFS_H                    = 1             // cdefs.h:20:1:
	X_SYS_SELECT_H                   = 1             // select.h:22:1:
	X_SYS_SIZE_T_H                   = 0             // stddef.h:188:1:
	X_SYS_SOCKET_H                   = 1             // socket.h:20:1:
	X_SYS_TYPES_H                    = 1             // types.h:23:1:
	X_THREAD_MUTEX_INTERNAL_H        = 1             // struct_mutex.h:20:1:
	X_THREAD_SHARED_TYPES_H          = 1             // thread-shared-types.h:20:1:
	X_T_SIZE                         = 0             // stddef.h:190:1:
	X_T_SIZE_                        = 0             // stddef.h:189:1:
	Linux                            = 1             // <predefined>:308:1:
	Unix                             = 1             // <predefined>:247:1:
)

// Bits in the FLAGS argument to `send', `recv', et al.
const ( /* socket.h:205:1: */
	MSG_OOB        = 1  // Process out-of-band data.
	MSG_PEEK       = 2  // Peek at incoming messages.
	MSG_DONTROUTE  = 4  // Don't use local routing.
	MSG_CTRUNC     = 8  // Control data lost before delivery.
	MSG_PROXY      = 16 // Supply or ask second address.
	MSG_TRUNC      = 32
	MSG_DONTWAIT   = 64  // Nonblocking IO.
	MSG_EOR        = 128 // End of record.
	MSG_WAITALL    = 256 // Wait for a full request.
	MSG_FIN        = 512
	MSG_SYN        = 1024
	MSG_CONFIRM    = 2048 // Confirm path validity.
	MSG_RST        = 4096
	MSG_ERRQUEUE   = 8192      // Fetch message from error queue.
	MSG_NOSIGNAL   = 16384     // Do not generate SIGPIPE.
	MSG_MORE       = 32768     // Sender will send more.
	MSG_WAITFORONE = 65536     // Wait for at least one packet to return.
	MSG_BATCH      = 262144    // sendmmsg: more messages coming.
	MSG_ZEROCOPY   = ********  // Use user data in kernel path.
	MSG_FASTOPEN   = ********* // Send data in TCP SYN.

	MSG_CMSG_CLOEXEC = **********
)

// Socket level message types.  This must match the definitions in
//
//	<linux/socket.h>.
const ( /* socket.h:363:1: */
	SCM_RIGHTS = 1
)

// Get the architecture-dependent definition of enum __socket_type.
// Define enum __socket_type for generic Linux.
//    Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Types of sockets.
const ( /* socket_type.h:24:1: */
	SOCK_STREAM = 1 // Sequenced, reliable, connection-based
	// 				   byte streams.
	SOCK_DGRAM = 2 // Connectionless, unreliable datagrams
	// 				   of fixed maximum length.
	SOCK_RAW       = 3 // Raw protocol interface.
	SOCK_RDM       = 4 // Reliably-delivered messages.
	SOCK_SEQPACKET = 5 // Sequenced, reliable, connection-based,
	// 				   datagrams of fixed maximum length.
	SOCK_DCCP   = 6  // Datagram Congestion Control Protocol.
	SOCK_PACKET = 10 // Linux specific way of getting packets
	// 				   at the dev level.  For writing rarp and
	// 				   other similar things on the user level.

	// Flags to be ORed into the type parameter of socket and socketpair and
	//      used for the flags parameter of paccept.

	SOCK_CLOEXEC = 524288 // Atomically set close-on-exec flag for the
	// 				   new descriptor(s).
	SOCK_NONBLOCK = 2048
)

// The following constants should be used for the second parameter of
//
//	`shutdown'.
const ( /* socket.h:41:1: */
	SHUT_RD   = 0 // No more receptions.
	SHUT_WR   = 1 // No more transmissions.
	SHUT_RDWR = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Structure for scatter/gather I/O.
type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  uint64
} /* struct_iovec.h:26:1 */

// Copyright (C) 1989-2023 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// This operating system-specific header file defines the SOCK_*, PF_*,
//    AF_*, MSG_*, SOL_*, and SO_* constants, and the `struct sockaddr',
//    `struct msghdr', and `struct linger' types.
// System-specific socket constants and types.  Linux version.
//    Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1989-2023 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	POSIX Standard: 2.6 Primitive System Data Types	<sys/types.h>

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Bit size of the time_t type at glibc build time, general case.
//    Copyright (C) 2018-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Size in bits of the 'time_t' type of the default ABI.

// Convenience types.
type X__u_char = uint8   /* types.h:31:23 */
type X__u_short = uint16 /* types.h:32:28 */
type X__u_int = uint32   /* types.h:33:22 */
type X__u_long = uint64  /* types.h:34:27 */

// Fixed-size types, underlying types depend on word size and compiler.
type X__int8_t = int8     /* types.h:37:21 */
type X__uint8_t = uint8   /* types.h:38:23 */
type X__int16_t = int16   /* types.h:39:26 */
type X__uint16_t = uint16 /* types.h:40:28 */
type X__int32_t = int32   /* types.h:41:20 */
type X__uint32_t = uint32 /* types.h:42:22 */
type X__int64_t = int64   /* types.h:44:25 */
type X__uint64_t = uint64 /* types.h:45:27 */

// Smallest types with at least a given width.
type X__int_least8_t = int8     /* types.h:52:18 */
type X__uint_least8_t = uint8   /* types.h:53:19 */
type X__int_least16_t = int16   /* types.h:54:19 */
type X__uint_least16_t = uint16 /* types.h:55:20 */
type X__int_least32_t = int32   /* types.h:56:19 */
type X__uint_least32_t = uint32 /* types.h:57:20 */
type X__int_least64_t = int64   /* types.h:58:19 */
type X__uint_least64_t = uint64 /* types.h:59:20 */

// quad_t is also 64 bits.
type X__quad_t = int64    /* types.h:63:18 */
type X__u_quad_t = uint64 /* types.h:64:27 */

// Largest integral types.
type X__intmax_t = int64   /* types.h:72:18 */
type X__uintmax_t = uint64 /* types.h:73:27 */

// The machine-dependent file <bits/typesizes.h> defines __*_T_TYPE
//    macros for each of the OS types we define below.  The definitions
//    of those macros must use the following macros for underlying types.
//    We define __S<SIZE>_TYPE and __U<SIZE>_TYPE for the signed and unsigned
//    variants of each of the following integer types on this machine.
//
// 	16		-- "natural" 16-bit type (always short)
// 	32		-- "natural" 32-bit type (always int)
// 	64		-- "natural" 64-bit type (long or long long)
// 	LONG32		-- 32-bit type, traditionally long
// 	QUAD		-- 64-bit type, traditionally long long
// 	WORD		-- natural type of __WORDSIZE bits (int or long)
// 	LONGWORD	-- type of __WORDSIZE bits, traditionally long
//
//    We distinguish WORD/LONGWORD, 32/LONG32, and 64/QUAD so that the
//    conventional uses of `long' or `long long' type modifiers match the
//    types we define, even when a less-adorned type would be the same size.
//    This matters for (somewhat) portably writing printf/scanf formats for
//    these types, where using the appropriate l or ll format modifiers can
//    make the typedefs and the formats match up across all GNU platforms.  If
//    we used `long' when it's 64 bits where `long long' is expected, then the
//    compiler would warn about the formats not matching the argument types,
//    and the programmer changing them to shut up the compiler would break the
//    program's portability.
//
//    Here we assume what is presently the case in all the GCC configurations
//    we support: long long is always 64 bits, long is always word/address size,
//    and int is always 32 bits.

// No need to mark the typedef with __extension__.
// bits/typesizes.h -- underlying types for *_t.  For the generic Linux ABI.
//    Copyright (C) 2011-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// See <bits/types.h> for the meaning of these macros.  This file exists so
//    that <bits/types.h> need not vary across different GNU platforms.

// Tell the libc code that off_t and off64_t are actually the same type
//    for all ABI purposes, even if possibly expressed as different base types
//    for C type-checking purposes.

// Same for ino_t and ino64_t.

// And for __rlim_t and __rlim64_t.

// And for fsblkcnt_t, fsblkcnt64_t, fsfilcnt_t and fsfilcnt64_t.

// And for getitimer, setitimer and rusage

// Number of descriptors that can fit in an `fd_set'.

// bits/time64.h -- underlying types for __time64_t.  Generic version.
//    Copyright (C) 2018-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define __TIME64_T_TYPE so that it is always a 64-bit type.

// If we already have 64-bit time type then use it.

type X__dev_t = uint64                     /* types.h:145:25 */ // Type of device numbers.
type X__uid_t = uint32                     /* types.h:146:25 */ // Type of user identifications.
type X__gid_t = uint32                     /* types.h:147:25 */ // Type of group identifications.
type X__ino_t = uint64                     /* types.h:148:25 */ // Type of file serial numbers.
type X__ino64_t = uint64                   /* types.h:149:27 */ // Type of file serial numbers (LFS).
type X__mode_t = uint32                    /* types.h:150:26 */ // Type of file attribute bitmasks.
type X__nlink_t = uint32                   /* types.h:151:27 */ // Type of file link counts.
type X__off_t = int64                      /* types.h:152:25 */ // Type of file sizes and offsets.
type X__off64_t = int64                    /* types.h:153:27 */ // Type of file sizes and offsets (LFS).
type X__pid_t = int32                      /* types.h:154:25 */ // Type of process identifications.
type X__fsid_t = struct{ F__val [2]int32 } /* types.h:155:26 */ // Type of file system IDs.
type X__clock_t = int64                    /* types.h:156:27 */ // Type of CPU usage counts.
type X__rlim_t = uint64                    /* types.h:157:26 */ // Type for resource measurement.
type X__rlim64_t = uint64                  /* types.h:158:28 */ // Type for resource measurement (LFS).
type X__id_t = uint32                      /* types.h:159:24 */ // General type for IDs.
type X__time_t = int64                     /* types.h:160:26 */ // Seconds since the Epoch.
type X__useconds_t = uint32                /* types.h:161:30 */ // Count of microseconds.
type X__suseconds_t = int64                /* types.h:162:31 */ // Signed count of microseconds.
type X__suseconds64_t = int64              /* types.h:163:33 */

type X__daddr_t = int32 /* types.h:165:27 */ // The type of a disk address.
type X__key_t = int32   /* types.h:166:25 */ // Type of an IPC key.

// Clock ID used in clock and timer functions.
type X__clockid_t = int32 /* types.h:169:29 */

// Timer ID returned by `timer_create'.
type X__timer_t = uintptr /* types.h:172:12 */

// Type to represent block size.
type X__blksize_t = int32 /* types.h:175:29 */

// Types from the Large File Support interface.

// Type to count number of disk blocks.
type X__blkcnt_t = int64   /* types.h:180:28 */
type X__blkcnt64_t = int64 /* types.h:181:30 */

// Type to count file system blocks.
type X__fsblkcnt_t = uint64   /* types.h:184:30 */
type X__fsblkcnt64_t = uint64 /* types.h:185:32 */

// Type to count file system nodes.
type X__fsfilcnt_t = uint64   /* types.h:188:30 */
type X__fsfilcnt64_t = uint64 /* types.h:189:32 */

// Type of miscellaneous file system fields.
type X__fsword_t = int64 /* types.h:192:28 */

type X__ssize_t = int64 /* types.h:194:27 */ // Type of a byte count, or error.

// Signed long type used in system calls.
type X__syscall_slong_t = int64 /* types.h:197:33 */
// Unsigned long type used in system calls.
type X__syscall_ulong_t = uint64 /* types.h:199:33 */

// These few don't really vary by system, they always correspond
//
//	to one of the other defined types.
type X__loff_t = int64    /* types.h:203:19 */ // Type of file sizes and offsets (LFS).
type X__caddr_t = uintptr /* types.h:204:14 */

// Duplicates info from stdint.h but this is used in unistd.h.
type X__intptr_t = int64 /* types.h:207:25 */

// Duplicate info from sys/socket.h.
type X__socklen_t = uint32 /* types.h:210:23 */

// C99: An integer type that can be accessed as an atomic entity,
//
//	even in the presence of asynchronous interrupts.
//	It is not currently necessary for this to be machine-specific.
type X__sig_atomic_t = int32 /* types.h:215:13 */

// Seconds since the Epoch, visible to user code when time_t is too
//    narrow only for consistency with the old way of widening too-narrow
//    types.  User code should never use __time64_t.

type U_char = uint8     /* types.h:33:18 */
type U_short = uint16   /* types.h:34:19 */
type U_int = uint32     /* types.h:35:17 */
type U_long = uint64    /* types.h:36:18 */
type Quad_t = int64     /* types.h:37:18 */
type U_quad_t = uint64  /* types.h:38:20 */
type Fsid_t = X__fsid_t /* types.h:39:18 */
type Loff_t = int64     /* types.h:42:18 */

type Ino_t = uint64 /* types.h:49:19 */

type Dev_t = uint64 /* types.h:59:17 */

type Gid_t = uint32 /* types.h:64:17 */

type Mode_t = uint32 /* types.h:69:18 */

type Nlink_t = uint32 /* types.h:74:19 */

type Uid_t = uint32 /* types.h:79:17 */

type Off_t = int64 /* types.h:87:19 */

type Pid_t = int32 /* types.h:97:17 */

type Id_t = uint32 /* types.h:103:16 */

type Ssize_t = int64 /* types.h:108:19 */

type Daddr_t = int32   /* types.h:114:19 */
type Caddr_t = uintptr /* types.h:115:19 */

type Key_t = int32 /* types.h:121:17 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Returned by `clock'.
type Clock_t = int64 /* clock_t.h:7:19 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Clock ID used in clock and timer functions.
type Clockid_t = int32 /* clockid_t.h:7:21 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Returned by `time'.
type Time_t = int64 /* time_t.h:10:18 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Timer ID returned by `timer_create'.
type Timer_t = uintptr /* timer_t.h:7:19 */

// Copyright (C) 1989-2023 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Old compatibility names for C types.
type Ulong = uint64  /* types.h:148:27 */
type Ushort = uint16 /* types.h:149:28 */
type Uint = uint32   /* types.h:150:22 */

// These size-specific names are used by some of the inet code.

// Define intN_t types.
//    Copyright (C) 2017-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

type Int8_t = int8   /* stdint-intn.h:24:18 */
type Int16_t = int16 /* stdint-intn.h:25:19 */
type Int32_t = int32 /* stdint-intn.h:26:19 */
type Int64_t = int64 /* stdint-intn.h:27:19 */

// These were defined by ISO C without the first `_'.
type U_int8_t = uint8   /* types.h:158:19 */
type U_int16_t = uint16 /* types.h:159:20 */
type U_int32_t = uint32 /* types.h:160:20 */
type U_int64_t = uint64 /* types.h:161:20 */

type Register_t = int32 /* types.h:164:13 */

// It also defines `fd_set' and the FD_* macros for `select'.
// `fd_set' type and related macros, and `select'/`pselect' declarations.
//    Copyright (C) 1996-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	POSIX 1003.1g: 6.2 Select from File Descriptor Sets <sys/select.h>

// Copyright (C) 1991-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Get definition of needed basic types.
// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Get __FD_* definitions.
// Copyright (C) 1997-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We don't use `memset' because this would require a prototype and
//    the array isn't too big.

// Get sigset_t.

type X__sigset_t = struct{ F__val [16]uint64 } /* __sigset_t.h:8:3 */

// A set of signals to be blocked, unblocked, or waited for.
type Sigset_t = X__sigset_t /* sigset_t.h:7:20 */

// Get definition of timer specification structures.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// A time value that is accurate to the nearest
//
//	microsecond but also has a range of years.
type Timeval = struct {
	Ftv_sec  int64
	Ftv_usec int64
} /* struct_timeval.h:8:1 */

// NB: Include guard matches what <linux/time.h> uses.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Endian macros for string.h functions
//    Copyright (C) 1992-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// POSIX.1b structure for a time value.  This is like a `struct timeval' but
//
//	has nanoseconds instead of microseconds.
type Timespec = struct {
	Ftv_sec  int64
	Ftv_nsec int64
} /* struct_timespec.h:11:1 */

type Suseconds_t = int64 /* select.h:43:23 */

// The fd_set member is required to be an array of longs.
type X__fd_mask = int64 /* select.h:49:18 */

// Some versions of <linux/posix_types.h> define this macros.
// It's easier to assume 8-bit bytes than to get CHAR_BIT.

// fd_set for select and pselect.
type Fd_set = struct{ F__fds_bits [16]int64 } /* select.h:70:5 */

// Maximum number of file descriptors in `fd_set'.

// Sometimes the fd_set member is assumed to have this type.
type Fd_mask = int64 /* select.h:77:19 */

// Define some inlines helping to catch common problems.

type Blksize_t = int32 /* types.h:185:21 */

// Types from the Large File Support interface.
type Blkcnt_t = int64    /* types.h:205:22 */ // Type to count number of disk blocks.
type Fsblkcnt_t = uint64 /* types.h:209:24 */ // Type to count file system blocks.
type Fsfilcnt_t = uint64 /* types.h:213:24 */ // Type to count file system inodes.

// Now add the thread types.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Copyright (C) 1999-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Monotonically increasing wide counters (at least 62 bits).
//    Copyright (C) 2016-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Counter that is monotonically increasing (by less than 2**31 per
//
//	increment), with a single writer, and an arbitrary number of
//	readers.
type X__atomic_wide_counter = struct{ F__value64 uint64 } /* atomic_wide_counter.h:33:3 */

// Common definition of pthread_mutex_t.

type X__pthread_internal_list = struct {
	F__prev uintptr
	F__next uintptr
} /* thread-shared-types.h:51:9 */

// Common definition of pthread_mutex_t.

type X__pthread_list_t = X__pthread_internal_list /* thread-shared-types.h:55:3 */

type X__pthread_internal_slist = struct{ F__next uintptr } /* thread-shared-types.h:57:9 */

type X__pthread_slist_t = X__pthread_internal_slist /* thread-shared-types.h:60:3 */

// Arch-specific mutex definitions.  A generic implementation is provided
//    by sysdeps/nptl/bits/struct_mutex.h.  If required, an architecture
//    can override it by defining:
//
//    1. struct __pthread_mutex_s (used on both pthread_mutex_t and mtx_t
//       definition).  It should contains at least the internal members
//       defined in the generic version.
//
//    2. __LOCK_ALIGNMENT for any extra attribute for internal lock used with
//       atomic operations.
//
//    3. The macro __PTHREAD_MUTEX_INITIALIZER used for static initialization.
//       It should initialize the mutex internal flag.

// Default mutex implementation struct definitions.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Generic struct for both POSIX and C11 mutexes.  New ports are expected
//    to use the default layout, however architecture can redefine it to
//    add arch-specific extension (such as lock-elision).  The struct have
//    a size of 32 bytes on LP32 and 40 bytes on LP64 architectures.

type X__pthread_mutex_s = struct {
	F__lock   int32
	F__count  uint32
	F__owner  int32
	F__nusers uint32
	F__kind   int32
	F__spins  int32
	F__list   X__pthread_list_t
} /* struct_mutex.h:27:1 */

// Arch-sepecific read-write lock definitions.  A generic implementation is
//    provided by struct_rwlock.h.  If required, an architecture can override it
//    by defining:
//
//    1. struct __pthread_rwlock_arch_t (used on pthread_rwlock_t definition).
//       It should contain at least the internal members defined in the
//       generic version.
//
//    2. The macro __PTHREAD_RWLOCK_INITIALIZER used for static initialization.
//       It should initialize the rwlock internal type.

// Default read-write lock implementation struct definitions.
//    Copyright (C) 2019-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Endian macros for string.h functions
//    Copyright (C) 1992-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Generic struct for both POSIX read-write lock.  New ports are expected
//    to use the default layout, however archictetures can redefine it to add
//    arch-specific extensions (such as lock-elision).  The struct have a size
//    of 32 bytes on both LP32 and LP64 architectures.

type X__pthread_rwlock_arch_t = struct {
	F__readers       uint32
	F__writers       uint32
	F__wrphase_futex uint32
	F__writers_futex uint32
	F__pad3          uint32
	F__pad4          uint32
	F__flags         uint8
	F__shared        uint8
	F__pad1          uint8
	F__pad2          uint8
	F__cur_writer    int32
} /* struct_rwlock.h:29:1 */

// Common definition of pthread_cond_t.

type X__pthread_cond_s = struct {
	F__wseq         X__atomic_wide_counter
	F__g1_start     X__atomic_wide_counter
	F__g_refs       [2]uint32
	F__g_size       [2]uint32
	F__g1_orig_size uint32
	F__wrefs        uint32
	F__g_signals    [2]uint32
} /* thread-shared-types.h:94:1 */

type X__tss_t = uint32  /* thread-shared-types.h:105:22 */
type X__thrd_t = uint64 /* thread-shared-types.h:106:27 */

type X__once_flag = struct{ F__data int32 } /* thread-shared-types.h:111:3 */

// Thread identifiers.  The structure of the attribute type is not
//
//	exposed on purpose.
type Pthread_t = uint64 /* pthreadtypes.h:27:27 */

// Data structures for mutex handling.  The structure of the attribute
//
//	type is not exposed on purpose.
type Pthread_mutexattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:36:3 */

// Data structure for condition variable handling.  The structure of
//
//	the attribute type is not exposed on purpose.
type Pthread_condattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:45:3 */

// Keys for thread-specific data
type Pthread_key_t = uint32 /* pthreadtypes.h:49:22 */

// Once-only execution
type Pthread_once_t = int32 /* pthreadtypes.h:53:30 */

type Pthread_attr_t1 = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [56]int8
} /* pthreadtypes.h:56:1 */

type Pthread_attr_t = Pthread_attr_t1 /* pthreadtypes.h:62:30 */

type Pthread_mutex_t = struct{ F__data X__pthread_mutex_s } /* pthreadtypes.h:72:3 */

type Pthread_cond_t = struct{ F__data X__pthread_cond_s } /* pthreadtypes.h:80:3 */

// Data structure for reader-writer lock variable handling.  The
//
//	structure of the attribute type is deliberately not exposed.
type Pthread_rwlock_t = struct {
	F__ccgo_pad1 [0]uint64
	F__data      X__pthread_rwlock_arch_t
	F__ccgo_pad2 [24]byte
} /* pthreadtypes.h:91:3 */

type Pthread_rwlockattr_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [8]int8
} /* pthreadtypes.h:97:3 */

// POSIX spinlock data type.
type Pthread_spinlock_t = int32 /* pthreadtypes.h:103:22 */

// POSIX barriers data type.  The structure of the type is
//
//	deliberately not exposed.
type Pthread_barrier_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [32]int8
} /* pthreadtypes.h:112:3 */

type Pthread_barrierattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]int8
} /* pthreadtypes.h:118:3 */

// Type for length arguments in socket calls.
type Socklen_t = uint32 /* socket.h:33:21 */

// Protocol families.

// Address families.

// Socket level values.  Others are defined in the appropriate headers.
//
//    XXX These definitions also should go into the appropriate headers as
//    far as they are available.

// Maximum queue length specifiable by listen.

// Get the definition of the macro to define the common sockaddr members.
// Definition of struct sockaddr_* common members and sizes, generic version.
//    Copyright (C) 1995-2023 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/socket.h> instead.

// POSIX.1g specifies this type name for the `sa_family' member.
type Sa_family_t = uint16 /* sockaddr.h:28:28 */

// This macro is used to declare the initial common members
//    of the data types used for socket addresses, `struct sockaddr',
//    `struct sockaddr_in', `struct sockaddr_un', etc.

// Size of struct sockaddr_storage.

// Structure describing a generic socket address.
type Sockaddr = struct {
	Fsa_family uint16
	Fsa_data   [14]int8
} /* socket.h:183:1 */

// Structure large enough to hold any socket address (with the historical
//    exception of AF_UNIX).

type Sockaddr_storage = struct {
	Fss_family    uint16
	F__ss_padding [118]int8
	F__ss_align   uint64
} /* socket.h:196:1 */

// Structure describing messages sent by
//
//	`sendmsg' and received by `recvmsg'.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    uint32
	F__ccgo_pad1    [4]byte
	Fmsg_iov        uintptr
	Fmsg_iovlen     uint64
	Fmsg_control    uintptr
	Fmsg_controllen uint64
	Fmsg_flags      int32
	F__ccgo_pad2    [4]byte
} /* socket.h:262:1 */

// Structure used for storage of ancillary data object information.
type Cmsghdr = struct {
	F__ccgo_pad1 [0]uint64
	Fcmsg_len    uint64
	Fcmsg_level  int32
	Fcmsg_type   int32
} /* socket.h:280:1 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// *
// __struct_group() - Create a mirrored named and anonyomous struct
//
// @TAG: The tag name for the named sub-struct (usually empty)
// @NAME: The identifier name of the mirrored sub-struct
// @ATTRS: Any struct attributes (usually empty)
// @MEMBERS: The member declarations for the mirrored structs
//
// Used to create an anonymous union of two structs with identical layout
// and size: one anonymous and one named. The former's members can be used
// normally without sub-struct naming, and the latter can be used to
// reason about the start, end, and size of the group of struct members.
// The named struct can also be explicitly tagged for layer reuse, as well
// as both having struct attributes appended.

// *
// __DECLARE_FLEX_ARRAY() - Declare a flexible array usable in a union
//
// @TYPE: The type of each flexible array element
// @NAME: The name of the flexible array member
//
// In order to have a flexible array member in a union or alone in a
// struct, it needs to be wrapped in an anonymous struct with at least 1
// named member, but that member can be empty.

// This allows for 1024 file descriptors: if NR_OPEN is ever grown
// beyond that you'll have to change this too. But 1024 fd's seem to be
// enough even for such "real" unices like OSF/1, so hopefully this is
// one limit that doesn't have to be changed [again].
//
// Note that POSIX wants the FD_CLEAR(fd,fdsetp) defines to be in
// <sys/time.h> (and thus <linux/time.h>) - but this is a more logical
// place for them. Solved by having dummy defines in <sys/time.h>.

// This macro may have been defined in <gnu/types.h>. But we always
// use the one here.

type X__kernel_fd_set = struct{ Ffds_bits [16]uint64 } /* posix_types.h:27:3 */

// Type of a signal handler.
type X__kernel_sighandler_t = uintptr /* posix_types.h:30:14 */

// Type of a SYSV IPC key.
type X__kernel_key_t = int32 /* posix_types.h:33:13 */
type X__kernel_mqd_t = int32 /* posix_types.h:34:13 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// In order to keep safe and avoid regression, only unify uapi
// bitsperlong.h for some archs which are using newer toolchains
// that have the definitions of __CHAR_BIT__ and __SIZEOF_LONG__.
// See the following link for more info:
// https://lore.kernel.org/linux-arch/<EMAIL>/

// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.
//
// First the types that are often defined in different ways across
// architectures, so that you can override them.

type X__kernel_long_t = int64   /* posix_types.h:15:15 */
type X__kernel_ulong_t = uint64 /* posix_types.h:16:23 */

type X__kernel_ino_t = uint64 /* posix_types.h:20:26 */

type X__kernel_mode_t = uint32 /* posix_types.h:24:22 */

type X__kernel_pid_t = int32 /* posix_types.h:28:14 */

type X__kernel_ipc_pid_t = int32 /* posix_types.h:32:14 */

type X__kernel_uid_t = uint32 /* posix_types.h:36:22 */
type X__kernel_gid_t = uint32 /* posix_types.h:37:22 */

type X__kernel_suseconds_t = int64 /* posix_types.h:41:26 */

type X__kernel_daddr_t = int32 /* posix_types.h:45:14 */

type X__kernel_uid32_t = uint32 /* posix_types.h:49:22 */
type X__kernel_gid32_t = uint32 /* posix_types.h:50:22 */

type X__kernel_old_uid_t = uint32 /* posix_types.h:54:24 */
type X__kernel_old_gid_t = uint32 /* posix_types.h:55:24 */

type X__kernel_old_dev_t = uint32 /* posix_types.h:59:22 */

// Most 32 bit architectures use "unsigned int" size_t,
// and all 64 bit architectures use "unsigned long" size_t.
type X__kernel_size_t = uint64   /* posix_types.h:72:26 */
type X__kernel_ssize_t = int64   /* posix_types.h:73:25 */
type X__kernel_ptrdiff_t = int64 /* posix_types.h:74:25 */

type X__kernel_fsid_t = struct{ Fval [2]int32 } /* posix_types.h:81:3 */

// anything below here should be completely generic
type X__kernel_off_t = int64      /* posix_types.h:87:25 */
type X__kernel_loff_t = int64     /* posix_types.h:88:19 */
type X__kernel_old_time_t = int64 /* posix_types.h:89:25 */
type X__kernel_time_t = int64     /* posix_types.h:90:25 */
type X__kernel_time64_t = int64   /* posix_types.h:91:19 */
type X__kernel_clock_t = int64    /* posix_types.h:92:25 */
type X__kernel_timer_t = int32    /* posix_types.h:93:14 */
type X__kernel_clockid_t = int32  /* posix_types.h:94:14 */
type X__kernel_caddr_t = uintptr  /* posix_types.h:95:14 */
type X__kernel_uid16_t = uint16   /* posix_types.h:96:24 */
type X__kernel_gid16_t = uint16   /* posix_types.h:97:24 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// Socket-level I/O control calls.

// For setsockopt(2)

// Security levels - as per NRL IPv6 - don't actually do anything

// Socket filtering

// Instruct lower device to use last 4-bytes of skb data as FCS

// on 64-bit and x32, avoid the ?: operator

// Structure used to manipulate the SO_LINGER option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:392:1 */

// This is the 4.3 BSD `struct sockaddr' format, which is used as wire
//
//	format in the grotty old 4.3 `talk' protocol.
type Osockaddr = struct {
	Fsa_family uint16
	Fsa_data   [14]uint8
} /* struct_osockaddr.h:6:1 */

// Define some macros helping to catch buffer overflows.

var _ int8 /* gen.c:2:13: */
