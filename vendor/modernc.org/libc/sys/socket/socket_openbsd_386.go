// Code generated by 'ccgo sys/socket/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/socket/socket_openbsd_386.go -pkgname socket', DO NOT EDIT.

package socket

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_APPLETALK                   = 16     // socket.h:181:1:
	AF_BLUETOOTH                   = 32     // socket.h:199:1:
	AF_CCITT                       = 10     // socket.h:175:1:
	AF_CHAOS                       = 5      // socket.h:169:1:
	AF_CNT                         = 21     // socket.h:186:1:
	AF_COIP                        = 20     // socket.h:185:1:
	AF_DATAKIT                     = 9      // socket.h:174:1:
	AF_DECnet                      = 12     // socket.h:177:1:
	AF_DLI                         = 13     // socket.h:178:1:
	AF_E164                        = 26     // socket.h:192:1:
	AF_ECMA                        = 8      // socket.h:173:1:
	AF_ENCAP                       = 28     // socket.h:194:1:
	AF_HYLINK                      = 15     // socket.h:180:1:
	AF_IMPLINK                     = 3      // socket.h:167:1:
	AF_INET                        = 2      // socket.h:166:1:
	AF_INET6                       = 24     // socket.h:189:1:
	AF_IPX                         = 23     // socket.h:188:1:
	AF_ISDN                        = 26     // socket.h:191:1:
	AF_ISO                         = 7      // socket.h:171:1:
	AF_KEY                         = 30     // socket.h:196:1:
	AF_LAT                         = 14     // socket.h:179:1:
	AF_LINK                        = 18     // socket.h:183:1:
	AF_LOCAL                       = 1      // socket.h:165:1:
	AF_MAX                         = 36     // socket.h:203:1:
	AF_MPLS                        = 33     // socket.h:200:1:
	AF_NATM                        = 27     // socket.h:193:1:
	AF_NS                          = 6      // socket.h:170:1:
	AF_OSI                         = 7      // socket.h:172:1:
	AF_PUP                         = 4      // socket.h:168:1:
	AF_ROUTE                       = 17     // socket.h:182:1:
	AF_SIP                         = 29     // socket.h:195:1:
	AF_SNA                         = 11     // socket.h:176:1:
	AF_UNIX                        = 1      // socket.h:164:1:
	AF_UNSPEC                      = 0      // socket.h:163:1:
	BIG_ENDIAN                     = 4321   // endian.h:45:1:
	BYTE_ORDER                     = 1234   // endian.h:47:1:
	LITTLE_ENDIAN                  = 1234   // endian.h:44:1:
	MSG_BCAST                      = 0x100  // socket.h:508:1:
	MSG_CMSG_CLOEXEC               = 0x800  // socket.h:511:1:
	MSG_CTRUNC                     = 0x20   // socket.h:505:1:
	MSG_DONTROUTE                  = 0x4    // socket.h:502:1:
	MSG_DONTWAIT                   = 0x80   // socket.h:507:1:
	MSG_EOR                        = 0x8    // socket.h:503:1:
	MSG_MCAST                      = 0x200  // socket.h:509:1:
	MSG_NOSIGNAL                   = 0x400  // socket.h:510:1:
	MSG_OOB                        = 0x1    // socket.h:500:1:
	MSG_PEEK                       = 0x2    // socket.h:501:1:
	MSG_TRUNC                      = 0x10   // socket.h:504:1:
	MSG_WAITALL                    = 0x40   // socket.h:506:1:
	MSG_WAITFORONE                 = 0x1000 // socket.h:512:1:
	NET_BPF_BUFSIZE                = 1      // socket.h:452:1:
	NET_BPF_MAXBUFSIZE             = 2      // socket.h:453:1:
	NET_BPF_MAXID                  = 3      // socket.h:454:1:
	NET_KEY_MAXID                  = 3      // socket.h:441:1:
	NET_KEY_SADB_DUMP              = 1      // socket.h:439:1:
	NET_KEY_SPD_DUMP               = 2      // socket.h:440:1:
	NET_LINK_IFRXQ                 = 1      // socket.h:416:1:
	NET_LINK_IFRXQ_MAXID           = 3      // socket.h:428:1:
	NET_LINK_IFRXQ_PRESSURE_DROP   = 2      // socket.h:426:1:
	NET_LINK_IFRXQ_PRESSURE_RETURN = 1      // socket.h:424:1:
	NET_LINK_MAXID                 = 2      // socket.h:417:1:
	NET_MAXID                      = 36     // socket.h:314:1:
	NET_PFLOW_MAXID                = 2      // socket.h:466:1:
	NET_PFLOW_STATS                = 1      // socket.h:465:1:
	NET_RT_DUMP                    = 1      // socket.h:365:1:
	NET_RT_FLAGS                   = 2      // socket.h:366:1:
	NET_RT_IFLIST                  = 3      // socket.h:367:1:
	NET_RT_IFNAMES                 = 6      // socket.h:370:1:
	NET_RT_MAXID                   = 8      // socket.h:372:1:
	NET_RT_SOURCE                  = 7      // socket.h:371:1:
	NET_RT_STATS                   = 4      // socket.h:368:1:
	NET_RT_TABLE                   = 5      // socket.h:369:1:
	NET_UNIX_DEFERRED              = 7      // socket.h:389:1:
	NET_UNIX_INFLIGHT              = 6      // socket.h:388:1:
	NET_UNIX_MAXID                 = 8      // socket.h:390:1:
	NET_UNIX_PROTO_MAXID           = 3      // socket.h:405:1:
	PDP_ENDIAN                     = 3412   // endian.h:46:1:
	PF_APPLETALK                   = 16     // socket.h:267:1:
	PF_BLUETOOTH                   = 32     // socket.h:283:1:
	PF_BPF                         = 31     // socket.h:282:1:
	PF_CCITT                       = 10     // socket.h:261:1:
	PF_CHAOS                       = 5      // socket.h:255:1:
	PF_CNT                         = 21     // socket.h:272:1:
	PF_COIP                        = 20     // socket.h:271:1:
	PF_DATAKIT                     = 9      // socket.h:260:1:
	PF_DECnet                      = 12     // socket.h:263:1:
	PF_DLI                         = 13     // socket.h:264:1:
	PF_ECMA                        = 8      // socket.h:259:1:
	PF_ENCAP                       = 28     // socket.h:279:1:
	PF_HYLINK                      = 15     // socket.h:266:1:
	PF_IMPLINK                     = 3      // socket.h:253:1:
	PF_INET                        = 2      // socket.h:252:1:
	PF_INET6                       = 24     // socket.h:274:1:
	PF_IPX                         = 23     // socket.h:273:1:
	PF_ISDN                        = 26     // socket.h:277:1:
	PF_ISO                         = 7      // socket.h:257:1:
	PF_KEY                         = 30     // socket.h:281:1:
	PF_LAT                         = 14     // socket.h:265:1:
	PF_LINK                        = 18     // socket.h:269:1:
	PF_LOCAL                       = 1      // socket.h:250:1:
	PF_MAX                         = 36     // socket.h:287:1:
	PF_MPLS                        = 33     // socket.h:284:1:
	PF_NATM                        = 27     // socket.h:278:1:
	PF_NS                          = 6      // socket.h:256:1:
	PF_OSI                         = 7      // socket.h:258:1:
	PF_PFLOW                       = 34     // socket.h:285:1:
	PF_PIP                         = 25     // socket.h:276:1:
	PF_PIPEX                       = 35     // socket.h:286:1:
	PF_PUP                         = 4      // socket.h:254:1:
	PF_ROUTE                       = 17     // socket.h:268:1:
	PF_RTIP                        = 22     // socket.h:275:1:
	PF_SIP                         = 29     // socket.h:280:1:
	PF_SNA                         = 11     // socket.h:262:1:
	PF_UNIX                        = 1      // socket.h:251:1:
	PF_UNSPEC                      = 0      // socket.h:249:1:
	PF_XTP                         = 19     // socket.h:270:1:
	RT_TABLEID_BITS                = 8      // socket.h:150:1:
	RT_TABLEID_MASK                = 0xff   // socket.h:151:1:
	RT_TABLEID_MAX                 = 255    // socket.h:149:1:
	SCM_RIGHTS                     = 0x01   // socket.h:560:1:
	SCM_TIMESTAMP                  = 0x04   // socket.h:561:1:
	SHUT_RD                        = 0      // socket.h:292:1:
	SHUT_RDWR                      = 2      // socket.h:294:1:
	SHUT_WR                        = 1      // socket.h:293:1:
	SOCK_CLOEXEC                   = 0x8000 // socket.h:76:1:
	SOCK_DGRAM                     = 2      // socket.h:64:1:
	SOCK_DNS                       = 0x1000 // socket.h:81:1:
	SOCK_NONBLOCK                  = 0x4000 // socket.h:77:1:
	SOCK_RAW                       = 3      // socket.h:65:1:
	SOCK_RDM                       = 4      // socket.h:66:1:
	SOCK_SEQPACKET                 = 5      // socket.h:67:1:
	SOCK_STREAM                    = 1      // socket.h:63:1:
	SOL_SOCKET                     = 0xffff // socket.h:158:1:
	SOMAXCONN                      = 128    // socket.h:477:1:
	SO_ACCEPTCONN                  = 0x0002 // socket.h:88:1:
	SO_BINDANY                     = 0x1000 // socket.h:98:1:
	SO_BROADCAST                   = 0x0020 // socket.h:92:1:
	SO_DEBUG                       = 0x0001 // socket.h:87:1:
	SO_DOMAIN                      = 0x1024 // socket.h:116:1:
	SO_DONTROUTE                   = 0x0010 // socket.h:91:1:
	SO_ERROR                       = 0x1007 // socket.h:110:1:
	SO_KEEPALIVE                   = 0x0008 // socket.h:90:1:
	SO_LINGER                      = 0x0080 // socket.h:94:1:
	SO_NETPROC                     = 0x1020 // socket.h:112:1:
	SO_OOBINLINE                   = 0x0100 // socket.h:95:1:
	SO_PEERCRED                    = 0x1022 // socket.h:114:1:
	SO_PROTOCOL                    = 0x1025 // socket.h:117:1:
	SO_RCVBUF                      = 0x1002 // socket.h:105:1:
	SO_RCVLOWAT                    = 0x1004 // socket.h:107:1:
	SO_RCVTIMEO                    = 0x1006 // socket.h:109:1:
	SO_REUSEADDR                   = 0x0004 // socket.h:89:1:
	SO_REUSEPORT                   = 0x0200 // socket.h:96:1:
	SO_RTABLE                      = 0x1021 // socket.h:113:1:
	SO_SNDBUF                      = 0x1001 // socket.h:104:1:
	SO_SNDLOWAT                    = 0x1003 // socket.h:106:1:
	SO_SNDTIMEO                    = 0x1005 // socket.h:108:1:
	SO_SPLICE                      = 0x1023 // socket.h:115:1:
	SO_TIMESTAMP                   = 0x0800 // socket.h:97:1:
	SO_TYPE                        = 0x1008 // socket.h:111:1:
	SO_USELOOPBACK                 = 0x0040 // socket.h:93:1:
	SO_ZEROIZE                     = 0x2000 // socket.h:99:1:
	UIO_MAXIOV                     = 1024   // uio.h:84:1:
	UNPCTL_RECVSPACE               = 1      // socket.h:403:1:
	UNPCTL_SENDSPACE               = 2      // socket.h:404:1:
	X_BIG_ENDIAN                   = 4321   // _endian.h:43:1:
	X_BYTE_ORDER                   = 1234   // endian.h:58:1:
	X_CLOCKID_T_DEFINED_           = 0      // types.h:162:1:
	X_CLOCK_T_DEFINED_             = 0      // types.h:157:1:
	X_FILE_OFFSET_BITS             = 64     // <builtin>:25:1:
	X_ILP32                        = 1      // <predefined>:1:1:
	X_INT16_T_DEFINED_             = 0      // types.h:84:1:
	X_INT32_T_DEFINED_             = 0      // types.h:94:1:
	X_INT64_T_DEFINED_             = 0      // types.h:104:1:
	X_INT8_T_DEFINED_              = 0      // types.h:74:1:
	X_LITTLE_ENDIAN                = 1234   // _endian.h:42:1:
	X_MACHINE_CDEFS_H_             = 0      // cdefs.h:9:1:
	X_MACHINE_ENDIAN_H_            = 0      // endian.h:28:1:
	X_MACHINE__TYPES_H_            = 0      // _types.h:36:1:
	X_MAX_PAGE_SHIFT               = 12     // _types.h:52:1:
	X_OFF_T_DEFINED_               = 0      // types.h:192:1:
	X_PDP_ENDIAN                   = 3412   // _endian.h:44:1:
	X_PID_T_DEFINED_               = 0      // types.h:167:1:
	X_QUAD_HIGHWORD                = 1      // _endian.h:95:1:
	X_QUAD_LOWWORD                 = 0      // _endian.h:96:1:
	X_SA_FAMILY_T_DEFINED_         = 0      // socket.h:51:1:
	X_SIZE_T_DEFINED_              = 0      // uio.h:42:1:
	X_SOCKLEN_T_DEFINED_           = 0      // socket.h:46:1:
	X_SSIZE_T_DEFINED_             = 0      // uio.h:47:1:
	X_STACKALIGNBYTES              = 15     // _types.h:49:1:
	X_SYS_CDEFS_H_                 = 0      // cdefs.h:39:1:
	X_SYS_ENDIAN_H_                = 0      // endian.h:38:1:
	X_SYS_SOCKET_H_                = 0      // socket.h:36:1:
	X_SYS_TYPES_H_                 = 0      // types.h:41:1:
	X_SYS_UIO_H_                   = 0      // uio.h:36:1:
	X_SYS__ENDIAN_H_               = 0      // _endian.h:34:1:
	X_SYS__TYPES_H_                = 0      // _types.h:35:1:
	X_TIMER_T_DEFINED_             = 0      // types.h:187:1:
	X_TIMEVAL_DECLARED             = 0      // socket.h:130:1:
	X_TIME_T_DEFINED_              = 0      // types.h:182:1:
	X_UINT16_T_DEFINED_            = 0      // types.h:89:1:
	X_UINT32_T_DEFINED_            = 0      // types.h:99:1:
	X_UINT64_T_DEFINED_            = 0      // types.h:109:1:
	X_UINT8_T_DEFINED_             = 0      // types.h:79:1:
	I386                           = 1      // <predefined>:339:1:
	Pseudo_AF_HDRCMPLT             = 31     // socket.h:197:1:
	Pseudo_AF_PFLOW                = 34     // socket.h:201:1:
	Pseudo_AF_PIP                  = 25     // socket.h:190:1:
	Pseudo_AF_PIPEX                = 35     // socket.h:202:1:
	Pseudo_AF_RTIP                 = 22     // socket.h:187:1:
	Pseudo_AF_XTP                  = 19     // socket.h:184:1:
	Unix                           = 1      // <predefined>:340:1:
)

const ( /* uio.h:57:1: */
	UIO_READ  = 0
	UIO_WRITE = 1
)

// Segment flag values.
const ( /* uio.h:60:1: */
	UIO_USERSPACE = 0 // from user data space
	UIO_SYSSPACE  = 1
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$OpenBSD: socket.h,v 1.105 2022/09/03 21:13:48 mbuhl Exp $
//	$NetBSD: socket.h,v 1.14 1996/02/09 18:25:36 christos Exp $

// Copyright (c) 1982, 1985, 1986, 1988, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)socket.h	8.4 (Berkeley) 2/21/94

// get the definitions for struct iovec, size_t, ssize_t, and <sys/cdefs.h>
//	$OpenBSD: uio.h,v 1.19 2018/08/20 16:00:22 mpi Exp $
//	$NetBSD: uio.h,v 1.12 1996/02/09 18:25:45 christos Exp $

// Copyright (c) 1982, 1986, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)uio.h	8.5 (Berkeley) 2/22/94

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.10 2013/03/28 17:30:45 martynas Exp $

// Written by J.T. Conklin <<EMAIL>> 01/17/95.
// Public domain.

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  Do not use __CONCAT on double-quoted strings,
// such as those from the __STRING macro: to concatenate strings just put
// them next to each other.

// GCC1 and some versions of GCC2 declare dead (non-returning) and
// pure (no side effects) functions using "volatile" and "const";
// unfortunately, these then cause warnings under "-ansi -pedantic".
// GCC >= 2.5 uses the __attribute__((attrs)) style.  All of these
// work for GNU C++ (modulo a slight glitch in the C++ grammar in
// the distribution version of 2.5.5).

// __returns_twice makes the compiler not assume the function
// only returns once.  This affects registerisation of variables:
// even local variables need to be in memory across such a call.
// Example: setjmp()

// __only_inline makes the compiler only use this function definition
// for inlining; references that can't be inlined will be left as
// external references instead of generating a local copy.  The
// matching library should include a simple extern definition for
// the function to handle those references.  c.f. ctype.h

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// Delete pseudo-keywords wherever they are not available or needed.

// The __packed macro indicates that a variable or structure members
// should have the smallest possible alignment, despite any host CPU
// alignment requirements.
//
// The __aligned(x) macro specifies the minimum alignment of a
// variable or structure.
//
// These macros together are useful for describing the layout and
// alignment of messages exchanged with hardware or other systems.

// "The nice thing about standards is that there are so many to choose from."
// There are a number of "feature test macros" specified by (different)
// standards that determine which interfaces and types the header files
// should expose.
//
// Because of inconsistencies in these macros, we define our own
// set in the private name space that end in _VISIBLE.  These are
// always defined and so headers can test their values easily.
// Things can get tricky when multiple feature macros are defined.
// We try to take the union of all the features requested.
//
// The following macros are guaranteed to have a value after cdefs.h
// has been included:
//	__POSIX_VISIBLE
//	__XPG_VISIBLE
//	__ISO_C_VISIBLE
//	__BSD_VISIBLE

// X/Open Portability Guides and Single Unix Specifications.
// _XOPEN_SOURCE				XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4		XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1	XPG4v2
// _XOPEN_SOURCE == 500				XPG5
// _XOPEN_SOURCE == 520				XPG5v2
// _XOPEN_SOURCE == 600				POSIX 1003.1-2001 with XSI
// _XOPEN_SOURCE == 700				POSIX 1003.1-2008 with XSI
//
// The XPG spec implies a specific value for _POSIX_C_SOURCE.

// POSIX macros, these checks must follow the XOPEN ones above.
//
// _POSIX_SOURCE == 1		1003.1-1988 (superseded by _POSIX_C_SOURCE)
// _POSIX_C_SOURCE == 1		1003.1-1990
// _POSIX_C_SOURCE == 2		1003.2-1992
// _POSIX_C_SOURCE == 199309L	1003.1b-1993
// _POSIX_C_SOURCE == 199506L   1003.1c-1995, 1003.1i-1995,
//				and the omnibus ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L   1003.1-2001
// _POSIX_C_SOURCE == 200809L   1003.1-2008
//
// The POSIX spec implies a specific value for __ISO_C_VISIBLE, though
// this may be overridden by the _ISOC99_SOURCE macro later.

// _ANSI_SOURCE means to expose ANSI C89 interfaces only.
// If the user defines it in addition to one of the POSIX or XOPEN
// macros, assume the POSIX/XOPEN macro(s) should take precedence.

// _ISOC99_SOURCE, _ISOC11_SOURCE, __STDC_VERSION__, and __cplusplus
// override any of the other macros since they are non-exclusive.

// Finally deal with BSD-specific interfaces that are not covered
// by any standards.  We expose these when none of the POSIX or XPG
// macros is defined or if the user explicitly asks for them.

// Default values.

//	$OpenBSD: _types.h,v 1.10 2022/08/06 13:31:13 semarie Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

//	$OpenBSD: _types.h,v 1.23 2018/03/05 01:15:25 deraadt Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94
//	@(#)ansi.h	8.2 (Berkeley) 1/4/94

// _ALIGN(p) rounds p (pointer or byte index) up to a correctly-aligned
// value for all data types (int, long, ...).   The result is an
// unsigned long and must be cast to any desired pointer type.
//
// _ALIGNED_POINTER is a boolean macro that checks whether an address
// is valid to fetch data elements of type t from on this architecture.
// This does not reflect the optimal alignment, just the possibility
// (within reasonable limits).

// ******** Exact-width integer types
type X__int8_t = int8     /* _types.h:61:22 */
type X__uint8_t = uint8   /* _types.h:62:24 */
type X__int16_t = int16   /* _types.h:63:17 */
type X__uint16_t = uint16 /* _types.h:64:25 */
type X__int32_t = int32   /* _types.h:65:15 */
type X__uint32_t = uint32 /* _types.h:66:23 */
type X__int64_t = int64   /* _types.h:67:20 */
type X__uint64_t = uint64 /* _types.h:68:28 */

// ******** Minimum-width integer types
type X__int_least8_t = X__int8_t     /* _types.h:71:19 */
type X__uint_least8_t = X__uint8_t   /* _types.h:72:20 */
type X__int_least16_t = X__int16_t   /* _types.h:73:20 */
type X__uint_least16_t = X__uint16_t /* _types.h:74:21 */
type X__int_least32_t = X__int32_t   /* _types.h:75:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:76:21 */
type X__int_least64_t = X__int64_t   /* _types.h:77:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:78:21 */

// 7.18.1.3 Fastest minimum-width integer types
type X__int_fast8_t = X__int32_t    /* _types.h:81:20 */
type X__uint_fast8_t = X__uint32_t  /* _types.h:82:21 */
type X__int_fast16_t = X__int32_t   /* _types.h:83:20 */
type X__uint_fast16_t = X__uint32_t /* _types.h:84:21 */
type X__int_fast32_t = X__int32_t   /* _types.h:85:20 */
type X__uint_fast32_t = X__uint32_t /* _types.h:86:21 */
type X__int_fast64_t = X__int64_t   /* _types.h:87:20 */
type X__uint_fast64_t = X__uint64_t /* _types.h:88:21 */

// 7.18.1.4 Integer types capable of holding object pointers
type X__intptr_t = int32   /* _types.h:103:16 */
type X__uintptr_t = uint32 /* _types.h:104:24 */

// 7.18.1.5 Greatest-width integer types
type X__intmax_t = X__int64_t   /* _types.h:107:20 */
type X__uintmax_t = X__uint64_t /* _types.h:108:21 */

// Register size
type X__register_t = int32 /* _types.h:111:16 */

// VM system types
type X__vaddr_t = uint32 /* _types.h:114:24 */
type X__paddr_t = uint32 /* _types.h:115:24 */
type X__vsize_t = uint32 /* _types.h:116:24 */
type X__psize_t = uint32 /* _types.h:117:24 */

// Standard system types
type X__double_t = float64           /* _types.h:120:22 */
type X__float_t = float64            /* _types.h:121:22 */
type X__ptrdiff_t = int32            /* _types.h:122:16 */
type X__size_t = uint32              /* _types.h:123:24 */
type X__ssize_t = int32              /* _types.h:124:16 */
type X__va_list = X__builtin_va_list /* _types.h:126:27 */

// Wide character support types
type X__wchar_t = int32     /* _types.h:133:15 */
type X__wint_t = int32      /* _types.h:135:15 */
type X__rune_t = int32      /* _types.h:136:15 */
type X__wctrans_t = uintptr /* _types.h:137:14 */
type X__wctype_t = uintptr  /* _types.h:138:14 */

type X__blkcnt_t = X__int64_t    /* _types.h:39:19 */ // blocks allocated for file
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // optimal blocksize for I/O
type X__clock_t = X__int64_t     /* _types.h:41:19 */ // ticks in CLOCKS_PER_SEC
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // CLOCK_* identifiers
type X__cpuid_t = uint32         /* _types.h:43:23 */ // CPU id
type X__dev_t = X__int32_t       /* _types.h:44:19 */ // device number
type X__fixpt_t = X__uint32_t    /* _types.h:45:20 */ // fixed point number
type X__fsblkcnt_t = X__uint64_t /* _types.h:46:20 */ // file system block count
type X__fsfilcnt_t = X__uint64_t /* _types.h:47:20 */ // file system file count
type X__gid_t = X__uint32_t      /* _types.h:48:20 */ // group id
type X__id_t = X__uint32_t       /* _types.h:49:20 */ // may contain pid, uid or gid
type X__in_addr_t = X__uint32_t  /* _types.h:50:20 */ // base type for internet address
type X__in_port_t = X__uint16_t  /* _types.h:51:20 */ // IP port type
type X__ino_t = X__uint64_t      /* _types.h:52:20 */ // inode number
type X__key_t = int32            /* _types.h:53:15 */ // IPC key (for Sys V IPC)
type X__mode_t = X__uint32_t     /* _types.h:54:20 */ // permissions
type X__nlink_t = X__uint32_t    /* _types.h:55:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:56:19 */ // file offset or size
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process id
type X__rlim_t = X__uint64_t     /* _types.h:58:20 */ // resource limit
type X__sa_family_t = X__uint8_t /* _types.h:59:19 */ // sockaddr address family type
type X__segsz_t = X__int32_t     /* _types.h:60:19 */ // segment size
type X__socklen_t = X__uint32_t  /* _types.h:61:20 */ // length type for network syscalls
type X__suseconds_t = int32      /* _types.h:62:15 */ // microseconds (signed)
type X__time_t = X__int64_t      /* _types.h:63:19 */ // epoch time
type X__timer_t = X__int32_t     /* _types.h:64:19 */ // POSIX timer identifiers
type X__uid_t = X__uint32_t      /* _types.h:65:20 */ // user id
type X__useconds_t = X__uint32_t /* _types.h:66:20 */ // microseconds

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions. The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint32
	F__mbstate8  [128]int8
} /* _types.h:75:3 */

type Ssize_t = X__ssize_t /* uio.h:48:19 */

type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  Size_t
} /* uio.h:51:1 */

// Tell sys/endian.h we have MD variants of the swap macros.

// Note that these macros evaluate their arguments several times.

// Public names

// These are specified to be function-like macros to match the spec

// POSIX names

// original BSD names

// these were exposed here before

// ancient stuff

type U_char = uint8   /* types.h:51:23 */
type U_short = uint16 /* types.h:52:24 */
type U_int = uint32   /* types.h:53:22 */
type U_long = uint32  /* types.h:54:23 */

type Unchar = uint8  /* types.h:56:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:57:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:58:22 */ // Sys V compatibility
type Ulong = uint32  /* types.h:59:23 */ // Sys V compatibility

type Cpuid_t = X__cpuid_t       /* types.h:61:19 */ // CPU id
type Register_t = X__register_t /* types.h:62:22 */ // register-sized type

// XXX The exact-width bit types should only be exposed if __BSD_VISIBLE
//     but the rest of the includes are not ready for that yet.

type Int8_t = X__int8_t /* types.h:75:19 */

type Uint8_t = X__uint8_t /* types.h:80:20 */

type Int16_t = X__int16_t /* types.h:85:20 */

type Uint16_t = X__uint16_t /* types.h:90:21 */

type Int32_t = X__int32_t /* types.h:95:20 */

type Uint32_t = X__uint32_t /* types.h:100:21 */

type Int64_t = X__int64_t /* types.h:105:20 */

type Uint64_t = X__uint64_t /* types.h:110:21 */

// BSD-style unsigned bits types
type U_int8_t = X__uint8_t   /* types.h:114:19 */
type U_int16_t = X__uint16_t /* types.h:115:20 */
type U_int32_t = X__uint32_t /* types.h:116:20 */
type U_int64_t = X__uint64_t /* types.h:117:20 */

// quads, deprecated in favor of 64 bit int types
type Quad_t = X__int64_t    /* types.h:120:19 */
type U_quad_t = X__uint64_t /* types.h:121:20 */

// VM system types
type Vaddr_t = X__vaddr_t /* types.h:125:19 */
type Paddr_t = X__paddr_t /* types.h:126:19 */
type Vsize_t = X__vsize_t /* types.h:127:19 */
type Psize_t = X__psize_t /* types.h:128:19 */

// Standard system types
type Blkcnt_t = X__blkcnt_t       /* types.h:132:20 */ // blocks allocated for file
type Blksize_t = X__blksize_t     /* types.h:133:21 */ // optimal blocksize for I/O
type Caddr_t = uintptr            /* types.h:134:14 */ // core address
type Daddr32_t = X__int32_t       /* types.h:135:19 */ // 32-bit disk address
type Daddr_t = X__int64_t         /* types.h:136:19 */ // 64-bit disk address
type Dev_t = X__dev_t             /* types.h:137:18 */ // device number
type Fixpt_t = X__fixpt_t         /* types.h:138:19 */ // fixed point number
type Gid_t = X__gid_t             /* types.h:139:18 */ // group id
type Id_t = X__id_t               /* types.h:140:17 */ // may contain pid, uid or gid
type Ino_t = X__ino_t             /* types.h:141:18 */ // inode number
type Key_t = X__key_t             /* types.h:142:18 */ // IPC key (for Sys V IPC)
type Mode_t = X__mode_t           /* types.h:143:18 */ // permissions
type Nlink_t = X__nlink_t         /* types.h:144:19 */ // link count
type Rlim_t = X__rlim_t           /* types.h:145:18 */ // resource limit
type Segsz_t = X__segsz_t         /* types.h:146:19 */ // segment size
type Uid_t = X__uid_t             /* types.h:147:18 */ // user id
type Useconds_t = X__useconds_t   /* types.h:148:22 */ // microseconds
type Suseconds_t = X__suseconds_t /* types.h:149:23 */ // microseconds (signed)
type Fsblkcnt_t = X__fsblkcnt_t   /* types.h:150:22 */ // file system block count
type Fsfilcnt_t = X__fsfilcnt_t   /* types.h:151:22 */ // file system file count

// The following types may be defined in multiple header files.
type Clock_t = X__clock_t /* types.h:158:19 */

type Clockid_t = X__clockid_t /* types.h:163:21 */

type Pid_t = X__pid_t /* types.h:168:18 */

type Time_t = X__time_t /* types.h:183:18 */

type Timer_t = X__timer_t /* types.h:188:19 */

type Off_t = X__off_t /* types.h:193:18 */

// Major, minor numbers, dev_t's.

type Socklen_t = X__socklen_t /* socket.h:47:21 */ // length type for network syscalls

type Sa_family_t = X__sa_family_t /* socket.h:52:23 */ // sockaddr address family type

// Definitions related to sockets: types, address families, options.

// Types

// Socket creation flags

// Option flags per-socket.

// Additional options, not kept in so_options.

// Structure used for manipulating linger option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:122:1 */

type Timeval = struct {
	Ftv_sec  Time_t
	Ftv_usec Suseconds_t
} /* socket.h:131:1 */

// Structure used for manipulating splice option.
type Splice = struct {
	Fsp_fd   int32
	Fsp_max  Off_t
	Fsp_idle struct {
		Ftv_sec  Time_t
		Ftv_usec Suseconds_t
	}
} /* socket.h:140:1 */

// Maximum number of alternate routing tables

// Level number for (get/set)sockopt() to apply to socket itself.

// Address families.

// Structure used by kernel to store most
// addresses.
type Sockaddr = struct {
	Fsa_len    X__uint8_t
	Fsa_family Sa_family_t
	Fsa_data   [14]int8
} /* socket.h:209:1 */

// Sockaddr type which can hold any sockaddr type available
// in the system.
//
// Note: __ss_{len,family} is defined in RFC2553.  During RFC2553 discussion
// the field name went back and forth between ss_len and __ss_len,
// and RFC2553 specifies it to be __ss_len.  openbsd picked ss_len.
// For maximum portability, userland programmer would need to
// (1) make the code never touch ss_len portion (cast it into sockaddr and
// touch sa_len), or (2) add "-Dss_len=__ss_len" into CFLAGS to unify all
// occurrences (including header file) to __ss_len.
type Sockaddr_storage = struct {
	Fss_len    X__uint8_t
	Fss_family Sa_family_t
	F__ss_pad1 [6]uint8
	F__ss_pad2 X__uint64_t
	F__ss_pad3 [240]uint8
} /* socket.h:227:1 */

// Protocol families, same as address families for now.

// These are the valid values for the "how" field used by shutdown(2).

// Read using getsockopt() with SOL_SOCKET, SO_PEERCRED
type Sockpeercred = struct {
	Fuid Uid_t
	Fgid Gid_t
	Fpid Pid_t
} /* socket.h:300:1 */

// Definitions for network related sysctl, CTL_NET.
//
// Second level is protocol family.
// Third level is protocol number.
//
// Further levels are defined by the individual families below.

// PF_ROUTE - Routing table
//
// Four additional levels are defined:
//	Fourth: address family, 0 is wildcard
//	Fifth: type of info, defined below
//	Sixth: flag(s) to mask with for NET_RT_FLAGS
//	Seventh: routing table to use (facultative, defaults to 0)
//		 NET_RT_TABLE has the table id as sixth element.

// PF_UNIX - unix socket tunables

// PF_LINK - link layer or device tunables

// PF_KEY - Key Management

// PF_BPF  not really a family, but connected under CTL_NET

// PF_PFLOW not really a family, but connected under CTL_NET

// Maximum queue length specifiable by listen(2).

// Message header for recvmsg and sendmsg calls.
// Used value-result for recvmsg, value only for sendmsg.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    Socklen_t
	Fmsg_iov        uintptr
	Fmsg_iovlen     uint32
	Fmsg_control    uintptr
	Fmsg_controllen Socklen_t
	Fmsg_flags      int32
} /* socket.h:483:1 */

type Mmsghdr = struct {
	Fmsg_hdr struct {
		Fmsg_name       uintptr
		Fmsg_namelen    Socklen_t
		Fmsg_iov        uintptr
		Fmsg_iovlen     uint32
		Fmsg_control    uintptr
		Fmsg_controllen Socklen_t
		Fmsg_flags      int32
	}
	Fmsg_len uint32
} /* socket.h:493:1 */

// Header for ancillary data objects in msg_control buffer.
// Used for additional information with/about a datagram
// not expressible by flags.  The format is a sequence
// of message elements headed by cmsghdr structures.
type Cmsghdr = struct {
	Fcmsg_len   Socklen_t
	Fcmsg_level int32
	Fcmsg_type  int32
} /* socket.h:520:1 */

var _ int8 /* gen.c:2:13: */
