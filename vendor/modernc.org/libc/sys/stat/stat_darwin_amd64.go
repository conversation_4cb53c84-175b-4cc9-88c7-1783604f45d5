// Code generated by 'ccgo sys/stat/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o sys/stat/stat_darwin_amd64.go -pkgname stat', DO NOT EDIT.

package stat

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ACCESSPERMS                            = 511        // stat.h:297:1:
	ALLPERMS                               = 4095       // stat.h:299:1:
	DEFFILEMODE                            = 438        // stat.h:301:1:
	EF_IS_PURGEABLE                        = 0x00000008 // stat.h:370:1:
	EF_IS_SPARSE                           = 0x00000010 // stat.h:371:1:
	EF_IS_SYNC_ROOT                        = 0x00000004 // stat.h:369:1:
	EF_MAY_SHARE_BLOCKS                    = 0x00000001 // stat.h:367:1:
	EF_NO_XATTRS                           = 0x00000002 // stat.h:368:1:
	SF_APPEND                              = 0x00040000 // stat.h:340:1:
	SF_ARCHIVED                            = 0x00010000 // stat.h:338:1:
	SF_DATALESS                            = 0x40000000 // stat.h:359:1:
	SF_FIRMLINK                            = 0x00800000 // stat.h:351:1:
	SF_IMMUTABLE                           = 0x00020000 // stat.h:339:1:
	SF_NOUNLINK                            = 0x00100000 // stat.h:342:1:
	SF_RESTRICTED                          = 0x00080000 // stat.h:341:1:
	SF_SETTABLE                            = 0x3fff0000 // stat.h:336:1:
	SF_SUPPORTED                           = 0x009f0000 // stat.h:335:1:
	SF_SYNTHETIC                           = 0xc0000000 // stat.h:337:1:
	S_BLKSIZE                              = 512        // stat.h:303:1:
	S_IEXEC                                = 64         // _s_ifmt.h:72:1:
	S_IFBLK                                = 0060000    // _s_ifmt.h:39:1:
	S_IFCHR                                = 0020000    // _s_ifmt.h:37:1:
	S_IFDIR                                = 0040000    // _s_ifmt.h:38:1:
	S_IFIFO                                = 0010000    // _s_ifmt.h:36:1:
	S_IFLNK                                = 0120000    // _s_ifmt.h:41:1:
	S_IFMT                                 = 0170000    // _s_ifmt.h:35:1:
	S_IFREG                                = 0100000    // _s_ifmt.h:40:1:
	S_IFSOCK                               = 0140000    // _s_ifmt.h:42:1:
	S_IFWHT                                = 0160000    // _s_ifmt.h:44:1:
	S_IREAD                                = 256        // _s_ifmt.h:70:1:
	S_IRGRP                                = 0000040    // _s_ifmt.h:55:1:
	S_IROTH                                = 0000004    // _s_ifmt.h:60:1:
	S_IRUSR                                = 0000400    // _s_ifmt.h:50:1:
	S_IRWXG                                = 0000070    // _s_ifmt.h:54:1:
	S_IRWXO                                = 0000007    // _s_ifmt.h:59:1:
	S_IRWXU                                = 0000700    // _s_ifmt.h:49:1:
	S_ISGID                                = 0002000    // _s_ifmt.h:65:1:
	S_ISTXT                                = 512        // _s_ifmt.h:69:1:
	S_ISUID                                = 0004000    // _s_ifmt.h:64:1:
	S_ISVTX                                = 0001000    // _s_ifmt.h:66:1:
	S_IWGRP                                = 0000020    // _s_ifmt.h:56:1:
	S_IWOTH                                = 0000002    // _s_ifmt.h:61:1:
	S_IWRITE                               = 128        // _s_ifmt.h:71:1:
	S_IWUSR                                = 0000200    // _s_ifmt.h:51:1:
	S_IXGRP                                = 0000010    // _s_ifmt.h:57:1:
	S_IXOTH                                = 0000001    // _s_ifmt.h:62:1:
	S_IXUSR                                = 0000100    // _s_ifmt.h:52:1:
	UF_APPEND                              = 0x00000004 // stat.h:313:1:
	UF_COMPRESSED                          = 0x00000020 // stat.h:320:1:
	UF_DATAVAULT                           = 0x00000080 // stat.h:326:1:
	UF_HIDDEN                              = 0x00008000 // stat.h:330:1:
	UF_IMMUTABLE                           = 0x00000002 // stat.h:312:1:
	UF_NODUMP                              = 0x00000001 // stat.h:311:1:
	UF_OPAQUE                              = 0x00000008 // stat.h:314:1:
	UF_SETTABLE                            = 0x0000ffff // stat.h:310:1:
	UF_TRACKED                             = 0x00000040 // stat.h:324:1:
	UTIME_NOW                              = -1         // stat.h:393:1:
	UTIME_OMIT                             = -2         // stat.h:394:1:
	X_BLKCNT_T                             = 0          // _blkcnt_t.h:29:1:
	X_BLKSIZE_T                            = 0          // _blksize_t.h:29:1:
	X_BSD_I386__TYPES_H_                   = 0          // _types.h:29:1:
	X_BSD_MACHINE_TYPES_H_                 = 0          // types.h:32:1:
	X_BSD_MACHINE__TYPES_H_                = 0          // _types.h:29:1:
	X_CDEFS_H_                             = 0          // cdefs.h:68:1:
	X_DARWIN_FEATURE_64_BIT_INODE          = 1          // cdefs.h:745:1:
	X_DARWIN_FEATURE_ONLY_UNIX_CONFORMANCE = 1          // cdefs.h:771:1:
	X_DARWIN_FEATURE_UNIX_CONFORMANCE      = 3          // cdefs.h:779:1:
	X_DEV_T                                = 0          // _dev_t.h:29:1:
	X_FILESEC_T                            = 0          // _filesec_t.h:29:1:
	X_FILE_OFFSET_BITS                     = 64         // <builtin>:25:1:
	X_GID_T                                = 0          // _gid_t.h:29:1:
	X_INO64_T                              = 0          // _ino64_t.h:29:1:
	X_INO_T                                = 0          // _ino_t.h:29:1:
	X_INT16_T                              = 0          // _int16_t.h:29:1:
	X_INT32_T                              = 0          // _int32_t.h:29:1:
	X_INT64_T                              = 0          // _int64_t.h:29:1:
	X_INT8_T                               = 0          // _int8_t.h:29:1:
	X_INTPTR_T                             = 0          // _intptr_t.h:29:1:
	X_LP64                                 = 1          // <predefined>:1:1:
	X_MACHTYPES_H_                         = 0          // types.h:67:1:
	X_MODE_T                               = 0          // _mode_t.h:29:1:
	X_NLINK_T                              = 0          // _nlink_t.h:29:1:
	X_Nonnull                              = 0          // cdefs.h:243:1:
	X_Null_unspecified                     = 0          // cdefs.h:246:1:
	X_Nullable                             = 0          // cdefs.h:240:1:
	X_OFF_T                                = 0          // _off_t.h:29:1:
	X_SYS_STAT_H_                          = 0          // stat.h:71:1:
	X_SYS__PTHREAD_TYPES_H_                = 0          // _pthread_types.h:30:1:
	X_SYS__TYPES_H_                        = 0          // _types.h:30:1:
	X_TIME_T                               = 0          // _time_t.h:29:1:
	X_UID_T                                = 0          // _uid_t.h:29:1:
	X_UINTPTR_T                            = 0          // _uintptr_t.h:29:1:
	X_U_INT16_T                            = 0          // _u_int16_t.h:29:1:
	X_U_INT32_T                            = 0          // _u_int32_t.h:29:1:
	X_U_INT64_T                            = 0          // _u_int64_t.h:29:1:
	X_U_INT8_T                             = 0          // _u_int8_t.h:29:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

var X__darwin_check_fd_set_overflow uintptr /* <builtin>:146:5: */

// Copyright (c) 2000-2014 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 1995 NeXT Computer, Inc. All Rights Reserved
// -
// Copyright (c) 1982, 1986, 1989, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)stat.h	8.9 (Berkeley) 8/17/94

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// This SDK is designed to work with clang and specific versions of
// gcc >= 4.0 with Apple's patch sets

// Compatibility with compilers and environments that don't support compiler
// feature checking function-like macros.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  __CONCAT can also concatenate double-quoted
// strings produced by the __STRING macro, but this only works with ANSI C.

// In non-ANSI C environments, new programs will want ANSI-only C keywords
// deleted from the program and old programs will want them left alone.
// When using a compiler other than gcc, programs using the ANSI C keywords
// const, inline etc. as normal identifiers should define -DNO_ANSI_KEYWORDS.
// When using "gcc -traditional", we assume that this is the intent; if
// __GNUC__ is defined but __STDC__ is not, we leave the new keywords alone.

// __unused denotes variables and functions that may not be used, preventing
// the compiler from warning about it if not used.

// __used forces variables and functions to be included even if it appears
// to the compiler that they are not used (and would thust be discarded).

// __cold marks code used for debugging or that is rarely taken
// and tells the compiler to optimize for size and outline code.

// __deprecated causes the compiler to produce a warning when encountering
// code using the deprecated functionality.
// __deprecated_msg() does the same, and compilers that support it will print
// a message along with the deprecation warning.
// This may require turning on such warning with the -Wdeprecated flag.
// __deprecated_enum_msg() should be used on enums, and compilers that support
// it will print the deprecation warning.
// __kpi_deprecated() specifically indicates deprecation of kernel programming
// interfaces in Kernel.framework used by KEXTs.

// __unavailable causes the compiler to error out when encountering
// code using the tagged function of variable.

// Delete pseudo-keywords wherever they are not available or needed.

// We use `__restrict' as a way to define the `restrict' type qualifier
// without disturbing older software that is unaware of C99 keywords.

// Compatibility with compilers and environments that don't support the
// nullability feature.

// __disable_tail_calls causes the compiler to not perform tail call
// optimization inside the marked function.

// __not_tail_called causes the compiler to prevent tail call optimization
// on statically bound calls to the function.  It has no effect on indirect
// calls.  Virtual functions, objective-c methods, and functions marked as
// "always_inline" cannot be marked as __not_tail_called.

// __result_use_check warns callers of a function that not using the function
// return value is a bug, i.e. dismissing malloc() return value results in a
// memory leak.

// __swift_unavailable causes the compiler to mark a symbol as specifically
// unavailable in Swift, regardless of any other availability in C.

// __abortlike is the attribute to put on functions like abort() that are
// typically used to mark assertions. These optimize the codegen
// for outlining while still maintaining debugability.

// Declaring inline functions within headers is error-prone due to differences
// across various versions of the C language and extensions.  __header_inline
// can be used to declare inline functions within system headers.  In cases
// where you want to force inlining instead of letting the compiler make
// the decision, you can use __header_always_inline.
//
// Be aware that using inline for functions which compilers may also provide
// builtins can behave differently under various compilers.  If you intend to
// provide an inline version of such a function, you may want to use a macro
// instead.
//
// The check for !__GNUC__ || __clang__ is because gcc doesn't correctly
// support c99 inline in some cases:
// http://gcc.gnu.org/bugzilla/show_bug.cgi?id=55965

// Compiler-dependent macros that bracket portions of code where the
// "-Wunreachable-code" warning should be ignored. Please use sparingly.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly.  Functions declared
// with these attributes will cause compilation warnings if there is a
// mismatch between the format string and subsequent function parameter
// types.

// Source compatibility only, ID string not emitted in object file

// __alloc_size can be used to label function arguments that represent the
// size of memory that the function allocates and returns. The one-argument
// form labels a single argument that gives the allocation size (where the
// arguments are numbered from 1):
//
// void	*malloc(size_t __size) __alloc_size(1);
//
// The two-argument form handles the case where the size is calculated as the
// product of two arguments:
//
// void	*calloc(size_t __count, size_t __size) __alloc_size(1,2);

// COMPILATION ENVIRONMENTS -- see compat(5) for additional detail
//
// DEFAULT	By default newly complied code will get POSIX APIs plus
//		Apple API extensions in scope.
//
//		Most users will use this compilation environment to avoid
//		behavioral differences between 32 and 64 bit code.
//
// LEGACY	Defining _NONSTD_SOURCE will get pre-POSIX APIs plus Apple
//		API extensions in scope.
//
//		This is generally equivalent to the Tiger release compilation
//		environment, except that it cannot be applied to 64 bit code;
//		its use is discouraged.
//
//		We expect this environment to be deprecated in the future.
//
// STRICT	Defining _POSIX_C_SOURCE or _XOPEN_SOURCE restricts the
//		available APIs to exactly the set of APIs defined by the
//		corresponding standard, based on the value defined.
//
//		A correct, portable definition for _POSIX_C_SOURCE is 200112L.
//		A correct, portable definition for _XOPEN_SOURCE is 600L.
//
//		Apple API extensions are not visible in this environment,
//		which can cause Apple specific code to fail to compile,
//		or behave incorrectly if prototypes are not in scope or
//		warnings about missing prototypes are not enabled or ignored.
//
// In any compilation environment, for correct symbol resolution to occur,
// function prototypes must be in scope.  It is recommended that all Apple
// tools users add either the "-Wall" or "-Wimplicit-function-declaration"
// compiler flags to their projects to be warned when a function is being
// used without a prototype in scope.

// These settings are particular to each product.
// Platform: MacOSX
// #undef __DARWIN_ONLY_UNIX_CONFORMANCE (automatically set for 64-bit)

// The __DARWIN_ALIAS macros are used to do symbol renaming; they allow
// legacy code to use the old symbol, thus maintaining binary compatibility
// while new code can use a standards compliant version of the same function.
//
// __DARWIN_ALIAS is used by itself if the function signature has not
// changed, it is used along with a #ifdef check for __DARWIN_UNIX03
// if the signature has changed.  Because the __LP64__ environment
// only supports UNIX03 semantics it causes __DARWIN_UNIX03 to be
// defined, but causes __DARWIN_ALIAS to do no symbol mangling.
//
// As a special case, when XCode is used to target a specific version of the
// OS, the manifest constant __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__
// will be defined by the compiler, with the digits representing major version
// time 100 + minor version times 10 (e.g. 10.5 := 1050).  If we are targeting
// pre-10.5, and it is the default compilation environment, revert the
// compilation environment to pre-__DARWIN_UNIX03.

// symbol suffixes used for symbol versioning

// symbol versioning macros

// symbol release macros
// Copyright (c) 2010 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.  This permits us to convert values for feature
// testing, as necessary, using only _POSIX_C_SOURCE.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1L		1003.1-1990
//  _POSIX_C_SOURCE == 2L		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309L		1003.1b-1993
//  _POSIX_C_SOURCE == 199506L		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112L		1003.1-2001
//  _POSIX_C_SOURCE == 200809L		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1L.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2L.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.

// POSIX C deprecation macros
// Copyright (c) 2010 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Set a single macro which will always be defined and can be used to determine
// the appropriate namespace.  For POSIX, these values will correspond to
// _POSIX_C_SOURCE value.  Currently there are two additional levels corresponding
// to ANSI (_ANSI_SOURCE) and Darwin extensions (_DARWIN_C_SOURCE)

// If the developer has neither requested a strict language mode nor a version
// of POSIX, turn on functionality provided by __STDC_WANT_LIB_EXT1__ as part
// of __DARWIN_C_FULL.

// long long is not supported in c89 (__STRICT_ANSI__), but g++ -ansi and
// c99 still want long longs.  While not perfect, we allow long longs for
// g++.

// ****************************************
//
//  Public darwin-specific feature macros
//

// _DARWIN_FEATURE_64_BIT_INODE indicates that the ino_t type is 64-bit, and
// structures modified for 64-bit inodes (like struct stat) will be used.

// _DARWIN_FEATURE_64_ONLY_BIT_INODE indicates that the ino_t type may only
// be 64-bit; there is no support for 32-bit ino_t when this macro is defined
// (and non-zero).  There is no struct stat64 either, as the regular
// struct stat will already be the 64-bit version.

// _DARWIN_FEATURE_ONLY_VERS_1050 indicates that only those APIs updated
// in 10.5 exists; no pre-10.5 variants are available.

// _DARWIN_FEATURE_ONLY_UNIX_CONFORMANCE indicates only UNIX conforming API
// are available (the legacy BSD APIs are not available)

// _DARWIN_FEATURE_UNIX_CONFORMANCE indicates whether UNIX conformance is on,
// and specifies the conformance level (3 is SUSv3)

// This macro casts away the qualifier from the variable
//
// Note: use at your own risk, removing qualifiers can result in
// catastrophic run-time failures.

// __XNU_PRIVATE_EXTERN is a linkage decoration indicating that a symbol can be
// used from other compilation units, but not other libraries or executables.

// Architecture validation for current SDK

// Similar to OS_ENUM/OS_CLOSED_ENUM/OS_OPTIONS/OS_CLOSED_OPTIONS
//
// This provides more advanced type checking on compilers supporting
// the proper extensions, even in C.

// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2003 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// This header file contains integer types.  It's intended to also contain
// flotaing point and other arithmetic types, as needed, later.

type X__int8_t = int8     /* _types.h:37:33 */
type X__uint8_t = uint8   /* _types.h:41:33 */
type X__int16_t = int16   /* _types.h:42:33 */
type X__uint16_t = uint16 /* _types.h:43:33 */
type X__int32_t = int32   /* _types.h:44:33 */
type X__uint32_t = uint32 /* _types.h:45:33 */
type X__int64_t = int64   /* _types.h:46:33 */
type X__uint64_t = uint64 /* _types.h:47:33 */

type X__darwin_intptr_t = int64   /* _types.h:49:33 */
type X__darwin_natural_t = uint32 /* _types.h:50:33 */

// The rune type below is declared to be an ``int'' instead of the more natural
// ``unsigned long'' or ``long''.  Two things are happening here.  It is not
// unsigned so that EOF (-1) can be naturally assigned to it and used.  Also,
// it looks like 10646 will be a 31 bit standard.  This means that if your
// ints cannot hold 32 bits, you will be in trouble.  The reason an int was
// chosen over a long is that the is*() and to*() routines take ints (says
// ANSI C), but they use __darwin_ct_rune_t instead of int.  By changing it
// here, you lose a bit of ANSI conformance, but your programs will still
// work.
//
// NOTE: rune_t is not covered by ANSI nor other standards, and should not
// be instantiated outside of lib/libc/locale.  Use wchar_t.  wchar_t and
// rune_t must be the same type.  Also wint_t must be no narrower than
// wchar_t, and should also be able to hold all members of the largest
// character set plus one extra value (WEOF). wint_t must be at least 16 bits.

type X__darwin_ct_rune_t = int32 /* _types.h:70:33 */ // ct_rune_t

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]int8
} /* _types.h:79:3 */

type X__darwin_mbstate_t = X__mbstate_t /* _types.h:81:33 */ // mbstate_t

type X__darwin_ptrdiff_t = int64 /* _types.h:84:33 */ // ptr1 - ptr2

type X__darwin_size_t = uint64 /* _types.h:92:33 */ // sizeof()

type X__darwin_va_list = X__builtin_va_list /* _types.h:98:33 */ // va_list

type X__darwin_wchar_t = int32 /* _types.h:104:33 */ // wchar_t

type X__darwin_rune_t = X__darwin_wchar_t /* _types.h:109:33 */ // rune_t

type X__darwin_wint_t = int32 /* _types.h:112:33 */ // wint_t

type X__darwin_clock_t = uint64        /* _types.h:117:33 */ // clock()
type X__darwin_socklen_t = X__uint32_t /* _types.h:118:33 */ // socklen_t (duh)
type X__darwin_ssize_t = int64         /* _types.h:119:33 */ // byte count or error
type X__darwin_time_t = int64          /* _types.h:120:33 */ // time()

// Type definitions; takes common type definitions that must be used
// in multiple header files due to [XSI], removes them from the system
// space, and puts them in the implementation space.

type X__darwin_blkcnt_t = X__int64_t                    /* _types.h:55:25 */ // total blocks
type X__darwin_blksize_t = X__int32_t                   /* _types.h:56:25 */ // preferred block size
type X__darwin_dev_t = X__int32_t                       /* _types.h:57:25 */ // dev_t
type X__darwin_fsblkcnt_t = uint32                      /* _types.h:58:25 */ // Used by statvfs and fstatvfs
type X__darwin_fsfilcnt_t = uint32                      /* _types.h:59:25 */ // Used by statvfs and fstatvfs
type X__darwin_gid_t = X__uint32_t                      /* _types.h:60:25 */ // [???] process and group IDs
type X__darwin_id_t = X__uint32_t                       /* _types.h:61:25 */ // [XSI] pid_t, uid_t, or gid_t
type X__darwin_ino64_t = X__uint64_t                    /* _types.h:62:25 */ // [???] Used for 64 bit inodes
type X__darwin_ino_t = X__darwin_ino64_t                /* _types.h:64:26 */ // [???] Used for inodes
type X__darwin_mach_port_name_t = X__darwin_natural_t   /* _types.h:68:28 */ // Used by mach
type X__darwin_mach_port_t = X__darwin_mach_port_name_t /* _types.h:69:35 */ // Used by mach
type X__darwin_mode_t = X__uint16_t                     /* _types.h:70:25 */ // [???] Some file attributes
type X__darwin_off_t = X__int64_t                       /* _types.h:71:25 */ // [???] Used for file sizes
type X__darwin_pid_t = X__int32_t                       /* _types.h:72:25 */ // [???] process and group IDs
type X__darwin_sigset_t = X__uint32_t                   /* _types.h:73:25 */ // [???] signal set
type X__darwin_suseconds_t = X__int32_t                 /* _types.h:74:25 */ // [???] microseconds
type X__darwin_uid_t = X__uint32_t                      /* _types.h:75:25 */ // [???] user IDs
type X__darwin_useconds_t = X__uint32_t                 /* _types.h:76:25 */ // [???] microseconds
type X__darwin_uuid_t = [16]uint8                       /* _types.h:77:25 */
type X__darwin_uuid_string_t = [37]int8                 /* _types.h:78:17 */

// Copyright (c) 2003-2013 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// pthread opaque structures

type X__darwin_pthread_handler_rec = struct {
	F__routine uintptr
	F__arg     uintptr
	F__next    uintptr
} /* _pthread_types.h:57:1 */

type X_opaque_pthread_attr_t = struct {
	F__sig    int64
	F__opaque [56]int8
} /* _pthread_types.h:63:1 */

type X_opaque_pthread_cond_t = struct {
	F__sig    int64
	F__opaque [40]int8
} /* _pthread_types.h:68:1 */

type X_opaque_pthread_condattr_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:73:1 */

type X_opaque_pthread_mutex_t = struct {
	F__sig    int64
	F__opaque [56]int8
} /* _pthread_types.h:78:1 */

type X_opaque_pthread_mutexattr_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:83:1 */

type X_opaque_pthread_once_t = struct {
	F__sig    int64
	F__opaque [8]int8
} /* _pthread_types.h:88:1 */

type X_opaque_pthread_rwlock_t = struct {
	F__sig    int64
	F__opaque [192]int8
} /* _pthread_types.h:93:1 */

type X_opaque_pthread_rwlockattr_t = struct {
	F__sig    int64
	F__opaque [16]int8
} /* _pthread_types.h:98:1 */

type X_opaque_pthread_t = struct {
	F__sig           int64
	F__cleanup_stack uintptr
	F__opaque        [8176]int8
} /* _pthread_types.h:103:1 */

type X__darwin_pthread_attr_t = X_opaque_pthread_attr_t             /* _pthread_types.h:109:39 */
type X__darwin_pthread_cond_t = X_opaque_pthread_cond_t             /* _pthread_types.h:110:39 */
type X__darwin_pthread_condattr_t = X_opaque_pthread_condattr_t     /* _pthread_types.h:111:43 */
type X__darwin_pthread_key_t = uint64                               /* _pthread_types.h:112:23 */
type X__darwin_pthread_mutex_t = X_opaque_pthread_mutex_t           /* _pthread_types.h:113:40 */
type X__darwin_pthread_mutexattr_t = X_opaque_pthread_mutexattr_t   /* _pthread_types.h:114:44 */
type X__darwin_pthread_once_t = X_opaque_pthread_once_t             /* _pthread_types.h:115:39 */
type X__darwin_pthread_rwlock_t = X_opaque_pthread_rwlock_t         /* _pthread_types.h:116:41 */
type X__darwin_pthread_rwlockattr_t = X_opaque_pthread_rwlockattr_t /* _pthread_types.h:117:45 */
type X__darwin_pthread_t = uintptr                                  /* _pthread_types.h:118:34 */

// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

//
//     These macros are for use in OS header files. They enable function prototypes
//     and Objective-C methods to be tagged with the OS version in which they
//     were first available; and, if applicable, the OS version in which they
//     became deprecated.
//
//     The desktop Mac OS X and iOS each have different version numbers.
//     The __OSX_AVAILABLE_STARTING() macro allows you to specify both the desktop
//     and iOS version numbers.  For instance:
//         __OSX_AVAILABLE_STARTING(__MAC_10_2,__IPHONE_2_0)
//     means the function/method was first available on Mac OS X 10.2 on the desktop
//     and first available in iOS 2.0 on the iPhone.
//
//     If a function is available on one platform, but not the other a _NA (not
//     applicable) parameter is used.  For instance:
//             __OSX_AVAILABLE_STARTING(__MAC_10_3,__IPHONE_NA)
//     means that the function/method was first available on Mac OS X 10.3, and it
//     currently not implemented on the iPhone.
//
//     At some point, a function/method may be deprecated.  That means Apple
//     recommends applications stop using the function, either because there is a
//     better replacement or the functionality is being phased out.  Deprecated
//     functions/methods can be tagged with a __OSX_AVAILABLE_BUT_DEPRECATED()
//     macro which specifies the OS version where the function became available
//     as well as the OS version in which it became deprecated.  For instance:
//         __OSX_AVAILABLE_BUT_DEPRECATED(__MAC_10_0,__MAC_10_5,__IPHONE_NA,__IPHONE_NA)
//     means that the function/method was introduced in Mac OS X 10.0, then
//     became deprecated beginning in Mac OS X 10.5.  On iOS the function
//     has never been available.
//
//     For these macros to function properly, a program must specify the OS version range
//     it is targeting.  The min OS version is specified as an option to the compiler:
//     -mmacosx-version-min=10.x when building for Mac OS X, and -miphoneos-version-min=y.z
//     when building for the iPhone.  The upper bound for the OS version is rarely needed,
//     but it can be set on the command line via: -D__MAC_OS_X_VERSION_MAX_ALLOWED=10x0 for
//     Mac OS X and __IPHONE_OS_VERSION_MAX_ALLOWED = y0z00 for iOS.
//
//     Examples:
//
//         A function available in Mac OS X 10.5 and later, but not on the phone:
//
//             extern void mymacfunc() __OSX_AVAILABLE_STARTING(__MAC_10_5,__IPHONE_NA);
//
//
//         An Objective-C method in Mac OS X 10.5 and later, but not on the phone:
//
//             @interface MyClass : NSObject
//             -(void) mymacmethod __OSX_AVAILABLE_STARTING(__MAC_10_5,__IPHONE_NA);
//             @end
//
//
//         An enum available on the phone, but not available on Mac OS X:
//
//             #if __IPHONE_OS_VERSION_MIN_REQUIRED
//                 enum { myEnum = 1 };
//             #endif
//            Note: this works when targeting the Mac OS X platform because
//            __IPHONE_OS_VERSION_MIN_REQUIRED is undefined which evaluates to zero.
//
//
//         An enum with values added in different iPhoneOS versions:
//
// 			enum {
// 			    myX  = 1,	// Usable on iPhoneOS 2.1 and later
// 			    myY  = 2,	// Usable on iPhoneOS 3.0 and later
// 			    myZ  = 3,	// Usable on iPhoneOS 3.0 and later
// 				...
// 		      Note: you do not want to use #if with enumeration values
// 			  when a client needs to see all values at compile time
// 			  and use runtime logic to only use the viable values.
//
//
//     It is also possible to use the *_VERSION_MIN_REQUIRED in source code to make one
//     source base that can be compiled to target a range of OS versions.  It is best
//     to not use the _MAC_* and __IPHONE_* macros for comparisons, but rather their values.
//     That is because you might get compiled on an old OS that does not define a later
//     OS version macro, and in the C preprocessor undefined values evaluate to zero
//     in expresssions, which could cause the #if expression to evaluate in an unexpected
//     way.
//
//         #ifdef __MAC_OS_X_VERSION_MIN_REQUIRED
//             // code only compiled when targeting Mac OS X and not iPhone
//             // note use of 1050 instead of __MAC_10_5
//             #if __MAC_OS_X_VERSION_MIN_REQUIRED < 1050
//                 // code in here might run on pre-Leopard OS
//             #else
//                 // code here can assume Leopard or later
//             #endif
//         #endif
//
//

// __API_TO_BE_DEPRECATED is used as a version number in API that will be deprecated
// in an upcoming release. This soft deprecation is an intermediate step before formal
// deprecation to notify developers about the API before compiler warnings are generated.
// You can find all places in your code that use soft deprecated API by redefining the
// value of this macro to your current minimum deployment target, for example:
// (macOS)
//   clang -D__API_TO_BE_DEPRECATED=10.12 <other compiler flags>
// (iOS)
//   clang -D__API_TO_BE_DEPRECATED=11.0 <other compiler flags>

// __MAC_NA is not defined to a value but is uses as a token by macros to indicate that the API is unavailable

// __IPHONE_NA is not defined to a value but is uses as a token by macros to indicate that the API is unavailable

// Copyright (c) 2007-2016 by Apple Inc.. All rights reserved.
//
// @APPLE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this
// file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_LICENSE_HEADER_END@

//
//     File:       AvailabilityInternal.h
//
//     Contains:   implementation details of __OSX_AVAILABLE_* macros from <Availability.h>
//

// compiler for Mac OS X sets __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__

// make sure a default max version is set

//
//  Macros for defining which versions/platform a given symbol can be used.
//
//  @see http://clang.llvm.org/docs/AttributeReference.html#availability
//

// Evaluate to nothing for compilers that don't support availability.

// Swift compiler version
// Allows for project-agnostic “epochs” for frameworks imported into Swift via the Clang importer, like #if _compiler_version for Swift
// Example:
//
//  #if __swift_compiler_version_at_least(800, 2, 20)
//  - (nonnull NSString *)description;
//  #else
//  - (NSString *)description;
//  #endif

// If __SPI_AVAILABLE has not been defined elsewhere, disable it.

// for use to document app extension usage

// for use marking APIs available info for Mac OSX

// for use marking APIs available info for iOS

// for use marking APIs available info for tvOS

// for use marking APIs available info for Watch OS

// for use marking APIs unavailable for swift

//
//  Macros for defining which versions/platform a given symbol can be used.
//
//  @see http://clang.llvm.org/docs/AttributeReference.html#availability
//
//  * Note that these macros are only compatible with clang compilers that
//  * support the following target selection options:
//  *
//  * -mmacosx-version-min
//  * -miphoneos-version-min
//  * -mwatchos-version-min
//  * -mtvos-version-min
//

// Evaluate to nothing for compilers that don't support availability.

// If SPI decorations have not been defined elsewhere, disable them.

// [XSI] The timespec structure may be defined as described in <time.h>
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.

// Copyright (c) 2000-2008 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

// Copyright (c) 2000-2003 Apple Computer, Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2018 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Basic integral types.  Omit the typedef if
// not possible for a machine/compiler combination.
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int8_t = int8 /* _int8_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int16_t = int16 /* _int16_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int32_t = int32 /* _int32_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Int64_t = int64 /* _int64_t.h:30:33 */

// Copyright (c) 2016 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int8_t = uint8 /* _u_int8_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int16_t = uint16 /* _u_int16_t.h:30:41 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int32_t = uint32 /* _u_int32_t.h:30:33 */
// Copyright (c) 2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type U_int64_t = uint64 /* _u_int64_t.h:30:33 */

type Register_t = Int64_t /* types.h:87:33 */

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.

type Intptr_t = X__darwin_intptr_t /* _intptr_t.h:32:33 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
type Uintptr_t = uint64 /* _uintptr_t.h:30:33 */

// These types are used for reserving the largest possible size.
type User_addr_t = U_int64_t  /* types.h:97:33 */
type User_size_t = U_int64_t  /* types.h:98:33 */
type User_ssize_t = Int64_t   /* types.h:99:33 */
type User_long_t = Int64_t    /* types.h:100:33 */
type User_ulong_t = U_int64_t /* types.h:101:33 */
type User_time_t = Int64_t    /* types.h:102:33 */
type User_off_t = Int64_t     /* types.h:103:33 */

// This defines the size of syscall arguments after copying into the kernel:
type Syscall_arg_t = U_int64_t /* types.h:111:33 */

type Timespec = struct {
	Ftv_sec  X__darwin_time_t
	Ftv_nsec int64
} /* _timespec.h:33:1 */

// [XSI] The blkcnt_t, blksize_t, dev_t, ino_t, mode_t, nlink_t, uid_t,
// gid_t, off_t, and time_t types shall be defined as described in
// <sys/types.h>.
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Blkcnt_t = X__darwin_blkcnt_t /* _blkcnt_t.h:31:34 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Blksize_t = X__darwin_blksize_t /* _blksize_t.h:31:35 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Dev_t = X__darwin_dev_t /* _dev_t.h:31:31 */ // device number
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Ino_t = X__darwin_ino_t /* _ino_t.h:31:33 */ // inode number

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Ino64_t = X__darwin_ino64_t /* _ino64_t.h:31:33 */ // 64bit inode number

// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Mode_t = X__darwin_mode_t /* _mode_t.h:31:33 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
type Nlink_t = X__uint16_t /* _nlink_t.h:31:27 */ // link count
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Uid_t = X__darwin_uid_t /* _uid_t.h:31:31 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Gid_t = X__darwin_gid_t /* _gid_t.h:31:25 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2003-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@

type Off_t = X__darwin_off_t /* _off_t.h:31:33 */
// Copyright (c) 2003-2012 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright (c) 2000-2007 Apple Inc. All rights reserved.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_START@
//
// This file contains Original Code and/or Modifications of Original Code
// as defined in and that are subject to the Apple Public Source License
// Version 2.0 (the 'License'). You may not use this file except in
// compliance with the License. The rights granted to you under the License
// may not be used to create, or enable the creation or redistribution of,
// unlawful or unlicensed copies of an Apple operating system, or to
// circumvent, violate, or enable the circumvention or violation of, any
// terms of an Apple operating system software license agreement.
//
// Please obtain a copy of the License at
// http://www.opensource.apple.com/apsl/ and read it before using this file.
//
// The Original Code and all software distributed under the License are
// distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
// EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
// INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
// Please see the License for the specific language governing rights and
// limitations under the License.
//
// @APPLE_OSREFERENCE_LICENSE_HEADER_END@
// Copyright 1995 NeXT Computer, Inc. All rights reserved.
type Time_t = X__darwin_time_t /* _time_t.h:31:33 */

// XXX So deprecated, it would make your head spin
//
// The old stat structure.  In fact, this is not used by the kernel at all,
// and should not be used by user space, and should be removed from this
// header file entirely (along with the unused cvtstat() prototype in
// vnode_internal.h).
type Ostat = struct {
	Fst_dev       X__uint16_t
	F__ccgo_pad1  [6]byte
	Fst_ino       Ino_t
	Fst_mode      Mode_t
	Fst_nlink     Nlink_t
	Fst_uid       X__uint16_t
	Fst_gid       X__uint16_t
	Fst_rdev      X__uint16_t
	F__ccgo_pad2  [2]byte
	Fst_size      X__int32_t
	Fst_atimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_mtimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_ctimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_blksize X__int32_t
	Fst_blocks  X__int32_t
	Fst_flags   X__uint32_t
	Fst_gen     X__uint32_t
} /* stat.h:110:1 */

// This structure is used as the second parameter to the fstat64(),
// lstat64(), and stat64() functions, and for struct stat when
// __DARWIN_64_BIT_INO_T is set. __DARWIN_STRUCT_STAT64 is defined
// above, depending on whether we use struct timespec or the direct
// components.
//
// This is simillar to stat except for 64bit inode number
// number instead of 32bit ino_t and the addition of create(birth) time.

// [XSI] This structure is used as the second parameter to the fstat(),
// lstat(), and stat() functions.

type Stat = struct {
	Fst_dev       Dev_t
	Fst_mode      Mode_t
	Fst_nlink     Nlink_t
	Fst_ino       X__darwin_ino64_t
	Fst_uid       Uid_t
	Fst_gid       Gid_t
	Fst_rdev      Dev_t
	F__ccgo_pad1  [4]byte
	Fst_atimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_mtimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_ctimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_birthtimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_size    Off_t
	Fst_blocks  Blkcnt_t
	Fst_blksize Blksize_t
	Fst_flags   X__uint32_t
	Fst_gen     X__uint32_t
	Fst_lspare  X__int32_t
	Fst_qspare  [2]X__int64_t
} /* stat.h:182:1 */

type Stat64 = struct {
	Fst_dev       Dev_t
	Fst_mode      Mode_t
	Fst_nlink     Nlink_t
	Fst_ino       X__darwin_ino64_t
	Fst_uid       Uid_t
	Fst_gid       Gid_t
	Fst_rdev      Dev_t
	F__ccgo_pad1  [4]byte
	Fst_atimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_mtimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_ctimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_birthtimespec struct {
		Ftv_sec  X__darwin_time_t
		Ftv_nsec int64
	}
	Fst_size    Off_t
	Fst_blocks  Blkcnt_t
	Fst_blksize Blksize_t
	Fst_flags   X__uint32_t
	Fst_gen     X__uint32_t
	Fst_lspare  X__int32_t
	Fst_qspare  [2]X__int64_t
} /* stat.h:221:1 */

type Filesec_t = uintptr /* _filesec_t.h:31:25 */

var _ int8 /* gen.c:2:13: */
