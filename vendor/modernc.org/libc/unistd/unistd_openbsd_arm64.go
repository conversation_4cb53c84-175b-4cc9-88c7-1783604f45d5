// Code generated by 'ccgo unistd/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o unistd/unistd_openbsd_arm64.go -pkgname unistd', DO NOT EDIT.

package unistd

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BIG_ENDIAN                          = 4321   // endian.h:45:1:
	BYTE_ORDER                          = 1234   // endian.h:47:1:
	F_LOCK                              = 1      // unistd.h:48:1:
	F_OK                                = 0      // unistd.h:49:1:
	F_TEST                              = 3      // unistd.h:50:1:
	F_TLOCK                             = 2      // unistd.h:49:1:
	F_ULOCK                             = 0      // unistd.h:47:1:
	<PERSON>BIND_BLOCK_MAX                     = 2      // unistd.h:77:1:
	KBIND_DATA_MAX                      = 24     // unistd.h:78:1:
	LITTLE_ENDIAN                       = 1234   // endian.h:44:1:
	L_INCR                              = 1      // unistd.h:62:1:
	L_SET                               = 0      // unistd.h:61:1:
	L_XTND                              = 2      // unistd.h:63:1:
	PDP_ENDIAN                          = 3412   // endian.h:46:1:
	R_OK                                = 0x04   // unistd.h:52:1:
	SEEK_CUR                            = 1      // unistd.h:56:1:
	SEEK_END                            = 2      // unistd.h:57:1:
	SEEK_SET                            = 0      // unistd.h:55:1:
	STDERR_FILENO                       = 2      // unistd.h:44:1:
	STDIN_FILENO                        = 0      // unistd.h:42:1:
	STDOUT_FILENO                       = 1      // unistd.h:43:1:
	W_OK                                = 0x02   // unistd.h:51:1:
	X_OK                                = 0x01   // unistd.h:50:1:
	X_BIG_ENDIAN                        = 4321   // _endian.h:43:1:
	X_BYTE_ORDER                        = 1234   // endian.h:60:1:
	X_CLOCKID_T_DEFINED_                = 0      // types.h:162:1:
	X_CLOCK_T_DEFINED_                  = 0      // types.h:157:1:
	X_CS_PATH                           = 1      // unistd.h:286:1:
	X_CS_POSIX_V6_ILP32_OFF32_CFLAGS    = 2      // unistd.h:287:1:
	X_CS_POSIX_V6_ILP32_OFF32_LDFLAGS   = 3      // unistd.h:288:1:
	X_CS_POSIX_V6_ILP32_OFF32_LIBS      = 4      // unistd.h:289:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_CFLAGS   = 5      // unistd.h:290:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LDFLAGS  = 6      // unistd.h:291:1:
	X_CS_POSIX_V6_ILP32_OFFBIG_LIBS     = 7      // unistd.h:292:1:
	X_CS_POSIX_V6_LP64_OFF64_CFLAGS     = 8      // unistd.h:293:1:
	X_CS_POSIX_V6_LP64_OFF64_LDFLAGS    = 9      // unistd.h:294:1:
	X_CS_POSIX_V6_LP64_OFF64_LIBS       = 10     // unistd.h:295:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_CFLAGS   = 11     // unistd.h:296:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LDFLAGS  = 12     // unistd.h:297:1:
	X_CS_POSIX_V6_LPBIG_OFFBIG_LIBS     = 13     // unistd.h:298:1:
	X_CS_POSIX_V6_WIDTH_RESTRICTED_ENVS = 14     // unistd.h:299:1:
	X_CS_POSIX_V7_ILP32_OFF32_CFLAGS    = 16     // unistd.h:301:1:
	X_CS_POSIX_V7_ILP32_OFF32_LDFLAGS   = 17     // unistd.h:302:1:
	X_CS_POSIX_V7_ILP32_OFF32_LIBS      = 18     // unistd.h:303:1:
	X_CS_POSIX_V7_ILP32_OFFBIG_CFLAGS   = 19     // unistd.h:304:1:
	X_CS_POSIX_V7_ILP32_OFFBIG_LDFLAGS  = 20     // unistd.h:305:1:
	X_CS_POSIX_V7_ILP32_OFFBIG_LIBS     = 21     // unistd.h:306:1:
	X_CS_POSIX_V7_LP64_OFF64_CFLAGS     = 22     // unistd.h:307:1:
	X_CS_POSIX_V7_LP64_OFF64_LDFLAGS    = 23     // unistd.h:308:1:
	X_CS_POSIX_V7_LP64_OFF64_LIBS       = 24     // unistd.h:309:1:
	X_CS_POSIX_V7_LPBIG_OFFBIG_CFLAGS   = 25     // unistd.h:310:1:
	X_CS_POSIX_V7_LPBIG_OFFBIG_LDFLAGS  = 26     // unistd.h:311:1:
	X_CS_POSIX_V7_LPBIG_OFFBIG_LIBS     = 27     // unistd.h:312:1:
	X_CS_POSIX_V7_THREADS_CFLAGS        = 28     // unistd.h:313:1:
	X_CS_POSIX_V7_THREADS_LDFLAGS       = 29     // unistd.h:314:1:
	X_CS_POSIX_V7_WIDTH_RESTRICTED_ENVS = 30     // unistd.h:315:1:
	X_CS_V6_ENV                         = 15     // unistd.h:300:1:
	X_CS_V7_ENV                         = 31     // unistd.h:316:1:
	X_FILE_OFFSET_BITS                  = 64     // <builtin>:25:1:
	X_GETOPT_DEFINED_                   = 0      // unistd.h:385:1:
	X_INT16_T_DEFINED_                  = 0      // types.h:84:1:
	X_INT32_T_DEFINED_                  = 0      // types.h:94:1:
	X_INT64_T_DEFINED_                  = 0      // types.h:104:1:
	X_INT8_T_DEFINED_                   = 0      // types.h:74:1:
	X_INTPTR_T_DEFINED_                 = 0      // unistd.h:319:1:
	X_LITTLE_ENDIAN                     = 1234   // _endian.h:42:1:
	X_LP64                              = 1      // <predefined>:1:1:
	X_MACHINE_CDEFS_H_                  = 0      // cdefs.h:4:1:
	X_MACHINE_ENDIAN_H_                 = 0      // endian.h:20:1:
	X_MACHINE__TYPES_H_                 = 0      // _types.h:35:1:
	X_MAX_PAGE_SHIFT                    = 12     // _types.h:57:1:
	X_OFF_T_DEFINED_                    = 0      // types.h:192:1:
	X_PC_2_SYMLINKS                     = 10     // unistd.h:93:1:
	X_PC_ALLOC_SIZE_MIN                 = 11     // unistd.h:94:1:
	X_PC_ASYNC_IO                       = 12     // unistd.h:95:1:
	X_PC_CHOWN_RESTRICTED               = 7      // unistd.h:90:1:
	X_PC_FILESIZEBITS                   = 13     // unistd.h:96:1:
	X_PC_LINK_MAX                       = 1      // unistd.h:84:1:
	X_PC_MAX_CANON                      = 2      // unistd.h:85:1:
	X_PC_MAX_INPUT                      = 3      // unistd.h:86:1:
	X_PC_NAME_MAX                       = 4      // unistd.h:87:1:
	X_PC_NO_TRUNC                       = 8      // unistd.h:91:1:
	X_PC_PATH_MAX                       = 5      // unistd.h:88:1:
	X_PC_PIPE_BUF                       = 6      // unistd.h:89:1:
	X_PC_PRIO_IO                        = 14     // unistd.h:97:1:
	X_PC_REC_INCR_XFER_SIZE             = 15     // unistd.h:98:1:
	X_PC_REC_MAX_XFER_SIZE              = 16     // unistd.h:99:1:
	X_PC_REC_MIN_XFER_SIZE              = 17     // unistd.h:100:1:
	X_PC_REC_XFER_ALIGN                 = 18     // unistd.h:101:1:
	X_PC_SYMLINK_MAX                    = 19     // unistd.h:102:1:
	X_PC_SYNC_IO                        = 20     // unistd.h:103:1:
	X_PC_TIMESTAMP_RESOLUTION           = 21     // unistd.h:104:1:
	X_PC_VDISABLE                       = 9      // unistd.h:92:1:
	X_PDP_ENDIAN                        = 3412   // _endian.h:44:1:
	X_PID_T_DEFINED_                    = 0      // types.h:167:1:
	X_POSIX2_CHAR_TERM                  = 1      // unistd.h:116:1:
	X_POSIX2_C_BIND                     = 200112 // unistd.h:114:1:
	X_POSIX2_C_DEV                      = -1     // unistd.h:115:1:
	X_POSIX2_FORT_DEV                   = -1     // unistd.h:117:1:
	X_POSIX2_FORT_RUN                   = -1     // unistd.h:118:1:
	X_POSIX2_LOCALEDEF                  = -1     // unistd.h:119:1:
	X_POSIX2_PBS                        = -1     // unistd.h:120:1:
	X_POSIX2_PBS_ACCOUNTING             = -1     // unistd.h:121:1:
	X_POSIX2_PBS_CHECKPOINT             = -1     // unistd.h:122:1:
	X_POSIX2_PBS_LOCATE                 = -1     // unistd.h:123:1:
	X_POSIX2_PBS_MESSAGE                = -1     // unistd.h:124:1:
	X_POSIX2_PBS_TRACK                  = -1     // unistd.h:125:1:
	X_POSIX2_SW_DEV                     = 200112 // unistd.h:126:1:
	X_POSIX2_UPE                        = 200112 // unistd.h:127:1:
	X_POSIX2_VERSION                    = 200809 // unistd.h:148:1:
	X_POSIX_ADVISORY_INFO               = -1     // unistd.h:65:1:
	X_POSIX_ASYNCHRONOUS_IO             = -1     // unistd.h:66:1:
	X_POSIX_ASYNC_IO                    = -1     // unistd.h:41:1:
	X_POSIX_BARRIERS                    = 200112 // unistd.h:67:1:
	X_POSIX_CHOWN_RESTRICTED            = 1      // unistd.h:68:1:
	X_POSIX_CLOCK_SELECTION             = -1     // unistd.h:69:1:
	X_POSIX_CPUTIME                     = 200809 // unistd.h:70:1:
	X_POSIX_FSYNC                       = 200112 // unistd.h:71:1:
	X_POSIX_IPV6                        = 0      // unistd.h:72:1:
	X_POSIX_JOB_CONTROL                 = 1      // unistd.h:73:1:
	X_POSIX_MAPPED_FILES                = 200112 // unistd.h:74:1:
	X_POSIX_MEMLOCK                     = 200112 // unistd.h:75:1:
	X_POSIX_MEMLOCK_RANGE               = 200112 // unistd.h:76:1:
	X_POSIX_MEMORY_PROTECTION           = 200112 // unistd.h:77:1:
	X_POSIX_MESSAGE_PASSING             = -1     // unistd.h:78:1:
	X_POSIX_MONOTONIC_CLOCK             = 200112 // unistd.h:79:1:
	X_POSIX_NO_TRUNC                    = 1      // unistd.h:80:1:
	X_POSIX_PRIORITIZED_IO              = -1     // unistd.h:81:1:
	X_POSIX_PRIORITY_SCHEDULING         = -1     // unistd.h:82:1:
	X_POSIX_PRIO_IO                     = -1     // unistd.h:42:1:
	X_POSIX_RAW_SOCKETS                 = 200112 // unistd.h:83:1:
	X_POSIX_READER_WRITER_LOCKS         = 200112 // unistd.h:84:1:
	X_POSIX_REALTIME_SIGNALS            = -1     // unistd.h:85:1:
	X_POSIX_REGEXP                      = 1      // unistd.h:86:1:
	X_POSIX_SAVED_IDS                   = 1      // unistd.h:87:1:
	X_POSIX_SEMAPHORES                  = 200112 // unistd.h:88:1:
	X_POSIX_SHARED_MEMORY_OBJECTS       = 200809 // unistd.h:89:1:
	X_POSIX_SHELL                       = 1      // unistd.h:90:1:
	X_POSIX_SPAWN                       = 200112 // unistd.h:91:1:
	X_POSIX_SPIN_LOCKS                  = 200112 // unistd.h:92:1:
	X_POSIX_SPORADIC_SERVER             = -1     // unistd.h:93:1:
	X_POSIX_SYNCHRONIZED_IO             = -1     // unistd.h:94:1:
	X_POSIX_SYNC_IO                     = -1     // unistd.h:43:1:
	X_POSIX_THREADS                     = 200112 // unistd.h:106:1:
	X_POSIX_THREAD_ATTR_STACKADDR       = 200112 // unistd.h:95:1:
	X_POSIX_THREAD_ATTR_STACKSIZE       = 200112 // unistd.h:96:1:
	X_POSIX_THREAD_CPUTIME              = 200809 // unistd.h:97:1:
	X_POSIX_THREAD_PRIORITY_SCHEDULING  = -1     // unistd.h:100:1:
	X_POSIX_THREAD_PRIO_INHERIT         = -1     // unistd.h:98:1:
	X_POSIX_THREAD_PRIO_PROTECT         = -1     // unistd.h:99:1:
	X_POSIX_THREAD_PROCESS_SHARED       = -1     // unistd.h:101:1:
	X_POSIX_THREAD_ROBUST_PRIO_INHERIT  = -1     // unistd.h:102:1:
	X_POSIX_THREAD_ROBUST_PRIO_PROTECT  = -1     // unistd.h:103:1:
	X_POSIX_THREAD_SAFE_FUNCTIONS       = 200112 // unistd.h:104:1:
	X_POSIX_THREAD_SPORADIC_SERVER      = -1     // unistd.h:105:1:
	X_POSIX_TIMEOUTS                    = 200112 // unistd.h:107:1:
	X_POSIX_TIMERS                      = -1     // unistd.h:108:1:
	X_POSIX_TRACE                       = -1     // unistd.h:109:1:
	X_POSIX_TRACE_EVENT_FILTER          = -1     // unistd.h:110:1:
	X_POSIX_TRACE_INHERIT               = -1     // unistd.h:111:1:
	X_POSIX_TRACE_LOG                   = -1     // unistd.h:112:1:
	X_POSIX_TYPED_MEMORY_OBJECTS        = -1     // unistd.h:113:1:
	X_POSIX_V6_ILP32_OFF32              = -1     // unistd.h:128:1:
	X_POSIX_V6_ILP32_OFFBIG             = 0      // unistd.h:129:1:
	X_POSIX_V6_LP64_OFF64               = 0      // unistd.h:130:1:
	X_POSIX_V6_LPBIG_OFFBIG             = 0      // unistd.h:131:1:
	X_POSIX_V7_ILP32_OFF32              = -1     // unistd.h:132:1:
	X_POSIX_V7_ILP32_OFFBIG             = 0      // unistd.h:133:1:
	X_POSIX_V7_LP64_OFF64               = 0      // unistd.h:134:1:
	X_POSIX_V7_LPBIG_OFFBIG             = 0      // unistd.h:135:1:
	X_POSIX_VDISABLE                    = 255    // unistd.h:40:1:
	X_POSIX_VERSION                     = 200809 // unistd.h:46:1:
	X_QUAD_HIGHWORD                     = 1      // _endian.h:95:1:
	X_QUAD_LOWWORD                      = 0      // _endian.h:96:1:
	X_RET_PROTECTOR                     = 1      // <predefined>:2:1:
	X_SC_2_CHAR_TERM                    = 20     // unistd.h:172:1:
	X_SC_2_C_BIND                       = 18     // unistd.h:170:1:
	X_SC_2_C_DEV                        = 19     // unistd.h:171:1:
	X_SC_2_FORT_DEV                     = 21     // unistd.h:173:1:
	X_SC_2_FORT_RUN                     = 22     // unistd.h:174:1:
	X_SC_2_LOCALEDEF                    = 23     // unistd.h:175:1:
	X_SC_2_PBS                          = 35     // unistd.h:188:1:
	X_SC_2_PBS_ACCOUNTING               = 36     // unistd.h:189:1:
	X_SC_2_PBS_CHECKPOINT               = 37     // unistd.h:190:1:
	X_SC_2_PBS_LOCATE                   = 38     // unistd.h:191:1:
	X_SC_2_PBS_MESSAGE                  = 39     // unistd.h:192:1:
	X_SC_2_PBS_TRACK                    = 40     // unistd.h:193:1:
	X_SC_2_SW_DEV                       = 24     // unistd.h:176:1:
	X_SC_2_UPE                          = 25     // unistd.h:177:1:
	X_SC_2_VERSION                      = 17     // unistd.h:169:1:
	X_SC_ADVISORY_INFO                  = 41     // unistd.h:194:1:
	X_SC_AIO_LISTIO_MAX                 = 42     // unistd.h:195:1:
	X_SC_AIO_MAX                        = 43     // unistd.h:196:1:
	X_SC_AIO_PRIO_DELTA_MAX             = 44     // unistd.h:197:1:
	X_SC_ARG_MAX                        = 1      // unistd.h:153:1:
	X_SC_ASYNCHRONOUS_IO                = 45     // unistd.h:198:1:
	X_SC_ATEXIT_MAX                     = 46     // unistd.h:199:1:
	X_SC_AVPHYS_PAGES                   = 501    // unistd.h:281:1:
	X_SC_BARRIERS                       = 47     // unistd.h:200:1:
	X_SC_BC_BASE_MAX                    = 9      // unistd.h:161:1:
	X_SC_BC_DIM_MAX                     = 10     // unistd.h:162:1:
	X_SC_BC_SCALE_MAX                   = 11     // unistd.h:163:1:
	X_SC_BC_STRING_MAX                  = 12     // unistd.h:164:1:
	X_SC_CHILD_MAX                      = 2      // unistd.h:154:1:
	X_SC_CLK_TCK                        = 3      // unistd.h:155:1:
	X_SC_CLOCK_SELECTION                = 48     // unistd.h:201:1:
	X_SC_COLL_WEIGHTS_MAX               = 13     // unistd.h:165:1:
	X_SC_CPUTIME                        = 49     // unistd.h:202:1:
	X_SC_DELAYTIMER_MAX                 = 50     // unistd.h:203:1:
	X_SC_EXPR_NEST_MAX                  = 14     // unistd.h:166:1:
	X_SC_FSYNC                          = 29     // unistd.h:182:1:
	X_SC_GETGR_R_SIZE_MAX               = 100    // unistd.h:253:1:
	X_SC_GETPW_R_SIZE_MAX               = 101    // unistd.h:254:1:
	X_SC_HOST_NAME_MAX                  = 33     // unistd.h:186:1:
	X_SC_IOV_MAX                        = 51     // unistd.h:204:1:
	X_SC_IPV6                           = 52     // unistd.h:205:1:
	X_SC_JOB_CONTROL                    = 6      // unistd.h:158:1:
	X_SC_LINE_MAX                       = 15     // unistd.h:167:1:
	X_SC_LOGIN_NAME_MAX                 = 102    // unistd.h:255:1:
	X_SC_MAPPED_FILES                   = 53     // unistd.h:206:1:
	X_SC_MEMLOCK                        = 54     // unistd.h:207:1:
	X_SC_MEMLOCK_RANGE                  = 55     // unistd.h:208:1:
	X_SC_MEMORY_PROTECTION              = 56     // unistd.h:209:1:
	X_SC_MESSAGE_PASSING                = 57     // unistd.h:210:1:
	X_SC_MONOTONIC_CLOCK                = 34     // unistd.h:187:1:
	X_SC_MQ_OPEN_MAX                    = 58     // unistd.h:211:1:
	X_SC_MQ_PRIO_MAX                    = 59     // unistd.h:212:1:
	X_SC_NGROUPS_MAX                    = 4      // unistd.h:156:1:
	X_SC_NPROCESSORS_CONF               = 502    // unistd.h:282:1:
	X_SC_NPROCESSORS_ONLN               = 503    // unistd.h:283:1:
	X_SC_OPEN_MAX                       = 5      // unistd.h:157:1:
	X_SC_PAGESIZE                       = 28     // unistd.h:180:1:
	X_SC_PAGE_SIZE                      = 28     // unistd.h:181:1:
	X_SC_PHYS_PAGES                     = 500    // unistd.h:280:1:
	X_SC_PRIORITIZED_IO                 = 60     // unistd.h:213:1:
	X_SC_PRIORITY_SCHEDULING            = 61     // unistd.h:214:1:
	X_SC_RAW_SOCKETS                    = 62     // unistd.h:215:1:
	X_SC_READER_WRITER_LOCKS            = 63     // unistd.h:216:1:
	X_SC_REALTIME_SIGNALS               = 64     // unistd.h:217:1:
	X_SC_REGEXP                         = 65     // unistd.h:218:1:
	X_SC_RE_DUP_MAX                     = 16     // unistd.h:168:1:
	X_SC_RTSIG_MAX                      = 66     // unistd.h:219:1:
	X_SC_SAVED_IDS                      = 7      // unistd.h:159:1:
	X_SC_SEMAPHORES                     = 67     // unistd.h:220:1:
	X_SC_SEM_NSEMS_MAX                  = 31     // unistd.h:184:1:
	X_SC_SEM_VALUE_MAX                  = 32     // unistd.h:185:1:
	X_SC_SHARED_MEMORY_OBJECTS          = 68     // unistd.h:221:1:
	X_SC_SHELL                          = 69     // unistd.h:222:1:
	X_SC_SIGQUEUE_MAX                   = 70     // unistd.h:223:1:
	X_SC_SPAWN                          = 71     // unistd.h:224:1:
	X_SC_SPIN_LOCKS                     = 72     // unistd.h:225:1:
	X_SC_SPORADIC_SERVER                = 73     // unistd.h:226:1:
	X_SC_SS_REPL_MAX                    = 74     // unistd.h:227:1:
	X_SC_STREAM_MAX                     = 26     // unistd.h:178:1:
	X_SC_SYMLOOP_MAX                    = 76     // unistd.h:229:1:
	X_SC_SYNCHRONIZED_IO                = 75     // unistd.h:228:1:
	X_SC_THREADS                        = 91     // unistd.h:244:1:
	X_SC_THREAD_ATTR_STACKADDR          = 77     // unistd.h:230:1:
	X_SC_THREAD_ATTR_STACKSIZE          = 78     // unistd.h:231:1:
	X_SC_THREAD_CPUTIME                 = 79     // unistd.h:232:1:
	X_SC_THREAD_DESTRUCTOR_ITERATIONS   = 80     // unistd.h:233:1:
	X_SC_THREAD_KEYS_MAX                = 81     // unistd.h:234:1:
	X_SC_THREAD_PRIORITY_SCHEDULING     = 84     // unistd.h:237:1:
	X_SC_THREAD_PRIO_INHERIT            = 82     // unistd.h:235:1:
	X_SC_THREAD_PRIO_PROTECT            = 83     // unistd.h:236:1:
	X_SC_THREAD_PROCESS_SHARED          = 85     // unistd.h:238:1:
	X_SC_THREAD_ROBUST_PRIO_INHERIT     = 86     // unistd.h:239:1:
	X_SC_THREAD_ROBUST_PRIO_PROTECT     = 87     // unistd.h:240:1:
	X_SC_THREAD_SAFE_FUNCTIONS          = 103    // unistd.h:256:1:
	X_SC_THREAD_SPORADIC_SERVER         = 88     // unistd.h:241:1:
	X_SC_THREAD_STACK_MIN               = 89     // unistd.h:242:1:
	X_SC_THREAD_THREADS_MAX             = 90     // unistd.h:243:1:
	X_SC_TIMEOUTS                       = 92     // unistd.h:245:1:
	X_SC_TIMERS                         = 94     // unistd.h:247:1:
	X_SC_TIMER_MAX                      = 93     // unistd.h:246:1:
	X_SC_TRACE                          = 95     // unistd.h:248:1:
	X_SC_TRACE_EVENT_FILTER             = 96     // unistd.h:249:1:
	X_SC_TRACE_EVENT_NAME_MAX           = 97     // unistd.h:250:1:
	X_SC_TRACE_INHERIT                  = 98     // unistd.h:251:1:
	X_SC_TRACE_LOG                      = 99     // unistd.h:252:1:
	X_SC_TRACE_NAME_MAX                 = 104    // unistd.h:257:1:
	X_SC_TRACE_SYS_MAX                  = 105    // unistd.h:258:1:
	X_SC_TRACE_USER_EVENT_MAX           = 106    // unistd.h:259:1:
	X_SC_TTY_NAME_MAX                   = 107    // unistd.h:260:1:
	X_SC_TYPED_MEMORY_OBJECTS           = 108    // unistd.h:261:1:
	X_SC_TZNAME_MAX                     = 27     // unistd.h:179:1:
	X_SC_V6_ILP32_OFF32                 = 109    // unistd.h:262:1:
	X_SC_V6_ILP32_OFFBIG                = 110    // unistd.h:263:1:
	X_SC_V6_LP64_OFF64                  = 111    // unistd.h:264:1:
	X_SC_V6_LPBIG_OFFBIG                = 112    // unistd.h:265:1:
	X_SC_V7_ILP32_OFF32                 = 113    // unistd.h:266:1:
	X_SC_V7_ILP32_OFFBIG                = 114    // unistd.h:267:1:
	X_SC_V7_LP64_OFF64                  = 115    // unistd.h:268:1:
	X_SC_V7_LPBIG_OFFBIG                = 116    // unistd.h:269:1:
	X_SC_VERSION                        = 8      // unistd.h:160:1:
	X_SC_XOPEN_CRYPT                    = 117    // unistd.h:270:1:
	X_SC_XOPEN_ENH_I18N                 = 118    // unistd.h:271:1:
	X_SC_XOPEN_LEGACY                   = 119    // unistd.h:272:1:
	X_SC_XOPEN_REALTIME                 = 120    // unistd.h:273:1:
	X_SC_XOPEN_REALTIME_THREADS         = 121    // unistd.h:274:1:
	X_SC_XOPEN_SHM                      = 30     // unistd.h:183:1:
	X_SC_XOPEN_STREAMS                  = 122    // unistd.h:275:1:
	X_SC_XOPEN_UNIX                     = 123    // unistd.h:276:1:
	X_SC_XOPEN_UUCP                     = 124    // unistd.h:277:1:
	X_SC_XOPEN_VERSION                  = 125    // unistd.h:278:1:
	X_SIZE_T_DEFINED_                   = 0      // types.h:172:1:
	X_SSIZE_T_DEFINED_                  = 0      // types.h:177:1:
	X_STACKALIGNBYTES                   = 15     // _types.h:54:1:
	X_SYS_CDEFS_H_                      = 0      // cdefs.h:39:1:
	X_SYS_ENDIAN_H_                     = 0      // endian.h:38:1:
	X_SYS_TYPES_H_                      = 0      // types.h:41:1:
	X_SYS_UNISTD_H_                     = 0      // unistd.h:36:1:
	X_SYS__ENDIAN_H_                    = 0      // _endian.h:34:1:
	X_SYS__TYPES_H_                     = 0      // _types.h:35:1:
	X_TIMER_T_DEFINED_                  = 0      // types.h:187:1:
	X_TIME_T_DEFINED_                   = 0      // types.h:182:1:
	X_UINT16_T_DEFINED_                 = 0      // types.h:89:1:
	X_UINT32_T_DEFINED_                 = 0      // types.h:99:1:
	X_UINT64_T_DEFINED_                 = 0      // types.h:109:1:
	X_UINT8_T_DEFINED_                  = 0      // types.h:79:1:
	X_UNISTD_H_                         = 0      // unistd.h:36:1:
	X_XOPEN_CRYPT                       = 1      // unistd.h:137:1:
	X_XOPEN_ENH_I18N                    = -1     // unistd.h:138:1:
	X_XOPEN_LEGACY                      = -1     // unistd.h:139:1:
	X_XOPEN_REALTIME                    = -1     // unistd.h:140:1:
	X_XOPEN_REALTIME_THREADS            = -1     // unistd.h:141:1:
	X_XOPEN_SHM                         = 1      // unistd.h:142:1:
	X_XOPEN_STREAMS                     = -1     // unistd.h:143:1:
	X_XOPEN_UNIX                        = -1     // unistd.h:145:1:
	X_XOPEN_UUCP                        = -1     // unistd.h:144:1:
	Unix                                = 1      // <predefined>:360:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$OpenBSD: unistd.h,v 1.106 2018/07/13 09:25:22 beck Exp $
//	$NetBSD: unistd.h,v 1.26.4.1 1996/05/28 02:31:51 mrg Exp $

// -
// Copyright (c) 1991 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)unistd.h	5.13 (Berkeley) 6/17/91

//	$OpenBSD: _null.h,v 1.2 2016/09/09 22:07:58 millert Exp $

// Written by Todd C. Miller, September 9, 2016
// Public domain.

//	$OpenBSD: types.h,v 1.49 2022/08/06 13:31:13 semarie Exp $
//	$NetBSD: types.h,v 1.29 1996/11/15 22:48:25 jtc Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.1 2016/12/17 23:38:33 patrick Exp $

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  Do not use __CONCAT on double-quoted strings,
// such as those from the __STRING macro: to concatenate strings just put
// them next to each other.

// GCC1 and some versions of GCC2 declare dead (non-returning) and
// pure (no side effects) functions using "volatile" and "const";
// unfortunately, these then cause warnings under "-ansi -pedantic".
// GCC >= 2.5 uses the __attribute__((attrs)) style.  All of these
// work for GNU C++ (modulo a slight glitch in the C++ grammar in
// the distribution version of 2.5.5).

// __returns_twice makes the compiler not assume the function
// only returns once.  This affects registerisation of variables:
// even local variables need to be in memory across such a call.
// Example: setjmp()

// __only_inline makes the compiler only use this function definition
// for inlining; references that can't be inlined will be left as
// external references instead of generating a local copy.  The
// matching library should include a simple extern definition for
// the function to handle those references.  c.f. ctype.h

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// Delete pseudo-keywords wherever they are not available or needed.

// The __packed macro indicates that a variable or structure members
// should have the smallest possible alignment, despite any host CPU
// alignment requirements.
//
// The __aligned(x) macro specifies the minimum alignment of a
// variable or structure.
//
// These macros together are useful for describing the layout and
// alignment of messages exchanged with hardware or other systems.

// "The nice thing about standards is that there are so many to choose from."
// There are a number of "feature test macros" specified by (different)
// standards that determine which interfaces and types the header files
// should expose.
//
// Because of inconsistencies in these macros, we define our own
// set in the private name space that end in _VISIBLE.  These are
// always defined and so headers can test their values easily.
// Things can get tricky when multiple feature macros are defined.
// We try to take the union of all the features requested.
//
// The following macros are guaranteed to have a value after cdefs.h
// has been included:
//	__POSIX_VISIBLE
//	__XPG_VISIBLE
//	__ISO_C_VISIBLE
//	__BSD_VISIBLE

// X/Open Portability Guides and Single Unix Specifications.
// _XOPEN_SOURCE				XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4		XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1	XPG4v2
// _XOPEN_SOURCE == 500				XPG5
// _XOPEN_SOURCE == 520				XPG5v2
// _XOPEN_SOURCE == 600				POSIX 1003.1-2001 with XSI
// _XOPEN_SOURCE == 700				POSIX 1003.1-2008 with XSI
//
// The XPG spec implies a specific value for _POSIX_C_SOURCE.

// POSIX macros, these checks must follow the XOPEN ones above.
//
// _POSIX_SOURCE == 1		1003.1-1988 (superseded by _POSIX_C_SOURCE)
// _POSIX_C_SOURCE == 1		1003.1-1990
// _POSIX_C_SOURCE == 2		1003.2-1992
// _POSIX_C_SOURCE == 199309L	1003.1b-1993
// _POSIX_C_SOURCE == 199506L   1003.1c-1995, 1003.1i-1995,
//				and the omnibus ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L   1003.1-2001
// _POSIX_C_SOURCE == 200809L   1003.1-2008
//
// The POSIX spec implies a specific value for __ISO_C_VISIBLE, though
// this may be overridden by the _ISOC99_SOURCE macro later.

// _ANSI_SOURCE means to expose ANSI C89 interfaces only.
// If the user defines it in addition to one of the POSIX or XOPEN
// macros, assume the POSIX/XOPEN macro(s) should take precedence.

// _ISOC99_SOURCE, _ISOC11_SOURCE, __STDC_VERSION__, and __cplusplus
// override any of the other macros since they are non-exclusive.

// Finally deal with BSD-specific interfaces that are not covered
// by any standards.  We expose these when none of the POSIX or XPG
// macros is defined or if the user explicitly asks for them.

// Default values.

//	$OpenBSD: endian.h,v 1.25 2014/12/21 04:49:00 guenther Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Public definitions for little- and big-endian systems.
// This file should be included as <endian.h> in userspace and as
// <sys/endian.h> in the kernel.
//
// System headers that need endian information but that can't or don't
// want to export the public names here should include <sys/_endian.h>
// and use the internal names: _BYTE_ORDER, _*_ENDIAN, etc.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: _endian.h,v 1.8 2018/01/11 23:13:37 dlg Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Internal endianness macros.  This pulls in <machine/endian.h> to
// get the correct setting direction for the platform and sets internal
// ('__' prefix) macros appropriately.

//	$OpenBSD: _types.h,v 1.10 2022/08/06 13:31:13 semarie Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

// $OpenBSD: _types.h,v 1.4 2018/03/05 01:15:25 deraadt Exp $
// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94
//	@(#)ansi.h	8.2 (Berkeley) 1/4/94

// _ALIGN(p) rounds p (pointer or byte index) up to a correctly-aligned
// value for all data types (int, long, ...).   The result is an
// unsigned long and must be cast to any desired pointer type.
//
// _ALIGNED_POINTER is a boolean macro that checks whether an address
// is valid to fetch data elements of type t from on this architecture.
// This does not reflect the optimal alignment, just the possibility
// (within reasonable limits).

// ******** Exact-width integer types
type X__int8_t = int8     /* _types.h:60:22 */
type X__uint8_t = uint8   /* _types.h:61:24 */
type X__int16_t = int16   /* _types.h:62:17 */
type X__uint16_t = uint16 /* _types.h:63:25 */
type X__int32_t = int32   /* _types.h:64:15 */
type X__uint32_t = uint32 /* _types.h:65:23 */
// LONGLONG
type X__int64_t = int64 /* _types.h:67:20 */
// LONGLONG
type X__uint64_t = uint64 /* _types.h:69:28 */

// ******** Minimum-width integer types
type X__int_least8_t = X__int8_t     /* _types.h:72:19 */
type X__uint_least8_t = X__uint8_t   /* _types.h:73:20 */
type X__int_least16_t = X__int16_t   /* _types.h:74:20 */
type X__uint_least16_t = X__uint16_t /* _types.h:75:21 */
type X__int_least32_t = X__int32_t   /* _types.h:76:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:77:21 */
type X__int_least64_t = X__int64_t   /* _types.h:78:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:79:21 */

// 7.18.1.3 Fastest minimum-width integer types
type X__int_fast8_t = X__int32_t    /* _types.h:82:20 */
type X__uint_fast8_t = X__uint32_t  /* _types.h:83:21 */
type X__int_fast16_t = X__int32_t   /* _types.h:84:20 */
type X__uint_fast16_t = X__uint32_t /* _types.h:85:21 */
type X__int_fast32_t = X__int32_t   /* _types.h:86:20 */
type X__uint_fast32_t = X__uint32_t /* _types.h:87:21 */
type X__int_fast64_t = X__int64_t   /* _types.h:88:20 */
type X__uint_fast64_t = X__uint64_t /* _types.h:89:21 */

// 7.18.1.4 Integer types capable of holding object pointers
type X__intptr_t = int64   /* _types.h:104:16 */
type X__uintptr_t = uint64 /* _types.h:105:24 */

// 7.18.1.5 Greatest-width integer types
type X__intmax_t = X__int64_t   /* _types.h:108:20 */
type X__uintmax_t = X__uint64_t /* _types.h:109:21 */

// Register size
type X__register_t = int64 /* _types.h:112:16 */

// VM system types
type X__vaddr_t = uint64 /* _types.h:115:24 */
type X__paddr_t = uint64 /* _types.h:116:24 */
type X__vsize_t = uint64 /* _types.h:117:24 */
type X__psize_t = uint64 /* _types.h:118:24 */

// Standard system types
type X__double_t = float64           /* _types.h:121:18 */
type X__float_t = float32            /* _types.h:122:17 */
type X__ptrdiff_t = int64            /* _types.h:123:16 */
type X__size_t = uint64              /* _types.h:124:24 */
type X__ssize_t = int64              /* _types.h:125:16 */
type X__va_list = X__builtin_va_list /* _types.h:127:27 */

// Wide character support types
type X__wchar_t = int32     /* _types.h:137:15 */
type X__wint_t = int32      /* _types.h:140:15 */
type X__rune_t = int32      /* _types.h:141:15 */
type X__wctrans_t = uintptr /* _types.h:142:14 */
type X__wctype_t = uintptr  /* _types.h:143:14 */

type X__blkcnt_t = X__int64_t    /* _types.h:39:19 */ // blocks allocated for file
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // optimal blocksize for I/O
type X__clock_t = X__int64_t     /* _types.h:41:19 */ // ticks in CLOCKS_PER_SEC
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // CLOCK_* identifiers
type X__cpuid_t = uint64         /* _types.h:43:23 */ // CPU id
type X__dev_t = X__int32_t       /* _types.h:44:19 */ // device number
type X__fixpt_t = X__uint32_t    /* _types.h:45:20 */ // fixed point number
type X__fsblkcnt_t = X__uint64_t /* _types.h:46:20 */ // file system block count
type X__fsfilcnt_t = X__uint64_t /* _types.h:47:20 */ // file system file count
type X__gid_t = X__uint32_t      /* _types.h:48:20 */ // group id
type X__id_t = X__uint32_t       /* _types.h:49:20 */ // may contain pid, uid or gid
type X__in_addr_t = X__uint32_t  /* _types.h:50:20 */ // base type for internet address
type X__in_port_t = X__uint16_t  /* _types.h:51:20 */ // IP port type
type X__ino_t = X__uint64_t      /* _types.h:52:20 */ // inode number
type X__key_t = int64            /* _types.h:53:15 */ // IPC key (for Sys V IPC)
type X__mode_t = X__uint32_t     /* _types.h:54:20 */ // permissions
type X__nlink_t = X__uint32_t    /* _types.h:55:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:56:19 */ // file offset or size
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process id
type X__rlim_t = X__uint64_t     /* _types.h:58:20 */ // resource limit
type X__sa_family_t = X__uint8_t /* _types.h:59:19 */ // sockaddr address family type
type X__segsz_t = X__int32_t     /* _types.h:60:19 */ // segment size
type X__socklen_t = X__uint32_t  /* _types.h:61:20 */ // length type for network syscalls
type X__suseconds_t = int64      /* _types.h:62:15 */ // microseconds (signed)
type X__time_t = X__int64_t      /* _types.h:63:19 */ // epoch time
type X__timer_t = X__int32_t     /* _types.h:64:19 */ // POSIX timer identifiers
type X__uid_t = X__uint32_t      /* _types.h:65:20 */ // user id
type X__useconds_t = X__uint32_t /* _types.h:66:20 */ // microseconds

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions. The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]int8
} /* _types.h:75:3 */

// Tell sys/endian.h we have MD variants of the swap macros.

// Note that these macros evaluate their arguments several times.

// Public names

// These are specified to be function-like macros to match the spec

// POSIX names

// original BSD names

// these were exposed here before

// ancient stuff

type U_char = uint8   /* types.h:51:23 */
type U_short = uint16 /* types.h:52:24 */
type U_int = uint32   /* types.h:53:22 */
type U_long = uint64  /* types.h:54:23 */

type Unchar = uint8  /* types.h:56:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:57:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:58:22 */ // Sys V compatibility
type Ulong = uint64  /* types.h:59:23 */ // Sys V compatibility

type Cpuid_t = X__cpuid_t       /* types.h:61:19 */ // CPU id
type Register_t = X__register_t /* types.h:62:22 */ // register-sized type

// XXX The exact-width bit types should only be exposed if __BSD_VISIBLE
//     but the rest of the includes are not ready for that yet.

type Int8_t = X__int8_t /* types.h:75:19 */

type Uint8_t = X__uint8_t /* types.h:80:20 */

type Int16_t = X__int16_t /* types.h:85:20 */

type Uint16_t = X__uint16_t /* types.h:90:21 */

type Int32_t = X__int32_t /* types.h:95:20 */

type Uint32_t = X__uint32_t /* types.h:100:21 */

type Int64_t = X__int64_t /* types.h:105:20 */

type Uint64_t = X__uint64_t /* types.h:110:21 */

// BSD-style unsigned bits types
type U_int8_t = X__uint8_t   /* types.h:114:19 */
type U_int16_t = X__uint16_t /* types.h:115:20 */
type U_int32_t = X__uint32_t /* types.h:116:20 */
type U_int64_t = X__uint64_t /* types.h:117:20 */

// quads, deprecated in favor of 64 bit int types
type Quad_t = X__int64_t    /* types.h:120:19 */
type U_quad_t = X__uint64_t /* types.h:121:20 */

// VM system types
type Vaddr_t = X__vaddr_t /* types.h:125:19 */
type Paddr_t = X__paddr_t /* types.h:126:19 */
type Vsize_t = X__vsize_t /* types.h:127:19 */
type Psize_t = X__psize_t /* types.h:128:19 */

// Standard system types
type Blkcnt_t = X__blkcnt_t       /* types.h:132:20 */ // blocks allocated for file
type Blksize_t = X__blksize_t     /* types.h:133:21 */ // optimal blocksize for I/O
type Caddr_t = uintptr            /* types.h:134:14 */ // core address
type Daddr32_t = X__int32_t       /* types.h:135:19 */ // 32-bit disk address
type Daddr_t = X__int64_t         /* types.h:136:19 */ // 64-bit disk address
type Dev_t = X__dev_t             /* types.h:137:18 */ // device number
type Fixpt_t = X__fixpt_t         /* types.h:138:19 */ // fixed point number
type Gid_t = X__gid_t             /* types.h:139:18 */ // group id
type Id_t = X__id_t               /* types.h:140:17 */ // may contain pid, uid or gid
type Ino_t = X__ino_t             /* types.h:141:18 */ // inode number
type Key_t = X__key_t             /* types.h:142:18 */ // IPC key (for Sys V IPC)
type Mode_t = X__mode_t           /* types.h:143:18 */ // permissions
type Nlink_t = X__nlink_t         /* types.h:144:19 */ // link count
type Rlim_t = X__rlim_t           /* types.h:145:18 */ // resource limit
type Segsz_t = X__segsz_t         /* types.h:146:19 */ // segment size
type Uid_t = X__uid_t             /* types.h:147:18 */ // user id
type Useconds_t = X__useconds_t   /* types.h:148:22 */ // microseconds
type Suseconds_t = X__suseconds_t /* types.h:149:23 */ // microseconds (signed)
type Fsblkcnt_t = X__fsblkcnt_t   /* types.h:150:22 */ // file system block count
type Fsfilcnt_t = X__fsfilcnt_t   /* types.h:151:22 */ // file system file count

// The following types may be defined in multiple header files.
type Clock_t = X__clock_t /* types.h:158:19 */

type Clockid_t = X__clockid_t /* types.h:163:21 */

type Pid_t = X__pid_t /* types.h:168:18 */

type Ssize_t = X__ssize_t /* types.h:178:19 */

type Time_t = X__time_t /* types.h:183:18 */

type Timer_t = X__timer_t /* types.h:188:19 */

type Off_t = X__off_t /* types.h:193:18 */

// Major, minor numbers, dev_t's.

//	$OpenBSD: unistd.h,v 1.31 2015/07/20 00:56:10 guenther Exp $
//	$NetBSD: unistd.h,v 1.10 1994/06/29 06:46:06 cgd Exp $

// Copyright (c) 1989, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)unistd.h	8.2 (Berkeley) 1/7/94

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

// Define the POSIX.1 version we target for compliance.

// access function

// whence values for lseek(2)

// old BSD whence values for lseek(2); renamed by POSIX 1003.1

// the parameters argument passed to the __tfork() syscall
type X__tfork = struct {
	Ftf_tcb   uintptr
	Ftf_tid   uintptr
	Ftf_stack uintptr
} /* unistd.h:66:1 */

// the parameters argument for the kbind() syscall
type X__kbind = struct {
	Fkb_addr uintptr
	Fkb_size Size_t
} /* unistd.h:73:1 */

// the pathconf(2) variable values are part of the ABI

// configurable pathname variables

// POSIX options and option groups we unconditionally do or don't
// implement.  Please keep this list in alphabetical order.
//
// Anything which is defined as zero below **must** have an
// implementation for the corresponding sysconf() which is able to
// determine conclusively whether or not the feature is supported.
// Anything which is defined as other than -1 below **must** have
// complete headers, types, and function declarations as specified by
// the POSIX standard; however, if the relevant sysconf() function
// returns -1, the functions may be stubbed out.

// Define the POSIX.2 version we target for compliance.

// the sysconf(3) variable values are part of the ABI

// configurable system variables

// configurable system strings

type Intptr_t = X__intptr_t /* unistd.h:320:21 */

var _ int8 /* gen.c:2:13: */
