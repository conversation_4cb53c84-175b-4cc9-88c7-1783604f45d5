// Code generated by 'ccgo unistd/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -o unistd/unistd_linux_riscv64.go -pkgname unistd', DO NOT EDIT.

package unistd

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	F_LOCK                             = 1
	F_OK                               = 0
	F_TEST                             = 3
	F_TLOCK                            = 2
	F_ULOCK                            = 0
	L_INCR                             = 1
	L_SET                              = 0
	L_XTND                             = 2
	R_OK                               = 4
	SEEK_CUR                           = 1
	SEEK_END                           = 2
	SEEK_SET                           = 0
	STDERR_FILENO                      = 2
	STDIN_FILENO                       = 0
	STDOUT_FILENO                      = 1
	W_OK                               = 2
	X_OK                               = 1
	X_ATFILE_SOURCE                    = 1
	X_BITS_POSIX_OPT_H                 = 1
	X_BITS_TIME64_H                    = 1
	X_BITS_TYPESIZES_H                 = 1
	X_BITS_TYPES_H                     = 1
	X_BSD_SIZE_T_                      = 0
	X_BSD_SIZE_T_DEFINED_              = 0
	X_DEFAULT_SOURCE                   = 1
	X_FEATURES_H                       = 1
	X_FILE_OFFSET_BITS                 = 64
	X_GCC_SIZE_T                       = 0
	X_GETOPT_CORE_H                    = 1
	X_GETOPT_POSIX_H                   = 1
	X_LFS64_ASYNCHRONOUS_IO            = 1
	X_LFS64_LARGEFILE                  = 1
	X_LFS64_STDIO                      = 1
	X_LFS_ASYNCHRONOUS_IO              = 1
	X_LFS_LARGEFILE                    = 1
	X_LP64                             = 1
	X_POSIX2_CHAR_TERM                 = 200809
	X_POSIX2_C_BIND                    = 200809
	X_POSIX2_C_DEV                     = 200809
	X_POSIX2_C_VERSION                 = 200809
	X_POSIX2_LOCALEDEF                 = 200809
	X_POSIX2_SW_DEV                    = 200809
	X_POSIX2_VERSION                   = 200809
	X_POSIX_ADVISORY_INFO              = 200809
	X_POSIX_ASYNCHRONOUS_IO            = 200809
	X_POSIX_ASYNC_IO                   = 1
	X_POSIX_BARRIERS                   = 200809
	X_POSIX_CHOWN_RESTRICTED           = 0
	X_POSIX_CLOCK_SELECTION            = 200809
	X_POSIX_CPUTIME                    = 0
	X_POSIX_C_SOURCE                   = 200809
	X_POSIX_FSYNC                      = 200809
	X_POSIX_IPV6                       = 200809
	X_POSIX_JOB_CONTROL                = 1
	X_POSIX_MAPPED_FILES               = 200809
	X_POSIX_MEMLOCK                    = 200809
	X_POSIX_MEMLOCK_RANGE              = 200809
	X_POSIX_MEMORY_PROTECTION          = 200809
	X_POSIX_MESSAGE_PASSING            = 200809
	X_POSIX_MONOTONIC_CLOCK            = 0
	X_POSIX_NO_TRUNC                   = 1
	X_POSIX_PRIORITIZED_IO             = 200809
	X_POSIX_PRIORITY_SCHEDULING        = 200809
	X_POSIX_RAW_SOCKETS                = 200809
	X_POSIX_READER_WRITER_LOCKS        = 200809
	X_POSIX_REALTIME_SIGNALS           = 200809
	X_POSIX_REENTRANT_FUNCTIONS        = 1
	X_POSIX_REGEXP                     = 1
	X_POSIX_SAVED_IDS                  = 1
	X_POSIX_SEMAPHORES                 = 200809
	X_POSIX_SHARED_MEMORY_OBJECTS      = 200809
	X_POSIX_SHELL                      = 1
	X_POSIX_SOURCE                     = 1
	X_POSIX_SPAWN                      = 200809
	X_POSIX_SPIN_LOCKS                 = 200809
	X_POSIX_SPORADIC_SERVER            = -1
	X_POSIX_SYNCHRONIZED_IO            = 200809
	X_POSIX_THREADS                    = 200809
	X_POSIX_THREAD_ATTR_STACKADDR      = 200809
	X_POSIX_THREAD_ATTR_STACKSIZE      = 200809
	X_POSIX_THREAD_CPUTIME             = 0
	X_POSIX_THREAD_PRIORITY_SCHEDULING = 200809
	X_POSIX_THREAD_PRIO_INHERIT        = 200809
	X_POSIX_THREAD_PRIO_PROTECT        = 200809
	X_POSIX_THREAD_PROCESS_SHARED      = 200809
	X_POSIX_THREAD_ROBUST_PRIO_INHERIT = 200809
	X_POSIX_THREAD_ROBUST_PRIO_PROTECT = -1
	X_POSIX_THREAD_SAFE_FUNCTIONS      = 200809
	X_POSIX_THREAD_SPORADIC_SERVER     = -1
	X_POSIX_TIMEOUTS                   = 200809
	X_POSIX_TIMERS                     = 200809
	X_POSIX_TRACE                      = -1
	X_POSIX_TRACE_EVENT_FILTER         = -1
	X_POSIX_TRACE_INHERIT              = -1
	X_POSIX_TRACE_LOG                  = -1
	X_POSIX_TYPED_MEMORY_OBJECTS       = -1
	X_POSIX_V6_ILP32_OFF32             = -1
	X_POSIX_V6_ILP32_OFFBIG            = -1
	X_POSIX_V6_LP64_OFF64              = 1
	X_POSIX_V6_LPBIG_OFFBIG            = -1
	X_POSIX_V7_ILP32_OFF32             = -1
	X_POSIX_V7_ILP32_OFFBIG            = -1
	X_POSIX_V7_LP64_OFF64              = 1
	X_POSIX_V7_LPBIG_OFFBIG            = -1
	X_POSIX_VDISABLE                   = 0
	X_POSIX_VERSION                    = 200809
	X_SIZET_                           = 0
	X_SIZE_T                           = 0
	X_SIZE_T_                          = 0
	X_SIZE_T_DECLARED                  = 0
	X_SIZE_T_DEFINED                   = 0
	X_SIZE_T_DEFINED_                  = 0
	X_STDC_PREDEF_H                    = 1
	X_SYS_CDEFS_H                      = 1
	X_SYS_SIZE_T_H                     = 0
	X_T_SIZE                           = 0
	X_T_SIZE_                          = 0
	X_UNISTD_H                         = 1
	X_XBS5_ILP32_OFF32                 = -1
	X_XBS5_ILP32_OFFBIG                = -1
	X_XBS5_LP64_OFF64                  = 1
	X_XBS5_LPBIG_OFFBIG                = -1
	X_XOPEN_ENH_I18N                   = 1
	X_XOPEN_LEGACY                     = 1
	X_XOPEN_REALTIME                   = 1
	X_XOPEN_REALTIME_THREADS           = 1
	X_XOPEN_SHM                        = 1
	X_XOPEN_UNIX                       = 1
	X_XOPEN_VERSION                    = 700
	X_XOPEN_XCU_VERSION                = 4
	X_XOPEN_XPG2                       = 1
	X_XOPEN_XPG3                       = 1
	X_XOPEN_XPG4                       = 1
	Linux                              = 1
	Unix                               = 1
)

// Get the `_PC_*' symbols for the NAME argument to `pathconf' and `fpathconf';
//    the `_SC_*' symbols for the NAME argument to `sysconf';
//    and the `_CS_*' symbols for the NAME argument to `confstr'.
// `sysconf', `pathconf', and `confstr' NAME values.  Generic version.
//    Copyright (C) 1993-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Values for the NAME argument to `pathconf' and `fpathconf'.
const ( /* confname.h:24:1: */
	X_PC_LINK_MAX           = 0
	X_PC_MAX_CANON          = 1
	X_PC_MAX_INPUT          = 2
	X_PC_NAME_MAX           = 3
	X_PC_PATH_MAX           = 4
	X_PC_PIPE_BUF           = 5
	X_PC_CHOWN_RESTRICTED   = 6
	X_PC_NO_TRUNC           = 7
	X_PC_VDISABLE           = 8
	X_PC_SYNC_IO            = 9
	X_PC_ASYNC_IO           = 10
	X_PC_PRIO_IO            = 11
	X_PC_SOCK_MAXBUF        = 12
	X_PC_FILESIZEBITS       = 13
	X_PC_REC_INCR_XFER_SIZE = 14
	X_PC_REC_MAX_XFER_SIZE  = 15
	X_PC_REC_MIN_XFER_SIZE  = 16
	X_PC_REC_XFER_ALIGN     = 17
	X_PC_ALLOC_SIZE_MIN     = 18
	X_PC_SYMLINK_MAX        = 19
	X_PC_2_SYMLINKS         = 20
)

// Values for the NAME argument to `confstr'.
const ( /* confname.h:539:1: */
	X_CS_PATH = 0 // The default search path.

	X_CS_V6_WIDTH_RESTRICTED_ENVS = 1

	X_CS_GNU_LIBC_VERSION       = 2
	X_CS_GNU_LIBPTHREAD_VERSION = 3

	X_CS_V5_WIDTH_RESTRICTED_ENVS = 4

	X_CS_V7_WIDTH_RESTRICTED_ENVS = 5

	X_CS_LFS_CFLAGS      = 1000
	X_CS_LFS_LDFLAGS     = 1001
	X_CS_LFS_LIBS        = 1002
	X_CS_LFS_LINTFLAGS   = 1003
	X_CS_LFS64_CFLAGS    = 1004
	X_CS_LFS64_LDFLAGS   = 1005
	X_CS_LFS64_LIBS      = 1006
	X_CS_LFS64_LINTFLAGS = 1007

	X_CS_XBS5_ILP32_OFF32_CFLAGS     = 1100
	X_CS_XBS5_ILP32_OFF32_LDFLAGS    = 1101
	X_CS_XBS5_ILP32_OFF32_LIBS       = 1102
	X_CS_XBS5_ILP32_OFF32_LINTFLAGS  = 1103
	X_CS_XBS5_ILP32_OFFBIG_CFLAGS    = 1104
	X_CS_XBS5_ILP32_OFFBIG_LDFLAGS   = 1105
	X_CS_XBS5_ILP32_OFFBIG_LIBS      = 1106
	X_CS_XBS5_ILP32_OFFBIG_LINTFLAGS = 1107
	X_CS_XBS5_LP64_OFF64_CFLAGS      = 1108
	X_CS_XBS5_LP64_OFF64_LDFLAGS     = 1109
	X_CS_XBS5_LP64_OFF64_LIBS        = 1110
	X_CS_XBS5_LP64_OFF64_LINTFLAGS   = 1111
	X_CS_XBS5_LPBIG_OFFBIG_CFLAGS    = 1112
	X_CS_XBS5_LPBIG_OFFBIG_LDFLAGS   = 1113
	X_CS_XBS5_LPBIG_OFFBIG_LIBS      = 1114
	X_CS_XBS5_LPBIG_OFFBIG_LINTFLAGS = 1115

	X_CS_POSIX_V6_ILP32_OFF32_CFLAGS     = 1116
	X_CS_POSIX_V6_ILP32_OFF32_LDFLAGS    = 1117
	X_CS_POSIX_V6_ILP32_OFF32_LIBS       = 1118
	X_CS_POSIX_V6_ILP32_OFF32_LINTFLAGS  = 1119
	X_CS_POSIX_V6_ILP32_OFFBIG_CFLAGS    = 1120
	X_CS_POSIX_V6_ILP32_OFFBIG_LDFLAGS   = 1121
	X_CS_POSIX_V6_ILP32_OFFBIG_LIBS      = 1122
	X_CS_POSIX_V6_ILP32_OFFBIG_LINTFLAGS = 1123
	X_CS_POSIX_V6_LP64_OFF64_CFLAGS      = 1124
	X_CS_POSIX_V6_LP64_OFF64_LDFLAGS     = 1125
	X_CS_POSIX_V6_LP64_OFF64_LIBS        = 1126
	X_CS_POSIX_V6_LP64_OFF64_LINTFLAGS   = 1127
	X_CS_POSIX_V6_LPBIG_OFFBIG_CFLAGS    = 1128
	X_CS_POSIX_V6_LPBIG_OFFBIG_LDFLAGS   = 1129
	X_CS_POSIX_V6_LPBIG_OFFBIG_LIBS      = 1130
	X_CS_POSIX_V6_LPBIG_OFFBIG_LINTFLAGS = 1131

	X_CS_POSIX_V7_ILP32_OFF32_CFLAGS     = 1132
	X_CS_POSIX_V7_ILP32_OFF32_LDFLAGS    = 1133
	X_CS_POSIX_V7_ILP32_OFF32_LIBS       = 1134
	X_CS_POSIX_V7_ILP32_OFF32_LINTFLAGS  = 1135
	X_CS_POSIX_V7_ILP32_OFFBIG_CFLAGS    = 1136
	X_CS_POSIX_V7_ILP32_OFFBIG_LDFLAGS   = 1137
	X_CS_POSIX_V7_ILP32_OFFBIG_LIBS      = 1138
	X_CS_POSIX_V7_ILP32_OFFBIG_LINTFLAGS = 1139
	X_CS_POSIX_V7_LP64_OFF64_CFLAGS      = 1140
	X_CS_POSIX_V7_LP64_OFF64_LDFLAGS     = 1141
	X_CS_POSIX_V7_LP64_OFF64_LIBS        = 1142
	X_CS_POSIX_V7_LP64_OFF64_LINTFLAGS   = 1143
	X_CS_POSIX_V7_LPBIG_OFFBIG_CFLAGS    = 1144
	X_CS_POSIX_V7_LPBIG_OFFBIG_LDFLAGS   = 1145
	X_CS_POSIX_V7_LPBIG_OFFBIG_LIBS      = 1146
	X_CS_POSIX_V7_LPBIG_OFFBIG_LINTFLAGS = 1147

	X_CS_V6_ENV = 1148
	X_CS_V7_ENV = 1149
)

// Values for the argument to `sysconf'.
const ( /* confname.h:71:1: */
	X_SC_ARG_MAX               = 0
	X_SC_CHILD_MAX             = 1
	X_SC_CLK_TCK               = 2
	X_SC_NGROUPS_MAX           = 3
	X_SC_OPEN_MAX              = 4
	X_SC_STREAM_MAX            = 5
	X_SC_TZNAME_MAX            = 6
	X_SC_JOB_CONTROL           = 7
	X_SC_SAVED_IDS             = 8
	X_SC_REALTIME_SIGNALS      = 9
	X_SC_PRIORITY_SCHEDULING   = 10
	X_SC_TIMERS                = 11
	X_SC_ASYNCHRONOUS_IO       = 12
	X_SC_PRIORITIZED_IO        = 13
	X_SC_SYNCHRONIZED_IO       = 14
	X_SC_FSYNC                 = 15
	X_SC_MAPPED_FILES          = 16
	X_SC_MEMLOCK               = 17
	X_SC_MEMLOCK_RANGE         = 18
	X_SC_MEMORY_PROTECTION     = 19
	X_SC_MESSAGE_PASSING       = 20
	X_SC_SEMAPHORES            = 21
	X_SC_SHARED_MEMORY_OBJECTS = 22
	X_SC_AIO_LISTIO_MAX        = 23
	X_SC_AIO_MAX               = 24
	X_SC_AIO_PRIO_DELTA_MAX    = 25
	X_SC_DELAYTIMER_MAX        = 26
	X_SC_MQ_OPEN_MAX           = 27
	X_SC_MQ_PRIO_MAX           = 28
	X_SC_VERSION               = 29
	X_SC_PAGESIZE              = 30
	X_SC_RTSIG_MAX             = 31
	X_SC_SEM_NSEMS_MAX         = 32
	X_SC_SEM_VALUE_MAX         = 33
	X_SC_SIGQUEUE_MAX          = 34
	X_SC_TIMER_MAX             = 35

	// Values for the argument to `sysconf'
	//        corresponding to _POSIX2_* symbols.
	X_SC_BC_BASE_MAX        = 36
	X_SC_BC_DIM_MAX         = 37
	X_SC_BC_SCALE_MAX       = 38
	X_SC_BC_STRING_MAX      = 39
	X_SC_COLL_WEIGHTS_MAX   = 40
	X_SC_EQUIV_CLASS_MAX    = 41
	X_SC_EXPR_NEST_MAX      = 42
	X_SC_LINE_MAX           = 43
	X_SC_RE_DUP_MAX         = 44
	X_SC_CHARCLASS_NAME_MAX = 45

	X_SC_2_VERSION   = 46
	X_SC_2_C_BIND    = 47
	X_SC_2_C_DEV     = 48
	X_SC_2_FORT_DEV  = 49
	X_SC_2_FORT_RUN  = 50
	X_SC_2_SW_DEV    = 51
	X_SC_2_LOCALEDEF = 52

	X_SC_PII                 = 53
	X_SC_PII_XTI             = 54
	X_SC_PII_SOCKET          = 55
	X_SC_PII_INTERNET        = 56
	X_SC_PII_OSI             = 57
	X_SC_POLL                = 58
	X_SC_SELECT              = 59
	X_SC_UIO_MAXIOV          = 60
	X_SC_IOV_MAX             = 60
	X_SC_PII_INTERNET_STREAM = 61
	X_SC_PII_INTERNET_DGRAM  = 62
	X_SC_PII_OSI_COTS        = 63
	X_SC_PII_OSI_CLTS        = 64
	X_SC_PII_OSI_M           = 65
	X_SC_T_IOV_MAX           = 66

	// Values according to POSIX 1003.1c (POSIX threads).
	X_SC_THREADS                      = 67
	X_SC_THREAD_SAFE_FUNCTIONS        = 68
	X_SC_GETGR_R_SIZE_MAX             = 69
	X_SC_GETPW_R_SIZE_MAX             = 70
	X_SC_LOGIN_NAME_MAX               = 71
	X_SC_TTY_NAME_MAX                 = 72
	X_SC_THREAD_DESTRUCTOR_ITERATIONS = 73
	X_SC_THREAD_KEYS_MAX              = 74
	X_SC_THREAD_STACK_MIN             = 75
	X_SC_THREAD_THREADS_MAX           = 76
	X_SC_THREAD_ATTR_STACKADDR        = 77
	X_SC_THREAD_ATTR_STACKSIZE        = 78
	X_SC_THREAD_PRIORITY_SCHEDULING   = 79
	X_SC_THREAD_PRIO_INHERIT          = 80
	X_SC_THREAD_PRIO_PROTECT          = 81
	X_SC_THREAD_PROCESS_SHARED        = 82

	X_SC_NPROCESSORS_CONF = 83
	X_SC_NPROCESSORS_ONLN = 84
	X_SC_PHYS_PAGES       = 85
	X_SC_AVPHYS_PAGES     = 86
	X_SC_ATEXIT_MAX       = 87
	X_SC_PASS_MAX         = 88

	X_SC_XOPEN_VERSION     = 89
	X_SC_XOPEN_XCU_VERSION = 90
	X_SC_XOPEN_UNIX        = 91
	X_SC_XOPEN_CRYPT       = 92
	X_SC_XOPEN_ENH_I18N    = 93
	X_SC_XOPEN_SHM         = 94

	X_SC_2_CHAR_TERM = 95
	X_SC_2_C_VERSION = 96
	X_SC_2_UPE       = 97

	X_SC_XOPEN_XPG2 = 98
	X_SC_XOPEN_XPG3 = 99
	X_SC_XOPEN_XPG4 = 100

	X_SC_CHAR_BIT   = 101
	X_SC_CHAR_MAX   = 102
	X_SC_CHAR_MIN   = 103
	X_SC_INT_MAX    = 104
	X_SC_INT_MIN    = 105
	X_SC_LONG_BIT   = 106
	X_SC_WORD_BIT   = 107
	X_SC_MB_LEN_MAX = 108
	X_SC_NZERO      = 109
	X_SC_SSIZE_MAX  = 110
	X_SC_SCHAR_MAX  = 111
	X_SC_SCHAR_MIN  = 112
	X_SC_SHRT_MAX   = 113
	X_SC_SHRT_MIN   = 114
	X_SC_UCHAR_MAX  = 115
	X_SC_UINT_MAX   = 116
	X_SC_ULONG_MAX  = 117
	X_SC_USHRT_MAX  = 118

	X_SC_NL_ARGMAX  = 119
	X_SC_NL_LANGMAX = 120
	X_SC_NL_MSGMAX  = 121
	X_SC_NL_NMAX    = 122
	X_SC_NL_SETMAX  = 123
	X_SC_NL_TEXTMAX = 124

	X_SC_XBS5_ILP32_OFF32  = 125
	X_SC_XBS5_ILP32_OFFBIG = 126
	X_SC_XBS5_LP64_OFF64   = 127
	X_SC_XBS5_LPBIG_OFFBIG = 128

	X_SC_XOPEN_LEGACY           = 129
	X_SC_XOPEN_REALTIME         = 130
	X_SC_XOPEN_REALTIME_THREADS = 131

	X_SC_ADVISORY_INFO          = 132
	X_SC_BARRIERS               = 133
	X_SC_BASE                   = 134
	X_SC_C_LANG_SUPPORT         = 135
	X_SC_C_LANG_SUPPORT_R       = 136
	X_SC_CLOCK_SELECTION        = 137
	X_SC_CPUTIME                = 138
	X_SC_THREAD_CPUTIME         = 139
	X_SC_DEVICE_IO              = 140
	X_SC_DEVICE_SPECIFIC        = 141
	X_SC_DEVICE_SPECIFIC_R      = 142
	X_SC_FD_MGMT                = 143
	X_SC_FIFO                   = 144
	X_SC_PIPE                   = 145
	X_SC_FILE_ATTRIBUTES        = 146
	X_SC_FILE_LOCKING           = 147
	X_SC_FILE_SYSTEM            = 148
	X_SC_MONOTONIC_CLOCK        = 149
	X_SC_MULTI_PROCESS          = 150
	X_SC_SINGLE_PROCESS         = 151
	X_SC_NETWORKING             = 152
	X_SC_READER_WRITER_LOCKS    = 153
	X_SC_SPIN_LOCKS             = 154
	X_SC_REGEXP                 = 155
	X_SC_REGEX_VERSION          = 156
	X_SC_SHELL                  = 157
	X_SC_SIGNALS                = 158
	X_SC_SPAWN                  = 159
	X_SC_SPORADIC_SERVER        = 160
	X_SC_THREAD_SPORADIC_SERVER = 161
	X_SC_SYSTEM_DATABASE        = 162
	X_SC_SYSTEM_DATABASE_R      = 163
	X_SC_TIMEOUTS               = 164
	X_SC_TYPED_MEMORY_OBJECTS   = 165
	X_SC_USER_GROUPS            = 166
	X_SC_USER_GROUPS_R          = 167
	X_SC_2_PBS                  = 168
	X_SC_2_PBS_ACCOUNTING       = 169
	X_SC_2_PBS_LOCATE           = 170
	X_SC_2_PBS_MESSAGE          = 171
	X_SC_2_PBS_TRACK            = 172
	X_SC_SYMLOOP_MAX            = 173
	X_SC_STREAMS                = 174
	X_SC_2_PBS_CHECKPOINT       = 175

	X_SC_V6_ILP32_OFF32  = 176
	X_SC_V6_ILP32_OFFBIG = 177
	X_SC_V6_LP64_OFF64   = 178
	X_SC_V6_LPBIG_OFFBIG = 179

	X_SC_HOST_NAME_MAX      = 180
	X_SC_TRACE              = 181
	X_SC_TRACE_EVENT_FILTER = 182
	X_SC_TRACE_INHERIT      = 183
	X_SC_TRACE_LOG          = 184

	X_SC_LEVEL1_ICACHE_SIZE     = 185
	X_SC_LEVEL1_ICACHE_ASSOC    = 186
	X_SC_LEVEL1_ICACHE_LINESIZE = 187
	X_SC_LEVEL1_DCACHE_SIZE     = 188
	X_SC_LEVEL1_DCACHE_ASSOC    = 189
	X_SC_LEVEL1_DCACHE_LINESIZE = 190
	X_SC_LEVEL2_CACHE_SIZE      = 191
	X_SC_LEVEL2_CACHE_ASSOC     = 192
	X_SC_LEVEL2_CACHE_LINESIZE  = 193
	X_SC_LEVEL3_CACHE_SIZE      = 194
	X_SC_LEVEL3_CACHE_ASSOC     = 195
	X_SC_LEVEL3_CACHE_LINESIZE  = 196
	X_SC_LEVEL4_CACHE_SIZE      = 197
	X_SC_LEVEL4_CACHE_ASSOC     = 198
	X_SC_LEVEL4_CACHE_LINESIZE  = 199
	// Leave room here, maybe we need a few more cache levels some day.

	X_SC_IPV6        = 235
	X_SC_RAW_SOCKETS = 236

	X_SC_V7_ILP32_OFF32  = 237
	X_SC_V7_ILP32_OFFBIG = 238
	X_SC_V7_LP64_OFF64   = 239
	X_SC_V7_LPBIG_OFFBIG = 240

	X_SC_SS_REPL_MAX = 241

	X_SC_TRACE_EVENT_NAME_MAX = 242
	X_SC_TRACE_NAME_MAX       = 243
	X_SC_TRACE_SYS_MAX        = 244
	X_SC_TRACE_USER_EVENT_MAX = 245

	X_SC_XOPEN_STREAMS = 246

	X_SC_THREAD_ROBUST_PRIO_INHERIT = 247
	X_SC_THREAD_ROBUST_PRIO_PROTECT = 248

	X_SC_MINSIGSTKSZ = 249

	X_SC_SIGSTKSZ = 250
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// Copyright (C) 1991-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	POSIX Standard: 2.10 Symbolic Constants		<unistd.h>

// Copyright (C) 1991-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// These are defined by the user (or the compiler)
//    to specify the desired environment:
//
//    __STRICT_ANSI__	ISO Standard C.
//    _ISOC99_SOURCE	Extensions to ISO C89 from ISO C99.
//    _ISOC11_SOURCE	Extensions to ISO C99 from ISO C11.
//    _ISOC2X_SOURCE	Extensions to ISO C99 from ISO C2X.
//    __STDC_WANT_LIB_EXT2__
// 			Extensions to ISO C99 from TR 27431-2:2010.
//    __STDC_WANT_IEC_60559_BFP_EXT__
// 			Extensions to ISO C11 from TS 18661-1:2014.
//    __STDC_WANT_IEC_60559_FUNCS_EXT__
// 			Extensions to ISO C11 from TS 18661-4:2015.
//    __STDC_WANT_IEC_60559_TYPES_EXT__
// 			Extensions to ISO C11 from TS 18661-3:2015.
//    __STDC_WANT_IEC_60559_EXT__
// 			ISO C2X interfaces defined only in Annex F.
//
//    _POSIX_SOURCE	IEEE Std 1003.1.
//    _POSIX_C_SOURCE	If ==1, like _POSIX_SOURCE; if >=2 add IEEE Std 1003.2;
// 			if >=199309L, add IEEE Std 1003.1b-1993;
// 			if >=199506L, add IEEE Std 1003.1c-1995;
// 			if >=200112L, all of IEEE 1003.1-2004
// 			if >=200809L, all of IEEE 1003.1-2008
//    _XOPEN_SOURCE	Includes POSIX and XPG things.  Set to 500 if
// 			Single Unix conformance is wanted, to 600 for the
// 			sixth revision, to 700 for the seventh revision.
//    _XOPEN_SOURCE_EXTENDED XPG things and X/Open Unix extensions.
//    _LARGEFILE_SOURCE	Some more functions for correct standard I/O.
//    _LARGEFILE64_SOURCE	Additional functionality from LFS for large files.
//    _FILE_OFFSET_BITS=N	Select default filesystem interface.
//    _ATFILE_SOURCE	Additional *at interfaces.
//    _DYNAMIC_STACK_SIZE_SOURCE Select correct (but non compile-time constant)
// 			MINSIGSTKSZ, SIGSTKSZ and PTHREAD_STACK_MIN.
//    _GNU_SOURCE		All of the above, plus GNU extensions.
//    _DEFAULT_SOURCE	The default set of features (taking precedence over
// 			__STRICT_ANSI__).
//
//    _FORTIFY_SOURCE	Add security hardening to many library functions.
// 			Set to 1 or 2; 2 performs stricter checks than 1.
//
//    _REENTRANT, _THREAD_SAFE
// 			Obsolete; equivalent to _POSIX_C_SOURCE=199506L.
//
//    The `-ansi' switch to the GNU C compiler, and standards conformance
//    options such as `-std=c99', define __STRICT_ANSI__.  If none of
//    these are defined, or if _DEFAULT_SOURCE is defined, the default is
//    to have _POSIX_SOURCE set to one and _POSIX_C_SOURCE set to
//    200809L, as well as enabling miscellaneous functions from BSD and
//    SVID.  If more than one of these are defined, they accumulate.  For
//    example __STRICT_ANSI__, _POSIX_SOURCE and _POSIX_C_SOURCE together
//    give you ISO C, 1003.1, and 1003.2, but nothing else.
//
//    These are defined by this file and are used by the
//    header files to decide what to declare or define:
//
//    __GLIBC_USE (F)	Define things from feature set F.  This is defined
// 			to 1 or 0; the subsequent macros are either defined
// 			or undefined, and those tests should be moved to
// 			__GLIBC_USE.
//    __USE_ISOC11		Define ISO C11 things.
//    __USE_ISOC99		Define ISO C99 things.
//    __USE_ISOC95		Define ISO C90 AMD1 (C95) things.
//    __USE_ISOCXX11	Define ISO C++11 things.
//    __USE_POSIX		Define IEEE Std 1003.1 things.
//    __USE_POSIX2		Define IEEE Std 1003.2 things.
//    __USE_POSIX199309	Define IEEE Std 1003.1, and .1b things.
//    __USE_POSIX199506	Define IEEE Std 1003.1, .1b, .1c and .1i things.
//    __USE_XOPEN		Define XPG things.
//    __USE_XOPEN_EXTENDED	Define X/Open Unix things.
//    __USE_UNIX98		Define Single Unix V2 things.
//    __USE_XOPEN2K        Define XPG6 things.
//    __USE_XOPEN2KXSI     Define XPG6 XSI things.
//    __USE_XOPEN2K8       Define XPG7 things.
//    __USE_XOPEN2K8XSI    Define XPG7 XSI things.
//    __USE_LARGEFILE	Define correct standard I/O things.
//    __USE_LARGEFILE64	Define LFS things with separate names.
//    __USE_FILE_OFFSET64	Define 64bit interface as default.
//    __USE_MISC		Define things from 4.3BSD or System V Unix.
//    __USE_ATFILE		Define *at interfaces and AT_* constants for them.
//    __USE_DYNAMIC_STACK_SIZE Define correct (but non compile-time constant)
// 			MINSIGSTKSZ, SIGSTKSZ and PTHREAD_STACK_MIN.
//    __USE_GNU		Define GNU extensions.
//    __USE_FORTIFY_LEVEL	Additional security measures used, according to level.
//
//    The macros `__GNU_LIBRARY__', `__GLIBC__', and `__GLIBC_MINOR__' are
//    defined by this file unconditionally.  `__GNU_LIBRARY__' is provided
//    only for compatibility.  All new code should use the other symbols
//    to test for features.
//
//    All macros listed above as possibly being defined by this file are
//    explicitly undefined if they are not explicitly defined.
//    Feature-test macros that are not defined by the user or compiler
//    but are implied by the other feature-test macros defined (or by the
//    lack of any definitions) are defined by the file.
//
//    ISO C feature test macros depend on the definition of the macro
//    when an affected header is included, not when the first system
//    header is included, and so they are handled in
//    <bits/libc-header-start.h>, which does not have a multiple include
//    guard.  Feature test macros that can be handled from the first
//    system header included are handled here.

// Undefine everything, so we get a clean slate.

// Suppress kernel-name space pollution unless user expressedly asks
//    for it.

// Convenience macro to test the version of gcc.
//    Use like this:
//    #if __GNUC_PREREQ (2,8)
//    ... code requiring gcc 2.8 or later ...
//    #endif
//    Note: only works for GCC 2.0 and later, because __GNUC_MINOR__ was
//    added in 2.0.

// Similarly for clang.  Features added to GCC after version 4.2 may
//    or may not also be available in clang, and clang's definitions of
//    __GNUC(_MINOR)__ are fixed at 4 and 2 respectively.  Not all such
//    features can be queried via __has_extension/__has_feature.

// Whether to use feature set F.

// _BSD_SOURCE and _SVID_SOURCE are deprecated aliases for
//    _DEFAULT_SOURCE.  If _DEFAULT_SOURCE is present we do not
//    issue a warning; the expectation is that the source is being
//    transitioned to use the new macro.

// If _GNU_SOURCE was defined by the user, turn on all the other features.

// If nothing (other than _GNU_SOURCE and _DEFAULT_SOURCE) is defined,
//    define _DEFAULT_SOURCE.

// This is to enable the ISO C2X extension.

// This is to enable the ISO C11 extension.

// This is to enable the ISO C99 extension.

// This is to enable the ISO C90 Amendment 1:1995 extension.

// If none of the ANSI/POSIX macros are defined, or if _DEFAULT_SOURCE
//    is defined, use POSIX.1-2008 (or another version depending on
//    _XOPEN_SOURCE).

// Some C libraries once required _REENTRANT and/or _THREAD_SAFE to be
//    defined in all multithreaded code.  GNU libc has not required this
//    for many years.  We now treat them as compatibility synonyms for
//    _POSIX_C_SOURCE=199506L, which is the earliest level of POSIX with
//    comprehensive support for multithreaded code.  Using them never
//    lowers the selected level of POSIX conformance, only raises it.

// Features part to handle 64-bit time_t support.
//    Copyright (C) 2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We need to know the word size in order to check the time size.
// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// Bit size of the time_t type at glibc build time, RISC-V case.
//    Copyright (C) 2020-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// RV32 and RV64 both use 64-bit time_t

// The function 'gets' existed in C89, but is impossible to use
//    safely.  It has been removed from ISO C11 and ISO C++14.  Note: for
//    compatibility with various implementations of <cstdio>, this test
//    must consider only the value of __cplusplus when compiling C++.

// GNU formerly extended the scanf functions with modified format
//    specifiers %as, %aS, and %a[...] that allocate a buffer for the
//    input using malloc.  This extension conflicts with ISO C99, which
//    defines %a as a standalone format specifier that reads a floating-
//    point number; moreover, POSIX.1-2008 provides the same feature
//    using the modifier letter 'm' instead (%ms, %mS, %m[...]).
//
//    We now follow C99 unless GNU extensions are active and the compiler
//    is specifically in C89 or C++98 mode (strict or not).  For
//    instance, with GCC, -std=gnu11 will have C99-compliant scanf with
//    or without -D_GNU_SOURCE, but -std=c89 -D_GNU_SOURCE will have the
//    old extension.

// Get definitions of __STDC_* predefined macros, if the compiler has
//    not preincluded this header automatically.
// Copyright (C) 1991-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This macro indicates that the installed library is the GNU C Library.
//    For historic reasons the value now is 6 and this will stay from now
//    on.  The use of this variable is deprecated.  Use __GLIBC__ and
//    __GLIBC_MINOR__ now (see below) when you want to test for a specific
//    GNU C library version and use the values in <gnu/lib-names.h> to get
//    the sonames of the shared libraries.

// Major and minor version number of the GNU C library package.  Use
//    these macros to test for features in specific releases.

// This is here only because every header file already includes this one.
// Copyright (C) 1992-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We are almost always included from features.h.

// The GNU libc does not support any K&R compilers or the traditional mode
//    of ISO C compilers anymore.  Check for some of the combinations not
//    supported anymore.

// Some user header file might have defined this before.

// Compilers that lack __has_attribute may object to
//        #if defined __has_attribute && __has_attribute (...)
//    even though they do not need to evaluate the right-hand side of the &&.
//    Similarly for __has_builtin, etc.

// All functions, except those with callbacks or those that
//    synchronize memory, are leaf functions.

// GCC can always grok prototypes.  For C++ programs we add throw()
//    to help it optimize the function calls.  But this only works with
//    gcc 2.8.x and egcs.  For gcc 3.4 and up we even mark C functions
//    as non-throwing using a function attribute since programs can use
//    the -fexceptions options for C code as well.

// These two macros are not used in glibc anymore.  They are kept here
//    only because some other projects expect the macros to be defined.

// For these things, GCC behaves the ANSI way normally,
//    and the non-ANSI way under -traditional.

// This is not a typedef so `const __ptr_t' does the right thing.

// C++ needs to know that types and declarations are C, not C++.

// Fortify support.

// Use __builtin_dynamic_object_size at _FORTIFY_SOURCE=3 when available.

// Support for flexible arrays.
//    Headers that should use flexible arrays only if they're "real"
//    (e.g. only if they won't affect sizeof()) should test
//    #if __glibc_c99_flexarr_available.

// __asm__ ("xyz") is used throughout the headers to rename functions
//    at the assembly language level.  This is wrapped by the __REDIRECT
//    macro, in order to support compilers that can do this some other
//    way.  When compilers don't support asm-names at all, we have to do
//    preprocessor tricks instead (which don't have exactly the right
//    semantics, but it's the best we can do).
//
//    Example:
//    int __REDIRECT(setpgrp, (__pid_t pid, __pid_t pgrp), setpgid);

//
// #elif __SOME_OTHER_COMPILER__
//
// # define __REDIRECT(name, proto, alias) name proto; 	_Pragma("let " #name " = " #alias)

// GCC and clang have various useful declarations that can be made with
//    the '__attribute__' syntax.  All of the ways we use this do fine if
//    they are omitted for compilers that don't understand it.

// At some point during the gcc 2.96 development the `malloc' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Tell the compiler which arguments to an allocation function
//    indicate the size of the allocation.

// At some point during the gcc 2.96 development the `pure' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// This declaration tells the compiler that the value is constant.

// At some point during the gcc 3.1 development the `used' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Since version 3.2, gcc allows marking deprecated functions.

// Since version 4.5, gcc also allows one to specify the message printed
//    when a deprecated function is used.  clang claims to be gcc 4.2, but
//    may also support this feature.

// At some point during the gcc 2.8 development the `format_arg' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.
//    If several `format_arg' attributes are given for the same function, in
//    gcc-3.0 and older, all but the last one are ignored.  In newer gccs,
//    all designated arguments are considered.

// At some point during the gcc 2.97 development the `strfmon' format
//    attribute for functions was introduced.  We don't want to use it
//    unconditionally (although this would be possible) since it
//    generates warnings.

// The nonnull function attribute marks pointer parameters that
//    must not be NULL.

// The returns_nonnull function attribute marks the return type of the function
//    as always being non-null.

// If fortification mode, we warn about unused results of certain
//    function calls which can lead to problems.

// Forces a function to be always inlined.
// The Linux kernel defines __always_inline in stddef.h (283d7573), and
//    it conflicts with this definition.  Therefore undefine it first to
//    allow either header to be included first.

// Associate error messages with the source location of the call site rather
//    than with the source location inside the function.

// GCC 4.3 and above with -std=c99 or -std=gnu99 implements ISO C99
//    inline semantics, unless -fgnu89-inline is used.  Using __GNUC_STDC_INLINE__
//    or __GNUC_GNU_INLINE is not a good enough check for gcc because gcc versions
//    older than 4.3 may define these macros and still not guarantee GNU inlining
//    semantics.
//
//    clang++ identifies itself as gcc-4.2, but has support for GNU inlining
//    semantics, that can be checked for by using the __GNUC_STDC_INLINE_ and
//    __GNUC_GNU_INLINE__ macro definitions.

// GCC 4.3 and above allow passing all anonymous arguments of an
//    __extern_always_inline function to some other vararg function.

// It is possible to compile containing GCC extensions even if GCC is
//    run in pedantic mode if the uses are carefully marked using the
//    `__extension__' keyword.  But this is not generally available before
//    version 2.8.

// __restrict is known in EGCS 1.2 and above, and in clang.
//    It works also in C++ mode (outside of arrays), but only when spelled
//    as '__restrict', not 'restrict'.

// ISO C99 also allows to declare arrays as non-overlapping.  The syntax is
//      array_name[restrict]
//    GCC 3.1 and clang support this.
//    This syntax is not usable in C++ mode.

// Describes a char array whose address can safely be passed as the first
//    argument to strncpy and strncat, as the char array is not necessarily
//    a NUL-terminated string.

// Undefine (also defined in libc-symbols.h).
// Copies attributes from the declaration or type referenced by
//    the argument.

// The #ifndef lets Gnulib avoid including these on non-glibc
//    platforms, where the includes typically do not exist.
// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// Properties of long double type.  ldbl-128 version.
//    Copyright (C) 2016-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License  published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// long double is distinct from double, so there is nothing to
//    define here.

// __glibc_macro_warning (MESSAGE) issues warning MESSAGE.  This is
//    intended for use in preprocessor macros.
//
//    Note: MESSAGE must be a _single_ string; concatenation of string
//    literals is not supported.

// Generic selection (ISO C11) is a C-only feature, available in GCC
//    since version 4.9.  Previous versions do not provide generic
//    selection, even though they might set __STDC_VERSION__ to 201112L,
//    when in -std=c11 mode.  Thus, we must check for !defined __GNUC__
//    when testing __STDC_VERSION__ for generic selection support.
//    On the other hand, Clang also defines __GNUC__, so a clang-specific
//    check is required to enable the use of generic selection.

// Designates a 1-based positional argument ref-index of pointer type
//    that can be used to access size-index elements of the pointed-to
//    array according to access mode, or at least one element when
//    size-index is not provided:
//      access (access-mode, <ref-index> [, <size-index>])

// Designates dealloc as a function to call to deallocate objects
//    allocated by the declared function.

// Specify that a function such as setjmp or vfork may return
//    twice.

// If we don't have __REDIRECT, prototypes will be missing if
//    __USE_FILE_OFFSET64 but not __USE_LARGEFILE[64].

// Decide whether we can define 'extern inline' functions in headers.

// This is here only because every header file already includes this one.
//    Get the definitions of all the appropriate `__stub_FUNCTION' symbols.
//    <gnu/stubs.h> contains `#define __stub_FUNCTION' when FUNCTION is a stub
//    that will always return failure (and set errno to ENOSYS).
// This file is automatically generated.
//    This file selects the right generated file of `__stub_FUNCTION' macros
//    based on the architecture being compiled for.

// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// This file is automatically generated.
//    It defines a symbol `__stub_FUNCTION' for each function
//    in the C library which is a stub, meaning it will fail
//    every time called, usually setting errno to ENOSYS.

// These may be used to determine what facilities are present at compile time.
//    Their values can be obtained at run time from `sysconf'.

// POSIX Standard approved as ISO/IEC 9945-1 as of September 2008.

// These are not #ifdef __USE_POSIX2 because they are
//    in the theoretically application-owned namespace.

// The utilities on GNU systems also correspond to this version.

// The utilities on GNU systems also correspond to this version.

// This symbol was required until the 2001 edition of POSIX.

// If defined, the implementation supports the
//    C Language Bindings Option.

// If defined, the implementation supports the
//    C Language Development Utilities Option.

// If defined, the implementation supports the
//    Software Development Utilities Option.

// If defined, the implementation supports the
//    creation of locales with the localedef utility.

// X/Open version number to which the library conforms.  It is selectable.

// Commands and utilities from XPG4 are available.

// We are compatible with the old published standards as well.

// The X/Open Unix extensions are available.

// The enhanced internationalization capabilities according to XPG4.2
//    are present.

// The legacy interfaces are also available.

// Get values of POSIX options:
//
//    If these symbols are defined, the corresponding features are
//    always available.  If not, they may be available sometimes.
//    The current values can be obtained with `sysconf'.
//
//    _POSIX_JOB_CONTROL		Job control is supported.
//    _POSIX_SAVED_IDS		Processes have a saved set-user-ID
// 				and a saved set-group-ID.
//    _POSIX_REALTIME_SIGNALS	Real-time, queued signals are supported.
//    _POSIX_PRIORITY_SCHEDULING	Priority scheduling is supported.
//    _POSIX_TIMERS		POSIX.4 clocks and timers are supported.
//    _POSIX_ASYNCHRONOUS_IO	Asynchronous I/O is supported.
//    _POSIX_PRIORITIZED_IO	Prioritized asynchronous I/O is supported.
//    _POSIX_SYNCHRONIZED_IO	Synchronizing file data is supported.
//    _POSIX_FSYNC			The fsync function is present.
//    _POSIX_MAPPED_FILES		Mapping of files to memory is supported.
//    _POSIX_MEMLOCK		Locking of all memory is supported.
//    _POSIX_MEMLOCK_RANGE		Locking of ranges of memory is supported.
//    _POSIX_MEMORY_PROTECTION	Setting of memory protections is supported.
//    _POSIX_MESSAGE_PASSING	POSIX.4 message queues are supported.
//    _POSIX_SEMAPHORES		POSIX.4 counting semaphores are supported.
//    _POSIX_SHARED_MEMORY_OBJECTS	POSIX.4 shared memory objects are supported.
//    _POSIX_THREADS		POSIX.1c pthreads are supported.
//    _POSIX_THREAD_ATTR_STACKADDR	Thread stack address attribute option supported.
//    _POSIX_THREAD_ATTR_STACKSIZE	Thread stack size attribute option supported.
//    _POSIX_THREAD_SAFE_FUNCTIONS	Thread-safe functions are supported.
//    _POSIX_THREAD_PRIORITY_SCHEDULING
// 				POSIX.1c thread execution scheduling supported.
//    _POSIX_THREAD_PRIO_INHERIT	Thread priority inheritance option supported.
//    _POSIX_THREAD_PRIO_PROTECT	Thread priority protection option supported.
//    _POSIX_THREAD_PROCESS_SHARED	Process-shared synchronization supported.
//    _POSIX_PII			Protocol-independent interfaces are supported.
//    _POSIX_PII_XTI		XTI protocol-indep. interfaces are supported.
//    _POSIX_PII_SOCKET		Socket protocol-indep. interfaces are supported.
//    _POSIX_PII_INTERNET		Internet family of protocols supported.
//    _POSIX_PII_INTERNET_STREAM	Connection-mode Internet protocol supported.
//    _POSIX_PII_INTERNET_DGRAM	Connectionless Internet protocol supported.
//    _POSIX_PII_OSI		ISO/OSI family of protocols supported.
//    _POSIX_PII_OSI_COTS		Connection-mode ISO/OSI service supported.
//    _POSIX_PII_OSI_CLTS		Connectionless ISO/OSI service supported.
//    _POSIX_POLL			Implementation supports `poll' function.
//    _POSIX_SELECT		Implementation supports `select' and `pselect'.
//
//    _XOPEN_REALTIME		X/Open realtime support is available.
//    _XOPEN_REALTIME_THREADS	X/Open realtime thread support is available.
//    _XOPEN_SHM			Shared memory interface according to XPG4.2.
//
//    _XBS5_ILP32_OFF32		Implementation provides environment with 32-bit
// 				int, long, pointer, and off_t types.
//    _XBS5_ILP32_OFFBIG		Implementation provides environment with 32-bit
// 				int, long, and pointer and off_t with at least
// 				64 bits.
//    _XBS5_LP64_OFF64		Implementation provides environment with 32-bit
// 				int, and 64-bit long, pointer, and off_t types.
//    _XBS5_LPBIG_OFFBIG		Implementation provides environment with at
// 				least 32 bits int and long, pointer, and off_t
// 				with at least 64 bits.
//
//    If any of these symbols is defined as -1, the corresponding option is not
//    true for any file.  If any is defined as other than -1, the corresponding
//    option is true for all files.  If a symbol is not defined at all, the value
//    for a specific file can be obtained from `pathconf' and `fpathconf'.
//
//    _POSIX_CHOWN_RESTRICTED	Only the super user can use `chown' to change
// 				the owner of a file.  `chown' can only be used
// 				to change the group ID of a file to a group of
// 				which the calling process is a member.
//    _POSIX_NO_TRUNC		Pathname components longer than
// 				NAME_MAX generate an error.
//    _POSIX_VDISABLE		If defined, if the value of an element of the
// 				`c_cc' member of `struct termios' is
// 				_POSIX_VDISABLE, no character will have the
// 				effect associated with that element.
//    _POSIX_SYNC_IO		Synchronous I/O may be performed.
//    _POSIX_ASYNC_IO		Asynchronous I/O may be performed.
//    _POSIX_PRIO_IO		Prioritized Asynchronous I/O may be performed.
//
//    Support for the Large File Support interface is not generally available.
//    If it is available the following constants are defined to one.
//    _LFS64_LARGEFILE		Low-level I/O supports large files.
//    _LFS64_STDIO			Standard I/O supports large files.
//

// Define POSIX options for Linux.
//    Copyright (C) 1996-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public License as
//    published by the Free Software Foundation; either version 2.1 of the
//    License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; see the file COPYING.LIB.  If
//    not, see <https://www.gnu.org/licenses/>.

// Job control is supported.

// Processes have a saved set-user-ID and a saved set-group-ID.

// Priority scheduling is not supported with the correct semantics,
//    but GNU/Linux applications expect that the corresponding interfaces
//    are available, even though the semantics do not meet the POSIX
//    requirements.  See glibc bug 14829.

// Synchronizing file data is supported.

// The fsync function is present.

// Mapping of files to memory is supported.

// Locking of all memory is supported.

// Locking of ranges of memory is supported.

// Setting of memory protections is supported.

// Some filesystems allow all users to change file ownership.

// `c_cc' member of 'struct termios' structure can be disabled by
//    using the value _POSIX_VDISABLE.

// Filenames are not silently truncated.

// X/Open realtime support is available.

// X/Open thread realtime support is available.

// XPG4.2 shared memory is supported.

// Tell we have POSIX threads.

// We have the reentrant functions described in POSIX.

// We provide priority scheduling for threads.

// We support user-defined stack sizes.

// We support user-defined stacks.

// We support priority inheritence.

// We support priority protection, though only for non-robust
//    mutexes.

// We support priority inheritence for robust mutexes.

// We do not support priority protection for robust mutexes.

// We support POSIX.1b semaphores.

// Real-time signals are supported.

// We support asynchronous I/O.
// Alternative name for Unix98.
// Support for prioritization is also available.

// The LFS support in asynchronous I/O is also available.

// The rest of the LFS is also available.

// POSIX shared memory objects are implemented.

// CPU-time clocks support needs to be checked at runtime.

// Clock support in threads must be also checked at runtime.

// GNU libc provides regular expression handling.

// Reader/Writer locks are available.

// We have a POSIX shell.

// We support the Timeouts option.

// We support spinlocks.

// The `spawn' function family is supported.

// We have POSIX timers.

// The barrier functions are available.

// POSIX message queues are available.

// Thread process-shared synchronization is supported.

// The monotonic clock might be available.

// The clock selection interfaces are available.

// Advisory information interfaces are available.

// IPv6 support is available.

// Raw socket support is available.

// We have at least one terminal.

// Neither process nor thread sporadic server interfaces is available.

// trace.h is not available.

// Typed memory objects are not available.

// Get the environment definitions from Unix98.
// Copyright (C) 2020-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// This header should define the following symbols under the described
//    situations.  A value `1' means that the model is always supported,
//    `-1' means it is never supported.  Undefined means it cannot be
//    statically decided.
//
//    _POSIX_V7_ILP32_OFF32   32bit int, long, pointers, and off_t type
//    _POSIX_V7_ILP32_OFFBIG  32bit int, long, and pointers and larger off_t type
//
//    _POSIX_V7_LP64_OFF32    64bit long and pointers and 32bit off_t type
//    _POSIX_V7_LPBIG_OFFBIG  64bit long and pointers and large off_t type
//
//    The macros _POSIX_V6_ILP32_OFF32, _POSIX_V6_ILP32_OFFBIG,
//    _POSIX_V6_LP64_OFF32, _POSIX_V6_LPBIG_OFFBIG, _XBS5_ILP32_OFF32,
//    _XBS5_ILP32_OFFBIG, _XBS5_LP64_OFF32, and _XBS5_LPBIG_OFFBIG were
//    used in previous versions of the Unix standard and are available
//    only for compatibility.

// We can never provide environments with 32-bit wide pointers.
// We also have no use (for now) for an environment with bigger pointers
//    and offsets.

// By default we have 64-bit wide `long int', pointers and `off_t'.

// Standard file descriptors.

// All functions that are not declared anywhere else.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Copyright (C) 1991-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// Bit size of the time_t type at glibc build time, RISC-V case.
//    Copyright (C) 2020-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.  RISC-V version.
//    Copyright (C) 2002-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// RV32 and RV64 both use 64-bit time_t

// Convenience types.
type X__u_char = uint8   /* types.h:31:23 */
type X__u_short = uint16 /* types.h:32:28 */
type X__u_int = uint32   /* types.h:33:22 */
type X__u_long = uint64  /* types.h:34:27 */

// Fixed-size types, underlying types depend on word size and compiler.
type X__int8_t = int8     /* types.h:37:21 */
type X__uint8_t = uint8   /* types.h:38:23 */
type X__int16_t = int16   /* types.h:39:26 */
type X__uint16_t = uint16 /* types.h:40:28 */
type X__int32_t = int32   /* types.h:41:20 */
type X__uint32_t = uint32 /* types.h:42:22 */
type X__int64_t = int64   /* types.h:44:25 */
type X__uint64_t = uint64 /* types.h:45:27 */

// Smallest types with at least a given width.
type X__int_least8_t = X__int8_t     /* types.h:52:18 */
type X__uint_least8_t = X__uint8_t   /* types.h:53:19 */
type X__int_least16_t = X__int16_t   /* types.h:54:19 */
type X__uint_least16_t = X__uint16_t /* types.h:55:20 */
type X__int_least32_t = X__int32_t   /* types.h:56:19 */
type X__uint_least32_t = X__uint32_t /* types.h:57:20 */
type X__int_least64_t = X__int64_t   /* types.h:58:19 */
type X__uint_least64_t = X__uint64_t /* types.h:59:20 */

// quad_t is also 64 bits.
type X__quad_t = int64    /* types.h:63:18 */
type X__u_quad_t = uint64 /* types.h:64:27 */

// Largest integral types.
type X__intmax_t = int64   /* types.h:72:18 */
type X__uintmax_t = uint64 /* types.h:73:27 */

// The machine-dependent file <bits/typesizes.h> defines __*_T_TYPE
//    macros for each of the OS types we define below.  The definitions
//    of those macros must use the following macros for underlying types.
//    We define __S<SIZE>_TYPE and __U<SIZE>_TYPE for the signed and unsigned
//    variants of each of the following integer types on this machine.
//
// 	16		-- "natural" 16-bit type (always short)
// 	32		-- "natural" 32-bit type (always int)
// 	64		-- "natural" 64-bit type (long or long long)
// 	LONG32		-- 32-bit type, traditionally long
// 	QUAD		-- 64-bit type, traditionally long long
// 	WORD		-- natural type of __WORDSIZE bits (int or long)
// 	LONGWORD	-- type of __WORDSIZE bits, traditionally long
//
//    We distinguish WORD/LONGWORD, 32/LONG32, and 64/QUAD so that the
//    conventional uses of `long' or `long long' type modifiers match the
//    types we define, even when a less-adorned type would be the same size.
//    This matters for (somewhat) portably writing printf/scanf formats for
//    these types, where using the appropriate l or ll format modifiers can
//    make the typedefs and the formats match up across all GNU platforms.  If
//    we used `long' when it's 64 bits where `long long' is expected, then the
//    compiler would warn about the formats not matching the argument types,
//    and the programmer changing them to shut up the compiler would break the
//    program's portability.
//
//    Here we assume what is presently the case in all the GCC configurations
//    we support: long long is always 64 bits, long is always word/address size,
//    and int is always 32 bits.

// No need to mark the typedef with __extension__.
// bits/typesizes.h -- underlying types for *_t.  For the generic Linux ABI.
//    Copyright (C) 2011-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//    Contributed by Chris Metcalf <<EMAIL>>, 2011.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library.  If not, see
//    <https://www.gnu.org/licenses/>.

// See <bits/types.h> for the meaning of these macros.  This file exists so
//    that <bits/types.h> need not vary across different GNU platforms.

// Tell the libc code that off_t and off64_t are actually the same type
//    for all ABI purposes, even if possibly expressed as different base types
//    for C type-checking purposes.

// Same for ino_t and ino64_t.

// And for __rlim_t and __rlim64_t.

// And for fsblkcnt_t, fsblkcnt64_t, fsfilcnt_t and fsfilcnt64_t.

// And for getitimer, setitimer and rusage

// Number of descriptors that can fit in an `fd_set'.

// bits/time64.h -- underlying types for __time64_t.  RISC-V version.
//    Copyright (C) 2020-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define __TIME64_T_TYPE so that it is always a 64-bit type.

// If we already have 64-bit time type then use it.

type X__dev_t = uint64                     /* types.h:145:25 */ // Type of device numbers.
type X__uid_t = uint32                     /* types.h:146:25 */ // Type of user identifications.
type X__gid_t = uint32                     /* types.h:147:25 */ // Type of group identifications.
type X__ino_t = uint64                     /* types.h:148:25 */ // Type of file serial numbers.
type X__ino64_t = uint64                   /* types.h:149:27 */ // Type of file serial numbers (LFS).
type X__mode_t = uint32                    /* types.h:150:26 */ // Type of file attribute bitmasks.
type X__nlink_t = uint32                   /* types.h:151:27 */ // Type of file link counts.
type X__off_t = int64                      /* types.h:152:25 */ // Type of file sizes and offsets.
type X__off64_t = int64                    /* types.h:153:27 */ // Type of file sizes and offsets (LFS).
type X__pid_t = int32                      /* types.h:154:25 */ // Type of process identifications.
type X__fsid_t = struct{ F__val [2]int32 } /* types.h:155:26 */ // Type of file system IDs.
type X__clock_t = int64                    /* types.h:156:27 */ // Type of CPU usage counts.
type X__rlim_t = uint64                    /* types.h:157:26 */ // Type for resource measurement.
type X__rlim64_t = uint64                  /* types.h:158:28 */ // Type for resource measurement (LFS).
type X__id_t = uint32                      /* types.h:159:24 */ // General type for IDs.
type X__time_t = int64                     /* types.h:160:26 */ // Seconds since the Epoch.
type X__useconds_t = uint32                /* types.h:161:30 */ // Count of microseconds.
type X__suseconds_t = int64                /* types.h:162:31 */ // Signed count of microseconds.
type X__suseconds64_t = int64              /* types.h:163:33 */

type X__daddr_t = int32 /* types.h:165:27 */ // The type of a disk address.
type X__key_t = int32   /* types.h:166:25 */ // Type of an IPC key.

// Clock ID used in clock and timer functions.
type X__clockid_t = int32 /* types.h:169:29 */

// Timer ID returned by `timer_create'.
type X__timer_t = uintptr /* types.h:172:12 */

// Type to represent block size.
type X__blksize_t = int32 /* types.h:175:29 */

// Types from the Large File Support interface.

// Type to count number of disk blocks.
type X__blkcnt_t = int64   /* types.h:180:28 */
type X__blkcnt64_t = int64 /* types.h:181:30 */

// Type to count file system blocks.
type X__fsblkcnt_t = uint64   /* types.h:184:30 */
type X__fsblkcnt64_t = uint64 /* types.h:185:32 */

// Type to count file system nodes.
type X__fsfilcnt_t = uint64   /* types.h:188:30 */
type X__fsfilcnt64_t = uint64 /* types.h:189:32 */

// Type of miscellaneous file system fields.
type X__fsword_t = int64 /* types.h:192:28 */

type X__ssize_t = int64 /* types.h:194:27 */ // Type of a byte count, or error.

// Signed long type used in system calls.
type X__syscall_slong_t = int64 /* types.h:197:33 */
// Unsigned long type used in system calls.
type X__syscall_ulong_t = uint64 /* types.h:199:33 */

// These few don't really vary by system, they always correspond
//
//	to one of the other defined types.
type X__loff_t = X__off64_t /* types.h:203:19 */ // Type of file sizes and offsets (LFS).
type X__caddr_t = uintptr   /* types.h:204:14 */

// Duplicates info from stdint.h but this is used in unistd.h.
type X__intptr_t = int64 /* types.h:207:25 */

// Duplicate info from sys/socket.h.
type X__socklen_t = uint32 /* types.h:210:23 */

// C99: An integer type that can be accessed as an atomic entity,
//
//	even in the presence of asynchronous interrupts.
//	It is not currently necessary for this to be machine-specific.
type X__sig_atomic_t = int32 /* types.h:215:13 */

// Seconds since the Epoch, visible to user code when time_t is too
//    narrow only for consistency with the old way of widening too-narrow
//    types.  User code should never use __time64_t.

type Ssize_t = X__ssize_t /* unistd.h:220:19 */

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// The Single Unix specification says that some more types are
//
//	available here.
type Gid_t = X__gid_t /* unistd.h:232:17 */

type Uid_t = X__uid_t /* unistd.h:237:17 */

type Off_t = X__off64_t /* unistd.h:245:19 */

type Useconds_t = X__useconds_t /* unistd.h:255:22 */

type Pid_t = X__pid_t /* unistd.h:260:17 */

type Intptr_t = X__intptr_t /* unistd.h:267:20 */

type Socklen_t = X__socklen_t /* unistd.h:274:21 */

// Define some macros helping to catch buffer overflows.

// System-specific extensions.
// System-specific extensions of <unistd.h>, Linux version.
//    Copyright (C) 2019-2021 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

var _ uint8 /* gen.c:2:13: */
