// Code generated by 'ccgo netinet/in/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o netinet/in/in_netbsd_amd64.go -pkgname in', DO NOT EDIT.

package in

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_APPLETALK                  = 16                   // socket.h:218:1:
	AF_ARP                        = 28                   // socket.h:237:1:
	AF_BLUETOOTH                  = 31                   // socket.h:243:1:
	AF_CAN                        = 35                   // socket.h:247:1:
	AF_CCITT                      = 10                   // socket.h:212:1:
	AF_CHAOS                      = 5                    // socket.h:206:1:
	AF_CNT                        = 21                   // socket.h:225:1:
	AF_COIP                       = 20                   // socket.h:224:1:
	AF_DATAKIT                    = 9                    // socket.h:211:1:
	AF_DECnet                     = 12                   // socket.h:214:1:
	AF_DLI                        = 13                   // socket.h:215:1:
	AF_E164                       = 26                   // socket.h:235:1:
	AF_ECMA                       = 8                    // socket.h:210:1:
	AF_ETHER                      = 36                   // socket.h:248:1:
	AF_HYLINK                     = 15                   // socket.h:217:1:
	AF_IEEE80211                  = 32                   // socket.h:244:1:
	AF_IMPLINK                    = 3                    // socket.h:204:1:
	AF_INET                       = 2                    // socket.h:203:1:
	AF_INET6                      = 24                   // socket.h:230:1:
	AF_IPX                        = 23                   // socket.h:229:1:
	AF_ISDN                       = 26                   // socket.h:234:1:
	AF_ISO                        = 7                    // socket.h:208:1:
	AF_LAT                        = 14                   // socket.h:216:1:
	AF_LINK                       = 18                   // socket.h:220:1:
	AF_LOCAL                      = 1                    // socket.h:201:1:
	AF_MAX                        = 37                   // socket.h:249:1:
	AF_MPLS                       = 33                   // socket.h:245:1:
	AF_NATM                       = 27                   // socket.h:236:1:
	AF_NS                         = 6                    // socket.h:207:1:
	AF_OROUTE                     = 17                   // socket.h:219:1:
	AF_OSI                        = 7                    // socket.h:209:1:
	AF_PUP                        = 4                    // socket.h:205:1:
	AF_ROUTE                      = 34                   // socket.h:246:1:
	AF_SNA                        = 11                   // socket.h:213:1:
	AF_UNIX                       = 1                    // socket.h:202:1:
	AF_UNSPEC                     = 0                    // socket.h:200:1:
	BIG_ENDIAN                    = 4321                 // endian.h:101:1:
	BYTE_ORDER                    = 1234                 // endian.h:103:1:
	CTL_IPPROTO_IPSEC             = 258                  // in.h:118:1:
	FD_SETSIZE                    = 256                  // fd_set.h:59:1:
	ICMP6_FILTER                  = 18                   // in6.h:393:1:
	INET6_ADDRSTRLEN              = 46                   // in6.h:138:1:
	INET6_IS_ADDR_LINKLOCAL       = 1                    // in6.h:678:1:
	INET6_IS_ADDR_MC_LINKLOCAL    = 2                    // in6.h:679:1:
	INET6_IS_ADDR_SITELOCAL       = 4                    // in6.h:680:1:
	INET_ADDRSTRLEN               = 16                   // in.h:249:1:
	INT16_MAX                     = 32767                // common_int_limits.h:53:1:
	INT16_MIN                     = -32768               // common_int_limits.h:47:1:
	INT32_MAX                     = 2147483647           // common_int_limits.h:54:1:
	INT32_MIN                     = -2147483648          // common_int_limits.h:48:1:
	INT64_MAX                     = 9223372036854775807  // common_int_limits.h:55:1:
	INT64_MIN                     = -9223372036854775808 // common_int_limits.h:49:1:
	INT8_MAX                      = 127                  // common_int_limits.h:52:1:
	INT8_MIN                      = -128                 // common_int_limits.h:46:1:
	INTMAX_MAX                    = 9223372036854775807  // common_int_limits.h:111:1:
	INTMAX_MIN                    = -9223372036854775808 // common_int_limits.h:110:1:
	INTPTR_MAX                    = 9223372036854775807  // common_int_limits.h:105:1:
	INTPTR_MIN                    = -9223372036854775808 // common_int_limits.h:104:1:
	INT_FAST16_MAX                = 2147483647           // common_int_limits.h:93:1:
	INT_FAST16_MIN                = -2147483648          // common_int_limits.h:87:1:
	INT_FAST32_MAX                = 2147483647           // common_int_limits.h:94:1:
	INT_FAST32_MIN                = -2147483648          // common_int_limits.h:88:1:
	INT_FAST64_MAX                = 9223372036854775807  // common_int_limits.h:95:1:
	INT_FAST64_MIN                = -9223372036854775808 // common_int_limits.h:89:1:
	INT_FAST8_MAX                 = 2147483647           // common_int_limits.h:92:1:
	INT_FAST8_MIN                 = -2147483648          // common_int_limits.h:86:1:
	INT_LEAST16_MAX               = 32767                // common_int_limits.h:73:1:
	INT_LEAST16_MIN               = -32768               // common_int_limits.h:67:1:
	INT_LEAST32_MAX               = 2147483647           // common_int_limits.h:74:1:
	INT_LEAST32_MIN               = -2147483648          // common_int_limits.h:68:1:
	INT_LEAST64_MAX               = 9223372036854775807  // common_int_limits.h:75:1:
	INT_LEAST64_MIN               = -9223372036854775808 // common_int_limits.h:69:1:
	INT_LEAST8_MAX                = 127                  // common_int_limits.h:72:1:
	INT_LEAST8_MIN                = -128                 // common_int_limits.h:66:1:
	IN_CLASSA_MAX                 = 128                  // in.h:182:1:
	IN_CLASSA_NSHIFT              = 24                   // in.h:180:1:
	IN_CLASSB_MAX                 = 65536                // in.h:189:1:
	IN_CLASSB_NSHIFT              = 16                   // in.h:187:1:
	IN_CLASSC_NSHIFT              = 8                    // in.h:194:1:
	IN_CLASSD_NSHIFT              = 28                   // in.h:201:1:
	IN_LOOPBACKNET                = 127                  // in.h:236:1:
	IPCTL_ALLOWSRCRT              = 7                    // in.h:348:1:
	IPCTL_ANONPORTMAX             = 11                   // in.h:352:1:
	IPCTL_ANONPORTMIN             = 10                   // in.h:351:1:
	IPCTL_CHECKINTERFACE          = 20                   // in.h:361:1:
	IPCTL_DAD_COUNT               = 25                   // in.h:366:1:
	IPCTL_DEFTTL                  = 3                    // in.h:344:1:
	IPCTL_DIRECTEDBCAST           = 6                    // in.h:347:1:
	IPCTL_FORWARDING              = 1                    // in.h:342:1:
	IPCTL_FORWSRCRT               = 5                    // in.h:346:1:
	IPCTL_GIF_TTL                 = 15                   // in.h:356:1:
	IPCTL_GRE_TTL                 = 19                   // in.h:360:1:
	IPCTL_HOSTZEROBROADCAST       = 14                   // in.h:355:1:
	IPCTL_IFQ                     = 21                   // in.h:362:1:
	IPCTL_LOOPBACKCKSUM           = 23                   // in.h:364:1:
	IPCTL_LOWPORTMAX              = 17                   // in.h:358:1:
	IPCTL_LOWPORTMIN              = 16                   // in.h:357:1:
	IPCTL_MAXFLOWS                = 13                   // in.h:354:1:
	IPCTL_MAXFRAGPACKETS          = 18                   // in.h:359:1:
	IPCTL_MTUDISC                 = 9                    // in.h:350:1:
	IPCTL_MTUDISCTIMEOUT          = 12                   // in.h:353:1:
	IPCTL_RANDOMID                = 22                   // in.h:363:1:
	IPCTL_SENDREDIRECTS           = 2                    // in.h:343:1:
	IPCTL_STATS                   = 24                   // in.h:365:1:
	IPCTL_SUBNETSARELOCAL         = 8                    // in.h:349:1:
	IPPORT_ANONMAX                = 65535                // in.h:151:1:
	IPPORT_ANONMIN                = 49152                // in.h:150:1:
	IPPORT_RESERVED               = 1024                 // in.h:149:1:
	IPPORT_RESERVEDMAX            = 1023                 // in.h:153:1:
	IPPORT_RESERVEDMIN            = 600                  // in.h:152:1:
	IPPROTO_AH                    = 51                   // in.h:95:1:
	IPPROTO_CARP                  = 112                  // in.h:107:1:
	IPPROTO_DCCP                  = 33                   // in.h:88:1:
	IPPROTO_DONE                  = 257                  // in.h:115:1:
	IPPROTO_DSTOPTS               = 60                   // in.h:100:1:
	IPPROTO_EGP                   = 8                    // in.h:83:1:
	IPPROTO_ENCAP                 = 98                   // in.h:103:1:
	IPPROTO_EON                   = 80                   // in.h:101:1:
	IPPROTO_ESP                   = 50                   // in.h:94:1:
	IPPROTO_ETHERIP               = 97                   // in.h:102:1:
	IPPROTO_FRAGMENT              = 44                   // in.h:91:1:
	IPPROTO_GGP                   = 3                    // in.h:79:1:
	IPPROTO_GRE                   = 47                   // in.h:93:1:
	IPPROTO_HOPOPTS               = 0                    // in.h:76:1:
	IPPROTO_ICMP                  = 1                    // in.h:77:1:
	IPPROTO_ICMPV6                = 58                   // in.h:98:1:
	IPPROTO_IDP                   = 22                   // in.h:86:1:
	IPPROTO_IGMP                  = 2                    // in.h:78:1:
	IPPROTO_IP                    = 0                    // in.h:75:1:
	IPPROTO_IPCOMP                = 108                  // in.h:105:1:
	IPPROTO_IPIP                  = 4                    // in.h:81:1:
	IPPROTO_IPV4                  = 4                    // in.h:80:1:
	IPPROTO_IPV6                  = 41                   // in.h:89:1:
	IPPROTO_IPV6_ICMP             = 58                   // in.h:97:1:
	IPPROTO_L2TP                  = 115                  // in.h:108:1:
	IPPROTO_MAX                   = 256                  // in.h:112:1:
	IPPROTO_MOBILE                = 55                   // in.h:96:1:
	IPPROTO_NONE                  = 59                   // in.h:99:1:
	IPPROTO_PFSYNC                = 240                  // in.h:110:1:
	IPPROTO_PIM                   = 103                  // in.h:104:1:
	IPPROTO_PUP                   = 12                   // in.h:84:1:
	IPPROTO_RAW                   = 255                  // in.h:111:1:
	IPPROTO_ROUTING               = 43                   // in.h:90:1:
	IPPROTO_RSVP                  = 46                   // in.h:92:1:
	IPPROTO_SCTP                  = 132                  // in.h:109:1:
	IPPROTO_TCP                   = 6                    // in.h:82:1:
	IPPROTO_TP                    = 29                   // in.h:87:1:
	IPPROTO_UDP                   = 17                   // in.h:85:1:
	IPPROTO_VRRP                  = 112                  // in.h:106:1:
	IPV6CTL_ACCEPT_RTADV          = 12                   // in6.h:509:1:
	IPV6CTL_ADDRCTLPOLICY         = 38                   // in6.h:530:1:
	IPV6CTL_ANONPORTMAX           = 29                   // in6.h:524:1:
	IPV6CTL_ANONPORTMIN           = 28                   // in6.h:523:1:
	IPV6CTL_AUTO_FLOWLABEL        = 17                   // in6.h:514:1:
	IPV6CTL_AUTO_LINKLOCAL        = 35                   // in6.h:528:1:
	IPV6CTL_DAD_COUNT             = 16                   // in6.h:513:1:
	IPV6CTL_DEFHLIM               = 3                    // in6.h:500:1:
	IPV6CTL_DEFMCASTHLIM          = 18                   // in6.h:515:1:
	IPV6CTL_FORWARDING            = 1                    // in6.h:498:1:
	IPV6CTL_FORWSRCRT             = 5                    // in6.h:502:1:
	IPV6CTL_GIF_HLIM              = 19                   // in6.h:516:1:
	IPV6CTL_HDRNESTLIMIT          = 15                   // in6.h:512:1:
	IPV6CTL_IFQ                   = 42                   // in6.h:534:1:
	IPV6CTL_KAME_VERSION          = 20                   // in6.h:517:1:
	IPV6CTL_KEEPFAITH             = 13                   // in6.h:510:1:
	IPV6CTL_LOG_INTERVAL          = 14                   // in6.h:511:1:
	IPV6CTL_LOWPORTMAX            = 31                   // in6.h:526:1:
	IPV6CTL_LOWPORTMIN            = 30                   // in6.h:525:1:
	IPV6CTL_MAXFRAGPACKETS        = 9                    // in6.h:506:1:
	IPV6CTL_MAXFRAGS              = 41                   // in6.h:533:1:
	IPV6CTL_MRTPROTO              = 8                    // in6.h:505:1:
	IPV6CTL_MRTSTATS              = 7                    // in6.h:504:1:
	IPV6CTL_RR_PRUNE              = 22                   // in6.h:519:1:
	IPV6CTL_RTADV_MAXROUTES       = 43                   // in6.h:535:1:
	IPV6CTL_RTADV_NUMROUTES       = 44                   // in6.h:537:1:
	IPV6CTL_SENDREDIRECTS         = 2                    // in6.h:499:1:
	IPV6CTL_SOURCECHECK           = 10                   // in6.h:507:1:
	IPV6CTL_SOURCECHECK_LOGINT    = 11                   // in6.h:508:1:
	IPV6CTL_STATS                 = 6                    // in6.h:503:1:
	IPV6CTL_USE_DEFAULTZONE       = 39                   // in6.h:531:1:
	IPV6CTL_USE_DEPRECATED        = 21                   // in6.h:518:1:
	IPV6CTL_V6ONLY                = 24                   // in6.h:521:1:
	IPV6PORT_ANONMAX              = 65535                // in6.h:115:1:
	IPV6PORT_ANONMIN              = 49152                // in6.h:114:1:
	IPV6PORT_RESERVED             = 1024                 // in6.h:113:1:
	IPV6PORT_RESERVEDMAX          = 1023                 // in6.h:117:1:
	IPV6PORT_RESERVEDMIN          = 600                  // in6.h:116:1:
	IPV6_CHECKSUM                 = 26                   // in6.h:405:1:
	IPV6_DEFAULT_MULTICAST_HOPS   = 1                    // in6.h:453:1:
	IPV6_DEFAULT_MULTICAST_LOOP   = 1                    // in6.h:454:1:
	IPV6_DONTFRAG                 = 62                   // in6.h:441:1:
	IPV6_DSTOPTS                  = 50                   // in6.h:432:1:
	IPV6_FAITH                    = 29                   // in6.h:409:1:
	IPV6_HOPLIMIT                 = 47                   // in6.h:429:1:
	IPV6_HOPOPTS                  = 49                   // in6.h:431:1:
	IPV6_IPSEC_POLICY             = 28                   // in6.h:408:1:
	IPV6_JOIN_GROUP               = 12                   // in6.h:388:1:
	IPV6_LEAVE_GROUP              = 13                   // in6.h:389:1:
	IPV6_MULTICAST_HOPS           = 10                   // in6.h:385:1:
	IPV6_MULTICAST_IF             = 9                    // in6.h:384:1:
	IPV6_MULTICAST_LOOP           = 11                   // in6.h:386:1:
	IPV6_NEXTHOP                  = 48                   // in6.h:430:1:
	IPV6_PATHMTU                  = 44                   // in6.h:424:1:
	IPV6_PKTINFO                  = 46                   // in6.h:428:1:
	IPV6_PORTALGO                 = 17                   // in6.h:392:1:
	IPV6_PORTRANGE                = 14                   // in6.h:390:1:
	IPV6_PORTRANGE_DEFAULT        = 0                    // in6.h:484:1:
	IPV6_PORTRANGE_HIGH           = 1                    // in6.h:485:1:
	IPV6_PORTRANGE_LOW            = 2                    // in6.h:486:1:
	IPV6_PREFER_TEMPADDR          = 63                   // in6.h:442:1:
	IPV6_RECVDSTOPTS              = 40                   // in6.h:418:1:
	IPV6_RECVHOPLIMIT             = 37                   // in6.h:415:1:
	IPV6_RECVHOPOPTS              = 39                   // in6.h:417:1:
	IPV6_RECVPATHMTU              = 43                   // in6.h:423:1:
	IPV6_RECVPKTINFO              = 36                   // in6.h:414:1:
	IPV6_RECVRTHDR                = 38                   // in6.h:416:1:
	IPV6_RECVTCLASS               = 57                   // in6.h:435:1:
	IPV6_RTHDR                    = 51                   // in6.h:433:1:
	IPV6_RTHDRDSTOPTS             = 35                   // in6.h:412:1:
	IPV6_RTHDR_LOOSE              = 0                    // in6.h:446:1:
	IPV6_RTHDR_STRICT             = 1                    // in6.h:447:1:
	IPV6_RTHDR_TYPE_0             = 0                    // in6.h:448:1:
	IPV6_SOCKOPT_RESERVED1        = 3                    // in6.h:382:1:
	IPV6_TCLASS                   = 61                   // in6.h:440:1:
	IPV6_UNICAST_HOPS             = 4                    // in6.h:383:1:
	IPV6_USE_MIN_MTU              = 42                   // in6.h:422:1:
	IPV6_V6ONLY                   = 27                   // in6.h:406:1:
	IP_ADD_MEMBERSHIP             = 12                   // in.h:283:1:
	IP_DEFAULT_MULTICAST_LOOP     = 1                    // in.h:312:1:
	IP_DEFAULT_MULTICAST_TTL      = 1                    // in.h:311:1:
	IP_DROP_MEMBERSHIP            = 13                   // in.h:284:1:
	IP_ERRORMTU                   = 21                   // in.h:288:1:
	IP_HDRINCL                    = 2                    // in.h:272:1:
	IP_IPSEC_POLICY               = 22                   // in.h:289:1:
	IP_MAX_MEMBERSHIPS            = 20                   // in.h:313:1:
	IP_MINTTL                     = 24                   // in.h:291:1:
	IP_MULTICAST_IF               = 9                    // in.h:279:1:
	IP_MULTICAST_LOOP             = 11                   // in.h:281:1:
	IP_MULTICAST_TTL              = 10                   // in.h:280:1:
	IP_OPTIONS                    = 1                    // in.h:271:1:
	IP_PKTINFO                    = 25                   // in.h:292:1:
	IP_PORTALGO                   = 18                   // in.h:285:1:
	IP_PORTRANGE                  = 19                   // in.h:286:1:
	IP_PORTRANGE_DEFAULT          = 0                    // in.h:327:1:
	IP_PORTRANGE_HIGH             = 1                    // in.h:328:1:
	IP_PORTRANGE_LOW              = 2                    // in.h:329:1:
	IP_RECVDSTADDR                = 7                    // in.h:277:1:
	IP_RECVIF                     = 20                   // in.h:287:1:
	IP_RECVOPTS                   = 5                    // in.h:275:1:
	IP_RECVPKTINFO                = 26                   // in.h:293:1:
	IP_RECVRETOPTS                = 6                    // in.h:276:1:
	IP_RECVTTL                    = 23                   // in.h:290:1:
	IP_RETOPTS                    = 8                    // in.h:278:1:
	IP_SENDSRCADDR                = 7                    // in.h:295:1:
	IP_TOS                        = 3                    // in.h:273:1:
	IP_TTL                        = 4                    // in.h:274:1:
	LITTLE_ENDIAN                 = 1234                 // endian.h:100:1:
	MSG_BCAST                     = 0x0100               // socket.h:497:1:
	MSG_CMSG_CLOEXEC              = 0x0800               // socket.h:501:1:
	MSG_CONTROLMBUF               = 0x2000000            // socket.h:515:1:
	MSG_CTRUNC                    = 0x0020               // socket.h:494:1:
	MSG_DONTROUTE                 = 0x0004               // socket.h:491:1:
	MSG_DONTWAIT                  = 0x0080               // socket.h:496:1:
	MSG_EOR                       = 0x0008               // socket.h:492:1:
	MSG_IOVUSRSPACE               = 0x4000000            // socket.h:516:1:
	MSG_LENUSRSPACE               = 0x8000000            // socket.h:517:1:
	MSG_MCAST                     = 0x0200               // socket.h:498:1:
	MSG_NAMEMBUF                  = 0x1000000            // socket.h:514:1:
	MSG_NBIO                      = 0x1000               // socket.h:502:1:
	MSG_NOSIGNAL                  = 0x0400               // socket.h:499:1:
	MSG_NOTIFICATION              = 0x4000               // socket.h:504:1:
	MSG_OOB                       = 0x0001               // socket.h:489:1:
	MSG_PEEK                      = 0x0002               // socket.h:490:1:
	MSG_TRUNC                     = 0x0010               // socket.h:493:1:
	MSG_USERFLAGS                 = 0x0ffffff            // socket.h:513:1:
	MSG_WAITALL                   = 0x0040               // socket.h:495:1:
	MSG_WAITFORONE                = 0x2000               // socket.h:503:1:
	NBBY                          = 8                    // types.h:316:1:
	NET_RT_DUMP                   = 1                    // socket.h:457:1:
	NET_RT_FLAGS                  = 2                    // socket.h:458:1:
	NET_RT_IFLIST                 = 6                    // socket.h:462:1:
	NET_RT_OIFLIST                = 5                    // socket.h:461:1:
	NET_RT_OOIFLIST               = 4                    // socket.h:460:1:
	NET_RT_OOOIFLIST              = 3                    // socket.h:459:1:
	NFDBITS                       = 32                   // fd_set.h:93:1:
	NODEVMAJOR                    = -1                   // types.h:258:1:
	PCB_ALL                       = 0                    // socket.h:444:1:
	PCB_SLOP                      = 20                   // socket.h:443:1:
	PDP_ENDIAN                    = 3412                 // endian.h:102:1:
	PF_APPLETALK                  = 16                   // socket.h:334:1:
	PF_ARP                        = 28                   // socket.h:351:1:
	PF_BLUETOOTH                  = 31                   // socket.h:355:1:
	PF_CAN                        = 35                   // socket.h:358:1:
	PF_CCITT                      = 10                   // socket.h:328:1:
	PF_CHAOS                      = 5                    // socket.h:322:1:
	PF_CNT                        = 21                   // socket.h:341:1:
	PF_COIP                       = 20                   // socket.h:340:1:
	PF_DATAKIT                    = 9                    // socket.h:327:1:
	PF_DECnet                     = 12                   // socket.h:330:1:
	PF_DLI                        = 13                   // socket.h:331:1:
	PF_E164                       = 26                   // socket.h:349:1:
	PF_ECMA                       = 8                    // socket.h:326:1:
	PF_ETHER                      = 36                   // socket.h:359:1:
	PF_HYLINK                     = 15                   // socket.h:333:1:
	PF_IMPLINK                    = 3                    // socket.h:320:1:
	PF_INET                       = 2                    // socket.h:319:1:
	PF_INET6                      = 24                   // socket.h:342:1:
	PF_IPX                        = 23                   // socket.h:343:1:
	PF_ISDN                       = 26                   // socket.h:348:1:
	PF_ISO                        = 7                    // socket.h:324:1:
	PF_KEY                        = 29                   // socket.h:353:1:
	PF_LAT                        = 14                   // socket.h:332:1:
	PF_LINK                       = 18                   // socket.h:336:1:
	PF_LOCAL                      = 1                    // socket.h:317:1:
	PF_MAX                        = 37                   // socket.h:361:1:
	PF_MPLS                       = 33                   // socket.h:356:1:
	PF_NATM                       = 27                   // socket.h:350:1:
	PF_NS                         = 6                    // socket.h:323:1:
	PF_OROUTE                     = 17                   // socket.h:335:1:
	PF_OSI                        = 7                    // socket.h:325:1:
	PF_PIP                        = 25                   // socket.h:346:1:
	PF_PUP                        = 4                    // socket.h:321:1:
	PF_ROUTE                      = 34                   // socket.h:357:1:
	PF_RTIP                       = 22                   // socket.h:345:1:
	PF_SNA                        = 11                   // socket.h:329:1:
	PF_UNIX                       = 1                    // socket.h:318:1:
	PF_UNSPEC                     = 0                    // socket.h:316:1:
	PF_XTP                        = 19                   // socket.h:338:1:
	PTRDIFF_MAX                   = 9223372036854775807  // common_int_limits.h:121:1:
	PTRDIFF_MIN                   = -9223372036854775808 // common_int_limits.h:120:1:
	SCM_CREDS                     = 0x10                 // socket.h:581:1:
	SCM_RIGHTS                    = 0x01                 // socket.h:576:1:
	SCM_TIMESTAMP                 = 0x08                 // socket.h:580:1:
	SHUT_RD                       = 0                    // socket.h:587:1:
	SHUT_RDWR                     = 2                    // socket.h:589:1:
	SHUT_WR                       = 1                    // socket.h:588:1:
	SIG_ATOMIC_MAX                = 2147483647           // common_int_limits.h:125:1:
	SIG_ATOMIC_MIN                = -2147483648          // common_int_limits.h:124:1:
	SIN6_LEN                      = 0                    // in6.h:144:1:
	SIZE_MAX                      = 18446744073709551615 // common_int_limits.h:128:1:
	SOCK_CLOEXEC                  = 0x10000000           // socket.h:113:1:
	SOCK_CONN_DGRAM               = 6                    // socket.h:110:1:
	SOCK_DCCP                     = 6                    // socket.h:111:1:
	SOCK_DGRAM                    = 2                    // socket.h:106:1:
	SOCK_FLAGS_MASK               = 0xf0000000           // socket.h:116:1:
	SOCK_NONBLOCK                 = 0x20000000           // socket.h:114:1:
	SOCK_NOSIGPIPE                = 0x40000000           // socket.h:115:1:
	SOCK_RAW                      = 3                    // socket.h:107:1:
	SOCK_RDM                      = 4                    // socket.h:108:1:
	SOCK_SEQPACKET                = 5                    // socket.h:109:1:
	SOCK_STREAM                   = 1                    // socket.h:105:1:
	SOL_SOCKET                    = 0xffff               // socket.h:195:1:
	SOMAXCONN                     = 128                  // socket.h:470:1:
	SO_ACCEPTCONN                 = 0x0002               // socket.h:122:1:
	SO_ACCEPTFILTER               = 0x1000               // socket.h:133:1:
	SO_BROADCAST                  = 0x0020               // socket.h:126:1:
	SO_DEBUG                      = 0x0001               // socket.h:121:1:
	SO_DEFOPTS                    = 27645                // socket.h:138:1:
	SO_DONTROUTE                  = 0x0010               // socket.h:125:1:
	SO_ERROR                      = 0x1007               // socket.h:169:1:
	SO_KEEPALIVE                  = 0x0008               // socket.h:124:1:
	SO_LINGER                     = 0x0080               // socket.h:128:1:
	SO_NOHEADER                   = 0x100a               // socket.h:173:1:
	SO_NOSIGPIPE                  = 0x0800               // socket.h:132:1:
	SO_OOBINLINE                  = 0x0100               // socket.h:129:1:
	SO_OVERFLOWED                 = 0x1009               // socket.h:171:1:
	SO_RCVBUF                     = 0x1002               // socket.h:164:1:
	SO_RCVLOWAT                   = 0x1004               // socket.h:166:1:
	SO_RCVTIMEO                   = 0x100c               // socket.h:178:1:
	SO_RERROR                     = 0x4000               // socket.h:135:1:
	SO_REUSEADDR                  = 0x0004               // socket.h:123:1:
	SO_REUSEPORT                  = 0x0200               // socket.h:130:1:
	SO_SNDBUF                     = 0x1001               // socket.h:163:1:
	SO_SNDLOWAT                   = 0x1003               // socket.h:165:1:
	SO_SNDTIMEO                   = 0x100b               // socket.h:177:1:
	SO_TIMESTAMP                  = 0x2000               // socket.h:134:1:
	SO_TYPE                       = 0x1008               // socket.h:170:1:
	SO_USELOOPBACK                = 0x0040               // socket.h:127:1:
	UINT16_MAX                    = 65535                // common_int_limits.h:59:1:
	UINT32_MAX                    = 4294967295           // common_int_limits.h:60:1:
	UINT64_MAX                    = 18446744073709551615 // common_int_limits.h:61:1:
	UINT8_MAX                     = 255                  // common_int_limits.h:58:1:
	UINTMAX_MAX                   = 18446744073709551615 // common_int_limits.h:112:1:
	UINTPTR_MAX                   = 18446744073709551615 // common_int_limits.h:106:1:
	UINT_FAST16_MAX               = 4294967295           // common_int_limits.h:99:1:
	UINT_FAST32_MAX               = 4294967295           // common_int_limits.h:100:1:
	UINT_FAST64_MAX               = 18446744073709551615 // common_int_limits.h:101:1:
	UINT_FAST8_MAX                = 4294967295           // common_int_limits.h:98:1:
	UINT_LEAST16_MAX              = 65535                // common_int_limits.h:79:1:
	UINT_LEAST32_MAX              = 4294967295           // common_int_limits.h:80:1:
	UINT_LEAST64_MAX              = 18446744073709551615 // common_int_limits.h:81:1:
	UINT_LEAST8_MAX               = 255                  // common_int_limits.h:78:1:
	UIO_MAXIOV                    = 1024                 // uio.h:97:1:
	WCHAR_MAX                     = 0x7fffffff           // wchar_limits.h:41:1:
	WCHAR_MIN                     = -2147483648          // wchar_limits.h:40:1:
	WINT_MAX                      = 0x7fffffff           // wchar_limits.h:45:1:
	WINT_MIN                      = -2147483648          // wchar_limits.h:44:1:
	X_AMD64_BYTE_SWAP_H_          = 0                    // byte_swap.h:37:1:
	X_AMD64_INT_CONST_H_          = 0                    // int_const.h:33:1:
	X_AMD64_INT_LIMITS_H_         = 0                    // int_limits.h:33:1:
	X_AMD64_INT_MWGWTYPES_H_      = 0                    // int_mwgwtypes.h:33:1:
	X_AMD64_INT_TYPES_H_          = 0                    // int_types.h:35:1:
	X_AMD64_WCHAR_LIMITS_H_       = 0                    // wchar_limits.h:33:1:
	X_BIG_ENDIAN                  = 4321                 // endian.h:44:1:
	X_BSD_INT16_T_                = 0                    // types.h:65:1:
	X_BSD_INT32_T_                = 0                    // types.h:75:1:
	X_BSD_INT64_T_                = 0                    // types.h:85:1:
	X_BSD_INT8_T_                 = 0                    // types.h:55:1:
	X_BSD_INTPTR_T_               = 0                    // stdint.h:80:1:
	X_BSD_UINT16_T_               = 0                    // types.h:70:1:
	X_BSD_UINT32_T_               = 0                    // in.h:52:1:
	X_BSD_UINT64_T_               = 0                    // types.h:90:1:
	X_BSD_UINT8_T_                = 0                    // in.h:47:1:
	X_BSD_UINTPTR_T_              = 0                    // stdint.h:85:1:
	X_BYTE_ORDER                  = 1234                 // endian_machdep.h:3:1:
	X_FILE_OFFSET_BITS            = 64                   // <builtin>:25:1:
	X_LIB_PTHREAD_TYPES_H         = 0                    // pthread_types.h:33:1:
	X_LITTLE_ENDIAN               = 1234                 // endian.h:43:1:
	X_LP64                        = 1                    // <predefined>:268:1:
	X_NETBSD_SOURCE               = 1                    // featuretest.h:70:1:
	X_NETINET6_IN6_H_             = 0                    // in6.h:65:1:
	X_NETINET_IN_H_               = 0                    // in.h:40:1:
	X_PDP_ENDIAN                  = 3412                 // endian.h:45:1:
	X_PT_BARRIERATTR_DEAD         = 0xDEAD0808           // pthread_types.h:278:1:
	X_PT_BARRIERATTR_MAGIC        = 0x88880808           // pthread_types.h:277:1:
	X_PT_BARRIER_DEAD             = 0xDEAD0008           // pthread_types.h:270:1:
	X_PT_BARRIER_MAGIC            = 0x88880008           // pthread_types.h:269:1:
	X_PT_CONDATTR_DEAD            = 0xDEAD0006           // pthread_types.h:197:1:
	X_PT_CONDATTR_MAGIC           = 0x66660006           // pthread_types.h:196:1:
	X_PT_COND_DEAD                = 0xDEAD0005           // pthread_types.h:182:1:
	X_PT_COND_MAGIC               = 0x55550005           // pthread_types.h:181:1:
	X_PT_MUTEXATTR_DEAD           = 0xDEAD0004           // pthread_types.h:167:1:
	X_PT_MUTEXATTR_MAGIC          = 0x44440004           // pthread_types.h:166:1:
	X_PT_MUTEX_DEAD               = 0xDEAD0003           // pthread_types.h:139:1:
	X_PT_MUTEX_MAGIC              = 0x33330003           // pthread_types.h:138:1:
	X_PT_RWLOCKATTR_DEAD          = 0xDEAD0909           // pthread_types.h:253:1:
	X_PT_RWLOCKATTR_MAGIC         = 0x99990909           // pthread_types.h:252:1:
	X_PT_RWLOCK_DEAD              = 0xDEAD0009           // pthread_types.h:236:1:
	X_PT_RWLOCK_MAGIC             = 0x99990009           // pthread_types.h:235:1:
	X_PT_SPINLOCK_DEAD            = 0xDEAD0007           // pthread_types.h:213:1:
	X_PT_SPINLOCK_MAGIC           = 0x77770007           // pthread_types.h:212:1:
	X_PT_SPINLOCK_PSHARED         = 0x00000001           // pthread_types.h:214:1:
	X_QUAD_HIGHWORD               = 1                    // endian.h:84:1:
	X_QUAD_LOWWORD                = 0                    // endian.h:85:1:
	X_SS_MAXSIZE                  = 128                  // socket.h:294:1:
	X_SYS_ANSI_H_                 = 0                    // ansi.h:33:1:
	X_SYS_BSWAP_H_                = 0                    // bswap.h:6:1:
	X_SYS_CDEFS_ELF_H_            = 0                    // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_                = 0                    // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_          = 0                    // common_ansi.h:33:1:
	X_SYS_COMMON_INT_LIMITS_H_    = 0                    // common_int_limits.h:33:1:
	X_SYS_COMMON_INT_MWGWTYPES_H_ = 0                    // common_int_mwgwtypes.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_     = 0                    // common_int_types.h:33:1:
	X_SYS_ENDIAN_H_               = 0                    // endian.h:35:1:
	X_SYS_FD_SET_H_               = 0                    // fd_set.h:35:1:
	X_SYS_SIGTYPES_H_             = 0                    // sigtypes.h:40:1:
	X_SYS_SOCKET_H_               = 0                    // socket.h:64:1:
	X_SYS_STDINT_H_               = 0                    // stdint.h:33:1:
	X_SYS_TYPES_H_                = 0                    // types.h:40:1:
	X_SYS_UIO_H_                  = 0                    // uio.h:35:1:
	X_X86_64_BSWAP_H_             = 0                    // bswap.h:6:1:
	X_X86_64_CDEFS_H_             = 0                    // cdefs.h:4:1:
	X_X86_64_TYPES_H_             = 0                    // types.h:35:1:
	Pseudo_AF_HDRCMPLT            = 30                   // socket.h:240:1:
	Pseudo_AF_KEY                 = 29                   // socket.h:239:1:
	Pseudo_AF_PIP                 = 25                   // socket.h:232:1:
	Pseudo_AF_RTIP                = 22                   // socket.h:227:1:
	Pseudo_AF_XTP                 = 19                   // socket.h:222:1:
)

// file offset

const ( /* uio.h:69:1: */
	UIO_READ  = 0
	UIO_WRITE = 1
)

// Segment flag values.
const ( /* uio.h:72:1: */
	UIO_USERSPACE = 0 // from user data space
	UIO_SYSSPACE  = 1
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$NetBSD: in.h,v 1.108 2018/11/09 11:46:28 maya Exp $

// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)in.h	8.3 (Berkeley) 1/3/94

// Constants and structures defined by the internet system,
// Per RFC 790, September 1981, and numerous additions.

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// 7.18.1.4 Integer types capable of holding object pointers

type X__intptr_t = int64   /* common_int_types.h:58:27 */
type X__uintptr_t = uint64 /* common_int_types.h:59:26 */

type Uint8_t = X__uint8_t /* in.h:46:19 */

type Uint32_t = X__uint32_t /* in.h:51:20 */

// return true if value 'a' fits in type 't'

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */

type In_addr_t = X__in_addr_t /* in.h:58:21 */

type In_port_t = X__in_port_t /* in.h:63:21 */

type Sa_family_t = X__sa_family_t /* in.h:68:23 */

// Protocols

// last return value of *_input(), meaning "all job for this pkt is done".

// sysctl placeholder for (FAST_)IPSEC

// Local port number conventions:
//
// Ports < IPPORT_RESERVED are reserved for privileged processes (e.g. root),
// unless a kernel is compiled with IPNOPRIVPORTS defined.
//
// When a user does a bind(2) or connect(2) with a port number of zero,
// a non-conflicting local port address is chosen.
//
// The default range is IPPORT_ANONMIN to IPPORT_ANONMAX, although
// that is settable by sysctl(3); net.inet.ip.anonportmin and
// net.inet.ip.anonportmax respectively.
//
// A user may set the IPPROTO_IP option IP_PORTRANGE to change this
// default assignment range.
//
// The value IP_PORTRANGE_DEFAULT causes the default behavior.
//
// The value IP_PORTRANGE_HIGH is the same as IP_PORTRANGE_DEFAULT,
// and exists only for FreeBSD compatibility purposes.
//
// The value IP_PORTRANGE_LOW changes the range to the "low" are
// that is (by convention) restricted to privileged processes.
// This convention is based on "vouchsafe" principles only.
// It is only secure if you trust the remote host to restrict these ports.
// The range is IPPORT_RESERVEDMIN to IPPORT_RESERVEDMAX.

// Internet address (a structure for historical reasons)
type In_addr = struct{ Fs_addr X__in_addr_t } /* in.h:158:1 */

// Definitions of bits in internet address integers.
// On subnets, the decomposition of addresses to host and net parts
// is done according to subnet mask, not the masks here.
//
// By byte-swapping the constants, we avoid ever having to byte-swap IP
// addresses inside the kernel.  Unfortunately, user-level programs rely
// on these macros not doing byte-swapping.

// These ones aren't really net and host fields, but routing needn't know.

// Socket address, internet style.
type Sockaddr_in = struct {
	Fsin_len    Uint8_t
	Fsin_family X__sa_family_t
	Fsin_port   X__in_port_t
	Fsin_addr   struct{ Fs_addr X__in_addr_t }
	Fsin_zero   [8]X__int8_t
} /* in.h:241:1 */

// Structure used to describe IP options.
// Used to store options internally, to pass them to a process,
// or to restore options retrieved earlier.
// The ip_dst is used for the first-hop gateway when using a source route
// (this gets put into the header proper).
type Ip_opts = struct {
	Fip_dst  struct{ Fs_addr X__in_addr_t }
	Fip_opts [40]X__int8_t
} /* in.h:258:1 */

// Options for use with [gs]etsockopt at the IP level.
// First word of comment is data type; bool is stored in int.
// The add and drop membership option numbers need to match with the v6 ones

// Information sent in the control message of a datagram socket for
// IP_PKTINFO and IP_RECVPKTINFO.
type In_pktinfo = struct {
	Fipi_addr    struct{ Fs_addr X__in_addr_t }
	Fipi_ifindex uint32
} /* in.h:301:1 */

// Defaults and limits for options

// Argument structure for IP_ADD_MEMBERSHIP and IP_DROP_MEMBERSHIP.
type Ip_mreq = struct {
	Fimr_multiaddr struct{ Fs_addr X__in_addr_t }
	Fimr_interface struct{ Fs_addr X__in_addr_t }
} /* in.h:318:1 */

// Argument for IP_PORTRANGE:
// - which range to search when port is unspecified at bind() or connect()

// Definitions for inet sysctl operations.
//
// Third level is protocol number.
// Fourth level is desired variable within that protocol.

// Names for IP sysctl objects
// IPCTL_DEFMTU=4, never implemented

// INET6 stuff
//	$NetBSD: in6.h,v ******** 2019/09/06 19:43:00 martin Exp $
//	$KAME: in6.h,v 1.83 2001/03/29 02:55:07 jinmei Exp $

// Copyright (C) 1995, 1996, 1997, and 1998 WIDE Project.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the project nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE PROJECT AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE PROJECT OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)in.h	8.3 (Berkeley) 1/3/94

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: socket.h,v 1.129 2018/11/04 16:30:29 christos Exp $

// Copyright (C) 1995, 1996, 1997, and 1998 WIDE Project.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the project nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE PROJECT AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE PROJECT OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// Copyright (c) 1982, 1985, 1986, 1988, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)socket.h	8.6 (Berkeley) 5/3/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Definitions related to sockets: types, address families, options.

// Data types.
//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Socklen_t = X__socklen_t /* socket.h:83:21 */

type Ssize_t = int64 /* socket.h:95:23 */

//	$NetBSD: uio.h,v 1.36 2011/07/27 13:20:07 uebayasi Exp $

// Copyright (c) 1982, 1986, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)uio.h	8.5 (Berkeley) 2/22/94

//	$NetBSD: ansi.h,v 1.11 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  Size_t
} /* uio.h:56:1 */

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Off_t = X__off_t /* uio.h:65:18 */

//	$NetBSD: sigtypes.h,v 1.11 2017/01/12 18:29:14 christos Exp $

// Copyright (c) 1982, 1986, 1989, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)signal.h	8.4 (Berkeley) 5/4/95

// This header file defines various signal-related types.  We also keep
// the macros to manipulate sigset_t here, to encapsulate knowledge of
// its internals.

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: ansi.h,v 1.11 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Sigset_t = struct{ F__bits [4]X__uint32_t } /* sigtypes.h:62:3 */

// Macro for manipulating signal masks.

type Sigaltstack = struct {
	Fss_sp       uintptr
	Fss_size     Size_t
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
} /* sigtypes.h:108:9 */

// Macro for manipulating signal masks.

type Stack_t = Sigaltstack /* sigtypes.h:116:3 */

// Socket types.

// Option flags per-socket.
// 	SO_OTIMESTAMP	0x0400

// Allowed default option flags

// Additional options, not kept in so_options.
// SO_OSNDTIMEO		0x1005
// SO_ORCVTIMEO		0x1006

// Structure used for manipulating linger option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:182:1 */

type Accept_filter_arg = struct {
	Faf_name [16]int8
	Faf_arg  [240]int8
} /* socket.h:187:1 */

// Level number for (get/set)sockopt() to apply to socket itself.

// Address families.

// Structure used by kernel to store most
// addresses.
type Sockaddr = struct {
	Fsa_len    X__uint8_t
	Fsa_family X__sa_family_t
	Fsa_data   [14]int8
} /* socket.h:255:1 */

// RFC 2553: protocol-independent placeholder for socket addresses

type Sockaddr_storage = struct {
	Fss_len     X__uint8_t
	Fss_family  X__sa_family_t
	F__ss_pad1  [6]int8
	F__ss_align X__int64_t
	F__ss_pad2  [112]int8
} /* socket.h:301:1 */

// Protocol families, same as address families for now.

type Pid_t = X__pid_t /* socket.h:366:18 */ // process id

type Gid_t = X__gid_t /* socket.h:371:18 */ // group id

type Uid_t = X__uid_t /* socket.h:376:18 */ // user id

// Socket credentials.
type Sockcred = struct {
	Fsc_pid     X__pid_t
	Fsc_uid     X__uid_t
	Fsc_euid    X__uid_t
	Fsc_gid     X__gid_t
	Fsc_egid    X__gid_t
	Fsc_ngroups int32
	Fsc_groups  [1]X__gid_t
} /* socket.h:383:1 */

// Compute size of a sockcred structure with groups.

// Definition for CTL_NET PCB fetching sysctls
type Kinfo_pcb = struct {
	Fki_pcbaddr  X__uint64_t
	Fki_ppcbaddr X__uint64_t
	Fki_sockaddr X__uint64_t
	Fki_family   X__uint32_t
	Fki_type     X__uint32_t
	Fki_protocol X__uint32_t
	Fki_pflags   X__uint32_t
	Fki_sostate  X__uint32_t
	Fki_prstate  X__uint32_t
	Fki_tstate   X__int32_t
	Fki_tflags   X__uint32_t
	Fki_rcvq     X__uint64_t
	Fki_sndq     X__uint64_t
	Fki_s        struct {
		F_kis_src struct {
			Fsa_len    X__uint8_t
			Fsa_family X__sa_family_t
			Fsa_data   [14]int8
		}
		F__ccgo_pad1 [248]byte
	}
	Fki_d struct {
		F_kid_dst struct {
			Fsa_len    X__uint8_t
			Fsa_family X__sa_family_t
			Fsa_data   [14]int8
		}
		F__ccgo_pad1 [248]byte
	}
	Fki_inode   X__uint64_t
	Fki_vnode   X__uint64_t
	Fki_conn    X__uint64_t
	Fki_refs    X__uint64_t
	Fki_nextref X__uint64_t
} /* socket.h:404:1 */

// PF_ROUTE - Routing table
//
// Three additional levels are defined:
//	Fourth: address family, 0 is wildcard
//	Fifth: type of info, defined below
//	Sixth: flag(s) to mask with for NET_RT_FLAGS

// Maximum queue length specifiable by listen(2).

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

// Message header for recvmsg and sendmsg calls.
// Used value-result for recvmsg, value only for sendmsg.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    X__socklen_t
	F__ccgo_pad1    [4]byte
	Fmsg_iov        uintptr
	Fmsg_iovlen     int32
	F__ccgo_pad2    [4]byte
	Fmsg_control    uintptr
	Fmsg_controllen X__socklen_t
	Fmsg_flags      int32
} /* socket.h:479:1 */

type Mmsghdr = struct {
	Fmsg_hdr struct {
		Fmsg_name       uintptr
		Fmsg_namelen    X__socklen_t
		F__ccgo_pad1    [4]byte
		Fmsg_iov        uintptr
		Fmsg_iovlen     int32
		F__ccgo_pad2    [4]byte
		Fmsg_control    uintptr
		Fmsg_controllen X__socklen_t
		Fmsg_flags      int32
	}
	Fmsg_len     uint32
	F__ccgo_pad1 [4]byte
} /* socket.h:506:1 */

// Extra flags used internally only

// Header for ancillary data objects in msg_control buffer.
// Used for additional information with/about a datagram
// not expressible by flags.  The format is a sequence
// of message elements headed by cmsghdr structures.
type Cmsghdr = struct {
	Fcmsg_len   X__socklen_t
	Fcmsg_level int32
	Fcmsg_type  int32
} /* socket.h:525:1 */

//	$NetBSD: endian.h,v 1.30 2016/02/27 21:37:35 christos Exp $

// Copyright (c) 1987, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)endian.h	8.1 (Berkeley) 6/11/93

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Definitions for byte order, according to byte significance from low
// address to high.

// C-family endian-ness definitions

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: types.h,v 1.102 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

// Machine type dependent parameters.
//	$NetBSD: types.h,v 1.60 2019/04/06 03:06:24 thorpej Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

type X__register_t = int64           /* types.h:68:19 */
type X__cpu_simple_lock_nv_t = uint8 /* types.h:69:24 */

// __cpu_simple_lock_t used to be a full word.

// The amd64 does not have strict alignment requirements.

//	$NetBSD: ansi.h,v 1.11 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Int8_t = X__int8_t /* types.h:54:18 */

type Int16_t = X__int16_t /* types.h:64:19 */

type Uint16_t = X__uint16_t /* types.h:69:20 */

type Int32_t = X__int32_t /* types.h:74:19 */

type Int64_t = X__int64_t /* types.h:84:19 */

type Uint64_t = X__uint64_t /* types.h:89:20 */

type U_int8_t = Uint8_t   /* types.h:93:18 */
type U_int16_t = Uint16_t /* types.h:94:18 */
type U_int32_t = Uint32_t /* types.h:95:18 */
type U_int64_t = Uint64_t /* types.h:96:18 */

//	$NetBSD: endian.h,v 1.1 2003/04/26 18:39:40 fvdl Exp $

//	$NetBSD: endian.h,v 1.30 2016/02/27 21:37:35 christos Exp $

// Copyright (c) 1987, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)endian.h	8.1 (Berkeley) 6/11/93

type U_char = uint8   /* types.h:101:23 */
type U_short = uint16 /* types.h:102:24 */
type U_int = uint32   /* types.h:103:22 */
type U_long = uint64  /* types.h:104:23 */

type Unchar = uint8  /* types.h:106:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:107:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:108:22 */ // Sys V compatibility
type Ulong = uint64  /* types.h:109:23 */ // Sys V compatibility

type U_quad_t = Uint64_t /* types.h:112:18 */ // quads
type Quad_t = Int64_t    /* types.h:113:18 */
type Qaddr_t = uintptr   /* types.h:114:16 */

// The types longlong_t and u_longlong_t exist for use with the
// Sun-derived XDR routines involving these types, and their usage
// in other contexts is discouraged.  Further note that these types
// may not be equivalent to "long long" and "unsigned long long",
// they are only guaranteed to be signed and unsigned 64-bit types
// respectively.  Portable programs that need 64-bit types should use
// the C99 types int64_t and uint64_t instead.

type Longlong_t = Int64_t    /* types.h:126:18 */ // for XDR
type U_longlong_t = Uint64_t /* types.h:127:18 */ // for XDR

type Blkcnt_t = Int64_t  /* types.h:129:18 */ // fs block count
type Blksize_t = Int32_t /* types.h:130:18 */ // fs optimal block size

type Fsblkcnt_t = X__fsblkcnt_t /* types.h:133:22 */ // fs block count (statvfs)

type Fsfilcnt_t = X__fsfilcnt_t /* types.h:138:22 */ // fs file count

// We don't and shouldn't use caddr_t in the kernel anymore
type Caddr_t = X__caddr_t /* types.h:145:19 */ // core address

type Daddr_t = Int64_t /* types.h:154:18 */ // disk address

type Dev_t = Uint64_t   /* types.h:157:18 */ // device number
type Fixpt_t = Uint32_t /* types.h:158:18 */ // fixed point number

type Id_t = Uint32_t  /* types.h:165:18 */ // group id, process id or user id
type Ino_t = Uint64_t /* types.h:166:18 */ // inode number
type Key_t = int64    /* types.h:167:15 */ // IPC key (for Sys V IPC)

type Mode_t = X__mode_t /* types.h:170:18 */ // permissions

type Nlink_t = Uint32_t /* types.h:174:18 */ // link count

type Lwpid_t = Int32_t /* types.h:185:18 */ // LWP id
type Rlim_t = Uint64_t /* types.h:186:18 */ // resource limit
type Segsz_t = Int32_t /* types.h:187:18 */ // segment size
type Swblk_t = Int32_t /* types.h:188:18 */ // swap offset

type Mqd_t = int32 /* types.h:195:14 */

type Cpuid_t = uint64 /* types.h:197:23 */

type Psetid_t = int32 /* types.h:199:14 */

type X__cpu_simple_lock_t = X__cpu_simple_lock_nv_t /* types.h:201:41 */

// Major, minor numbers, dev_t's.
type X__devmajor_t = Int32_t /* types.h:255:17 */
type X__devminor_t = Int32_t /* types.h:255:31 */

type Clock_t = uint32 /* types.h:268:24 */

type Time_t = X__int64_t /* types.h:289:23 */

type Clockid_t = int32 /* types.h:294:26 */

type Timer_t = int32 /* types.h:299:24 */

type Suseconds_t = int32 /* types.h:304:27 */

type Useconds_t = uint32 /* types.h:309:26 */

//	$NetBSD: fd_set.h,v 1.7 2018/06/24 12:05:40 kamil Exp $

// -
// Copyright (c) 1992, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	8.4 (Berkeley) 1/21/94

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

// Implementation dependent defines, hidden from user space.
// POSIX does not specify them.

type X__fd_mask = X__uint32_t /* fd_set.h:46:20 */

// 32 = 2 ^ 5

// Select uses bit fields of file descriptors.  These macros manipulate
// such bit fields.  Note: FD_SETSIZE may be defined by the user.

type Fd_set1 = struct{ Ffds_bits [8]X__fd_mask } /* fd_set.h:66:9 */

// 32 = 2 ^ 5

// Select uses bit fields of file descriptors.  These macros manipulate
// such bit fields.  Note: FD_SETSIZE may be defined by the user.

type Fd_set = Fd_set1 /* fd_set.h:68:3 */

// Expose our internals if we are not required to hide them.

type Kauth_cred_t = uintptr /* types.h:318:27 */

type Pri_t = int32 /* types.h:320:13 */

//	$NetBSD: pthread_types.h,v 1.23 2017/09/09 23:21:45 kamil Exp $

// -
// Copyright (c) 2001, 2008 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Nathan J. Williams.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// We use the "pthread_spin_t" name internally; "pthread_spinlock_t" is the
// POSIX spinlock object.
//
// C++ expects to be using PTHREAD_FOO_INITIALIZER as a member initializer.
// This does not work for volatile types.  Since C++ does not touch the guts
// of those types, we do not include volatile in the C++ definitions.
type Pthread_spin_t = X__cpu_simple_lock_t /* pthread_types.h:43:29 */
type X__pthread_spin_t = Pthread_spin_t    /* pthread_types.h:48:24 */

// Copied from PTQ_HEAD in pthread_queue.h

type Pthread_queue_struct_t = struct {
	Fptqh_first uintptr
	Fptqh_last  uintptr
} /* pthread_types.h:61:1 */

type Pthread_queue_t = Pthread_queue_struct_t /* pthread_types.h:62:39 */
type X__pthread_attr_st = struct {
	Fpta_magic   uint32
	Fpta_flags   int32
	Fpta_private uintptr
} /* pthread_types.h:65:1 */

type X__pthread_mutex_st = struct {
	Fptm_magic      uint32
	Fptm_errorcheck X__pthread_spin_t
	Fptm_pad1       [3]Uint8_t
	F__8            struct{ Fptm_ceiling uint8 }
	Fptm_pad2       [3]Uint8_t
	F__ccgo_pad1    [4]byte
	Fptm_owner      Pthread_t
	Fptm_waiters    uintptr
	Fptm_recursed   uint32
	F__ccgo_pad2    [4]byte
	Fptm_spare2     uintptr
} /* pthread_types.h:66:1 */

type X__pthread_mutexattr_st = struct {
	Fptma_magic   uint32
	F__ccgo_pad1  [4]byte
	Fptma_private uintptr
} /* pthread_types.h:67:1 */

type X__pthread_cond_st = struct {
	Fptc_magic   uint32
	Fptc_lock    X__pthread_spin_t
	F__ccgo_pad1 [3]byte
	Fptc_waiters Pthread_queue_t
	Fptc_mutex   uintptr
	Fptc_private uintptr
} /* pthread_types.h:68:1 */

type X__pthread_condattr_st = struct {
	Fptca_magic   uint32
	F__ccgo_pad1  [4]byte
	Fptca_private uintptr
} /* pthread_types.h:69:1 */

type X__pthread_rwlock_st = struct {
	Fptr_magic     uint32
	Fptr_interlock X__pthread_spin_t
	F__ccgo_pad1   [3]byte
	Fptr_rblocked  Pthread_queue_t
	Fptr_wblocked  Pthread_queue_t
	Fptr_nreaders  uint32
	F__ccgo_pad2   [4]byte
	Fptr_owner     Pthread_t
	Fptr_private   uintptr
} /* pthread_types.h:71:1 */

type X__pthread_rwlockattr_st = struct {
	Fptra_magic   uint32
	F__ccgo_pad1  [4]byte
	Fptra_private uintptr
} /* pthread_types.h:72:1 */

type X__pthread_barrier_st = struct {
	Fptb_magic      uint32
	Fptb_lock       Pthread_spin_t
	F__ccgo_pad1    [3]byte
	Fptb_waiters    Pthread_queue_t
	Fptb_initcount  uint32
	Fptb_curcount   uint32
	Fptb_generation uint32
	F__ccgo_pad2    [4]byte
	Fptb_private    uintptr
} /* pthread_types.h:73:1 */

type X__pthread_barrierattr_st = struct {
	Fptba_magic   uint32
	F__ccgo_pad1  [4]byte
	Fptba_private uintptr
} /* pthread_types.h:74:1 */

type Pthread_t = uintptr                           /* pthread_types.h:76:29 */
type Pthread_attr_t = X__pthread_attr_st           /* pthread_types.h:77:34 */
type Pthread_mutex_t = X__pthread_mutex_st         /* pthread_types.h:78:35 */
type Pthread_mutexattr_t = X__pthread_mutexattr_st /* pthread_types.h:79:39 */
type Pthread_cond_t = X__pthread_cond_st           /* pthread_types.h:80:34 */
type Pthread_condattr_t = X__pthread_condattr_st   /* pthread_types.h:81:38 */
type X__pthread_once_st = struct {
	Fpto_mutex   Pthread_mutex_t
	Fpto_done    int32
	F__ccgo_pad1 [4]byte
} /* pthread_types.h:82:9 */

type Pthread_once_t = X__pthread_once_st /* pthread_types.h:82:34 */
type X__pthread_spinlock_st = struct {
	Fpts_magic   uint32
	Fpts_spin    X__pthread_spin_t
	F__ccgo_pad1 [3]byte
	Fpts_flags   int32
} /* pthread_types.h:83:9 */

type Pthread_spinlock_t = X__pthread_spinlock_st       /* pthread_types.h:83:38 */
type Pthread_rwlock_t = X__pthread_rwlock_st           /* pthread_types.h:84:36 */
type Pthread_rwlockattr_t = X__pthread_rwlockattr_st   /* pthread_types.h:85:40 */
type Pthread_barrier_t = X__pthread_barrier_st         /* pthread_types.h:86:37 */
type Pthread_barrierattr_t = X__pthread_barrierattr_st /* pthread_types.h:87:41 */
type Pthread_key_t = int32                             /* pthread_types.h:88:13 */

//      $NetBSD: bswap.h,v 1.19 2015/03/12 15:28:16 christos Exp $

// Written by Manuel Bouyer. Public domain

//	$NetBSD: stdint.h,v 1.8 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 2001, 2004 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 1990 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)types.h	7.5 (Berkeley) 3/9/91

type Intptr_t = X__intptr_t /* stdint.h:79:20 */

type Uintptr_t = X__uintptr_t /* stdint.h:84:21 */

//	$NetBSD: int_mwgwtypes.h,v 1.8 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2001 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_mwgwtypes.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.2 Minimum-width integer types

type Int_least8_t = int8     /* common_int_mwgwtypes.h:45:32 */
type Uint_least8_t = uint8   /* common_int_mwgwtypes.h:46:32 */
type Int_least16_t = int16   /* common_int_mwgwtypes.h:47:32 */
type Uint_least16_t = uint16 /* common_int_mwgwtypes.h:48:32 */
type Int_least32_t = int32   /* common_int_mwgwtypes.h:49:32 */
type Uint_least32_t = uint32 /* common_int_mwgwtypes.h:50:32 */
type Int_least64_t = int64   /* common_int_mwgwtypes.h:51:32 */
type Uint_least64_t = uint64 /* common_int_mwgwtypes.h:52:32 */

// 7.18.1.3 Fastest minimum-width integer types
type Int_fast8_t = int32    /* common_int_mwgwtypes.h:55:32 */
type Uint_fast8_t = uint32  /* common_int_mwgwtypes.h:56:32 */
type Int_fast16_t = int32   /* common_int_mwgwtypes.h:57:32 */
type Uint_fast16_t = uint32 /* common_int_mwgwtypes.h:58:32 */
type Int_fast32_t = int32   /* common_int_mwgwtypes.h:59:32 */
type Uint_fast32_t = uint32 /* common_int_mwgwtypes.h:60:32 */
type Int_fast64_t = int64   /* common_int_mwgwtypes.h:61:32 */
type Uint_fast64_t = uint64 /* common_int_mwgwtypes.h:62:32 */

// ******** Greatest-width integer types

type Intmax_t = int64   /* common_int_mwgwtypes.h:66:33 */
type Uintmax_t = uint64 /* common_int_mwgwtypes.h:67:32 */

// Identification of the network protocol stack
// for *BSD-current/release: http://www.kame.net/dev/cvsweb.cgi/kame/COVERAGE
// has the table of implementation/integration differences.

// Local port number conventions:
//
// Ports < IPPORT_RESERVED are reserved for privileged processes (e.g. root),
// unless a kernel is compiled with IPNOPRIVPORTS defined.
//
// When a user does a bind(2) or connect(2) with a port number of zero,
// a non-conflicting local port address is chosen.
//
// The default range is IPPORT_ANONMIN to IPPORT_ANONMAX, although
// that is settable by sysctl(3); net.inet.ip.anonportmin and
// net.inet.ip.anonportmax respectively.
//
// A user may set the IPPROTO_IP option IP_PORTRANGE to change this
// default assignment range.
//
// The value IP_PORTRANGE_DEFAULT causes the default behavior.
//
// The value IP_PORTRANGE_HIGH is the same as IP_PORTRANGE_DEFAULT,
// and exists only for FreeBSD compatibility purposes.
//
// The value IP_PORTRANGE_LOW changes the range to the "low" are
// that is (by convention) restricted to privileged processes.
// This convention is based on "vouchsafe" principles only.
// It is only secure if you trust the remote host to restrict these ports.
// The range is IPPORT_RESERVEDMIN to IPPORT_RESERVEDMAX.

// IPv6 address
type In6_addr = struct {
	F__u6_addr struct {
		F__ccgo_pad1 [0]uint32
		F__u6_addr8  [16]X__uint8_t
	}
} /* in6.h:123:1 */

// Socket address for IPv6
type Sockaddr_in6 = struct {
	Fsin6_len      Uint8_t
	Fsin6_family   X__sa_family_t
	Fsin6_port     X__in_port_t
	Fsin6_flowinfo Uint32_t
	Fsin6_addr     struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]X__uint8_t
		}
	}
	Fsin6_scope_id Uint32_t
} /* in6.h:146:1 */

// Unspecified

// Loopback

// IPv4 compatible

// Mapped

// KAME Scope Values

// Unicast Scope
// Note that we must check topmost 10 bits only, not 16 bits (see RFC2373).

// Multicast

// Multicast Scope

// Options for use with [gs]etsockopt at the IPV6 level.
// First word of comment is data type; bool is stored in int.
// no hdrincl
// The join and leave membership option numbers need to match with the v4 ones
// RFC2292 options

// new socket options introduced in RFC3542

// more new socket options introduced in RFC3542

// to define items, should talk with KAME guys first, for *BSD compatibility

// Defaults and limits for options

// Argument structure for IPV6_JOIN_GROUP and IPV6_LEAVE_GROUP.
type Ipv6_mreq = struct {
	Fipv6mr_multiaddr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]X__uint8_t
		}
	}
	Fipv6mr_interface uint32
} /* in6.h:459:1 */

// IPV6_PKTINFO: Packet information(RFC2292 sec 5)
type In6_pktinfo = struct {
	Fipi6_addr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]X__uint8_t
		}
	}
	Fipi6_ifindex uint32
} /* in6.h:467:1 */

// Control structure for IPV6_RECVPATHMTU socket option.
type Ip6_mtuinfo = struct {
	Fip6m_addr struct {
		Fsin6_len      Uint8_t
		Fsin6_family   X__sa_family_t
		Fsin6_port     X__in_port_t
		Fsin6_flowinfo Uint32_t
		Fsin6_addr     struct {
			F__u6_addr struct {
				F__ccgo_pad1 [0]uint32
				F__u6_addr8  [16]X__uint8_t
			}
		}
		Fsin6_scope_id Uint32_t
	}
	Fip6m_mtu Uint32_t
} /* in6.h:475:1 */

var _ int8 /* gen.c:2:13: */
