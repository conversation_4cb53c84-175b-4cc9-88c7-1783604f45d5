// Code generated by 'ccgo netinet/in/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o netinet/in/in_freebsd_amd64.go -pkgname in', DO NOT EDIT.

package in

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	BIG_ENDIAN                    = 4321       // _endian.h:70:1:
	BYTE_ORDER                    = 1234       // _endian.h:72:1:
	ICMP6_FILTER                  = 18         // in6.h:420:1:
	ICMPV6CTL_ND6_ONLINKNSRFC4861 = 47         // in6.h:638:1:
	INET6_ADDRSTRLEN              = 46         // in6.h:112:1:
	INET_ADDRSTRLEN               = 16         // in.h:130:1:
	IN_CLASSA_HOST                = 0x00ffffff // in.h:357:1:
	IN_CLASSA_MAX                 = 128        // in.h:358:1:
	IN_CLASSA_NET                 = 0xff000000 // in.h:355:1:
	IN_CLASSA_NSHIFT              = 24         // in.h:356:1:
	IN_CLASSB_HOST                = 0x0000ffff // in.h:363:1:
	IN_CLASSB_MAX                 = 65536      // in.h:364:1:
	IN_CLASSB_NET                 = 0xffff0000 // in.h:361:1:
	IN_CLASSB_NSHIFT              = 16         // in.h:362:1:
	IN_CLASSC_HOST                = 0x000000ff // in.h:369:1:
	IN_CLASSC_NET                 = 0xffffff00 // in.h:367:1:
	IN_CLASSC_NSHIFT              = 8          // in.h:368:1:
	IN_CLASSD_HOST                = 0x0fffffff // in.h:379:1:
	IN_CLASSD_NET                 = 0xf0000000 // in.h:377:1:
	IN_CLASSD_NSHIFT              = 28         // in.h:378:1:
	IN_HISTORICAL_NETS            = 0          // in.h:351:1:
	IN_LOOPBACKNET                = 127        // in.h:412:1:
	IN_NETMASK_DEFAULT            = 0xffffff00 // in.h:372:1:
	IPCTL_ACCEPTSOURCEROUTE       = 13         // in.h:646:1:
	IPCTL_DEFTTL                  = 3          // in.h:634:1:
	IPCTL_DIRECTEDBROADCAST       = 9          // in.h:642:1:
	IPCTL_FASTFORWARDING          = 14         // in.h:647:1:
	IPCTL_FORWARDING              = 1          // in.h:632:1:
	IPCTL_GIF_TTL                 = 16         // in.h:649:1:
	IPCTL_INTRDQDROPS             = 18         // in.h:651:1:
	IPCTL_INTRDQMAXLEN            = 17         // in.h:650:1:
	IPCTL_INTRQDROPS              = 11         // in.h:644:1:
	IPCTL_INTRQMAXLEN             = 10         // in.h:643:1:
	IPCTL_SENDREDIRECTS           = 2          // in.h:633:1:
	IPCTL_SOURCEROUTE             = 8          // in.h:641:1:
	IPCTL_STATS                   = 12         // in.h:645:1:
	IPPORT_EPHEMERALFIRST         = 10000      // in.h:325:1:
	IPPORT_EPHEMERALLAST          = 65535      // in.h:326:1:
	IPPORT_HIFIRSTAUTO            = 49152      // in.h:331:1:
	IPPORT_HILASTAUTO             = 65535      // in.h:332:1:
	IPPORT_MAX                    = 65535      // in.h:342:1:
	IPPORT_RESERVED               = 1024       // in.h:320:1:
	IPPORT_RESERVEDSTART          = 600        // in.h:340:1:
	IPPROTO_3PC                   = 34         // in.h:173:1:
	IPPROTO_ADFS                  = 68         // in.h:206:1:
	IPPROTO_AH                    = 51         // in.h:189:1:
	IPPROTO_AHIP                  = 61         // in.h:199:1:
	IPPROTO_APES                  = 99         // in.h:237:1:
	IPPROTO_ARGUS                 = 13         // in.h:153:1:
	IPPROTO_AX25                  = 93         // in.h:231:1:
	IPPROTO_BHA                   = 49         // in.h:187:1:
	IPPROTO_BLT                   = 30         // in.h:169:1:
	IPPROTO_BRSATMON              = 76         // in.h:214:1:
	IPPROTO_CARP                  = 112        // in.h:247:1:
	IPPROTO_CFTP                  = 62         // in.h:200:1:
	IPPROTO_CHAOS                 = 16         // in.h:156:1:
	IPPROTO_CMTP                  = 38         // in.h:177:1:
	IPPROTO_CPHB                  = 73         // in.h:211:1:
	IPPROTO_CPNX                  = 72         // in.h:210:1:
	IPPROTO_DCCP                  = 33         // in.h:172:1:
	IPPROTO_DDP                   = 37         // in.h:176:1:
	IPPROTO_DGP                   = 86         // in.h:224:1:
	IPPROTO_DIVERT                = 258        // in.h:262:1:
	IPPROTO_DONE                  = 257        // in.h:259:1:
	IPPROTO_DSTOPTS               = 60         // in.h:198:1:
	IPPROTO_EGP                   = 8          // in.h:148:1:
	IPPROTO_EMCON                 = 14         // in.h:154:1:
	IPPROTO_ENCAP                 = 98         // in.h:236:1:
	IPPROTO_EON                   = 80         // in.h:218:1:
	IPPROTO_ESP                   = 50         // in.h:188:1:
	IPPROTO_ETHERIP               = 97         // in.h:235:1:
	IPPROTO_FRAGMENT              = 44         // in.h:182:1:
	IPPROTO_GGP                   = 3          // in.h:144:1:
	IPPROTO_GMTP                  = 100        // in.h:238:1:
	IPPROTO_GRE                   = 47         // in.h:185:1:
	IPPROTO_HELLO                 = 63         // in.h:201:1:
	IPPROTO_HIP                   = 139        // in.h:243:1:
	IPPROTO_HMP                   = 20         // in.h:159:1:
	IPPROTO_HOPOPTS               = 0          // in.h:142:1:
	IPPROTO_ICMP                  = 1          // in.h:44:1:
	IPPROTO_ICMPV6                = 58         // in.h:196:1:
	IPPROTO_IDP                   = 22         // in.h:161:1:
	IPPROTO_IDPR                  = 35         // in.h:174:1:
	IPPROTO_IDRP                  = 45         // in.h:183:1:
	IPPROTO_IGMP                  = 2          // in.h:143:1:
	IPPROTO_IGP                   = 85         // in.h:223:1:
	IPPROTO_IGRP                  = 88         // in.h:226:1:
	IPPROTO_IL                    = 40         // in.h:179:1:
	IPPROTO_INLSP                 = 52         // in.h:190:1:
	IPPROTO_INP                   = 32         // in.h:171:1:
	IPPROTO_IP                    = 0          // in.h:43:1:
	IPPROTO_IPCOMP                = 108        // in.h:239:1:
	IPPROTO_IPCV                  = 71         // in.h:209:1:
	IPPROTO_IPEIP                 = 94         // in.h:232:1:
	IPPROTO_IPIP                  = 4          // in.h:146:1:
	IPPROTO_IPPC                  = 67         // in.h:205:1:
	IPPROTO_IPV4                  = 4          // in.h:145:1:
	IPPROTO_IPV6                  = 41         // in.h:128:1:
	IPPROTO_IRTP                  = 28         // in.h:167:1:
	IPPROTO_KRYPTOLAN             = 65         // in.h:203:1:
	IPPROTO_LARP                  = 91         // in.h:229:1:
	IPPROTO_LEAF1                 = 25         // in.h:164:1:
	IPPROTO_LEAF2                 = 26         // in.h:165:1:
	IPPROTO_MAX                   = 256        // in.h:256:1:
	IPPROTO_MEAS                  = 19         // in.h:158:1:
	IPPROTO_MH                    = 135        // in.h:241:1:
	IPPROTO_MHRP                  = 48         // in.h:186:1:
	IPPROTO_MICP                  = 95         // in.h:233:1:
	IPPROTO_MOBILE                = 55         // in.h:193:1:
	IPPROTO_MPLS                  = 137        // in.h:249:1:
	IPPROTO_MTP                   = 92         // in.h:230:1:
	IPPROTO_MUX                   = 18         // in.h:157:1:
	IPPROTO_ND                    = 77         // in.h:215:1:
	IPPROTO_NHRP                  = 54         // in.h:192:1:
	IPPROTO_NONE                  = 59         // in.h:197:1:
	IPPROTO_NSP                   = 31         // in.h:170:1:
	IPPROTO_NVPII                 = 11         // in.h:151:1:
	IPPROTO_OLD_DIVERT            = 254        // in.h:255:1:
	IPPROTO_OSPFIGP               = 89         // in.h:227:1:
	IPPROTO_PFSYNC                = 240        // in.h:250:1:
	IPPROTO_PGM                   = 113        // in.h:248:1:
	IPPROTO_PIGP                  = 9          // in.h:149:1:
	IPPROTO_PIM                   = 103        // in.h:246:1:
	IPPROTO_PRM                   = 21         // in.h:160:1:
	IPPROTO_PUP                   = 12         // in.h:152:1:
	IPPROTO_PVP                   = 75         // in.h:213:1:
	IPPROTO_RAW                   = 255        // in.h:129:1:
	IPPROTO_RCCMON                = 10         // in.h:150:1:
	IPPROTO_RDP                   = 27         // in.h:166:1:
	IPPROTO_RESERVED_253          = 253        // in.h:251:1:
	IPPROTO_RESERVED_254          = 254        // in.h:252:1:
	IPPROTO_ROUTING               = 43         // in.h:181:1:
	IPPROTO_RSVP                  = 46         // in.h:184:1:
	IPPROTO_RVD                   = 66         // in.h:204:1:
	IPPROTO_SATEXPAK              = 64         // in.h:202:1:
	IPPROTO_SATMON                = 69         // in.h:207:1:
	IPPROTO_SCCSP                 = 96         // in.h:234:1:
	IPPROTO_SCTP                  = 132        // in.h:240:1:
	IPPROTO_SDRP                  = 42         // in.h:180:1:
	IPPROTO_SEND                  = 259        // in.h:263:1:
	IPPROTO_SHIM6                 = 140        // in.h:244:1:
	IPPROTO_SKIP                  = 57         // in.h:195:1:
	IPPROTO_SPACER                = 32767      // in.h:269:1:
	IPPROTO_SRPC                  = 90         // in.h:228:1:
	IPPROTO_ST                    = 7          // in.h:147:1:
	IPPROTO_SVMTP                 = 82         // in.h:220:1:
	IPPROTO_SWIPE                 = 53         // in.h:191:1:
	IPPROTO_TCF                   = 87         // in.h:225:1:
	IPPROTO_TCP                   = 6          // in.h:45:1:
	IPPROTO_TLSP                  = 56         // in.h:194:1:
	IPPROTO_TP                    = 29         // in.h:168:1:
	IPPROTO_TPXX                  = 39         // in.h:178:1:
	IPPROTO_TRUNK1                = 23         // in.h:162:1:
	IPPROTO_TRUNK2                = 24         // in.h:163:1:
	IPPROTO_TTP                   = 84         // in.h:222:1:
	IPPROTO_UDP                   = 17         // in.h:46:1:
	IPPROTO_UDPLITE               = 136        // in.h:242:1:
	IPPROTO_VINES                 = 83         // in.h:221:1:
	IPPROTO_VISA                  = 70         // in.h:208:1:
	IPPROTO_VMTP                  = 81         // in.h:219:1:
	IPPROTO_WBEXPAK               = 79         // in.h:217:1:
	IPPROTO_WBMON                 = 78         // in.h:216:1:
	IPPROTO_WSN                   = 74         // in.h:212:1:
	IPPROTO_XNET                  = 15         // in.h:155:1:
	IPPROTO_XTP                   = 36         // in.h:175:1:
	IPV6CTL_ACCEPT_RTADV          = 12         // in6.h:599:1:
	IPV6CTL_ADDRCTLPOLICY         = 38         // in6.h:624:1:
	IPV6CTL_AUTO_FLOWLABEL        = 17         // in6.h:604:1:
	IPV6CTL_AUTO_LINKLOCAL        = 35         // in6.h:621:1:
	IPV6CTL_DAD_COUNT             = 16         // in6.h:603:1:
	IPV6CTL_DEFHLIM               = 3          // in6.h:588:1:
	IPV6CTL_DEFMCASTHLIM          = 18         // in6.h:605:1:
	IPV6CTL_FORWARDING            = 1          // in6.h:586:1:
	IPV6CTL_FORWSRCRT             = 5          // in6.h:592:1:
	IPV6CTL_GIF_HLIM              = 19         // in6.h:606:1:
	IPV6CTL_HDRNESTLIMIT          = 15         // in6.h:602:1:
	IPV6CTL_INTRDQMAXLEN          = 52         // in6.h:645:1:
	IPV6CTL_INTRQMAXLEN           = 51         // in6.h:644:1:
	IPV6CTL_KAME_VERSION          = 20         // in6.h:607:1:
	IPV6CTL_LOG_INTERVAL          = 14         // in6.h:601:1:
	IPV6CTL_MAXFRAGBUCKETSIZE     = 54         // in6.h:648:1:
	IPV6CTL_MAXFRAGPACKETS        = 9          // in6.h:596:1:
	IPV6CTL_MAXFRAGS              = 41         // in6.h:627:1:
	IPV6CTL_MAXFRAGSPERPACKET     = 53         // in6.h:647:1:
	IPV6CTL_MAXID                 = 55         // in6.h:649:1:
	IPV6CTL_MCAST_PMTU            = 44         // in6.h:632:1:
	IPV6CTL_MRTPROTO              = 8          // in6.h:595:1:
	IPV6CTL_MRTSTATS              = 7          // in6.h:594:1:
	IPV6CTL_NORBIT_RAIF           = 49         // in6.h:640:1:
	IPV6CTL_NO_RADR               = 48         // in6.h:639:1:
	IPV6CTL_PREFER_TEMPADDR       = 37         // in6.h:623:1:
	IPV6CTL_RFC6204W3             = 50         // in6.h:642:1:
	IPV6CTL_RIP6STATS             = 36         // in6.h:622:1:
	IPV6CTL_RR_PRUNE              = 22         // in6.h:609:1:
	IPV6CTL_SENDREDIRECTS         = 2          // in6.h:587:1:
	IPV6CTL_SOURCECHECK           = 10         // in6.h:597:1:
	IPV6CTL_SOURCECHECK_LOGINT    = 11         // in6.h:598:1:
	IPV6CTL_STATS                 = 6          // in6.h:593:1:
	IPV6CTL_STEALTH               = 45         // in6.h:636:1:
	IPV6CTL_TEMPPLTIME            = 33         // in6.h:619:1:
	IPV6CTL_TEMPVLTIME            = 34         // in6.h:620:1:
	IPV6CTL_USETEMPADDR           = 32         // in6.h:618:1:
	IPV6CTL_USE_DEFAULTZONE       = 39         // in6.h:625:1:
	IPV6CTL_USE_DEPRECATED        = 21         // in6.h:608:1:
	IPV6CTL_V6ONLY                = 24         // in6.h:613:1:
	IPV6PORT_ANONMAX              = 65535      // in6.h:89:1:
	IPV6PORT_ANONMIN              = 49152      // in6.h:88:1:
	IPV6PORT_RESERVED             = 1024       // in6.h:87:1:
	IPV6PORT_RESERVEDMAX          = 1023       // in6.h:91:1:
	IPV6PORT_RESERVEDMIN          = 600        // in6.h:90:1:
	IPV6PROTO_MAXID               = 104        // in6.h:581:1:
	IPV6_AUTOFLOWLABEL            = 59         // in6.h:484:1:
	IPV6_BINDANY                  = 64         // in6.h:493:1:
	IPV6_BINDMULTI                = 65         // in6.h:495:1:
	IPV6_BINDV6ONLY               = 27         // in6.h:435:1:
	IPV6_CHECKSUM                 = 26         // in6.h:432:1:
	IPV6_DEFAULT_MULTICAST_HOPS   = 1          // in6.h:527:1:
	IPV6_DEFAULT_MULTICAST_LOOP   = 1          // in6.h:528:1:
	IPV6_DONTFRAG                 = 62         // in6.h:487:1:
	IPV6_DSTOPTS                  = 50         // in6.h:475:1:
	IPV6_FLOWID                   = 67         // in6.h:497:1:
	IPV6_FLOWTYPE                 = 68         // in6.h:498:1:
	IPV6_FW_ADD                   = 30         // in6.h:441:1:
	IPV6_FW_DEL                   = 31         // in6.h:442:1:
	IPV6_FW_FLUSH                 = 32         // in6.h:443:1:
	IPV6_FW_GET                   = 34         // in6.h:445:1:
	IPV6_FW_ZERO                  = 33         // in6.h:444:1:
	IPV6_HOPLIMIT                 = 47         // in6.h:472:1:
	IPV6_HOPOPTS                  = 49         // in6.h:474:1:
	IPV6_IPSEC_POLICY             = 28         // in6.h:438:1:
	IPV6_JOIN_GROUP               = 12         // in6.h:417:1:
	IPV6_LEAVE_GROUP              = 13         // in6.h:418:1:
	IPV6_MAX_GROUP_SRC_FILTER     = 512        // in6.h:539:1:
	IPV6_MAX_MEMBERSHIPS          = 4095       // in6.h:533:1:
	IPV6_MAX_SOCK_SRC_FILTER      = 128        // in6.h:540:1:
	IPV6_MSFILTER                 = 74         // in6.h:510:1:
	IPV6_MULTICAST_HOPS           = 10         // in6.h:415:1:
	IPV6_MULTICAST_IF             = 9          // in6.h:414:1:
	IPV6_MULTICAST_LOOP           = 11         // in6.h:416:1:
	IPV6_NEXTHOP                  = 48         // in6.h:473:1:
	IPV6_ORIGDSTADDR              = 72         // in6.h:503:1:
	IPV6_PATHMTU                  = 44         // in6.h:463:1:
	IPV6_PKTINFO                  = 46         // in6.h:471:1:
	IPV6_PORTRANGE                = 14         // in6.h:419:1:
	IPV6_PORTRANGE_DEFAULT        = 0          // in6.h:570:1:
	IPV6_PORTRANGE_HIGH           = 1          // in6.h:571:1:
	IPV6_PORTRANGE_LOW            = 2          // in6.h:572:1:
	IPV6_PREFER_TEMPADDR          = 63         // in6.h:489:1:
	IPV6_RECVDSTOPTS              = 40         // in6.h:455:1:
	IPV6_RECVFLOWID               = 70         // in6.h:500:1:
	IPV6_RECVHOPLIMIT             = 37         // in6.h:452:1:
	IPV6_RECVHOPOPTS              = 39         // in6.h:454:1:
	IPV6_RECVORIGDSTADDR          = 72         // in6.h:504:1:
	IPV6_RECVPATHMTU              = 43         // in6.h:461:1:
	IPV6_RECVPKTINFO              = 36         // in6.h:451:1:
	IPV6_RECVRSSBUCKETID          = 71         // in6.h:501:1:
	IPV6_RECVRTHDR                = 38         // in6.h:453:1:
	IPV6_RECVTCLASS               = 57         // in6.h:482:1:
	IPV6_RSSBUCKETID              = 69         // in6.h:499:1:
	IPV6_RSS_LISTEN_BUCKET        = 66         // in6.h:496:1:
	IPV6_RTHDR                    = 51         // in6.h:476:1:
	IPV6_RTHDRDSTOPTS             = 35         // in6.h:449:1:
	IPV6_RTHDR_LOOSE              = 0          // in6.h:520:1:
	IPV6_RTHDR_STRICT             = 1          // in6.h:521:1:
	IPV6_RTHDR_TYPE_0             = 0          // in6.h:522:1:
	IPV6_SOCKOPT_RESERVED1        = 3          // in6.h:412:1:
	IPV6_TCLASS                   = 61         // in6.h:486:1:
	IPV6_UNICAST_HOPS             = 4          // in6.h:413:1:
	IPV6_USE_MIN_MTU              = 42         // in6.h:460:1:
	IPV6_V6ONLY                   = 27         // in6.h:433:1:
	IPV6_VLAN_PCP                 = 75         // in6.h:515:1:
	IP_ADD_MEMBERSHIP             = 12         // in.h:434:1:
	IP_ADD_SOURCE_MEMBERSHIP      = 70         // in.h:490:1:
	IP_BINDANY                    = 24         // in.h:447:1:
	IP_BINDMULTI                  = 25         // in.h:448:1:
	IP_BLOCK_SOURCE               = 72         // in.h:492:1:
	IP_DEFAULT_MULTICAST_LOOP     = 1          // in.h:521:1:
	IP_DEFAULT_MULTICAST_TTL      = 1          // in.h:520:1:
	IP_DONTFRAG                   = 67         // in.h:486:1:
	IP_DROP_MEMBERSHIP            = 13         // in.h:435:1:
	IP_DROP_SOURCE_MEMBERSHIP     = 71         // in.h:491:1:
	IP_DUMMYNET3                  = 49         // in.h:465:1:
	IP_DUMMYNET_CONFIGURE         = 60         // in.h:479:1:
	IP_DUMMYNET_DEL               = 61         // in.h:480:1:
	IP_DUMMYNET_FLUSH             = 62         // in.h:481:1:
	IP_DUMMYNET_GET               = 64         // in.h:482:1:
	IP_FLOWID                     = 90         // in.h:511:1:
	IP_FLOWTYPE                   = 91         // in.h:512:1:
	IP_FW3                        = 48         // in.h:464:1:
	IP_FW_ADD                     = 50         // in.h:467:1:
	IP_FW_DEL                     = 51         // in.h:468:1:
	IP_FW_FLUSH                   = 52         // in.h:469:1:
	IP_FW_GET                     = 54         // in.h:471:1:
	IP_FW_NAT_CFG                 = 56         // in.h:474:1:
	IP_FW_NAT_DEL                 = 57         // in.h:475:1:
	IP_FW_NAT_GET_CONFIG          = 58         // in.h:476:1:
	IP_FW_NAT_GET_LOG             = 59         // in.h:477:1:
	IP_FW_RESETLOG                = 55         // in.h:472:1:
	IP_FW_TABLE_ADD               = 40         // in.h:458:1:
	IP_FW_TABLE_DEL               = 41         // in.h:459:1:
	IP_FW_TABLE_FLUSH             = 42         // in.h:460:1:
	IP_FW_TABLE_GETSIZE           = 43         // in.h:461:1:
	IP_FW_TABLE_LIST              = 44         // in.h:462:1:
	IP_FW_ZERO                    = 53         // in.h:470:1:
	IP_HDRINCL                    = 2          // in.h:422:1:
	IP_IPSEC_POLICY               = 21         // in.h:444:1:
	IP_MAX_GROUP_SRC_FILTER       = 512        // in.h:532:1:
	IP_MAX_MEMBERSHIPS            = 4095       // in.h:526:1:
	IP_MAX_SOCK_MUTE_FILTER       = 128        // in.h:534:1:
	IP_MAX_SOCK_SRC_FILTER        = 128        // in.h:533:1:
	IP_MINTTL                     = 66         // in.h:485:1:
	IP_MSFILTER                   = 74         // in.h:496:1:
	IP_MULTICAST_IF               = 9          // in.h:430:1:
	IP_MULTICAST_LOOP             = 11         // in.h:433:1:
	IP_MULTICAST_TTL              = 10         // in.h:432:1:
	IP_MULTICAST_VIF              = 14         // in.h:436:1:
	IP_ONESBCAST                  = 23         // in.h:446:1:
	IP_OPTIONS                    = 1          // in.h:421:1:
	IP_ORIGDSTADDR                = 27         // in.h:450:1:
	IP_PORTRANGE                  = 19         // in.h:441:1:
	IP_PORTRANGE_DEFAULT          = 0          // in.h:625:1:
	IP_PORTRANGE_HIGH             = 1          // in.h:626:1:
	IP_PORTRANGE_LOW              = 2          // in.h:627:1:
	IP_RECVDSTADDR                = 7          // in.h:427:1:
	IP_RECVFLOWID                 = 93         // in.h:514:1:
	IP_RECVIF                     = 20         // in.h:442:1:
	IP_RECVOPTS                   = 5          // in.h:425:1:
	IP_RECVORIGDSTADDR            = 27         // in.h:451:1:
	IP_RECVRETOPTS                = 6          // in.h:426:1:
	IP_RECVRSSBUCKETID            = 94         // in.h:515:1:
	IP_RECVTOS                    = 68         // in.h:487:1:
	IP_RECVTTL                    = 65         // in.h:484:1:
	IP_RETOPTS                    = 8          // in.h:429:1:
	IP_RSSBUCKETID                = 92         // in.h:513:1:
	IP_RSS_LISTEN_BUCKET          = 26         // in.h:449:1:
	IP_RSVP_OFF                   = 16         // in.h:438:1:
	IP_RSVP_ON                    = 15         // in.h:437:1:
	IP_RSVP_VIF_OFF               = 18         // in.h:440:1:
	IP_RSVP_VIF_ON                = 17         // in.h:439:1:
	IP_SENDSRCADDR                = 7          // in.h:428:1:
	IP_TOS                        = 3          // in.h:423:1:
	IP_TTL                        = 4          // in.h:424:1:
	IP_UNBLOCK_SOURCE             = 73         // in.h:493:1:
	IP_VLAN_PCP                   = 75         // in.h:499:1:
	LITTLE_ENDIAN                 = 1234       // _endian.h:69:1:
	MCAST_BLOCK_SOURCE            = 84         // in.h:507:1:
	MCAST_EXCLUDE                 = 2          // in.h:619:1:
	MCAST_INCLUDE                 = 1          // in.h:618:1:
	MCAST_JOIN_GROUP              = 80         // in.h:503:1:
	MCAST_JOIN_SOURCE_GROUP       = 82         // in.h:505:1:
	MCAST_LEAVE_GROUP             = 81         // in.h:504:1:
	MCAST_LEAVE_SOURCE_GROUP      = 83         // in.h:506:1:
	MCAST_UNBLOCK_SOURCE          = 85         // in.h:508:1:
	MCAST_UNDEFINED               = 0          // in.h:617:1:
	PDP_ENDIAN                    = 3412       // _endian.h:71:1:
	SIN6_LEN                      = 0          // in6.h:122:1:
	X_BIG_ENDIAN                  = 4321       // _endian.h:47:1:
	X_BYTEORDER_FUNC_DEFINED      = 0          // in.h:118:1:
	X_BYTEORDER_PROTOTYPED        = 0          // in.h:108:1:
	X_BYTE_ORDER                  = 1234       // _endian.h:40:1:
	X_FILE_OFFSET_BITS            = 64         // <builtin>:25:1:
	X_IN_ADDR_T_DECLARED          = 0          // in.h:68:1:
	X_IN_PORT_T_DECLARED          = 0          // in.h:73:1:
	X_LITTLE_ENDIAN               = 1234       // _endian.h:46:1:
	X_LP64                        = 1          // <predefined>:1:1:
	X_MACHINE_ENDIAN_H_           = 0          // endian.h:36:1:
	X_MACHINE__LIMITS_H_          = 0          // _limits.h:36:1:
	X_MACHINE__TYPES_H_           = 0          // _types.h:42:1:
	X_NETINET6_IN6_H_             = 0          // in6.h:71:1:
	X_NETINET_IN_H_               = 0          // in.h:36:1:
	X_Nonnull                     = 0          // cdefs.h:790:1:
	X_Null_unspecified            = 0          // cdefs.h:792:1:
	X_Nullable                    = 0          // cdefs.h:791:1:
	X_PDP_ENDIAN                  = 3412       // _endian.h:48:1:
	X_QUAD_HIGHWORD               = 1          // _endian.h:55:1:
	X_QUAD_LOWWORD                = 0          // _endian.h:56:1:
	X_SA_FAMILY_T_DECLARED        = 0          // in.h:78:1:
	X_SIZE_T_DECLARED             = 0          // in6.h:701:1:
	X_SOCKLEN_T_DECLARED          = 0          // in.h:91:1:
	X_SS_MAXSIZE                  = 128        // _sockaddr_storage.h:41:1:
	X_STRUCT_IN_ADDR_DECLARED     = 0          // in.h:86:1:
	X_SYS_CDEFS_H_                = 0          // cdefs.h:39:1:
	X_SYS__ENDIAN_H_              = 0          // _endian.h:33:1:
	X_SYS__SOCKADDR_STORAGE_H_    = 0          // _sockaddr_storage.h:36:1:
	X_SYS__TYPES_H_               = 0          // _types.h:32:1:
	X_UINT16_T_DECLARED           = 0          // in.h:58:1:
	X_UINT32_T_DECLARED           = 0          // in.h:63:1:
	X_UINT8_T_DECLARED            = 0          // in.h:53:1:
	Unix                          = 1          // <predefined>:340:1:
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)in.h	8.3 (Berkeley) 1/3/94
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// Testing against Clang-specific extensions.

// This code has been put in place to help reduce the addition of
// compiler specific defines in FreeBSD code.  It helps to aid in
// having a compiler-agnostic source tree.

// Compiler memory barriers, specific to gcc and clang.

// XXX: if __GNUC__ >= 2: not tested everywhere originally, where replaced

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky to use if it must work in non-ANSI
// mode -- there must be no spaces between its arguments, and for nested
// __CONCAT's, all the __CONCAT's must be at the left.  __CONCAT can also
// concatenate double-quoted strings produced by the __STRING macro, but
// this only works with ANSI C.
//
// __XSTRING is like __STRING, but it expands any macros in its argument
// first.  It is only available with ANSI C.

// Compiler-dependent macros to help declare dead (non-returning) and
// pure (no side effects) functions, and unused variables.  They are
// null except for versions of gcc that are known to support the features
// properly (old versions of gcc-2 supported the dead and pure features
// in a different (wrong) way).  If we do not provide an implementation
// for a given compiler, let the compile fail if it is told to use
// a feature that we cannot live without.

// Keywords added in C11.

// Emulation of C11 _Generic().  Unlike the previously defined C11
// keywords, it is not possible to implement this using exactly the same
// syntax.  Therefore implement something similar under the name
// __generic().  Unlike _Generic(), this macro can only distinguish
// between a single type, so it requires nested invocations to
// distinguish multiple cases.

// C99 Static array indices in function parameter declarations.  Syntax such as:
// void bar(int myArray[static 10]);
// is allowed in C99 but not in C++.  Define __min_size appropriately so
// headers using it can be compiled in either language.  Use like this:
// void bar(int myArray[__min_size(10)]);

// XXX: should use `#if __STDC_VERSION__ < 199901'.

// C++11 exposes a load of C99 stuff

// GCC 2.95 provides `__restrict' as an extension to C90 to support the
// C99-specific `restrict' type qualifier.  We happen to use `__restrict' as
// a way to define the `restrict' type qualifier without disturbing older
// software that is unaware of C99 keywords.

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// We define this here since <stddef.h>, <sys/queue.h>, and <sys/types.h>
// require it.

// Given the pointer x to the member m of the struct s, return
// a pointer to the containing structure.  When using GCC, we first
// assign pointer x to a local variable, to check that its type is
// compatible with member m.

// Compiler-dependent macros to declare that functions take printf-like
// or scanf-like arguments.  They are null except for versions of gcc
// that are known to support the features properly (old versions of gcc-2
// didn't permit keeping the keywords out of the application namespace).

// Compiler-dependent macros that rely on FreeBSD-specific extensions.

// Embed the rcs id of a source file in the resulting library.  Note that in
// more recent ELF binutils, we use .ident allowing the ID to be stripped.
// Usage:
//	__FBSDID("$FreeBSD$");

// -
// The following definitions are an extension of the behavior originally
// implemented in <sys/_posix.h>, but with a different level of granularity.
// POSIX.1 requires that the macros we test be defined before any standard
// header file is included.
//
// Here's a quick run-down of the versions:
//  defined(_POSIX_SOURCE)		1003.1-1988
//  _POSIX_C_SOURCE == 1		1003.1-1990
//  _POSIX_C_SOURCE == 2		1003.2-1992 C Language Binding Option
//  _POSIX_C_SOURCE == 199309		1003.1b-1993
//  _POSIX_C_SOURCE == 199506		1003.1c-1995, 1003.1i-1995,
//					and the omnibus ISO/IEC 9945-1: 1996
//  _POSIX_C_SOURCE == 200112		1003.1-2001
//  _POSIX_C_SOURCE == 200809		1003.1-2008
//
// In addition, the X/Open Portability Guide, which is now the Single UNIX
// Specification, defines a feature-test macro which indicates the version of
// that specification, and which subsumes _POSIX_C_SOURCE.
//
// Our macros begin with two underscores to avoid namespace screwage.

// Deal with IEEE Std. 1003.1-1990, in which _POSIX_C_SOURCE == 1.

// Deal with IEEE Std. 1003.2-1992, in which _POSIX_C_SOURCE == 2.

// Deal with various X/Open Portability Guides and Single UNIX Spec.

// Deal with all versions of POSIX.  The ordering relative to the tests above is
// important.
// -
// Deal with _ANSI_SOURCE:
// If it is defined, and no other compilation environment is explicitly
// requested, then define our internal feature-test macros to zero.  This
// makes no difference to the preprocessor (undefined symbols in preprocessing
// expressions are defined to have value zero), but makes it more convenient for
// a test program to print out the values.
//
// If a program mistakenly defines _ANSI_SOURCE and some other macro such as
// _POSIX_C_SOURCE, we will assume that it wants the broader compilation
// environment (and in fact we will never get here).

// User override __EXT1_VISIBLE

// Old versions of GCC use non-standard ARM arch symbols; acle-compat.h
// translates them to __ARM_ARCH and the modern feature symbols defined by ARM.

// Nullability qualifiers: currently only supported by Clang.

// Type Safety Checking
//
// Clang provides additional attributes to enable checking type safety
// properties that cannot be enforced by the C type system.

// Lock annotations.
//
// Clang provides support for doing basic thread-safety tests at
// compile-time, by marking which locks will/should be held when
// entering/leaving a functions.
//
// Furthermore, it is also possible to annotate variables and structure
// members to enforce that they are only accessed when certain locks are
// held.

// Structure implements a lock.

// Function acquires an exclusive or shared lock.

// Function attempts to acquire an exclusive or shared lock.

// Function releases a lock.

// Function asserts that an exclusive or shared lock is held.

// Function requires that an exclusive or shared lock is or is not held.

// Function should not be analyzed.

// Function or variable should not be sanitized, e.g., by AddressSanitizer.
// GCC has the nosanitize attribute, but as a function attribute only, and
// warns on use as a variable attribute.

// Guard variables and structure members by lock.

// Alignment builtins for better type checking and improved code generation.
// Provide fallback versions for other compilers (GCC/Clang < 10):

// -
// SPDX-License-Identifier: BSD-2-Clause-FreeBSD
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95
// $FreeBSD$

// -
// This file is in the public domain.
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-4-Clause
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by the University of
//	California, Berkeley and its contributors.
// 4. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	From: @(#)ansi.h	8.2 (Berkeley) 1/4/94
//	From: @(#)types.h	8.3 (Berkeley) 1/5/94
// $FreeBSD$

// -
// This file is in the public domain.
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	8.3 (Berkeley) 1/4/94
// $FreeBSD$

// According to ANSI (section 2.2.4.2), the values below must be usable by
// #if preprocessing directives.  Additionally, the expression must have the
// same type as would an expression that is an object of the corresponding
// type converted according to the integral promotions.  The subtraction for
// INT_MIN, etc., is so the value is not unsigned; e.g., 0x80000000 is an
// unsigned int for 32-bit two's complement ANSI compilers (section 3.1.3.2).

// max value for an unsigned long long

// Quads and longs are the same on the amd64.  Ensure they stay in sync.

// Minimum signal stack size.

// Basic types upon which most other types are built.
type X__int8_t = int8     /* _types.h:55:22 */
type X__uint8_t = uint8   /* _types.h:56:24 */
type X__int16_t = int16   /* _types.h:57:17 */
type X__uint16_t = uint16 /* _types.h:58:25 */
type X__int32_t = int32   /* _types.h:59:15 */
type X__uint32_t = uint32 /* _types.h:60:23 */
type X__int64_t = int64   /* _types.h:62:16 */
type X__uint64_t = uint64 /* _types.h:63:24 */

// Standard type definitions.
type X__clock_t = X__int32_t         /* _types.h:75:19 */ // clock()...
type X__critical_t = X__int64_t      /* _types.h:76:19 */
type X__double_t = float64           /* _types.h:78:17 */
type X__float_t = float32            /* _types.h:79:16 */
type X__intfptr_t = X__int64_t       /* _types.h:81:19 */
type X__intptr_t = X__int64_t        /* _types.h:82:19 */
type X__intmax_t = X__int64_t        /* _types.h:93:19 */
type X__int_fast8_t = X__int32_t     /* _types.h:94:19 */
type X__int_fast16_t = X__int32_t    /* _types.h:95:19 */
type X__int_fast32_t = X__int32_t    /* _types.h:96:19 */
type X__int_fast64_t = X__int64_t    /* _types.h:97:19 */
type X__int_least8_t = X__int8_t     /* _types.h:98:18 */
type X__int_least16_t = X__int16_t   /* _types.h:99:19 */
type X__int_least32_t = X__int32_t   /* _types.h:100:19 */
type X__int_least64_t = X__int64_t   /* _types.h:101:19 */
type X__ptrdiff_t = X__int64_t       /* _types.h:103:19 */ // ptr1 - ptr2
type X__register_t = X__int64_t      /* _types.h:104:19 */
type X__segsz_t = X__int64_t         /* _types.h:105:19 */ // segment size (in pages)
type X__size_t = X__uint64_t         /* _types.h:106:20 */ // sizeof()
type X__ssize_t = X__int64_t         /* _types.h:107:19 */ // byte count or error
type X__time_t = X__int64_t          /* _types.h:108:19 */ // time()...
type X__uintfptr_t = X__uint64_t     /* _types.h:109:20 */
type X__uintptr_t = X__uint64_t      /* _types.h:110:20 */
type X__uintmax_t = X__uint64_t      /* _types.h:121:20 */
type X__uint_fast8_t = X__uint32_t   /* _types.h:122:20 */
type X__uint_fast16_t = X__uint32_t  /* _types.h:123:20 */
type X__uint_fast32_t = X__uint32_t  /* _types.h:124:20 */
type X__uint_fast64_t = X__uint64_t  /* _types.h:125:20 */
type X__uint_least8_t = X__uint8_t   /* _types.h:126:19 */
type X__uint_least16_t = X__uint16_t /* _types.h:127:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:128:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:129:20 */
type X__u_register_t = X__uint64_t   /* _types.h:131:20 */
type X__vm_offset_t = X__uint64_t    /* _types.h:132:20 */
type X__vm_paddr_t = X__uint64_t     /* _types.h:133:20 */
type X__vm_size_t = X__uint64_t      /* _types.h:134:20 */
type X___wchar_t = int32             /* _types.h:141:14 */

// Standard type definitions.
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // file block size
type X__blkcnt_t = X__int64_t    /* _types.h:41:19 */ // file block count
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // clock_gettime()...
type X__fflags_t = X__uint32_t   /* _types.h:43:20 */ // file flags
type X__fsblkcnt_t = X__uint64_t /* _types.h:44:20 */
type X__fsfilcnt_t = X__uint64_t /* _types.h:45:20 */
type X__gid_t = X__uint32_t      /* _types.h:46:20 */
type X__id_t = X__int64_t        /* _types.h:47:19 */ // can hold a gid_t, pid_t, or uid_t
type X__ino_t = X__uint64_t      /* _types.h:48:20 */ // inode number
type X__key_t = int64            /* _types.h:49:15 */ // IPC key (for Sys V IPC)
type X__lwpid_t = X__int32_t     /* _types.h:50:19 */ // Thread ID (a.k.a. LWP)
type X__mode_t = X__uint16_t     /* _types.h:51:20 */ // permissions
type X__accmode_t = int32        /* _types.h:52:14 */ // access permissions
type X__nl_item = int32          /* _types.h:53:14 */
type X__nlink_t = X__uint64_t    /* _types.h:54:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:55:19 */ // file offset
type X__off64_t = X__int64_t     /* _types.h:56:19 */ // file offset (alias)
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process [group]
type X__rlim_t = X__int64_t      /* _types.h:58:19 */ // resource limit - intentionally
// signed, because of legacy code
// that uses -1 for RLIM_INFINITY
type X__sa_family_t = X__uint8_t /* _types.h:61:19 */
type X__socklen_t = X__uint32_t  /* _types.h:62:20 */
type X__suseconds_t = int64      /* _types.h:63:15 */ // microseconds (signed)
type X__timer_t = uintptr        /* _types.h:64:24 */ // timer_gettime()...
type X__mqd_t = uintptr          /* _types.h:65:21 */ // mq_open()...
type X__uid_t = X__uint32_t      /* _types.h:66:20 */
type X__useconds_t = uint32      /* _types.h:67:22 */ // microseconds (unsigned)
type X__cpuwhich_t = int32       /* _types.h:68:14 */ // which parameter for cpuset.
type X__cpulevel_t = int32       /* _types.h:69:14 */ // level parameter for cpuset.
type X__cpusetid_t = int32       /* _types.h:70:14 */ // cpuset identifier.
type X__daddr_t = X__int64_t     /* _types.h:71:19 */ // bwrite(3), FIOBMAP2, etc

// Unusual type definitions.
// rune_t is declared to be an “int” instead of the more natural
// “unsigned long” or “long”.  Two things are happening here.  It is not
// unsigned so that EOF (-1) can be naturally assigned to it and used.  Also,
// it looks like 10646 will be a 31 bit standard.  This means that if your
// ints cannot hold 32 bits, you will be in trouble.  The reason an int was
// chosen over a long is that the is*() and to*() routines take ints (says
// ANSI C), but they use __ct_rune_t instead of int.
//
// NOTE: rune_t is not covered by ANSI nor other standards, and should not
// be instantiated outside of lib/libc/locale.  Use wchar_t.  wint_t and
// rune_t must be the same type.  Also, wint_t should be able to hold all
// members of the largest character set plus one extra value (WEOF), and
// must be at least 16 bits.
type X__ct_rune_t = int32     /* _types.h:91:14 */ // arg type for ctype funcs
type X__rune_t = X__ct_rune_t /* _types.h:92:21 */ // rune_t (see above)
type X__wint_t = X__ct_rune_t /* _types.h:93:21 */ // wint_t (see above)

// Clang already provides these types as built-ins, but only in C++ mode.
type X__char16_t = X__uint_least16_t /* _types.h:97:26 */
type X__char32_t = X__uint_least32_t /* _types.h:98:26 */
// In C++11, char16_t and char32_t are built-in types.

type X__max_align_t = struct {
	F__max_align1 int64
	F__max_align2 float64
} /* _types.h:111:3 */

type X__dev_t = X__uint64_t /* _types.h:113:20 */ // device number

type X__fixpt_t = X__uint32_t /* _types.h:115:20 */ // fixed point number

// mbstate_t is an opaque object to keep conversion state during multibyte
// stream conversions.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]int8
} /* _types.h:124:3 */

type X__rman_res_t = X__uintmax_t /* _types.h:126:25 */

// Types for varargs. These are all provided by builtin types these
// days, so centralize their definition.
type X__va_list = X__builtin_va_list /* _types.h:133:27 */ // internally known to gcc
type X__gnuc_va_list = X__va_list    /* _types.h:140:20 */ // compatibility w/GNU headers

// When the following macro is defined, the system uses 64-bit inode numbers.
// Programs can use this to avoid including <sys/param.h>, with its associated
// namespace pollution.

// -
// This file is in the public domain.
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1987, 1991 Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)endian.h	7.8 (Berkeley) 4/3/91
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-2-Clause-FreeBSD
//
// Copyright (c) 2002 Mike Barcroft <<EMAIL>>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD$

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1987, 1991 Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// BSD Compatiblity

// Definitions for byte order, according to byte significance from low
// address to high.

// Define the order of 32-bit words in 64-bit words.

// Deprecated variants that don't have enough underscores to be useful in more
// strict namespaces.

// bswap primitives, based on compiler builtins

// Protocols common to RFC 1700, POSIX, and X/Open.

type Uint8_t = X__uint8_t /* in.h:52:20 */

type Uint16_t = X__uint16_t /* in.h:57:21 */

type Uint32_t = X__uint32_t /* in.h:62:21 */

type In_addr_t = Uint32_t /* in.h:67:19 */

type In_port_t = Uint16_t /* in.h:72:19 */

type Sa_family_t = X__sa_family_t /* in.h:77:24 */

// Internet address (a structure for historical reasons).
type In_addr = struct{ Fs_addr In_addr_t } /* in.h:83:1 */

type Socklen_t = X__socklen_t /* in.h:90:21 */

// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (c) 1982, 1985, 1986, 1988, 1993, 1994
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)socket.h	8.4 (Berkeley) 2/21/94
// $FreeBSD$

// RFC 2553: protocol-independent placeholder for socket addresses

type Sockaddr_storage = struct {
	Fss_len     uint8
	Fss_family  Sa_family_t
	F__ss_pad1  [6]int8
	F__ss_align X__int64_t
	F__ss_pad2  [112]int8
} /* _sockaddr_storage.h:48:1 */

// Socket address, internet style.
type Sockaddr_in = struct {
	Fsin_len    Uint8_t
	Fsin_family Sa_family_t
	Fsin_port   In_port_t
	Fsin_addr   struct{ Fs_addr In_addr_t }
	Fsin_zero   [8]int8
} /* in.h:97:1 */

// Constants and structures defined by the internet system,
// Per RFC 790, September 1981, and numerous additions.

// Protocols (RFC 1700)
// 101-254: Partly Unassigned
// 255: Reserved
// BSD Private, local use, namespace incursion, no longer used

// last return value of *_input(), meaning "all job for this pkt is done".

// Only used internally, so can be outside the range of valid IP protocols.

// Defined to avoid confusion.  The master value is defined by
// PROTO_SPACER in sys/protosw.h.

// Local port number conventions:
//
// When a user does a bind(2) or connect(2) with a port number of zero,
// a non-conflicting local port address is chosen.
// The default range is IPPORT_HIFIRSTAUTO through
// IPPORT_HILASTAUTO, although that is settable by sysctl.
//
// A user may set the IPPROTO_IP option IP_PORTRANGE to change this
// default assignment range.
//
// The value IP_PORTRANGE_DEFAULT causes the default behavior.
//
// The value IP_PORTRANGE_HIGH changes the range of candidate port numbers
// into the "high" range.  These are reserved for client outbound connections
// which do not want to be filtered by any firewalls.
//
// The value IP_PORTRANGE_LOW changes the range to the "low" are
// that is (by convention) restricted to privileged processes.  This
// convention is based on "vouchsafe" principles only.  It is only secure
// if you trust the remote host to restrict these ports.
//
// The default range of ports and the high range can be changed by
// sysctl(3).  (net.inet.ip.portrange.{hi,low,}{first,last})
//
// Changing those values has bad security implications if you are
// using a stateless firewall that is allowing packets outside of that
// range in order to allow transparent outgoing connections.
//
// Such a firewall configuration will generally depend on the use of these
// default values.  If you change them, you may find your Security
// Administrator looking for you with a heavy object.
//
// For a slightly more orthodox text view on this:
//
//            ftp://ftp.isi.edu/in-notes/iana/assignments/port-numbers
//
//    port numbers are divided into three ranges:
//
//                0 -  1023 Well Known Ports
//             1024 - 49151 Registered Ports
//            49152 - 65535 Dynamic and/or Private Ports
//

// Ports < IPPORT_RESERVED are reserved for
// privileged processes (e.g. root).         (IP_PORTRANGE_LOW)

// Default local port range, used by IP_PORTRANGE_DEFAULT

// Dynamic port range, used by IP_PORTRANGE_HIGH.

// Scanning for a free reserved port return a value below IPPORT_RESERVED,
// but higher than IPPORT_RESERVEDSTART.  Traditionally the start value was
// 512, but that conflicts with some well-known-services that firewalls may
// have a fit if we use.

// Historical definitions of bits in internet address integers
// (pre-CIDR).  Class A/B/C are long obsolete, and now deprecated.
// Hide these definitions from the kernel unless IN_HISTORICAL_NETS
// is defined.  Provide the historical definitions to user level for now.

// Options for use with [gs]etsockopt at the IP level.
// First word of comment is data type; bool is stored in int.
// for IPSEC
// unused; was IP_FAITH

// Options for controlling the firewall and dummynet.
// Historical options (from 40 to 64) will eventually be
// replaced by only two options, IP_FW3 and IP_DUMMYNET3.

// IPv4 Source Filter Multicast API [RFC3678]

// The following option is private; do not use it from user applications.

// The following option deals with the 802.1Q Ethernet Priority Code Point
//      -1 use interface default

// Protocol Independent Multicast API [RFC3678]

// Flow and RSS definitions

// Defaults and limits for options

// Limit for IPv4 multicast memberships

// Default resource limits for IPv4 multicast source filtering.
// These may be modified by sysctl.

// Argument structure for IP_ADD_MEMBERSHIP and IP_DROP_MEMBERSHIP.
type Ip_mreq = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_interface struct{ Fs_addr In_addr_t }
} /* in.h:539:1 */

// Modified argument structure for IP_MULTICAST_IF, obtained from Linux.
// This is used to specify an interface index for multicast sends, as
// the IPv4 legacy APIs do not support this (unless IP_SENDIF is available).
type Ip_mreqn = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_address   struct{ Fs_addr In_addr_t }
	Fimr_ifindex   int32
} /* in.h:549:1 */

// Argument structure for IPv4 Multicast Source Filter APIs. [RFC3678]
type Ip_mreq_source = struct {
	Fimr_multiaddr  struct{ Fs_addr In_addr_t }
	Fimr_sourceaddr struct{ Fs_addr In_addr_t }
	Fimr_interface  struct{ Fs_addr In_addr_t }
} /* in.h:558:1 */

// Argument structures for Protocol-Independent Multicast Source
// Filter APIs. [RFC3678]
type Group_req = struct {
	Fgr_interface Uint32_t
	F__ccgo_pad1  [4]byte
	Fgr_group     struct {
		Fss_len     uint8
		Fss_family  Sa_family_t
		F__ss_pad1  [6]int8
		F__ss_align X__int64_t
		F__ss_pad2  [112]int8
	}
} /* in.h:568:1 */

type Group_source_req = struct {
	Fgsr_interface Uint32_t
	F__ccgo_pad1   [4]byte
	Fgsr_group     struct {
		Fss_len     uint8
		Fss_family  Sa_family_t
		F__ss_pad1  [6]int8
		F__ss_align X__int64_t
		F__ss_pad2  [112]int8
	}
	Fgsr_source struct {
		Fss_len     uint8
		Fss_family  Sa_family_t
		F__ss_pad1  [6]int8
		F__ss_align X__int64_t
		F__ss_pad2  [112]int8
	}
} /* in.h:573:1 */

// The following structure is private; do not use it from user applications.
// It is used to communicate IP_MSFILTER/IPV6_MSFILTER information between
// the RFC 3678 libc functions and the kernel.
type X__msfilterreq = struct {
	Fmsfr_ifindex Uint32_t
	Fmsfr_fmode   Uint32_t
	Fmsfr_nsrcs   Uint32_t
	F__ccgo_pad1  [4]byte
	Fmsfr_group   struct {
		Fss_len     uint8
		Fss_family  Sa_family_t
		F__ss_pad1  [6]int8
		F__ss_align X__int64_t
		F__ss_pad2  [112]int8
	}
	Fmsfr_srcs uintptr
} /* in.h:586:1 */

// Filter modes; also used to represent per-socket filter mode internally.

// Argument for IP_PORTRANGE:
// - which range to search when port is unspecified at bind() or connect()

// Identifiers for IP sysctl nodes
//	IPCTL_RTEXPIRE		5	deprecated
//	IPCTL_RTMINEXPIRE	6	deprecated
//	IPCTL_RTMAXCACHE	7	deprecated
// 15, unused, was: IPCTL_KEEPFAITH

// INET6 stuff
// -
// SPDX-License-Identifier: BSD-3-Clause
//
// Copyright (C) 1995, 1996, 1997, and 1998 WIDE Project.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the project nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE PROJECT AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE PROJECT OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	$KAME: in6.h,v 1.89 2001/05/27 13:28:35 itojun Exp $

// -
// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)in.h	8.3 (Berkeley) 1/3/94
// $FreeBSD$

// Identification of the network protocol stack
// for *BSD-current/release: http://www.kame.net/dev/cvsweb.cgi/kame/COVERAGE
// has the table of implementation/integration differences.

// IPv6 port allocation rules should mirror the IPv4 rules and are controlled
// by the net.inet.ip.portrange sysctl tree. The following defines exist
// for compatibility with userland applications that need them.

// IPv6 address
type In6_addr = struct {
	F__u6_addr struct {
		F__ccgo_pad1 [0]uint32
		F__u6_addr8  [16]Uint8_t
	}
} /* in6.h:97:1 */

// XXX missing POSIX.1-2001 macro IPPROTO_IPV6.

// Socket address for IPv6

type Sockaddr_in6 = struct {
	Fsin6_len      Uint8_t
	Fsin6_family   Sa_family_t
	Fsin6_port     In_port_t
	Fsin6_flowinfo Uint32_t
	Fsin6_addr     struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]Uint8_t
		}
	}
	Fsin6_scope_id Uint32_t
} /* in6.h:125:1 */

type Route_in6 = struct {
	Fro_nh      uintptr
	Fro_lle     uintptr
	Fro_prepend uintptr
	Fro_plen    Uint16_t
	Fro_flags   Uint16_t
	Fro_mtu     Uint16_t
	Fspare      Uint16_t
	Fro_dst     struct {
		Fsin6_len      Uint8_t
		Fsin6_family   Sa_family_t
		Fsin6_port     In_port_t
		Fsin6_flowinfo Uint32_t
		Fsin6_addr     struct {
			F__u6_addr struct {
				F__ccgo_pad1 [0]uint32
				F__u6_addr8  [16]Uint8_t
			}
		}
		Fsin6_scope_id Uint32_t
	}
	F__ccgo_pad1 [4]byte
} /* in6.h:379:1 */

// Options for use with [gs]etsockopt at the IPV6 level.
// First word of comment is data type; bool is stored in int.
// no hdrincl
// RFC2292 options

// 29; unused; was IPV6_FAITH

// new socket options introduced in RFC3542

// more new socket options introduced in RFC3542

// The following option is private; do not use it from user applications.
// It is deliberately defined to the same value as IP_MSFILTER.

// The following option deals with the 802.1Q Ethernet Priority Code Point
//      -1 use interface default

// to define items, should talk with KAME guys first, for *BSD compatibility

// Defaults and limits for options

// Limit for IPv6 multicast memberships

// Default resource limits for IPv6 multicast source filtering.
// These may be modified by sysctl.

// Argument structure for IPV6_JOIN_GROUP and IPV6_LEAVE_GROUP.
type Ipv6_mreq = struct {
	Fipv6mr_multiaddr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]Uint8_t
		}
	}
	Fipv6mr_interface uint32
} /* in6.h:545:1 */

// IPV6_PKTINFO: Packet information(RFC2292 sec 5)
type In6_pktinfo = struct {
	Fipi6_addr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]Uint8_t
		}
	}
	Fipi6_ifindex uint32
} /* in6.h:553:1 */

// Control structure for IPV6_RECVPATHMTU socket option.
type Ip6_mtuinfo = struct {
	Fip6m_addr struct {
		Fsin6_len      Uint8_t
		Fsin6_family   Sa_family_t
		Fsin6_port     In_port_t
		Fsin6_flowinfo Uint32_t
		Fsin6_addr     struct {
			F__u6_addr struct {
				F__ccgo_pad1 [0]uint32
				F__u6_addr8  [16]Uint8_t
			}
		}
		Fsin6_scope_id Uint32_t
	}
	Fip6m_mtu Uint32_t
} /* in6.h:561:1 */

var _ int8 /* gen.c:2:13: */
