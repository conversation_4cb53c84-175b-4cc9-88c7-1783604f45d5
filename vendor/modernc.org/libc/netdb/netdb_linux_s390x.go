// Code generated by 'ccgo netdb/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -o netdb/netdb_linux_s390x.go -pkgname netdb', DO NOT EDIT.

package netdb

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AF_ALG                           = 38
	AF_APPLETALK                     = 5
	AF_ASH                           = 18
	AF_ATMPVC                        = 8
	AF_ATMSVC                        = 20
	AF_AX25                          = 3
	AF_BLUETOOTH                     = 31
	AF_BRIDGE                        = 7
	AF_CAIF                          = 37
	AF_CAN                           = 29
	AF_DECnet                        = 12
	AF_ECONET                        = 19
	AF_FILE                          = 1
	AF_IB                            = 27
	AF_IEEE802154                    = 36
	AF_INET                          = 2
	AF_INET6                         = 10
	AF_IPX                           = 4
	AF_IRDA                          = 23
	AF_ISDN                          = 34
	AF_IUCV                          = 32
	AF_KCM                           = 41
	AF_KEY                           = 15
	AF_LLC                           = 26
	AF_LOCAL                         = 1
	AF_MAX                           = 45
	AF_MPLS                          = 28
	AF_NETBEUI                       = 13
	AF_NETLINK                       = 16
	AF_NETROM                        = 6
	AF_NFC                           = 39
	AF_PACKET                        = 17
	AF_PHONET                        = 35
	AF_PPPOX                         = 24
	AF_QIPCRTR                       = 42
	AF_RDS                           = 21
	AF_ROSE                          = 11
	AF_ROUTE                         = 16
	AF_RXRPC                         = 33
	AF_SECURITY                      = 14
	AF_SMC                           = 43
	AF_SNA                           = 22
	AF_TIPC                          = 30
	AF_UNIX                          = 1
	AF_UNSPEC                        = 0
	AF_VSOCK                         = 40
	AF_WANPIPE                       = 25
	AF_X25                           = 9
	AF_XDP                           = 44
	AI_ADDRCONFIG                    = 0x0020
	AI_ALL                           = 0x0010
	AI_CANONNAME                     = 0x0002
	AI_NUMERICHOST                   = 0x0004
	AI_NUMERICSERV                   = 0x0400
	AI_PASSIVE                       = 0x0001
	AI_V4MAPPED                      = 0x0008
	BIG_ENDIAN                       = 4321
	BYTE_ORDER                       = 4321
	EAI_AGAIN                        = -3
	EAI_BADFLAGS                     = -1
	EAI_FAIL                         = -4
	EAI_FAMILY                       = -6
	EAI_MEMORY                       = -10
	EAI_NONAME                       = -2
	EAI_OVERFLOW                     = -12
	EAI_SERVICE                      = -8
	EAI_SOCKTYPE                     = -7
	EAI_SYSTEM                       = -11
	FD_SETSIZE                       = 1024
	FIOGETOWN                        = 0x8903
	FIOSETOWN                        = 0x8901
	HOST_NOT_FOUND                   = 1
	INET6_ADDRSTRLEN                 = 46
	INET_ADDRSTRLEN                  = 16
	IN_CLASSA_HOST                   = 16777215
	IN_CLASSA_MAX                    = 128
	IN_CLASSA_NET                    = 0xff000000
	IN_CLASSA_NSHIFT                 = 24
	IN_CLASSB_HOST                   = 65535
	IN_CLASSB_MAX                    = 65536
	IN_CLASSB_NET                    = 0xffff0000
	IN_CLASSB_NSHIFT                 = 16
	IN_CLASSC_HOST                   = 255
	IN_CLASSC_NET                    = 0xffffff00
	IN_CLASSC_NSHIFT                 = 8
	IN_LOOPBACKNET                   = 127
	IPPORT_RESERVED1                 = 1024
	IPV6_2292DSTOPTS                 = 4
	IPV6_2292HOPLIMIT                = 8
	IPV6_2292HOPOPTS                 = 3
	IPV6_2292PKTINFO                 = 2
	IPV6_2292PKTOPTIONS              = 6
	IPV6_2292RTHDR                   = 5
	IPV6_ADDRFORM                    = 1
	IPV6_ADDR_PREFERENCES            = 72
	IPV6_ADD_MEMBERSHIP              = 20
	IPV6_AUTHHDR                     = 10
	IPV6_AUTOFLOWLABEL               = 70
	IPV6_CHECKSUM                    = 7
	IPV6_DONTFRAG                    = 62
	IPV6_DROP_MEMBERSHIP             = 21
	IPV6_DSTOPTS                     = 59
	IPV6_FREEBIND                    = 78
	IPV6_HDRINCL                     = 36
	IPV6_HOPLIMIT                    = 52
	IPV6_HOPOPTS                     = 54
	IPV6_IPSEC_POLICY                = 34
	IPV6_JOIN_ANYCAST                = 27
	IPV6_JOIN_GROUP                  = 20
	IPV6_LEAVE_ANYCAST               = 28
	IPV6_LEAVE_GROUP                 = 21
	IPV6_MINHOPCOUNT                 = 73
	IPV6_MTU                         = 24
	IPV6_MTU_DISCOVER                = 23
	IPV6_MULTICAST_ALL               = 29
	IPV6_MULTICAST_HOPS              = 18
	IPV6_MULTICAST_IF                = 17
	IPV6_MULTICAST_LOOP              = 19
	IPV6_NEXTHOP                     = 9
	IPV6_ORIGDSTADDR                 = 74
	IPV6_PATHMTU                     = 61
	IPV6_PKTINFO                     = 50
	IPV6_PMTUDISC_DO                 = 2
	IPV6_PMTUDISC_DONT               = 0
	IPV6_PMTUDISC_INTERFACE          = 4
	IPV6_PMTUDISC_OMIT               = 5
	IPV6_PMTUDISC_PROBE              = 3
	IPV6_PMTUDISC_WANT               = 1
	IPV6_RECVDSTOPTS                 = 58
	IPV6_RECVERR                     = 25
	IPV6_RECVFRAGSIZE                = 77
	IPV6_RECVHOPLIMIT                = 51
	IPV6_RECVHOPOPTS                 = 53
	IPV6_RECVORIGDSTADDR             = 74
	IPV6_RECVPATHMTU                 = 60
	IPV6_RECVPKTINFO                 = 49
	IPV6_RECVRTHDR                   = 56
	IPV6_RECVTCLASS                  = 66
	IPV6_ROUTER_ALERT                = 22
	IPV6_ROUTER_ALERT_ISOLATE        = 30
	IPV6_RTHDR                       = 57
	IPV6_RTHDRDSTOPTS                = 55
	IPV6_RTHDR_LOOSE                 = 0
	IPV6_RTHDR_STRICT                = 1
	IPV6_RTHDR_TYPE_0                = 0
	IPV6_RXDSTOPTS                   = 59
	IPV6_RXHOPOPTS                   = 54
	IPV6_TCLASS                      = 67
	IPV6_TRANSPARENT                 = 75
	IPV6_UNICAST_HOPS                = 16
	IPV6_UNICAST_IF                  = 76
	IPV6_V6ONLY                      = 26
	IPV6_XFRM_POLICY                 = 35
	IP_ADD_MEMBERSHIP                = 35
	IP_ADD_SOURCE_MEMBERSHIP         = 39
	IP_BIND_ADDRESS_NO_PORT          = 24
	IP_BLOCK_SOURCE                  = 38
	IP_CHECKSUM                      = 23
	IP_DEFAULT_MULTICAST_LOOP        = 1
	IP_DEFAULT_MULTICAST_TTL         = 1
	IP_DROP_MEMBERSHIP               = 36
	IP_DROP_SOURCE_MEMBERSHIP        = 40
	IP_FREEBIND                      = 15
	IP_HDRINCL                       = 3
	IP_IPSEC_POLICY                  = 16
	IP_MAX_MEMBERSHIPS               = 20
	IP_MINTTL                        = 21
	IP_MSFILTER                      = 41
	IP_MTU                           = 14
	IP_MTU_DISCOVER                  = 10
	IP_MULTICAST_ALL                 = 49
	IP_MULTICAST_IF                  = 32
	IP_MULTICAST_LOOP                = 34
	IP_MULTICAST_TTL                 = 33
	IP_NODEFRAG                      = 22
	IP_OPTIONS                       = 4
	IP_ORIGDSTADDR                   = 20
	IP_PASSSEC                       = 18
	IP_PKTINFO                       = 8
	IP_PKTOPTIONS                    = 9
	IP_PMTUDISC                      = 10
	IP_PMTUDISC_DO                   = 2
	IP_PMTUDISC_DONT                 = 0
	IP_PMTUDISC_INTERFACE            = 4
	IP_PMTUDISC_OMIT                 = 5
	IP_PMTUDISC_PROBE                = 3
	IP_PMTUDISC_WANT                 = 1
	IP_RECVERR                       = 11
	IP_RECVFRAGSIZE                  = 25
	IP_RECVOPTS                      = 6
	IP_RECVORIGDSTADDR               = 20
	IP_RECVRETOPTS                   = 7
	IP_RECVTOS                       = 13
	IP_RECVTTL                       = 12
	IP_RETOPTS                       = 7
	IP_ROUTER_ALERT                  = 5
	IP_TOS                           = 1
	IP_TRANSPARENT                   = 19
	IP_TTL                           = 2
	IP_UNBLOCK_SOURCE                = 37
	IP_UNICAST_IF                    = 50
	IP_XFRM_POLICY                   = 17
	LITTLE_ENDIAN                    = 1234
	MCAST_BLOCK_SOURCE               = 43
	MCAST_EXCLUDE                    = 0
	MCAST_INCLUDE                    = 1
	MCAST_JOIN_GROUP                 = 42
	MCAST_JOIN_SOURCE_GROUP          = 46
	MCAST_LEAVE_GROUP                = 45
	MCAST_LEAVE_SOURCE_GROUP         = 47
	MCAST_MSFILTER                   = 48
	MCAST_UNBLOCK_SOURCE             = 44
	NETDB_INTERNAL                   = -1
	NETDB_SUCCESS                    = 0
	NI_DGRAM                         = 16
	NI_MAXHOST                       = 1025
	NI_MAXSERV                       = 32
	NI_NAMEREQD                      = 8
	NI_NOFQDN                        = 4
	NI_NUMERICHOST                   = 1
	NI_NUMERICSERV                   = 2
	NO_ADDRESS                       = 4
	NO_DATA                          = 4
	NO_RECOVERY                      = 3
	PDP_ENDIAN                       = 3412
	PF_ALG                           = 38
	PF_APPLETALK                     = 5
	PF_ASH                           = 18
	PF_ATMPVC                        = 8
	PF_ATMSVC                        = 20
	PF_AX25                          = 3
	PF_BLUETOOTH                     = 31
	PF_BRIDGE                        = 7
	PF_CAIF                          = 37
	PF_CAN                           = 29
	PF_DECnet                        = 12
	PF_ECONET                        = 19
	PF_FILE                          = 1
	PF_IB                            = 27
	PF_IEEE802154                    = 36
	PF_INET                          = 2
	PF_INET6                         = 10
	PF_IPX                           = 4
	PF_IRDA                          = 23
	PF_ISDN                          = 34
	PF_IUCV                          = 32
	PF_KCM                           = 41
	PF_KEY                           = 15
	PF_LLC                           = 26
	PF_LOCAL                         = 1
	PF_MAX                           = 45
	PF_MPLS                          = 28
	PF_NETBEUI                       = 13
	PF_NETLINK                       = 16
	PF_NETROM                        = 6
	PF_NFC                           = 39
	PF_PACKET                        = 17
	PF_PHONET                        = 35
	PF_PPPOX                         = 24
	PF_QIPCRTR                       = 42
	PF_RDS                           = 21
	PF_ROSE                          = 11
	PF_ROUTE                         = 16
	PF_RXRPC                         = 33
	PF_SECURITY                      = 14
	PF_SMC                           = 43
	PF_SNA                           = 22
	PF_TIPC                          = 30
	PF_UNIX                          = 1
	PF_UNSPEC                        = 0
	PF_VSOCK                         = 40
	PF_WANPIPE                       = 25
	PF_X25                           = 9
	PF_XDP                           = 44
	SCM_TIMESTAMP                    = 29
	SCM_TIMESTAMPING                 = 37
	SCM_TIMESTAMPING_OPT_STATS       = 54
	SCM_TIMESTAMPING_PKTINFO         = 58
	SCM_TIMESTAMPNS                  = 35
	SCM_TXTIME                       = 61
	SCM_WIFI_STATUS                  = 41
	SIOCATMARK                       = 0x8905
	SIOCGPGRP                        = 0x8904
	SIOCGSTAMP                       = 0x8906
	SIOCGSTAMPNS                     = 0x8907
	SIOCSPGRP                        = 0x8902
	SOL_AAL                          = 265
	SOL_ALG                          = 279
	SOL_ATM                          = 264
	SOL_BLUETOOTH                    = 274
	SOL_CAIF                         = 278
	SOL_DCCP                         = 269
	SOL_DECNET                       = 261
	SOL_ICMPV6                       = 58
	SOL_IP                           = 0
	SOL_IPV6                         = 41
	SOL_IRDA                         = 266
	SOL_IUCV                         = 277
	SOL_KCM                          = 281
	SOL_LLC                          = 268
	SOL_NETBEUI                      = 267
	SOL_NETLINK                      = 270
	SOL_NFC                          = 280
	SOL_PACKET                       = 263
	SOL_PNPIPE                       = 275
	SOL_PPPOL2TP                     = 273
	SOL_RAW                          = 255
	SOL_RDS                          = 276
	SOL_RXRPC                        = 272
	SOL_SOCKET                       = 1
	SOL_TIPC                         = 271
	SOL_TLS                          = 282
	SOL_X25                          = 262
	SOL_XDP                          = 283
	SOMAXCONN                        = 4096
	SO_ACCEPTCONN                    = 30
	SO_ATTACH_BPF                    = 50
	SO_ATTACH_FILTER                 = 26
	SO_ATTACH_REUSEPORT_CBPF         = 51
	SO_ATTACH_REUSEPORT_EBPF         = 52
	SO_BINDTODEVICE                  = 25
	SO_BINDTOIFINDEX                 = 62
	SO_BPF_EXTENSIONS                = 48
	SO_BROADCAST                     = 6
	SO_BSDCOMPAT                     = 14
	SO_BUSY_POLL                     = 46
	SO_CNX_ADVICE                    = 53
	SO_COOKIE                        = 57
	SO_DEBUG                         = 1
	SO_DETACH_BPF                    = 27
	SO_DETACH_FILTER                 = 27
	SO_DETACH_REUSEPORT_BPF          = 68
	SO_DOMAIN                        = 39
	SO_DONTROUTE                     = 5
	SO_ERROR                         = 4
	SO_GET_FILTER                    = 26
	SO_INCOMING_CPU                  = 49
	SO_INCOMING_NAPI_ID              = 56
	SO_KEEPALIVE                     = 9
	SO_LINGER                        = 13
	SO_LOCK_FILTER                   = 44
	SO_MARK                          = 36
	SO_MAX_PACING_RATE               = 47
	SO_MEMINFO                       = 55
	SO_NOFCS                         = 43
	SO_NO_CHECK                      = 11
	SO_OOBINLINE                     = 10
	SO_PASSCRED                      = 16
	SO_PASSSEC                       = 34
	SO_PEEK_OFF                      = 42
	SO_PEERCRED                      = 17
	SO_PEERGROUPS                    = 59
	SO_PEERNAME                      = 28
	SO_PEERSEC                       = 31
	SO_PRIORITY                      = 12
	SO_PROTOCOL                      = 38
	SO_RCVBUF                        = 8
	SO_RCVBUFFORCE                   = 33
	SO_RCVLOWAT                      = 18
	SO_RCVTIMEO                      = 20
	SO_RCVTIMEO_NEW                  = 66
	SO_RCVTIMEO_OLD                  = 20
	SO_REUSEADDR                     = 2
	SO_REUSEPORT                     = 15
	SO_RXQ_OVFL                      = 40
	SO_SECURITY_AUTHENTICATION       = 22
	SO_SECURITY_ENCRYPTION_NETWORK   = 24
	SO_SECURITY_ENCRYPTION_TRANSPORT = 23
	SO_SELECT_ERR_QUEUE              = 45
	SO_SNDBUF                        = 7
	SO_SNDBUFFORCE                   = 32
	SO_SNDLOWAT                      = 19
	SO_SNDTIMEO                      = 21
	SO_SNDTIMEO_NEW                  = 67
	SO_SNDTIMEO_OLD                  = 21
	SO_TIMESTAMP                     = 29
	SO_TIMESTAMPING                  = 37
	SO_TIMESTAMPING_NEW              = 65
	SO_TIMESTAMPING_OLD              = 37
	SO_TIMESTAMPNS                   = 35
	SO_TIMESTAMPNS_NEW               = 64
	SO_TIMESTAMPNS_OLD               = 35
	SO_TIMESTAMP_NEW                 = 63
	SO_TIMESTAMP_OLD                 = 29
	SO_TXTIME                        = 61
	SO_TYPE                          = 3
	SO_WIFI_STATUS                   = 41
	SO_ZEROCOPY                      = 60
	TRY_AGAIN                        = 2
	X_ATFILE_SOURCE                  = 1
	X_BITS_BYTESWAP_H                = 1
	X_BITS_ENDIANNESS_H              = 1
	X_BITS_ENDIAN_H                  = 1
	X_BITS_PTHREADTYPES_ARCH_H       = 1
	X_BITS_PTHREADTYPES_COMMON_H     = 1
	X_BITS_SOCKADDR_H                = 1
	X_BITS_STDINT_INTN_H             = 1
	X_BITS_STDINT_UINTN_H            = 1
	X_BITS_TIME64_H                  = 1
	X_BITS_TYPESIZES_H               = 1
	X_BITS_TYPES_H                   = 1
	X_BITS_UINTN_IDENTITY_H          = 1
	X_BSD_SIZE_T_                    = 0
	X_BSD_SIZE_T_DEFINED_            = 0
	X_DEFAULT_SOURCE                 = 1
	X_ENDIAN_H                       = 1
	X_FEATURES_H                     = 1
	X_FILE_OFFSET_BITS               = 64
	X_GCC_SIZE_T                     = 0
	X_LINUX_POSIX_TYPES_H            = 0
	X_LP64                           = 1
	X_NETDB_H                        = 1
	X_NETINET_IN_H                   = 1
	X_PATH_HEQUIV                    = "/etc/hosts.equiv"
	X_PATH_HOSTS                     = "/etc/hosts"
	X_PATH_NETWORKS                  = "/etc/networks"
	X_PATH_NSSWITCH_CONF             = "/etc/nsswitch.conf"
	X_PATH_PROTOCOLS                 = "/etc/protocols"
	X_PATH_SERVICES                  = "/etc/services"
	X_POSIX_C_SOURCE                 = 200809
	X_POSIX_SOURCE                   = 1
	X_RPC_NETDB_H                    = 1
	X_RWLOCK_INTERNAL_H              = 0
	X_SIZET_                         = 0
	X_SIZE_T                         = 0
	X_SIZE_T_                        = 0
	X_SIZE_T_DECLARED                = 0
	X_SIZE_T_DEFINED                 = 0
	X_SIZE_T_DEFINED_                = 0
	X_SS_SIZE                        = 128
	X_STDC_PREDEF_H                  = 1
	X_STRUCT_TIMESPEC                = 1
	X_SYS_CDEFS_H                    = 1
	X_SYS_SELECT_H                   = 1
	X_SYS_SIZE_T_H                   = 0
	X_SYS_SOCKET_H                   = 1
	X_SYS_TYPES_H                    = 1
	X_THREAD_MUTEX_INTERNAL_H        = 1
	X_THREAD_SHARED_TYPES_H          = 1
	X_T_SIZE                         = 0
	X_T_SIZE_                        = 0
	Linux                            = 1
	Unix                             = 1
)

// Bits in the FLAGS argument to `send', `recv', et al.
const ( /* socket.h:200:1: */
	MSG_OOB        = 1  // Process out-of-band data.
	MSG_PEEK       = 2  // Peek at incoming messages.
	MSG_DONTROUTE  = 4  // Don't use local routing.
	MSG_CTRUNC     = 8  // Control data lost before delivery.
	MSG_PROXY      = 16 // Supply or ask second address.
	MSG_TRUNC      = 32
	MSG_DONTWAIT   = 64  // Nonblocking IO.
	MSG_EOR        = 128 // End of record.
	MSG_WAITALL    = 256 // Wait for a full request.
	MSG_FIN        = 512
	MSG_SYN        = 1024
	MSG_CONFIRM    = 2048 // Confirm path validity.
	MSG_RST        = 4096
	MSG_ERRQUEUE   = 8192      // Fetch message from error queue.
	MSG_NOSIGNAL   = 16384     // Do not generate SIGPIPE.
	MSG_MORE       = 32768     // Sender will send more.
	MSG_WAITFORONE = 65536     // Wait for at least one packet to return.
	MSG_BATCH      = 262144    // sendmmsg: more messages coming.
	MSG_ZEROCOPY   = ********  // Use user data in kernel path.
	MSG_FASTOPEN   = ********* // Send data in TCP SYN.

	MSG_CMSG_CLOEXEC = **********
)

// Socket level message types.  This must match the definitions in
//
//	<linux/socket.h>.
const ( /* socket.h:332:1: */
	SCM_RIGHTS = 1
)

// Get the architecture-dependent definition of enum __socket_type.
// Define enum __socket_type for generic Linux.
//    Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Types of sockets.
const ( /* socket_type.h:24:1: */
	SOCK_STREAM = 1 // Sequenced, reliable, connection-based
	// 				   byte streams.
	SOCK_DGRAM = 2 // Connectionless, unreliable datagrams
	// 				   of fixed maximum length.
	SOCK_RAW       = 3 // Raw protocol interface.
	SOCK_RDM       = 4 // Reliably-delivered messages.
	SOCK_SEQPACKET = 5 // Sequenced, reliable, connection-based,
	// 				   datagrams of fixed maximum length.
	SOCK_DCCP   = 6  // Datagram Congestion Control Protocol.
	SOCK_PACKET = 10 // Linux specific way of getting packets
	// 				   at the dev level.  For writing rarp and
	// 				   other similar things on the user level.

	// Flags to be ORed into the type parameter of socket and socketpair and
	//      used for the flags parameter of paccept.

	SOCK_CLOEXEC = 524288 // Atomically set close-on-exec flag for the
	// 				   new descriptor(s).
	SOCK_NONBLOCK = 2048
)

// Standard well-known ports.
const ( /* in.h:122:1: */
	IPPORT_ECHO       = 7  // Echo service.
	IPPORT_DISCARD    = 9  // Discard transmissions service.
	IPPORT_SYSTAT     = 11 // System status service.
	IPPORT_DAYTIME    = 13 // Time of day service.
	IPPORT_NETSTAT    = 15 // Network status service.
	IPPORT_FTP        = 21 // File Transfer Protocol.
	IPPORT_TELNET     = 23 // Telnet protocol.
	IPPORT_SMTP       = 25 // Simple Mail Transfer Protocol.
	IPPORT_TIMESERVER = 37 // Timeserver service.
	IPPORT_NAMESERVER = 42 // Domain Name Service.
	IPPORT_WHOIS      = 43 // Internet Whois service.
	IPPORT_MTP        = 57

	IPPORT_TFTP    = 69 // Trivial File Transfer Protocol.
	IPPORT_RJE     = 77
	IPPORT_FINGER  = 79 // Finger service.
	IPPORT_TTYLINK = 87
	IPPORT_SUPDUP  = 95 // SUPDUP protocol.

	IPPORT_EXECSERVER  = 512 // execd service.
	IPPORT_LOGINSERVER = 513 // rlogind service.
	IPPORT_CMDSERVER   = 514
	IPPORT_EFSSERVER   = 520

	// UDP ports.
	IPPORT_BIFFUDP     = 512
	IPPORT_WHOSERVER   = 513
	IPPORT_ROUTESERVER = 520

	// Ports less than this value are reserved for privileged processes.
	IPPORT_RESERVED = 1024

	// Ports greater this value are reserved for (non-privileged) servers.
	IPPORT_USERRESERVED = 5000
)

// Options for use with `getsockopt' and `setsockopt' at the IPv6 level.
//    The first word in the comment at the right is the data type used;
//    "bool" means a boolean value stored in an `int'.

// Advanced API (RFC3542) (1).

// Advanced API (RFC3542) (2).

// RFC5014.

// RFC5082.

// Obsolete synonyms for the above.

// IPV6_MTU_DISCOVER values.

// Socket level values for IPv6.

// Routing header options for IPv6.

// Standard well-defined IP protocols.
const ( /* in.h:40:1: */
	IPPROTO_IP      = 0   // Dummy protocol for TCP.
	IPPROTO_ICMP    = 1   // Internet Control Message Protocol.
	IPPROTO_IGMP    = 2   // Internet Group Management Protocol.
	IPPROTO_IPIP    = 4   // IPIP tunnels (older KA9Q tunnels use 94).
	IPPROTO_TCP     = 6   // Transmission Control Protocol.
	IPPROTO_EGP     = 8   // Exterior Gateway Protocol.
	IPPROTO_PUP     = 12  // PUP protocol.
	IPPROTO_UDP     = 17  // User Datagram Protocol.
	IPPROTO_IDP     = 22  // XNS IDP protocol.
	IPPROTO_TP      = 29  // SO Transport Protocol Class 4.
	IPPROTO_DCCP    = 33  // Datagram Congestion Control Protocol.
	IPPROTO_IPV6    = 41  // IPv6 header.
	IPPROTO_RSVP    = 46  // Reservation Protocol.
	IPPROTO_GRE     = 47  // General Routing Encapsulation.
	IPPROTO_ESP     = 50  // encapsulating security payload.
	IPPROTO_AH      = 51  // authentication header.
	IPPROTO_MTP     = 92  // Multicast Transport Protocol.
	IPPROTO_BEETPH  = 94  // IP option pseudo header for BEET.
	IPPROTO_ENCAP   = 98  // Encapsulation Header.
	IPPROTO_PIM     = 103 // Protocol Independent Multicast.
	IPPROTO_COMP    = 108 // Compression Header Protocol.
	IPPROTO_SCTP    = 132 // Stream Control Transmission Protocol.
	IPPROTO_UDPLITE = 136 // UDP-Lite protocol.
	IPPROTO_MPLS    = 137 // MPLS in IP.
	IPPROTO_RAW     = 255 // Raw IP packets.
	IPPROTO_MAX     = 256
)

// If __USE_KERNEL_IPV6_DEFS is 1 then the user has included the kernel
//
//	network headers first and we should use those ABI-identical definitions
//	instead of our own, otherwise 0.
const ( /* in.h:99:1: */
	IPPROTO_HOPOPTS  = 0  // IPv6 Hop-by-Hop options.
	IPPROTO_ROUTING  = 43 // IPv6 routing header.
	IPPROTO_FRAGMENT = 44 // IPv6 fragmentation header.
	IPPROTO_ICMPV6   = 58 // ICMPv6.
	IPPROTO_NONE     = 59 // IPv6 no next header.
	IPPROTO_DSTOPTS  = 60 // IPv6 destination options.
	IPPROTO_MH       = 135
)

// The following constants should be used for the second parameter of
//
//	`shutdown'.
const ( /* socket.h:41:1: */
	SHUT_RD   = 0 // No more receptions.
	SHUT_WR   = 1 // No more transmissions.
	SHUT_RDWR = 2
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// Copyright (C) 1996-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// All data returned by the network data base library are supplied in
//    host order and returned in network order (suitable for use in
//    system calls).

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// These are defined by the user (or the compiler)
//    to specify the desired environment:
//
//    __STRICT_ANSI__	ISO Standard C.
//    _ISOC99_SOURCE	Extensions to ISO C89 from ISO C99.
//    _ISOC11_SOURCE	Extensions to ISO C99 from ISO C11.
//    _ISOC2X_SOURCE	Extensions to ISO C99 from ISO C2X.
//    __STDC_WANT_LIB_EXT2__
// 			Extensions to ISO C99 from TR 27431-2:2010.
//    __STDC_WANT_IEC_60559_BFP_EXT__
// 			Extensions to ISO C11 from TS 18661-1:2014.
//    __STDC_WANT_IEC_60559_FUNCS_EXT__
// 			Extensions to ISO C11 from TS 18661-4:2015.
//    __STDC_WANT_IEC_60559_TYPES_EXT__
// 			Extensions to ISO C11 from TS 18661-3:2015.
//
//    _POSIX_SOURCE	IEEE Std 1003.1.
//    _POSIX_C_SOURCE	If ==1, like _POSIX_SOURCE; if >=2 add IEEE Std 1003.2;
// 			if >=199309L, add IEEE Std 1003.1b-1993;
// 			if >=199506L, add IEEE Std 1003.1c-1995;
// 			if >=200112L, all of IEEE 1003.1-2004
// 			if >=200809L, all of IEEE 1003.1-2008
//    _XOPEN_SOURCE	Includes POSIX and XPG things.  Set to 500 if
// 			Single Unix conformance is wanted, to 600 for the
// 			sixth revision, to 700 for the seventh revision.
//    _XOPEN_SOURCE_EXTENDED XPG things and X/Open Unix extensions.
//    _LARGEFILE_SOURCE	Some more functions for correct standard I/O.
//    _LARGEFILE64_SOURCE	Additional functionality from LFS for large files.
//    _FILE_OFFSET_BITS=N	Select default filesystem interface.
//    _ATFILE_SOURCE	Additional *at interfaces.
//    _GNU_SOURCE		All of the above, plus GNU extensions.
//    _DEFAULT_SOURCE	The default set of features (taking precedence over
// 			__STRICT_ANSI__).
//
//    _FORTIFY_SOURCE	Add security hardening to many library functions.
// 			Set to 1 or 2; 2 performs stricter checks than 1.
//
//    _REENTRANT, _THREAD_SAFE
// 			Obsolete; equivalent to _POSIX_C_SOURCE=199506L.
//
//    The `-ansi' switch to the GNU C compiler, and standards conformance
//    options such as `-std=c99', define __STRICT_ANSI__.  If none of
//    these are defined, or if _DEFAULT_SOURCE is defined, the default is
//    to have _POSIX_SOURCE set to one and _POSIX_C_SOURCE set to
//    200809L, as well as enabling miscellaneous functions from BSD and
//    SVID.  If more than one of these are defined, they accumulate.  For
//    example __STRICT_ANSI__, _POSIX_SOURCE and _POSIX_C_SOURCE together
//    give you ISO C, 1003.1, and 1003.2, but nothing else.
//
//    These are defined by this file and are used by the
//    header files to decide what to declare or define:
//
//    __GLIBC_USE (F)	Define things from feature set F.  This is defined
// 			to 1 or 0; the subsequent macros are either defined
// 			or undefined, and those tests should be moved to
// 			__GLIBC_USE.
//    __USE_ISOC11		Define ISO C11 things.
//    __USE_ISOC99		Define ISO C99 things.
//    __USE_ISOC95		Define ISO C90 AMD1 (C95) things.
//    __USE_ISOCXX11	Define ISO C++11 things.
//    __USE_POSIX		Define IEEE Std 1003.1 things.
//    __USE_POSIX2		Define IEEE Std 1003.2 things.
//    __USE_POSIX199309	Define IEEE Std 1003.1, and .1b things.
//    __USE_POSIX199506	Define IEEE Std 1003.1, .1b, .1c and .1i things.
//    __USE_XOPEN		Define XPG things.
//    __USE_XOPEN_EXTENDED	Define X/Open Unix things.
//    __USE_UNIX98		Define Single Unix V2 things.
//    __USE_XOPEN2K        Define XPG6 things.
//    __USE_XOPEN2KXSI     Define XPG6 XSI things.
//    __USE_XOPEN2K8       Define XPG7 things.
//    __USE_XOPEN2K8XSI    Define XPG7 XSI things.
//    __USE_LARGEFILE	Define correct standard I/O things.
//    __USE_LARGEFILE64	Define LFS things with separate names.
//    __USE_FILE_OFFSET64	Define 64bit interface as default.
//    __USE_MISC		Define things from 4.3BSD or System V Unix.
//    __USE_ATFILE		Define *at interfaces and AT_* constants for them.
//    __USE_GNU		Define GNU extensions.
//    __USE_FORTIFY_LEVEL	Additional security measures used, according to level.
//
//    The macros `__GNU_LIBRARY__', `__GLIBC__', and `__GLIBC_MINOR__' are
//    defined by this file unconditionally.  `__GNU_LIBRARY__' is provided
//    only for compatibility.  All new code should use the other symbols
//    to test for features.
//
//    All macros listed above as possibly being defined by this file are
//    explicitly undefined if they are not explicitly defined.
//    Feature-test macros that are not defined by the user or compiler
//    but are implied by the other feature-test macros defined (or by the
//    lack of any definitions) are defined by the file.
//
//    ISO C feature test macros depend on the definition of the macro
//    when an affected header is included, not when the first system
//    header is included, and so they are handled in
//    <bits/libc-header-start.h>, which does not have a multiple include
//    guard.  Feature test macros that can be handled from the first
//    system header included are handled here.

// Undefine everything, so we get a clean slate.

// Suppress kernel-name space pollution unless user expressedly asks
//    for it.

// Convenience macro to test the version of gcc.
//    Use like this:
//    #if __GNUC_PREREQ (2,8)
//    ... code requiring gcc 2.8 or later ...
//    #endif
//    Note: only works for GCC 2.0 and later, because __GNUC_MINOR__ was
//    added in 2.0.

// Similarly for clang.  Features added to GCC after version 4.2 may
//    or may not also be available in clang, and clang's definitions of
//    __GNUC(_MINOR)__ are fixed at 4 and 2 respectively.  Not all such
//    features can be queried via __has_extension/__has_feature.

// Whether to use feature set F.

// _BSD_SOURCE and _SVID_SOURCE are deprecated aliases for
//    _DEFAULT_SOURCE.  If _DEFAULT_SOURCE is present we do not
//    issue a warning; the expectation is that the source is being
//    transitioned to use the new macro.

// If _GNU_SOURCE was defined by the user, turn on all the other features.

// If nothing (other than _GNU_SOURCE and _DEFAULT_SOURCE) is defined,
//    define _DEFAULT_SOURCE.

// This is to enable the ISO C2X extension.

// This is to enable the ISO C11 extension.

// This is to enable the ISO C99 extension.

// This is to enable the ISO C90 Amendment 1:1995 extension.

// If none of the ANSI/POSIX macros are defined, or if _DEFAULT_SOURCE
//    is defined, use POSIX.1-2008 (or another version depending on
//    _XOPEN_SOURCE).

// Some C libraries once required _REENTRANT and/or _THREAD_SAFE to be
//    defined in all multithreaded code.  GNU libc has not required this
//    for many years.  We now treat them as compatibility synonyms for
//    _POSIX_C_SOURCE=199506L, which is the earliest level of POSIX with
//    comprehensive support for multithreaded code.  Using them never
//    lowers the selected level of POSIX conformance, only raises it.

// The function 'gets' existed in C89, but is impossible to use
//    safely.  It has been removed from ISO C11 and ISO C++14.  Note: for
//    compatibility with various implementations of <cstdio>, this test
//    must consider only the value of __cplusplus when compiling C++.

// GNU formerly extended the scanf functions with modified format
//    specifiers %as, %aS, and %a[...] that allocate a buffer for the
//    input using malloc.  This extension conflicts with ISO C99, which
//    defines %a as a standalone format specifier that reads a floating-
//    point number; moreover, POSIX.1-2008 provides the same feature
//    using the modifier letter 'm' instead (%ms, %mS, %m[...]).
//
//    We now follow C99 unless GNU extensions are active and the compiler
//    is specifically in C89 or C++98 mode (strict or not).  For
//    instance, with GCC, -std=gnu11 will have C99-compliant scanf with
//    or without -D_GNU_SOURCE, but -std=c89 -D_GNU_SOURCE will have the
//    old extension.

// Get definitions of __STDC_* predefined macros, if the compiler has
//    not preincluded this header automatically.
// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This macro indicates that the installed library is the GNU C Library.
//    For historic reasons the value now is 6 and this will stay from now
//    on.  The use of this variable is deprecated.  Use __GLIBC__ and
//    __GLIBC_MINOR__ now (see below) when you want to test for a specific
//    GNU C library version and use the values in <gnu/lib-names.h> to get
//    the sonames of the shared libraries.

// Major and minor version number of the GNU C library package.  Use
//    these macros to test for features in specific releases.

// This is here only because every header file already includes this one.
// Copyright (C) 1992-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We are almost always included from features.h.

// The GNU libc does not support any K&R compilers or the traditional mode
//    of ISO C compilers anymore.  Check for some of the combinations not
//    anymore supported.

// Some user header file might have defined this before.

// All functions, except those with callbacks or those that
//    synchronize memory, are leaf functions.

// GCC can always grok prototypes.  For C++ programs we add throw()
//    to help it optimize the function calls.  But this works only with
//    gcc 2.8.x and egcs.  For gcc 3.2 and up we even mark C functions
//    as non-throwing using a function attribute since programs can use
//    the -fexceptions options for C code as well.

// Compilers that are not clang may object to
//        #if defined __clang__ && __has_extension(...)
//    even though they do not need to evaluate the right-hand side of the &&.

// These two macros are not used in glibc anymore.  They are kept here
//    only because some other projects expect the macros to be defined.

// For these things, GCC behaves the ANSI way normally,
//    and the non-ANSI way under -traditional.

// This is not a typedef so `const __ptr_t' does the right thing.

// C++ needs to know that types and declarations are C, not C++.

// Fortify support.

// Support for flexible arrays.
//    Headers that should use flexible arrays only if they're "real"
//    (e.g. only if they won't affect sizeof()) should test
//    #if __glibc_c99_flexarr_available.

// __asm__ ("xyz") is used throughout the headers to rename functions
//    at the assembly language level.  This is wrapped by the __REDIRECT
//    macro, in order to support compilers that can do this some other
//    way.  When compilers don't support asm-names at all, we have to do
//    preprocessor tricks instead (which don't have exactly the right
//    semantics, but it's the best we can do).
//
//    Example:
//    int __REDIRECT(setpgrp, (__pid_t pid, __pid_t pgrp), setpgid);

//
// #elif __SOME_OTHER_COMPILER__
//
// # define __REDIRECT(name, proto, alias) name proto; 	_Pragma("let " #name " = " #alias)

// GCC has various useful declarations that can be made with the
//    `__attribute__' syntax.  All of the ways we use this do fine if
//    they are omitted for compilers that don't understand it.

// At some point during the gcc 2.96 development the `malloc' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Tell the compiler which arguments to an allocation function
//    indicate the size of the allocation.

// At some point during the gcc 2.96 development the `pure' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// This declaration tells the compiler that the value is constant.

// At some point during the gcc 3.1 development the `used' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.

// Since version 3.2, gcc allows marking deprecated functions.

// Since version 4.5, gcc also allows one to specify the message printed
//    when a deprecated function is used.  clang claims to be gcc 4.2, but
//    may also support this feature.

// At some point during the gcc 2.8 development the `format_arg' attribute
//    for functions was introduced.  We don't want to use it unconditionally
//    (although this would be possible) since it generates warnings.
//    If several `format_arg' attributes are given for the same function, in
//    gcc-3.0 and older, all but the last one are ignored.  In newer gccs,
//    all designated arguments are considered.

// At some point during the gcc 2.97 development the `strfmon' format
//    attribute for functions was introduced.  We don't want to use it
//    unconditionally (although this would be possible) since it
//    generates warnings.

// The nonull function attribute allows to mark pointer parameters which
//    must not be NULL.

// If fortification mode, we warn about unused results of certain
//    function calls which can lead to problems.

// Forces a function to be always inlined.
// The Linux kernel defines __always_inline in stddef.h (283d7573), and
//    it conflicts with this definition.  Therefore undefine it first to
//    allow either header to be included first.

// Associate error messages with the source location of the call site rather
//    than with the source location inside the function.

// GCC 4.3 and above with -std=c99 or -std=gnu99 implements ISO C99
//    inline semantics, unless -fgnu89-inline is used.  Using __GNUC_STDC_INLINE__
//    or __GNUC_GNU_INLINE is not a good enough check for gcc because gcc versions
//    older than 4.3 may define these macros and still not guarantee GNU inlining
//    semantics.
//
//    clang++ identifies itself as gcc-4.2, but has support for GNU inlining
//    semantics, that can be checked for by using the __GNUC_STDC_INLINE_ and
//    __GNUC_GNU_INLINE__ macro definitions.

// GCC 4.3 and above allow passing all anonymous arguments of an
//    __extern_always_inline function to some other vararg function.

// It is possible to compile containing GCC extensions even if GCC is
//    run in pedantic mode if the uses are carefully marked using the
//    `__extension__' keyword.  But this is not generally available before
//    version 2.8.

// __restrict is known in EGCS 1.2 and above.

// ISO C99 also allows to declare arrays as non-overlapping.  The syntax is
//      array_name[restrict]
//    GCC 3.1 supports this.

// Describes a char array whose address can safely be passed as the first
//    argument to strncpy and strncat, as the char array is not necessarily
//    a NUL-terminated string.

// Undefine (also defined in libc-symbols.h).
// Copies attributes from the declaration or type referenced by
//    the argument.

// Determine the wordsize from the preprocessor defines.

// Properties of long double type.  ldbl-opt version.
//    Copyright (C) 2016-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License  published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// __glibc_macro_warning (MESSAGE) issues warning MESSAGE.  This is
//    intended for use in preprocessor macros.
//
//    Note: MESSAGE must be a _single_ string; concatenation of string
//    literals is not supported.

// Generic selection (ISO C11) is a C-only feature, available in GCC
//    since version 4.9.  Previous versions do not provide generic
//    selection, even though they might set __STDC_VERSION__ to 201112L,
//    when in -std=c11 mode.  Thus, we must check for !defined __GNUC__
//    when testing __STDC_VERSION__ for generic selection support.
//    On the other hand, Clang also defines __GNUC__, so a clang-specific
//    check is required to enable the use of generic selection.

// If we don't have __REDIRECT, prototypes will be missing if
//    __USE_FILE_OFFSET64 but not __USE_LARGEFILE[64].

// Decide whether we can define 'extern inline' functions in headers.

// This is here only because every header file already includes this one.
//    Get the definitions of all the appropriate `__stub_FUNCTION' symbols.
//    <gnu/stubs.h> contains `#define __stub_FUNCTION' when FUNCTION is a stub
//    that will always return failure (and set errno to ENOSYS).
// This file is automatically generated.
//    This file selects the right generated file of `__stub_FUNCTION' macros
//    based on the architecture being compiled for.

// Determine the wordsize from the preprocessor defines.

// This file is automatically generated.
//    It defines a symbol `__stub_FUNCTION' for each function
//    in the C library which is a stub, meaning it will fail
//    every time called, usually setting errno to ENOSYS.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define uintN_t types.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Bit size of the time_t type at glibc build time, general case.
//    Copyright (C) 2018-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Size in bits of the 'time_t' type of the default ABI.

// Convenience types.
type X__u_char = uint8   /* types.h:31:23 */
type X__u_short = uint16 /* types.h:32:28 */
type X__u_int = uint32   /* types.h:33:22 */
type X__u_long = uint64  /* types.h:34:27 */

// Fixed-size types, underlying types depend on word size and compiler.
type X__int8_t = int8     /* types.h:37:21 */
type X__uint8_t = uint8   /* types.h:38:23 */
type X__int16_t = int16   /* types.h:39:26 */
type X__uint16_t = uint16 /* types.h:40:28 */
type X__int32_t = int32   /* types.h:41:20 */
type X__uint32_t = uint32 /* types.h:42:22 */
type X__int64_t = int64   /* types.h:44:25 */
type X__uint64_t = uint64 /* types.h:45:27 */

// Smallest types with at least a given width.
type X__int_least8_t = X__int8_t     /* types.h:52:18 */
type X__uint_least8_t = X__uint8_t   /* types.h:53:19 */
type X__int_least16_t = X__int16_t   /* types.h:54:19 */
type X__uint_least16_t = X__uint16_t /* types.h:55:20 */
type X__int_least32_t = X__int32_t   /* types.h:56:19 */
type X__uint_least32_t = X__uint32_t /* types.h:57:20 */
type X__int_least64_t = X__int64_t   /* types.h:58:19 */
type X__uint_least64_t = X__uint64_t /* types.h:59:20 */

// quad_t is also 64 bits.
type X__quad_t = int64    /* types.h:63:18 */
type X__u_quad_t = uint64 /* types.h:64:27 */

// Largest integral types.
type X__intmax_t = int64   /* types.h:72:18 */
type X__uintmax_t = uint64 /* types.h:73:27 */

// The machine-dependent file <bits/typesizes.h> defines __*_T_TYPE
//    macros for each of the OS types we define below.  The definitions
//    of those macros must use the following macros for underlying types.
//    We define __S<SIZE>_TYPE and __U<SIZE>_TYPE for the signed and unsigned
//    variants of each of the following integer types on this machine.
//
// 	16		-- "natural" 16-bit type (always short)
// 	32		-- "natural" 32-bit type (always int)
// 	64		-- "natural" 64-bit type (long or long long)
// 	LONG32		-- 32-bit type, traditionally long
// 	QUAD		-- 64-bit type, traditionally long long
// 	WORD		-- natural type of __WORDSIZE bits (int or long)
// 	LONGWORD	-- type of __WORDSIZE bits, traditionally long
//
//    We distinguish WORD/LONGWORD, 32/LONG32, and 64/QUAD so that the
//    conventional uses of `long' or `long long' type modifiers match the
//    types we define, even when a less-adorned type would be the same size.
//    This matters for (somewhat) portably writing printf/scanf formats for
//    these types, where using the appropriate l or ll format modifiers can
//    make the typedefs and the formats match up across all GNU platforms.  If
//    we used `long' when it's 64 bits where `long long' is expected, then the
//    compiler would warn about the formats not matching the argument types,
//    and the programmer changing them to shut up the compiler would break the
//    program's portability.
//
//    Here we assume what is presently the case in all the GCC configurations
//    we support: long long is always 64 bits, long is always word/address size,
//    and int is always 32 bits.

// No need to mark the typedef with __extension__.
// bits/typesizes.h -- underlying types for *_t.  Linux/s390 version.
//    Copyright (C) 2003-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// See <bits/types.h> for the meaning of these macros.  This file exists so
//    that <bits/types.h> need not vary across different GNU platforms.

// size_t is unsigned long int on s390 -m31.

// Tell the libc code that off_t and off64_t are actually the same type
//    for all ABI purposes, even if possibly expressed as different base types
//    for C type-checking purposes.

// Same for ino_t and ino64_t.

// And for __rlim_t and __rlim64_t.

// And for fsblkcnt_t, fsblkcnt64_t, fsfilcnt_t and fsfilcnt64_t.

// Number of descriptors that can fit in an `fd_set'.

// bits/time64.h -- underlying types for __time64_t.  Generic version.
//    Copyright (C) 2018-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Define __TIME64_T_TYPE so that it is always a 64-bit type.

// If we already have 64-bit time type then use it.

type X__dev_t = uint64                     /* types.h:145:25 */ // Type of device numbers.
type X__uid_t = uint32                     /* types.h:146:25 */ // Type of user identifications.
type X__gid_t = uint32                     /* types.h:147:25 */ // Type of group identifications.
type X__ino_t = uint64                     /* types.h:148:25 */ // Type of file serial numbers.
type X__ino64_t = uint64                   /* types.h:149:27 */ // Type of file serial numbers (LFS).
type X__mode_t = uint32                    /* types.h:150:26 */ // Type of file attribute bitmasks.
type X__nlink_t = uint64                   /* types.h:151:27 */ // Type of file link counts.
type X__off_t = int64                      /* types.h:152:25 */ // Type of file sizes and offsets.
type X__off64_t = int64                    /* types.h:153:27 */ // Type of file sizes and offsets (LFS).
type X__pid_t = int32                      /* types.h:154:25 */ // Type of process identifications.
type X__fsid_t = struct{ F__val [2]int32 } /* types.h:155:26 */ // Type of file system IDs.
type X__clock_t = int64                    /* types.h:156:27 */ // Type of CPU usage counts.
type X__rlim_t = uint64                    /* types.h:157:26 */ // Type for resource measurement.
type X__rlim64_t = uint64                  /* types.h:158:28 */ // Type for resource measurement (LFS).
type X__id_t = uint32                      /* types.h:159:24 */ // General type for IDs.
type X__time_t = int64                     /* types.h:160:26 */ // Seconds since the Epoch.
type X__useconds_t = uint32                /* types.h:161:30 */ // Count of microseconds.
type X__suseconds_t = int64                /* types.h:162:31 */ // Signed count of microseconds.

type X__daddr_t = int32 /* types.h:164:27 */ // The type of a disk address.
type X__key_t = int32   /* types.h:165:25 */ // Type of an IPC key.

// Clock ID used in clock and timer functions.
type X__clockid_t = int32 /* types.h:168:29 */

// Timer ID returned by `timer_create'.
type X__timer_t = uintptr /* types.h:171:12 */

// Type to represent block size.
type X__blksize_t = int64 /* types.h:174:29 */

// Types from the Large File Support interface.

// Type to count number of disk blocks.
type X__blkcnt_t = int64   /* types.h:179:28 */
type X__blkcnt64_t = int64 /* types.h:180:30 */

// Type to count file system blocks.
type X__fsblkcnt_t = uint64   /* types.h:183:30 */
type X__fsblkcnt64_t = uint64 /* types.h:184:32 */

// Type to count file system nodes.
type X__fsfilcnt_t = uint64   /* types.h:187:30 */
type X__fsfilcnt64_t = uint64 /* types.h:188:32 */

// Type of miscellaneous file system fields.
type X__fsword_t = int64 /* types.h:191:28 */

type X__ssize_t = int64 /* types.h:193:27 */ // Type of a byte count, or error.

// Signed long type used in system calls.
type X__syscall_slong_t = int64 /* types.h:196:33 */
// Unsigned long type used in system calls.
type X__syscall_ulong_t = uint64 /* types.h:198:33 */

// These few don't really vary by system, they always correspond
//
//	to one of the other defined types.
type X__loff_t = X__off64_t /* types.h:202:19 */ // Type of file sizes and offsets (LFS).
type X__caddr_t = uintptr   /* types.h:203:14 */

// Duplicates info from stdint.h but this is used in unistd.h.
type X__intptr_t = int64 /* types.h:206:25 */

// Duplicate info from sys/socket.h.
type X__socklen_t = uint32 /* types.h:209:23 */

// C99: An integer type that can be accessed as an atomic entity,
//
//	even in the presence of asynchronous interrupts.
//	It is not currently necessary for this to be machine-specific.
type X__sig_atomic_t = int32 /* types.h:214:13 */

// Seconds since the Epoch, visible to user code when time_t is too
//    narrow only for consistency with the old way of widening too-narrow
//    types.  User code should never use __time64_t.

type Uint8_t = X__uint8_t   /* stdint-uintn.h:24:19 */
type Uint16_t = X__uint16_t /* stdint-uintn.h:25:20 */
type Uint32_t = X__uint32_t /* stdint-uintn.h:26:20 */
type Uint64_t = X__uint64_t /* stdint-uintn.h:27:20 */

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Structure for scatter/gather I/O.
type Iovec = struct {
	Fiov_base uintptr
	Fiov_len  Size_t
} /* struct_iovec.h:26:1 */

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// This operating system-specific header file defines the SOCK_*, PF_*,
//    AF_*, MSG_*, SOL_*, and SO_* constants, and the `struct sockaddr',
//    `struct msghdr', and `struct linger' types.
// System-specific socket constants and types.  Linux version.
//    Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	POSIX Standard: 2.6 Primitive System Data Types	<sys/types.h>

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

type U_char = X__u_char     /* types.h:33:18 */
type U_short = X__u_short   /* types.h:34:19 */
type U_int = X__u_int       /* types.h:35:17 */
type U_long = X__u_long     /* types.h:36:18 */
type Quad_t = X__quad_t     /* types.h:37:18 */
type U_quad_t = X__u_quad_t /* types.h:38:20 */
type Fsid_t = X__fsid_t     /* types.h:39:18 */
type Loff_t = X__loff_t     /* types.h:42:18 */

type Ino_t = X__ino64_t /* types.h:49:19 */

type Dev_t = X__dev_t /* types.h:59:17 */

type Gid_t = X__gid_t /* types.h:64:17 */

type Mode_t = X__mode_t /* types.h:69:18 */

type Nlink_t = X__nlink_t /* types.h:74:19 */

type Uid_t = X__uid_t /* types.h:79:17 */

type Off_t = X__off64_t /* types.h:87:19 */

type Pid_t = X__pid_t /* types.h:97:17 */

type Id_t = X__id_t /* types.h:103:16 */

type Ssize_t = X__ssize_t /* types.h:108:19 */

type Daddr_t = X__daddr_t /* types.h:114:19 */
type Caddr_t = X__caddr_t /* types.h:115:19 */

type Key_t = X__key_t /* types.h:121:17 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Returned by `clock'.
type Clock_t = X__clock_t /* clock_t.h:7:19 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Clock ID used in clock and timer functions.
type Clockid_t = X__clockid_t /* clockid_t.h:7:21 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Returned by `time'.
type Time_t = X__time_t /* time_t.h:7:18 */

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Timer ID returned by `timer_create'.
type Timer_t = X__timer_t /* timer_t.h:7:19 */

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

// Old compatibility names for C types.
type Ulong = uint64  /* types.h:148:27 */
type Ushort = uint16 /* types.h:149:28 */
type Uint = uint32   /* types.h:150:22 */

// These size-specific names are used by some of the inet code.

// Define intN_t types.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

type Int8_t = X__int8_t   /* stdint-intn.h:24:18 */
type Int16_t = X__int16_t /* stdint-intn.h:25:19 */
type Int32_t = X__int32_t /* stdint-intn.h:26:19 */
type Int64_t = X__int64_t /* stdint-intn.h:27:19 */

// These were defined by ISO C without the first `_'.
type U_int8_t = X__uint8_t   /* types.h:158:19 */
type U_int16_t = X__uint16_t /* types.h:159:20 */
type U_int32_t = X__uint32_t /* types.h:160:20 */
type U_int64_t = X__uint64_t /* types.h:161:20 */

type Register_t = int32 /* types.h:164:13 */

// It also defines `fd_set' and the FD_* macros for `select'.
// `fd_set' type and related macros, and `select'/`pselect' declarations.
//    Copyright (C) 1996-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

//	POSIX 1003.1g: 6.2 Select from File Descriptor Sets <sys/select.h>

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Get definition of needed basic types.
// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Get __FD_* definitions.
// Copyright (C) 1997-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// We don't use `memset' because this would require a prototype and
//    the array isn't too big.

// Get sigset_t.

type X__sigset_t = struct{ F__val [16]uint64 } /* __sigset_t.h:8:3 */

// A set of signals to be blocked, unblocked, or waited for.
type Sigset_t = X__sigset_t /* sigset_t.h:7:20 */

// Get definition of timer specification structures.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// A time value that is accurate to the nearest
//
//	microsecond but also has a range of years.
type Timeval = struct {
	Ftv_sec  X__time_t
	Ftv_usec X__suseconds_t
} /* struct_timeval.h:8:1 */

// NB: Include guard matches what <linux/time.h> uses.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Endian macros for string.h functions
//    Copyright (C) 1992-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// POSIX.1b structure for a time value.  This is like a `struct timeval' but
//
//	has nanoseconds instead of microseconds.
type Timespec = struct {
	Ftv_sec  X__time_t
	Ftv_nsec X__syscall_slong_t
} /* struct_timespec.h:10:1 */

type Suseconds_t = X__suseconds_t /* select.h:43:23 */

// The fd_set member is required to be an array of longs.
type X__fd_mask = int64 /* select.h:49:18 */

// Some versions of <linux/posix_types.h> define this macros.
// It's easier to assume 8-bit bytes than to get CHAR_BIT.

// fd_set for select and pselect.
type Fd_set = struct{ F__fds_bits [16]X__fd_mask } /* select.h:70:5 */

// Maximum number of file descriptors in `fd_set'.

// Sometimes the fd_set member is assumed to have this type.
type Fd_mask = X__fd_mask /* select.h:77:19 */

// Define some inlines helping to catch common problems.

type Blksize_t = X__blksize_t /* types.h:185:21 */

// Types from the Large File Support interface.
type Blkcnt_t = X__blkcnt64_t     /* types.h:205:22 */ // Type to count number of disk blocks.
type Fsblkcnt_t = X__fsblkcnt64_t /* types.h:209:24 */ // Type to count file system blocks.
type Fsfilcnt_t = X__fsfilcnt64_t /* types.h:213:24 */ // Type to count file system inodes.

// Now add the thread types.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Common definition of pthread_mutex_t.

type X__pthread_internal_list = struct {
	F__prev uintptr
	F__next uintptr
} /* thread-shared-types.h:49:9 */

// Type to count file system inodes.

// Now add the thread types.
// Declaration of common pthread types for all architectures.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// For internal mutex and condition variable definitions.
// Common threading primitives definitions for both POSIX and C11.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Arch-specific definitions.  Each architecture must define the following
//    macros to define the expected sizes of pthread data types:
//
//    __SIZEOF_PTHREAD_ATTR_T        - size of pthread_attr_t.
//    __SIZEOF_PTHREAD_MUTEX_T       - size of pthread_mutex_t.
//    __SIZEOF_PTHREAD_MUTEXATTR_T   - size of pthread_mutexattr_t.
//    __SIZEOF_PTHREAD_COND_T        - size of pthread_cond_t.
//    __SIZEOF_PTHREAD_CONDATTR_T    - size of pthread_condattr_t.
//    __SIZEOF_PTHREAD_RWLOCK_T      - size of pthread_rwlock_t.
//    __SIZEOF_PTHREAD_RWLOCKATTR_T  - size of pthread_rwlockattr_t.
//    __SIZEOF_PTHREAD_BARRIER_T     - size of pthread_barrier_t.
//    __SIZEOF_PTHREAD_BARRIERATTR_T - size of pthread_barrierattr_t.
//
//    The additional macro defines any constraint for the lock alignment
//    inside the thread structures:
//
//    __LOCK_ALIGNMENT - for internal lock/futex usage.
//
//    Same idea but for the once locking primitive:
//
//    __ONCE_ALIGNMENT - for pthread_once_t/once_flag definition.

// Machine-specific pthread type layouts.  Generic version.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

// Determine the wordsize from the preprocessor defines.

// Common definition of pthread_mutex_t.

type X__pthread_list_t = X__pthread_internal_list /* thread-shared-types.h:53:3 */

type X__pthread_internal_slist = struct{ F__next uintptr } /* thread-shared-types.h:55:9 */

type X__pthread_slist_t = X__pthread_internal_slist /* thread-shared-types.h:58:3 */

// Arch-specific mutex definitions.  A generic implementation is provided
//    by sysdeps/nptl/bits/struct_mutex.h.  If required, an architecture
//    can override it by defining:
//
//    1. struct __pthread_mutex_s (used on both pthread_mutex_t and mtx_t
//       definition).  It should contains at least the internal members
//       defined in the generic version.
//
//    2. __LOCK_ALIGNMENT for any extra attribute for internal lock used with
//       atomic operations.
//
//    3. The macro __PTHREAD_MUTEX_INITIALIZER used for static initialization.
//       It should initialize the mutex internal flag.

// S390 internal mutex struct definitions.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

type X__pthread_mutex_s = struct {
	F__lock    int32
	F__count   uint32
	F__owner   int32
	F__nusers  uint32
	F__kind    int32
	F__spins   int16
	F__elision int16
	F__list    X__pthread_list_t
} /* struct_mutex.h:22:1 */

// Arch-sepecific read-write lock definitions.  A generic implementation is
//    provided by struct_rwlock.h.  If required, an architecture can override it
//    by defining:
//
//    1. struct __pthread_rwlock_arch_t (used on pthread_rwlock_t definition).
//       It should contain at least the internal members defined in the
//       generic version.
//
//    2. The macro __PTHREAD_RWLOCK_INITIALIZER used for static initialization.
//       It should initialize the rwlock internal type.

// S390 internal rwlock struct definitions.
//    Copyright (C) 2019-2020 Free Software Foundation, Inc.
//
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <http://www.gnu.org/licenses/>.

type X__pthread_rwlock_arch_t = struct {
	F__readers       uint32
	F__writers       uint32
	F__wrphase_futex uint32
	F__writers_futex uint32
	F__pad3          uint32
	F__pad4          uint32
	F__cur_writer    int32
	F__shared        int32
	F__pad1          uint64
	F__pad2          uint64
	F__flags         uint32
	F__ccgo_pad1     [4]byte
} /* struct_rwlock.h:23:1 */

// Common definition of pthread_cond_t.

type X__pthread_cond_s = struct {
	F__0            struct{ F__wseq uint64 }
	F__8            struct{ F__g1_start uint64 }
	F__g_refs       [2]uint32
	F__g_size       [2]uint32
	F__g1_orig_size uint32
	F__wrefs        uint32
	F__g_signals    [2]uint32
} /* thread-shared-types.h:92:1 */

// Thread identifiers.  The structure of the attribute type is not
//
//	exposed on purpose.
type Pthread_t = uint64 /* pthreadtypes.h:27:27 */

// Data structures for mutex handling.  The structure of the attribute
//
//	type is not exposed on purpose.
type Pthread_mutexattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:36:3 */

// Data structure for condition variable handling.  The structure of
//
//	the attribute type is not exposed on purpose.
type Pthread_condattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:45:3 */

// Keys for thread-specific data
type Pthread_key_t = uint32 /* pthreadtypes.h:49:22 */

// Once-only execution
type Pthread_once_t = int32 /* pthreadtypes.h:53:30 */

type Pthread_attr_t1 = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [56]uint8
} /* pthreadtypes.h:56:1 */

type Pthread_attr_t = Pthread_attr_t1 /* pthreadtypes.h:62:30 */

type Pthread_mutex_t = struct{ F__data X__pthread_mutex_s } /* pthreadtypes.h:72:3 */

type Pthread_cond_t = struct{ F__data X__pthread_cond_s } /* pthreadtypes.h:80:3 */

// Data structure for reader-writer lock variable handling.  The
//
//	structure of the attribute type is deliberately not exposed.
type Pthread_rwlock_t = struct{ F__data X__pthread_rwlock_arch_t } /* pthreadtypes.h:91:3 */

type Pthread_rwlockattr_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [8]uint8
} /* pthreadtypes.h:97:3 */

// POSIX spinlock data type.
type Pthread_spinlock_t = int32 /* pthreadtypes.h:103:22 */

// POSIX barriers data type.  The structure of the type is
//
//	deliberately not exposed.
type Pthread_barrier_t = struct {
	F__ccgo_pad1 [0]uint64
	F__size      [32]uint8
} /* pthreadtypes.h:112:3 */

type Pthread_barrierattr_t = struct {
	F__ccgo_pad1 [0]uint32
	F__size      [4]uint8
} /* pthreadtypes.h:118:3 */

// Type for length arguments in socket calls.
type Socklen_t = X__socklen_t /* socket.h:33:21 */

// Protocol families.

// Address families.

// Socket level values.  Others are defined in the appropriate headers.
//
//    XXX These definitions also should go into the appropriate headers as
//    far as they are available.

// Maximum queue length specifiable by listen.

// Get the definition of the macro to define the common sockaddr members.
// Definition of struct sockaddr_* common members and sizes, generic version.
//    Copyright (C) 1995-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/socket.h> instead.

// POSIX.1g specifies this type name for the `sa_family' member.
type Sa_family_t = uint16 /* sockaddr.h:28:28 */

// This macro is used to declare the initial common members
//    of the data types used for socket addresses, `struct sockaddr',
//    `struct sockaddr_in', `struct sockaddr_un', etc.

// Size of struct sockaddr_storage.

// Structure describing a generic socket address.
type Sockaddr = struct {
	Fsa_family Sa_family_t
	Fsa_data   [14]uint8
} /* socket.h:178:1 */

// Structure large enough to hold any socket address (with the historical
//    exception of AF_UNIX).

type Sockaddr_storage = struct {
	Fss_family    Sa_family_t
	F__ss_padding [118]uint8
	F__ss_align   uint64
} /* socket.h:191:1 */

// Structure describing messages sent by
//
//	`sendmsg' and received by `recvmsg'.
type Msghdr = struct {
	Fmsg_name       uintptr
	Fmsg_namelen    Socklen_t
	F__ccgo_pad1    [4]byte
	Fmsg_iov        uintptr
	Fmsg_iovlen     Size_t
	Fmsg_control    uintptr
	Fmsg_controllen Size_t
	Fmsg_flags      int32
	F__ccgo_pad2    [4]byte
} /* socket.h:257:1 */

// Structure used for storage of ancillary data object information.
type Cmsghdr = struct {
	F__ccgo_pad1 [0]uint64
	Fcmsg_len    Size_t
	Fcmsg_level  int32
	Fcmsg_type   int32
} /* socket.h:275:1 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// This allows for 1024 file descriptors: if NR_OPEN is ever grown
// beyond that you'll have to change this too. But 1024 fd's seem to be
// enough even for such "real" unices like OSF/1, so hopefully this is
// one limit that doesn't have to be changed [again].
//
// Note that POSIX wants the FD_CLEAR(fd,fdsetp) defines to be in
// <sys/time.h> (and thus <linux/time.h>) - but this is a more logical
// place for them. Solved by having dummy defines in <sys/time.h>.

// This macro may have been defined in <gnu/types.h>. But we always
// use the one here.

type X__kernel_fd_set = struct{ Ffds_bits [16]uint64 } /* posix_types.h:27:3 */

// Type of a signal handler.
type X__kernel_sighandler_t = uintptr /* posix_types.h:30:14 */

// Type of a SYSV IPC key.
type X__kernel_key_t = int32 /* posix_types.h:33:13 */
type X__kernel_mqd_t = int32 /* posix_types.h:34:13 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
//  S390 version
//

// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.  Also, we cannot
// assume GCC is being used.

type X__kernel_size_t = uint64 /* posix_types.h:16:25 */
type X__kernel_ssize_t = int64 /* posix_types.h:17:25 */

type X__kernel_old_dev_t = uint16 /* posix_types.h:20:24 */

type X__kernel_ino_t = uint32    /* posix_types.h:35:25 */
type X__kernel_mode_t = uint32   /* posix_types.h:36:25 */
type X__kernel_ipc_pid_t = int32 /* posix_types.h:37:25 */
type X__kernel_uid_t = uint32    /* posix_types.h:38:25 */
type X__kernel_gid_t = uint32    /* posix_types.h:39:25 */
type X__kernel_ptrdiff_t = int64 /* posix_types.h:40:25 */
type X__kernel_sigset_t = uint64 /* posix_types.h:41:25 */ // at least 32 bits

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// There seems to be no way of detecting this automatically from user
// space, so 64 bit architectures should override this in their
// bitsperlong.h. In particular, an architecture that supports
// both 32 and 64 bit user space must not rely on CONFIG_64BIT
// to decide it, but rather check a compiler provided macro.

// This file is generally used by user-level software, so you need to
// be a little careful about namespace pollution etc.
//
// First the types that are often defined in different ways across
// architectures, so that you can override them.

type X__kernel_long_t = int64   /* posix_types.h:15:15 */
type X__kernel_ulong_t = uint64 /* posix_types.h:16:23 */

type X__kernel_pid_t = int32 /* posix_types.h:28:14 */

type X__kernel_suseconds_t = X__kernel_long_t /* posix_types.h:41:26 */

type X__kernel_daddr_t = int32 /* posix_types.h:45:14 */

type X__kernel_uid32_t = uint32 /* posix_types.h:49:22 */
type X__kernel_gid32_t = uint32 /* posix_types.h:50:22 */

type X__kernel_old_uid_t = X__kernel_uid_t /* posix_types.h:54:24 */
type X__kernel_old_gid_t = X__kernel_gid_t /* posix_types.h:55:24 */

// Most 32 bit architectures use "unsigned int" size_t,
// and all 64 bit architectures use "unsigned long" size_t.

type X__kernel_fsid_t = struct{ Fval [2]int32 } /* posix_types.h:81:3 */

// anything below here should be completely generic
type X__kernel_off_t = X__kernel_long_t      /* posix_types.h:87:25 */
type X__kernel_loff_t = int64                /* posix_types.h:88:19 */
type X__kernel_old_time_t = X__kernel_long_t /* posix_types.h:89:25 */
type X__kernel_time_t = X__kernel_long_t     /* posix_types.h:90:25 */
type X__kernel_time64_t = int64              /* posix_types.h:91:19 */
type X__kernel_clock_t = X__kernel_long_t    /* posix_types.h:92:25 */
type X__kernel_timer_t = int32               /* posix_types.h:93:14 */
type X__kernel_clockid_t = int32             /* posix_types.h:94:14 */
type X__kernel_caddr_t = uintptr             /* posix_types.h:95:14 */
type X__kernel_uid16_t = uint16              /* posix_types.h:96:24 */
type X__kernel_gid16_t = uint16              /* posix_types.h:97:24 */

// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note

// Socket-level I/O control calls.

// For setsockopt(2)

// Security levels - as per NRL IPv6 - don't actually do anything

// Socket filtering

// Instruct lower device to use last 4-bytes of skb data as FCS

// on 64-bit and x32, avoid the ?: operator

// Structure used to manipulate the SO_LINGER option.
type Linger = struct {
	Fl_onoff  int32
	Fl_linger int32
} /* socket.h:361:1 */

// This is the 4.3 BSD `struct sockaddr' format, which is used as wire
//
//	format in the grotty old 4.3 `talk' protocol.
type Osockaddr = struct {
	Fsa_family uint16
	Fsa_data   [14]uint8
} /* struct_osockaddr.h:6:1 */

// Define some macros helping to catch buffer overflows.

// bits/types.h -- definitions of __*_t types underlying *_t types.
//    Copyright (C) 2002-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Never include this file directly; use <sys/types.h> instead.

// Internet address.
type In_addr_t = Uint32_t                  /* in.h:30:18 */
type In_addr = struct{ Fs_addr In_addr_t } /* in.h:31:1 */

// Get system-specific definitions.
// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Linux version.

// If the application has already included linux/in6.h from a linux-based
//    kernel then we will not define the IPv6 IPPROTO_* defines, in6_addr (nor the
//    defines), sockaddr_in6, or ipv6_mreq. Same for in6_ptkinfo or ip6_mtuinfo
//    in linux/ipv6.h. The ABI used by the linux-kernel and glibc match exactly.
//    Neither the linux kernel nor glibc should break this ABI without coordination.
//    In upstream kernel 56c176c9 the _UAPI prefix was stripped so we need to check
//    for _LINUX_IN6_H and _IPV6_H now, and keep checking the old versions for
//    maximum backwards compatibility.

// Options for use with `getsockopt' and `setsockopt' at the IP level.
//    The first word in the comment at the right is the data type used;
//    "bool" means a boolean value stored in an `int'.
// For BSD compatibility.

// TProxy original addresses

// IP_MTU_DISCOVER arguments.
// Always use interface mtu (ignores dst pmtu) but don't set DF flag.
//    Also incoming ICMP frag_needed notifications will be ignored on
//    this socket to prevent accepting spoofed ones.
// Like IP_PMTUDISC_INTERFACE but allow packets to be fragmented.

// To select the IP level.

// Structure used to describe IP options for IP_OPTIONS and IP_RETOPTS.
//
//	The `ip_dst' field is used for the first-hop gateway when using a
//	source route (this gets put into the header proper).
type Ip_opts = struct {
	Fip_dst  struct{ Fs_addr In_addr_t }
	Fip_opts [40]uint8
} /* in.h:142:1 */

// Like `struct ip_mreq' but including interface specification by index.
type Ip_mreqn = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_address   struct{ Fs_addr In_addr_t }
	Fimr_ifindex   int32
} /* in.h:149:1 */

// Structure used for IP_PKTINFO.
type In_pktinfo = struct {
	Fipi_ifindex  int32
	Fipi_spec_dst struct{ Fs_addr In_addr_t }
	Fipi_addr     struct{ Fs_addr In_addr_t }
} /* in.h:157:1 */

// Type to represent a port.
type In_port_t = Uint16_t /* in.h:119:18 */

// Definitions of the bits in an Internet address integer.
//
//    On subnets, host and network parts are found according to
//    the subnet mask, not these masks.

// Address to accept any incoming messages.
// Address to send to all hosts.
// Address indicating an error return.

// Network number for local host loopback.
// Address to loopback in software to local host.

// Defines for Multicast INADDR.

// IPv6 address
type In6_addr = struct {
	F__in6_u struct {
		F__ccgo_pad1 [0]uint32
		F__u6_addr8  [16]Uint8_t
	}
} /* in.h:212:1 */

// ::1

// Structure describing an Internet socket address.
type Sockaddr_in = struct {
	Fsin_family Sa_family_t
	Fsin_port   In_port_t
	Fsin_addr   struct{ Fs_addr In_addr_t }
	Fsin_zero   [8]uint8
} /* in.h:238:1 */

// Ditto, for IPv6.
type Sockaddr_in6 = struct {
	Fsin6_family   Sa_family_t
	Fsin6_port     In_port_t
	Fsin6_flowinfo Uint32_t
	Fsin6_addr     struct {
		F__in6_u struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]Uint8_t
		}
	}
	Fsin6_scope_id Uint32_t
} /* in.h:253:1 */

// IPv4 multicast request.
type Ip_mreq = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_interface struct{ Fs_addr In_addr_t }
} /* in.h:265:1 */

type Ip_mreq_source = struct {
	Fimr_multiaddr  struct{ Fs_addr In_addr_t }
	Fimr_interface  struct{ Fs_addr In_addr_t }
	Fimr_sourceaddr struct{ Fs_addr In_addr_t }
} /* in.h:274:1 */

// Likewise, for IPv6.
type Ipv6_mreq = struct {
	Fipv6mr_multiaddr struct {
		F__in6_u struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]Uint8_t
		}
	}
	Fipv6mr_interface uint32
} /* in.h:289:1 */

// Multicast group request.
type Group_req = struct {
	Fgr_interface Uint32_t
	F__ccgo_pad1  [4]byte
	Fgr_group     struct {
		Fss_family    Sa_family_t
		F__ss_padding [118]uint8
		F__ss_align   uint64
	}
} /* in.h:301:1 */

type Group_source_req = struct {
	Fgsr_interface Uint32_t
	F__ccgo_pad1   [4]byte
	Fgsr_group     struct {
		Fss_family    Sa_family_t
		F__ss_padding [118]uint8
		F__ss_align   uint64
	}
	Fgsr_source struct {
		Fss_family    Sa_family_t
		F__ss_padding [118]uint8
		F__ss_align   uint64
	}
} /* in.h:310:1 */

// Full-state filter operations.
type Ip_msfilter = struct {
	Fimsf_multiaddr struct{ Fs_addr In_addr_t }
	Fimsf_interface struct{ Fs_addr In_addr_t }
	Fimsf_fmode     Uint32_t
	Fimsf_numsrc    Uint32_t
	Fimsf_slist     [1]struct{ Fs_addr In_addr_t }
} /* in.h:324:1 */

type Group_filter = struct {
	Fgf_interface Uint32_t
	F__ccgo_pad1  [4]byte
	Fgf_group     struct {
		Fss_family    Sa_family_t
		F__ss_padding [118]uint8
		F__ss_align   uint64
	}
	Fgf_fmode  Uint32_t
	Fgf_numsrc Uint32_t
	Fgf_slist  [1]struct {
		Fss_family    Sa_family_t
		F__ss_padding [118]uint8
		F__ss_align   uint64
	}
} /* in.h:345:1 */

// Define uintN_t types.
//    Copyright (C) 2017-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// This is necessary to make this include file properly replace the
//    Sun version.
// @(#)netdb.h	2.1 88/07/29 3.9 RPCSRC
// Copyright (c) 2010, Oracle America, Inc.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials
//       provided with the distribution.
//     * Neither the name of the "Oracle America, Inc." nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
//   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
//   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
//   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
//   FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
//   COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
//   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
//   DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
//   GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
//   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
//   WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
//   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
//   OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Cleaned up for GNU <NAME_EMAIL>:
//    added multiple inclusion protection and use of <sys/cdefs.h>.
//    In GNU this file is #include'd by <netdb.h>.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.

// This avoids lossage on SunOS but only if stdtypes.h comes first.
//    There's no way to win with the other order!  Sun lossage.

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// A null pointer constant.

type Rpcent = struct {
	Fr_name      uintptr
	Fr_aliases   uintptr
	Fr_number    int32
	F__ccgo_pad1 [4]byte
} /* netdb.h:46:1 */

// Copyright (C) 1996-2020 Free Software Foundation, Inc.
//    This file is part of the GNU C Library.
//
//    The GNU C Library is free software; you can redistribute it and/or
//    modify it under the terms of the GNU Lesser General Public
//    License as published by the Free Software Foundation; either
//    version 2.1 of the License, or (at your option) any later version.
//
//    The GNU C Library is distributed in the hope that it will be useful,
//    but WITHOUT ANY WARRANTY; without even the implied warranty of
//    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
//    Lesser General Public License for more details.
//
//    You should have received a copy of the GNU Lesser General Public
//    License along with the GNU C Library; if not, see
//    <https://www.gnu.org/licenses/>.

// Description of data base entry for a single network.  NOTE: here a
//
//	poor assumption is made.  The network number is expected to fit
//	into an unsigned long int variable.
type Netent = struct {
	Fn_name     uintptr
	Fn_aliases  uintptr
	Fn_addrtype int32
	Fn_net      Uint32_t
} /* netdb.h:26:1 */

// Description of data base entry for a single host.
type Hostent = struct {
	Fh_name      uintptr
	Fh_aliases   uintptr
	Fh_addrtype  int32
	Fh_length    int32
	Fh_addr_list uintptr
} /* netdb.h:98:1 */

// Description of data base entry for a single service.
type Servent = struct {
	Fs_name      uintptr
	Fs_aliases   uintptr
	Fs_port      int32
	F__ccgo_pad1 [4]byte
	Fs_proto     uintptr
} /* netdb.h:255:1 */

// Description of data base entry for a single service.
type Protoent = struct {
	Fp_name      uintptr
	Fp_aliases   uintptr
	Fp_proto     int32
	F__ccgo_pad1 [4]byte
} /* netdb.h:324:1 */

// Extension from POSIX.1:2001.
// Structure to contain information about address of a service provider.
type Addrinfo = struct {
	Fai_flags     int32
	Fai_family    int32
	Fai_socktype  int32
	Fai_protocol  int32
	Fai_addrlen   Socklen_t
	F__ccgo_pad1  [4]byte
	Fai_addr      uintptr
	Fai_canonname uintptr
	Fai_next      uintptr
} /* netdb.h:565:1 */

var _ uint8 /* gen.c:2:13: */
