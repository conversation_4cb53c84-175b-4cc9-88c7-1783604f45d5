// Code generated by 'ccgo netdb/gen.c -crt-import-path  -export-defines  -export-enums  -export-externs X -export-fields F -export-structs  -export-typedefs  -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o netdb/netdb_darwin_amd64.go -pkgname netdb', DO NOT EDIT.

package netdb

var CAPI = map[string]struct{}{
	"__darwin_check_fd_set":          {},
	"__darwin_check_fd_set_overflow": {},
	"__darwin_fd_clr":                {},
	"__darwin_fd_isset":              {},
	"__darwin_fd_set":                {},
}
