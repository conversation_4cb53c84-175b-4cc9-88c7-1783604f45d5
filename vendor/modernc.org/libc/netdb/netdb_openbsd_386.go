// Code generated by 'ccgo netdb/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o netdb/netdb_openbsd_386.go -pkgname netdb', DO NOT EDIT.

package netdb

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AI_ADDRCONFIG                    = 64                 // netdb.h:165:1:
	AI_CANONNAME                     = 2                  // netdb.h:160:1:
	AI_EXT                           = 8                  // netdb.h:162:1:
	AI_FQDN                          = 32                 // netdb.h:164:1:
	AI_MASK                          = 119                // netdb.h:167:1:
	AI_NUMERICHOST                   = 4                  // netdb.h:161:1:
	AI_NUMERICSERV                   = 16                 // netdb.h:163:1:
	AI_PASSIVE                       = 1                  // netdb.h:159:1:
	BIG_ENDIAN                       = 4321               // endian.h:45:1:
	BYTE_ORDER                       = 1234               // endian.h:47:1:
	EAI_ADDRFAMILY                   = -9                 // netdb.h:196:1:
	EAI_AGAIN                        = -3                 // netdb.h:190:1:
	EAI_BADFLAGS                     = -1                 // netdb.h:188:1:
	EAI_BADHINTS                     = -12                // netdb.h:199:1:
	EAI_FAIL                         = -4                 // netdb.h:191:1:
	EAI_FAMILY                       = -6                 // netdb.h:193:1:
	EAI_MEMORY                       = -10                // netdb.h:197:1:
	EAI_NODATA                       = -5                 // netdb.h:192:1:
	EAI_NONAME                       = -2                 // netdb.h:189:1:
	EAI_OVERFLOW                     = -14                // netdb.h:201:1:
	EAI_PROTOCOL                     = -13                // netdb.h:200:1:
	EAI_SERVICE                      = -8                 // netdb.h:195:1:
	EAI_SOCKTYPE                     = -7                 // netdb.h:194:1:
	EAI_SYSTEM                       = -11                // netdb.h:198:1:
	ERRSET_FAIL                      = 2                  // netdb.h:225:1:
	ERRSET_INVAL                     = 3                  // netdb.h:226:1:
	ERRSET_NODATA                    = 5                  // netdb.h:228:1:
	ERRSET_NOMEMORY                  = 1                  // netdb.h:224:1:
	ERRSET_NONAME                    = 4                  // netdb.h:227:1:
	ERRSET_SUCCESS                   = 0                  // netdb.h:223:1:
	HOST_NOT_FOUND                   = 1                  // netdb.h:151:1:
	ICMP6_FILTER                     = 18                 // in6.h:304:1:
	INET6_ADDRSTRLEN                 = 46                 // in6.h:97:1:
	INET_ADDRSTRLEN                  = 16                 // in.h:382:1:
	IN_CLASSA_MAX                    = 128                // in.h:195:1:
	IN_CLASSA_NSHIFT                 = 24                 // in.h:193:1:
	IN_CLASSB_MAX                    = 65536              // in.h:202:1:
	IN_CLASSB_NSHIFT                 = 16                 // in.h:200:1:
	IN_CLASSC_NSHIFT                 = 8                  // in.h:207:1:
	IN_CLASSD_NSHIFT                 = 28                 // in.h:214:1:
	IN_LOOPBACKNET                   = 127                // in.h:253:1:
	IN_RFC3021_NSHIFT                = 31                 // in.h:219:1:
	IPCTL_ARPDOWN                    = 40                 // in.h:698:1:
	IPCTL_ARPQUEUE                   = 41                 // in.h:699:1:
	IPCTL_ARPQUEUED                  = 36                 // in.h:694:1:
	IPCTL_ARPTIMEOUT                 = 39                 // in.h:697:1:
	IPCTL_DEFTTL                     = 3                  // in.h:662:1:
	IPCTL_DIRECTEDBCAST              = 6                  // in.h:664:1:
	IPCTL_ENCDEBUG                   = 12                 // in.h:670:1:
	IPCTL_FORWARDING                 = 1                  // in.h:660:1:
	IPCTL_IFQUEUE                    = 30                 // in.h:688:1:
	IPCTL_IPPORT_FIRSTAUTO           = 7                  // in.h:665:1:
	IPCTL_IPPORT_HIFIRSTAUTO         = 9                  // in.h:667:1:
	IPCTL_IPPORT_HILASTAUTO          = 10                 // in.h:668:1:
	IPCTL_IPPORT_LASTAUTO            = 8                  // in.h:666:1:
	IPCTL_IPPORT_MAXQUEUE            = 11                 // in.h:669:1:
	IPCTL_IPSEC_ALLOCATIONS          = 18                 // in.h:676:1:
	IPCTL_IPSEC_AUTH_ALGORITHM       = 26                 // in.h:684:1:
	IPCTL_IPSEC_BYTES                = 20                 // in.h:678:1:
	IPCTL_IPSEC_EMBRYONIC_SA_TIMEOUT = 15                 // in.h:673:1:
	IPCTL_IPSEC_ENC_ALGORITHM        = 25                 // in.h:683:1:
	IPCTL_IPSEC_EXPIRE_ACQUIRE       = 14                 // in.h:672:1:
	IPCTL_IPSEC_FIRSTUSE             = 24                 // in.h:682:1:
	IPCTL_IPSEC_IPCOMP_ALGORITHM     = 29                 // in.h:687:1:
	IPCTL_IPSEC_REQUIRE_PFS          = 16                 // in.h:674:1:
	IPCTL_IPSEC_SOFT_ALLOCATIONS     = 17                 // in.h:675:1:
	IPCTL_IPSEC_SOFT_BYTES           = 19                 // in.h:677:1:
	IPCTL_IPSEC_SOFT_FIRSTUSE        = 23                 // in.h:681:1:
	IPCTL_IPSEC_SOFT_TIMEOUT         = 22                 // in.h:680:1:
	IPCTL_IPSEC_STATS                = 13                 // in.h:671:1:
	IPCTL_IPSEC_TIMEOUT              = 21                 // in.h:679:1:
	IPCTL_MAXID                      = 42                 // in.h:700:1:
	IPCTL_MFORWARDING                = 31                 // in.h:689:1:
	IPCTL_MRTMFC                     = 37                 // in.h:695:1:
	IPCTL_MRTPROTO                   = 34                 // in.h:692:1:
	IPCTL_MRTSTATS                   = 35                 // in.h:693:1:
	IPCTL_MRTVIF                     = 38                 // in.h:696:1:
	IPCTL_MTUDISC                    = 27                 // in.h:685:1:
	IPCTL_MTUDISCTIMEOUT             = 28                 // in.h:686:1:
	IPCTL_MULTIPATH                  = 32                 // in.h:690:1:
	IPCTL_SENDREDIRECTS              = 2                  // in.h:661:1:
	IPCTL_SOURCEROUTE                = 5                  // in.h:663:1:
	IPCTL_STATS                      = 33                 // in.h:691:1:
	IPPORT_HIFIRSTAUTO               = 49152              // in.h:159:1:
	IPPORT_HILASTAUTO                = 65535              // in.h:160:1:
	IPPORT_RESERVED                  = 1024               // in.h:153:1:
	IPPORT_USERRESERVED              = 49151              // in.h:154:1:
	IPPROTO_AH                       = 51                 // in.h:89:1:
	IPPROTO_CARP                     = 112                // in.h:99:1:
	IPPROTO_DIVERT                   = 258                // in.h:109:1:
	IPPROTO_DONE                     = 257                // in.h:173:1:
	IPPROTO_DSTOPTS                  = 60                 // in.h:93:1:
	IPPROTO_EGP                      = 8                  // in.h:78:1:
	IPPROTO_ENCAP                    = 98                 // in.h:96:1:
	IPPROTO_EON                      = 80                 // in.h:94:1:
	IPPROTO_ESP                      = 50                 // in.h:88:1:
	IPPROTO_ETHERIP                  = 97                 // in.h:95:1:
	IPPROTO_FRAGMENT                 = 44                 // in.h:85:1:
	IPPROTO_GGP                      = 3                  // in.h:74:1:
	IPPROTO_GRE                      = 47                 // in.h:87:1:
	IPPROTO_HOPOPTS                  = 0                  // in.h:71:1:
	IPPROTO_ICMP                     = 1                  // in.h:72:1:
	IPPROTO_ICMPV6                   = 58                 // in.h:91:1:
	IPPROTO_IDP                      = 22                 // in.h:81:1:
	IPPROTO_IGMP                     = 2                  // in.h:73:1:
	IPPROTO_IP                       = 0                  // in.h:70:1:
	IPPROTO_IPCOMP                   = 108                // in.h:98:1:
	IPPROTO_IPIP                     = 4                  // in.h:75:1:
	IPPROTO_IPV4                     = 4                  // in.h:76:1:
	IPPROTO_IPV6                     = 41                 // in.h:83:1:
	IPPROTO_MAX                      = 256                // in.h:106:1:
	IPPROTO_MAXID                    = 259                // in.h:393:1:
	IPPROTO_MOBILE                   = 55                 // in.h:90:1:
	IPPROTO_MPLS                     = 137                // in.h:102:1:
	IPPROTO_NONE                     = 59                 // in.h:92:1:
	IPPROTO_PFSYNC                   = 240                // in.h:103:1:
	IPPROTO_PIM                      = 103                // in.h:97:1:
	IPPROTO_PUP                      = 12                 // in.h:79:1:
	IPPROTO_RAW                      = 255                // in.h:104:1:
	IPPROTO_ROUTING                  = 43                 // in.h:84:1:
	IPPROTO_RSVP                     = 46                 // in.h:86:1:
	IPPROTO_SCTP                     = 132                // in.h:100:1:
	IPPROTO_TCP                      = 6                  // in.h:77:1:
	IPPROTO_TP                       = 29                 // in.h:82:1:
	IPPROTO_UDP                      = 17                 // in.h:80:1:
	IPPROTO_UDPLITE                  = 136                // in.h:101:1:
	IPSEC6_OUTSA                     = 56                 // in6.h:337:1:
	IPSEC_AUTH_LEVEL_DEFAULT         = 1                  // in.h:336:1:
	IPSEC_ESP_NETWORK_LEVEL_DEFAULT  = 1                  // in.h:338:1:
	IPSEC_ESP_TRANS_LEVEL_DEFAULT    = 1                  // in.h:337:1:
	IPSEC_IPCOMP_LEVEL_DEFAULT       = 1                  // in.h:339:1:
	IPSEC_LEVEL_AVAIL                = 0x01               // in.h:330:1:
	IPSEC_LEVEL_BYPASS               = 0x00               // in.h:328:1:
	IPSEC_LEVEL_DEFAULT              = 1                  // in.h:334:1:
	IPSEC_LEVEL_NONE                 = 0x00               // in.h:329:1:
	IPSEC_LEVEL_REQUIRE              = 0x03               // in.h:332:1:
	IPSEC_LEVEL_UNIQUE               = 0x04               // in.h:333:1:
	IPSEC_LEVEL_USE                  = 0x02               // in.h:331:1:
	IPV6CTL_ACCEPT_RTADV             = 12                 // in6.h:575:1:
	IPV6CTL_AUTO_FLOWLABEL           = 17                 // in6.h:579:1:
	IPV6CTL_DAD_COUNT                = 16                 // in6.h:578:1:
	IPV6CTL_DAD_PENDING              = 49                 // in6.h:589:1:
	IPV6CTL_DEFHLIM                  = 3                  // in6.h:567:1:
	IPV6CTL_DEFMCASTHLIM             = 18                 // in6.h:580:1:
	IPV6CTL_FORWARDING               = 1                  // in6.h:565:1:
	IPV6CTL_FORWSRCRT                = 5                  // in6.h:568:1:
	IPV6CTL_HDRNESTLIMIT             = 15                 // in6.h:577:1:
	IPV6CTL_IFQUEUE                  = 51                 // in6.h:591:1:
	IPV6CTL_LOG_INTERVAL             = 14                 // in6.h:576:1:
	IPV6CTL_MAXDYNROUTES             = 48                 // in6.h:588:1:
	IPV6CTL_MAXFRAGPACKETS           = 9                  // in6.h:572:1:
	IPV6CTL_MAXFRAGS                 = 41                 // in6.h:583:1:
	IPV6CTL_MAXID                    = 55                 // in6.h:595:1:
	IPV6CTL_MCAST_PMTU               = 44                 // in6.h:586:1:
	IPV6CTL_MFORWARDING              = 42                 // in6.h:584:1:
	IPV6CTL_MRTMFC                   = 53                 // in6.h:593:1:
	IPV6CTL_MRTMIF                   = 52                 // in6.h:592:1:
	IPV6CTL_MRTPROTO                 = 8                  // in6.h:571:1:
	IPV6CTL_MRTSTATS                 = 7                  // in6.h:570:1:
	IPV6CTL_MTUDISCTIMEOUT           = 50                 // in6.h:590:1:
	IPV6CTL_MULTIPATH                = 43                 // in6.h:585:1:
	IPV6CTL_NEIGHBORGCTHRESH         = 45                 // in6.h:587:1:
	IPV6CTL_SENDREDIRECTS            = 2                  // in6.h:566:1:
	IPV6CTL_SOIIKEY                  = 54                 // in6.h:594:1:
	IPV6CTL_SOURCECHECK              = 10                 // in6.h:573:1:
	IPV6CTL_SOURCECHECK_LOGINT       = 11                 // in6.h:574:1:
	IPV6CTL_STATS                    = 6                  // in6.h:569:1:
	IPV6CTL_USE_DEPRECATED           = 21                 // in6.h:581:1:
	IPV6PROTO_MAXID                  = 259                // in6.h:470:1:
	IPV6_AUTH_LEVEL                  = 53                 // in6.h:333:1:
	IPV6_AUTOFLOWLABEL               = 59                 // in6.h:341:1:
	IPV6_CHECKSUM                    = 26                 // in6.h:307:1:
	IPV6_DEFAULT_MULTICAST_HOPS      = 1                  // in6.h:360:1:
	IPV6_DEFAULT_MULTICAST_LOOP      = 1                  // in6.h:361:1:
	IPV6_DONTFRAG                    = 62                 // in6.h:345:1:
	IPV6_DSTOPTS                     = 50                 // in6.h:330:1:
	IPV6_ESP_NETWORK_LEVEL           = 55                 // in6.h:335:1:
	IPV6_ESP_TRANS_LEVEL             = 54                 // in6.h:334:1:
	IPV6_HOPLIMIT                    = 47                 // in6.h:327:1:
	IPV6_HOPOPTS                     = 49                 // in6.h:329:1:
	IPV6_IPCOMP_LEVEL                = 60                 // in6.h:342:1:
	IPV6_JOIN_GROUP                  = 12                 // in6.h:300:1:
	IPV6_LEAVE_GROUP                 = 13                 // in6.h:301:1:
	IPV6_MINHOPCOUNT                 = 65                 // in6.h:349:1:
	IPV6_MULTICAST_HOPS              = 10                 // in6.h:298:1:
	IPV6_MULTICAST_IF                = 9                  // in6.h:297:1:
	IPV6_MULTICAST_LOOP              = 11                 // in6.h:299:1:
	IPV6_NEXTHOP                     = 48                 // in6.h:328:1:
	IPV6_PATHMTU                     = 44                 // in6.h:322:1:
	IPV6_PIPEX                       = 63                 // in6.h:346:1:
	IPV6_PKTINFO                     = 46                 // in6.h:326:1:
	IPV6_PORTRANGE                   = 14                 // in6.h:302:1:
	IPV6_PORTRANGE_DEFAULT           = 0                  // in6.h:393:1:
	IPV6_PORTRANGE_HIGH              = 1                  // in6.h:394:1:
	IPV6_PORTRANGE_LOW               = 2                  // in6.h:395:1:
	IPV6_RECVDSTOPTS                 = 40                 // in6.h:317:1:
	IPV6_RECVDSTPORT                 = 64                 // in6.h:348:1:
	IPV6_RECVHOPLIMIT                = 37                 // in6.h:314:1:
	IPV6_RECVHOPOPTS                 = 39                 // in6.h:316:1:
	IPV6_RECVPATHMTU                 = 43                 // in6.h:320:1:
	IPV6_RECVPKTINFO                 = 36                 // in6.h:313:1:
	IPV6_RECVRTHDR                   = 38                 // in6.h:315:1:
	IPV6_RECVTCLASS                  = 57                 // in6.h:339:1:
	IPV6_RTABLE                      = 0x1021             // in6.h:351:1:
	IPV6_RTHDR                       = 51                 // in6.h:331:1:
	IPV6_RTHDRDSTOPTS                = 35                 // in6.h:311:1:
	IPV6_RTHDR_LOOSE                 = 0                  // in6.h:354:1:
	IPV6_RTHDR_TYPE_0                = 0                  // in6.h:355:1:
	IPV6_TCLASS                      = 61                 // in6.h:344:1:
	IPV6_UNICAST_HOPS                = 4                  // in6.h:296:1:
	IPV6_USE_MIN_MTU                 = 42                 // in6.h:319:1:
	IPV6_V6ONLY                      = 27                 // in6.h:308:1:
	IP_ADD_MEMBERSHIP                = 12                 // in.h:297:1:
	IP_AUTH_LEVEL                    = 20                 // in.h:300:1:
	IP_DEFAULT_MULTICAST_LOOP        = 1                  // in.h:347:1:
	IP_DEFAULT_MULTICAST_TTL         = 1                  // in.h:346:1:
	IP_DROP_MEMBERSHIP               = 13                 // in.h:298:1:
	IP_ESP_NETWORK_LEVEL             = 22                 // in.h:302:1:
	IP_ESP_TRANS_LEVEL               = 21                 // in.h:301:1:
	IP_HDRINCL                       = 2                  // in.h:287:1:
	IP_IPCOMP_LEVEL                  = 29                 // in.h:309:1:
	IP_IPDEFTTL                      = 37                 // in.h:317:1:
	IP_IPSECFLOWINFO                 = 36                 // in.h:316:1:
	IP_IPSEC_LOCAL_AUTH              = 27                 // in.h:307:1:
	IP_IPSEC_LOCAL_CRED              = 25                 // in.h:305:1:
	IP_IPSEC_LOCAL_ID                = 23                 // in.h:303:1:
	IP_IPSEC_REMOTE_AUTH             = 28                 // in.h:308:1:
	IP_IPSEC_REMOTE_CRED             = 26                 // in.h:306:1:
	IP_IPSEC_REMOTE_ID               = 24                 // in.h:304:1:
	IP_MAX_MEMBERSHIPS               = 4095               // in.h:354:1:
	IP_MINTTL                        = 32                 // in.h:312:1:
	IP_MIN_MEMBERSHIPS               = 15                 // in.h:353:1:
	IP_MULTICAST_IF                  = 9                  // in.h:294:1:
	IP_MULTICAST_LOOP                = 11                 // in.h:296:1:
	IP_MULTICAST_TTL                 = 10                 // in.h:295:1:
	IP_OPTIONS                       = 1                  // in.h:286:1:
	IP_PIPEX                         = 34                 // in.h:314:1:
	IP_PORTRANGE                     = 19                 // in.h:299:1:
	IP_PORTRANGE_DEFAULT             = 0                  // in.h:374:1:
	IP_PORTRANGE_HIGH                = 1                  // in.h:375:1:
	IP_PORTRANGE_LOW                 = 2                  // in.h:376:1:
	IP_RECVDSTADDR                   = 7                  // in.h:292:1:
	IP_RECVDSTPORT                   = 33                 // in.h:313:1:
	IP_RECVIF                        = 30                 // in.h:310:1:
	IP_RECVOPTS                      = 5                  // in.h:290:1:
	IP_RECVRETOPTS                   = 6                  // in.h:291:1:
	IP_RECVRTABLE                    = 35                 // in.h:315:1:
	IP_RECVTTL                       = 31                 // in.h:311:1:
	IP_RETOPTS                       = 8                  // in.h:293:1:
	IP_RTABLE                        = 0x1021             // in.h:321:1:
	IP_SENDSRCADDR                   = 7                  // in.h:318:1:
	IP_TOS                           = 3                  // in.h:288:1:
	IP_TTL                           = 4                  // in.h:289:1:
	LITTLE_ENDIAN                    = 1234               // endian.h:44:1:
	NETDB_INTERNAL                   = -1                 // netdb.h:149:1:
	NETDB_SUCCESS                    = 0                  // netdb.h:150:1:
	NI_DGRAM                         = 16                 // netdb.h:175:1:
	NI_MAXHOST                       = 256                // netdb.h:179:1:
	NI_MAXSERV                       = 32                 // netdb.h:180:1:
	NI_NAMEREQD                      = 8                  // netdb.h:174:1:
	NI_NOFQDN                        = 4                  // netdb.h:173:1:
	NI_NUMERICHOST                   = 1                  // netdb.h:171:1:
	NI_NUMERICSERV                   = 2                  // netdb.h:172:1:
	NO_ADDRESS                       = 4                  // netdb.h:155:1:
	NO_DATA                          = 4                  // netdb.h:154:1:
	NO_RECOVERY                      = 3                  // netdb.h:153:1:
	PDP_ENDIAN                       = 3412               // endian.h:46:1:
	RRSET_VALIDATED                  = 1                  // netdb.h:218:1:
	SCOPE_DELIMITER                  = 37                 // netdb.h:185:1:
	SIN6_LEN                         = 0                  // in6.h:104:1:
	TRY_AGAIN                        = 2                  // netdb.h:152:1:
	X_BIG_ENDIAN                     = 4321               // _endian.h:43:1:
	X_BYTE_ORDER                     = 1234               // endian.h:58:1:
	X_CLOCKID_T_DEFINED_             = 0                  // types.h:162:1:
	X_CLOCK_T_DEFINED_               = 0                  // types.h:157:1:
	X_FILE_OFFSET_BITS               = 64                 // <builtin>:25:1:
	X_ILP32                          = 1                  // <predefined>:1:1:
	X_INT16_T_DEFINED_               = 0                  // types.h:84:1:
	X_INT32_T_DEFINED_               = 0                  // types.h:94:1:
	X_INT64_T_DEFINED_               = 0                  // types.h:104:1:
	X_INT8_T_DEFINED_                = 0                  // types.h:74:1:
	X_IN_ADDR_DECLARED               = 0                  // in.h:163:1:
	X_IN_TYPES_DEFINED_              = 0                  // in.h:62:1:
	X_LITTLE_ENDIAN                  = 1234               // _endian.h:42:1:
	X_MACHINE_CDEFS_H_               = 0                  // cdefs.h:9:1:
	X_MACHINE_ENDIAN_H_              = 0                  // endian.h:28:1:
	X_MACHINE__TYPES_H_              = 0                  // _types.h:36:1:
	X_MAX_PAGE_SHIFT                 = 12                 // _types.h:52:1:
	X_NETDB_H_                       = 0                  // netdb.h:88:1:
	X_NETINET6_IN6_H_                = 0                  // in6.h:69:1:
	X_NETINET_IN_H_                  = 0                  // in.h:39:1:
	X_OFF_T_DEFINED_                 = 0                  // types.h:192:1:
	X_PATH_HEQUIV                    = "/etc/hosts.equiv" // netdb.h:97:1:
	X_PATH_HOSTS                     = "/etc/hosts"       // netdb.h:98:1:
	X_PATH_NETWORKS                  = "/etc/networks"    // netdb.h:99:1:
	X_PATH_PROTOCOLS                 = "/etc/protocols"   // netdb.h:100:1:
	X_PATH_SERVICES                  = "/etc/services"    // netdb.h:101:1:
	X_PDP_ENDIAN                     = 3412               // _endian.h:44:1:
	X_PID_T_DEFINED_                 = 0                  // types.h:167:1:
	X_QUAD_HIGHWORD                  = 1                  // _endian.h:95:1:
	X_QUAD_LOWWORD                   = 0                  // _endian.h:96:1:
	X_SA_FAMILY_T_DEFINED_           = 0                  // in.h:57:1:
	X_SIZE_T_DEFINED_                = 0                  // types.h:172:1:
	X_SOCKLEN_T_DEFINED_             = 0                  // in6.h:400:1:
	X_SSIZE_T_DEFINED_               = 0                  // types.h:177:1:
	X_STACKALIGNBYTES                = 15                 // _types.h:49:1:
	X_SYS_CDEFS_H_                   = 0                  // cdefs.h:39:1:
	X_SYS_ENDIAN_H_                  = 0                  // endian.h:38:1:
	X_SYS_TYPES_H_                   = 0                  // types.h:41:1:
	X_SYS__ENDIAN_H_                 = 0                  // _endian.h:34:1:
	X_SYS__TYPES_H_                  = 0                  // _types.h:35:1:
	X_TIMER_T_DEFINED_               = 0                  // types.h:187:1:
	X_TIME_T_DEFINED_                = 0                  // types.h:182:1:
	X_UINT16_T_DEFINED_              = 0                  // types.h:89:1:
	X_UINT32_T_DEFINED_              = 0                  // types.h:99:1:
	X_UINT64_T_DEFINED_              = 0                  // types.h:109:1:
	X_UINT8_T_DEFINED_               = 0                  // types.h:79:1:
	I386                             = 1                  // <predefined>:339:1:
	Unix                             = 1                  // <predefined>:340:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$OpenBSD: netdb.h,v 1.33 2015/01/18 20:29:31 deraadt Exp $

// ++Copyright++ 1980, 1983, 1988, 1993
// -
// Copyright (c) 1980, 1983, 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
// -
// Portions Copyright (c) 1993 by Digital Equipment Corporation.
//
// Permission to use, copy, modify, and distribute this software for any
// purpose with or without fee is hereby granted, provided that the above
// copyright notice and this permission notice appear in all copies, and that
// the name of Digital Equipment Corporation not be used in advertising or
// publicity pertaining to distribution of the document or software without
// specific, written prior permission.
//
// THE SOFTWARE IS PROVIDED "AS IS" AND DIGITAL EQUIPMENT CORP. DISCLAIMS ALL
// WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS.   IN NO EVENT SHALL DIGITAL EQUIPMENT
// CORPORATION BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL
// DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR
// PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
// ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS
// SOFTWARE.
// -
// --Copyright--

// Copyright (c) 1995, 1996, 1997, 1998, 1999 Craig Metz. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the author nor the names of any contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

//      @(#)netdb.h	8.1 (Berkeley) 6/2/93
//	$From: netdb.h,v 8.7 1996/05/09 05:59:09 vixie Exp $

//	$OpenBSD: in.h,v 1.141 2021/06/02 00:09:57 dlg Exp $
//	$NetBSD: in.h,v 1.20 1996/02/13 23:41:47 christos Exp $

// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// Constants and structures defined by the internet system,
// Per RFC 790, September 1981, and numerous additions.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.10 2013/03/28 17:30:45 martynas Exp $

// Written by J.T. Conklin <<EMAIL>> 01/17/95.
// Public domain.

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  Do not use __CONCAT on double-quoted strings,
// such as those from the __STRING macro: to concatenate strings just put
// them next to each other.

// GCC1 and some versions of GCC2 declare dead (non-returning) and
// pure (no side effects) functions using "volatile" and "const";
// unfortunately, these then cause warnings under "-ansi -pedantic".
// GCC >= 2.5 uses the __attribute__((attrs)) style.  All of these
// work for GNU C++ (modulo a slight glitch in the C++ grammar in
// the distribution version of 2.5.5).

// __returns_twice makes the compiler not assume the function
// only returns once.  This affects registerisation of variables:
// even local variables need to be in memory across such a call.
// Example: setjmp()

// __only_inline makes the compiler only use this function definition
// for inlining; references that can't be inlined will be left as
// external references instead of generating a local copy.  The
// matching library should include a simple extern definition for
// the function to handle those references.  c.f. ctype.h

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// Delete pseudo-keywords wherever they are not available or needed.

// The __packed macro indicates that a variable or structure members
// should have the smallest possible alignment, despite any host CPU
// alignment requirements.
//
// The __aligned(x) macro specifies the minimum alignment of a
// variable or structure.
//
// These macros together are useful for describing the layout and
// alignment of messages exchanged with hardware or other systems.

// "The nice thing about standards is that there are so many to choose from."
// There are a number of "feature test macros" specified by (different)
// standards that determine which interfaces and types the header files
// should expose.
//
// Because of inconsistencies in these macros, we define our own
// set in the private name space that end in _VISIBLE.  These are
// always defined and so headers can test their values easily.
// Things can get tricky when multiple feature macros are defined.
// We try to take the union of all the features requested.
//
// The following macros are guaranteed to have a value after cdefs.h
// has been included:
//	__POSIX_VISIBLE
//	__XPG_VISIBLE
//	__ISO_C_VISIBLE
//	__BSD_VISIBLE

// X/Open Portability Guides and Single Unix Specifications.
// _XOPEN_SOURCE				XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4		XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1	XPG4v2
// _XOPEN_SOURCE == 500				XPG5
// _XOPEN_SOURCE == 520				XPG5v2
// _XOPEN_SOURCE == 600				POSIX 1003.1-2001 with XSI
// _XOPEN_SOURCE == 700				POSIX 1003.1-2008 with XSI
//
// The XPG spec implies a specific value for _POSIX_C_SOURCE.

// POSIX macros, these checks must follow the XOPEN ones above.
//
// _POSIX_SOURCE == 1		1003.1-1988 (superseded by _POSIX_C_SOURCE)
// _POSIX_C_SOURCE == 1		1003.1-1990
// _POSIX_C_SOURCE == 2		1003.2-1992
// _POSIX_C_SOURCE == 199309L	1003.1b-1993
// _POSIX_C_SOURCE == 199506L   1003.1c-1995, 1003.1i-1995,
//				and the omnibus ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L   1003.1-2001
// _POSIX_C_SOURCE == 200809L   1003.1-2008
//
// The POSIX spec implies a specific value for __ISO_C_VISIBLE, though
// this may be overridden by the _ISOC99_SOURCE macro later.

// _ANSI_SOURCE means to expose ANSI C89 interfaces only.
// If the user defines it in addition to one of the POSIX or XOPEN
// macros, assume the POSIX/XOPEN macro(s) should take precedence.

// _ISOC99_SOURCE, _ISOC11_SOURCE, __STDC_VERSION__, and __cplusplus
// override any of the other macros since they are non-exclusive.

// Finally deal with BSD-specific interfaces that are not covered
// by any standards.  We expose these when none of the POSIX or XPG
// macros is defined or if the user explicitly asks for them.

// Default values.

//	$OpenBSD: types.h,v 1.49 2022/08/06 13:31:13 semarie Exp $
//	$NetBSD: types.h,v 1.29 1996/11/15 22:48:25 jtc Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: endian.h,v 1.25 2014/12/21 04:49:00 guenther Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Public definitions for little- and big-endian systems.
// This file should be included as <endian.h> in userspace and as
// <sys/endian.h> in the kernel.
//
// System headers that need endian information but that can't or don't
// want to export the public names here should include <sys/_endian.h>
// and use the internal names: _BYTE_ORDER, _*_ENDIAN, etc.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: _endian.h,v 1.8 2018/01/11 23:13:37 dlg Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Internal endianness macros.  This pulls in <machine/endian.h> to
// get the correct setting direction for the platform and sets internal
// ('__' prefix) macros appropriately.

//	$OpenBSD: _types.h,v 1.10 2022/08/06 13:31:13 semarie Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

//	$OpenBSD: _types.h,v 1.23 2018/03/05 01:15:25 deraadt Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94
//	@(#)ansi.h	8.2 (Berkeley) 1/4/94

// _ALIGN(p) rounds p (pointer or byte index) up to a correctly-aligned
// value for all data types (int, long, ...).   The result is an
// unsigned long and must be cast to any desired pointer type.
//
// _ALIGNED_POINTER is a boolean macro that checks whether an address
// is valid to fetch data elements of type t from on this architecture.
// This does not reflect the optimal alignment, just the possibility
// (within reasonable limits).

// ******** Exact-width integer types
type X__int8_t = int8     /* _types.h:61:22 */
type X__uint8_t = uint8   /* _types.h:62:24 */
type X__int16_t = int16   /* _types.h:63:17 */
type X__uint16_t = uint16 /* _types.h:64:25 */
type X__int32_t = int32   /* _types.h:65:15 */
type X__uint32_t = uint32 /* _types.h:66:23 */
type X__int64_t = int64   /* _types.h:67:20 */
type X__uint64_t = uint64 /* _types.h:68:28 */

// ******** Minimum-width integer types
type X__int_least8_t = X__int8_t     /* _types.h:71:19 */
type X__uint_least8_t = X__uint8_t   /* _types.h:72:20 */
type X__int_least16_t = X__int16_t   /* _types.h:73:20 */
type X__uint_least16_t = X__uint16_t /* _types.h:74:21 */
type X__int_least32_t = X__int32_t   /* _types.h:75:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:76:21 */
type X__int_least64_t = X__int64_t   /* _types.h:77:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:78:21 */

// 7.18.1.3 Fastest minimum-width integer types
type X__int_fast8_t = X__int32_t    /* _types.h:81:20 */
type X__uint_fast8_t = X__uint32_t  /* _types.h:82:21 */
type X__int_fast16_t = X__int32_t   /* _types.h:83:20 */
type X__uint_fast16_t = X__uint32_t /* _types.h:84:21 */
type X__int_fast32_t = X__int32_t   /* _types.h:85:20 */
type X__uint_fast32_t = X__uint32_t /* _types.h:86:21 */
type X__int_fast64_t = X__int64_t   /* _types.h:87:20 */
type X__uint_fast64_t = X__uint64_t /* _types.h:88:21 */

// 7.18.1.4 Integer types capable of holding object pointers
type X__intptr_t = int32   /* _types.h:103:16 */
type X__uintptr_t = uint32 /* _types.h:104:24 */

// 7.18.1.5 Greatest-width integer types
type X__intmax_t = X__int64_t   /* _types.h:107:20 */
type X__uintmax_t = X__uint64_t /* _types.h:108:21 */

// Register size
type X__register_t = int32 /* _types.h:111:16 */

// VM system types
type X__vaddr_t = uint32 /* _types.h:114:24 */
type X__paddr_t = uint32 /* _types.h:115:24 */
type X__vsize_t = uint32 /* _types.h:116:24 */
type X__psize_t = uint32 /* _types.h:117:24 */

// Standard system types
type X__double_t = float64           /* _types.h:120:22 */
type X__float_t = float64            /* _types.h:121:22 */
type X__ptrdiff_t = int32            /* _types.h:122:16 */
type X__size_t = uint32              /* _types.h:123:24 */
type X__ssize_t = int32              /* _types.h:124:16 */
type X__va_list = X__builtin_va_list /* _types.h:126:27 */

// Wide character support types
type X__wchar_t = int32     /* _types.h:133:15 */
type X__wint_t = int32      /* _types.h:135:15 */
type X__rune_t = int32      /* _types.h:136:15 */
type X__wctrans_t = uintptr /* _types.h:137:14 */
type X__wctype_t = uintptr  /* _types.h:138:14 */

type X__blkcnt_t = X__int64_t    /* _types.h:39:19 */ // blocks allocated for file
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // optimal blocksize for I/O
type X__clock_t = X__int64_t     /* _types.h:41:19 */ // ticks in CLOCKS_PER_SEC
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // CLOCK_* identifiers
type X__cpuid_t = uint32         /* _types.h:43:23 */ // CPU id
type X__dev_t = X__int32_t       /* _types.h:44:19 */ // device number
type X__fixpt_t = X__uint32_t    /* _types.h:45:20 */ // fixed point number
type X__fsblkcnt_t = X__uint64_t /* _types.h:46:20 */ // file system block count
type X__fsfilcnt_t = X__uint64_t /* _types.h:47:20 */ // file system file count
type X__gid_t = X__uint32_t      /* _types.h:48:20 */ // group id
type X__id_t = X__uint32_t       /* _types.h:49:20 */ // may contain pid, uid or gid
type X__in_addr_t = X__uint32_t  /* _types.h:50:20 */ // base type for internet address
type X__in_port_t = X__uint16_t  /* _types.h:51:20 */ // IP port type
type X__ino_t = X__uint64_t      /* _types.h:52:20 */ // inode number
type X__key_t = int32            /* _types.h:53:15 */ // IPC key (for Sys V IPC)
type X__mode_t = X__uint32_t     /* _types.h:54:20 */ // permissions
type X__nlink_t = X__uint32_t    /* _types.h:55:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:56:19 */ // file offset or size
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process id
type X__rlim_t = X__uint64_t     /* _types.h:58:20 */ // resource limit
type X__sa_family_t = X__uint8_t /* _types.h:59:19 */ // sockaddr address family type
type X__segsz_t = X__int32_t     /* _types.h:60:19 */ // segment size
type X__socklen_t = X__uint32_t  /* _types.h:61:20 */ // length type for network syscalls
type X__suseconds_t = int32      /* _types.h:62:15 */ // microseconds (signed)
type X__time_t = X__int64_t      /* _types.h:63:19 */ // epoch time
type X__timer_t = X__int32_t     /* _types.h:64:19 */ // POSIX timer identifiers
type X__uid_t = X__uint32_t      /* _types.h:65:20 */ // user id
type X__useconds_t = X__uint32_t /* _types.h:66:20 */ // microseconds

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions. The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint32
	F__mbstate8  [128]int8
} /* _types.h:75:3 */

// Tell sys/endian.h we have MD variants of the swap macros.

// Note that these macros evaluate their arguments several times.

// Public names

// These are specified to be function-like macros to match the spec

// POSIX names

// original BSD names

// these were exposed here before

// ancient stuff

type U_char = uint8   /* types.h:51:23 */
type U_short = uint16 /* types.h:52:24 */
type U_int = uint32   /* types.h:53:22 */
type U_long = uint32  /* types.h:54:23 */

type Unchar = uint8  /* types.h:56:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:57:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:58:22 */ // Sys V compatibility
type Ulong = uint32  /* types.h:59:23 */ // Sys V compatibility

type Cpuid_t = X__cpuid_t       /* types.h:61:19 */ // CPU id
type Register_t = X__register_t /* types.h:62:22 */ // register-sized type

// XXX The exact-width bit types should only be exposed if __BSD_VISIBLE
//     but the rest of the includes are not ready for that yet.

type Int8_t = X__int8_t /* types.h:75:19 */

type Uint8_t = X__uint8_t /* types.h:80:20 */

type Int16_t = X__int16_t /* types.h:85:20 */

type Uint16_t = X__uint16_t /* types.h:90:21 */

type Int32_t = X__int32_t /* types.h:95:20 */

type Uint32_t = X__uint32_t /* types.h:100:21 */

type Int64_t = X__int64_t /* types.h:105:20 */

type Uint64_t = X__uint64_t /* types.h:110:21 */

// BSD-style unsigned bits types
type U_int8_t = X__uint8_t   /* types.h:114:19 */
type U_int16_t = X__uint16_t /* types.h:115:20 */
type U_int32_t = X__uint32_t /* types.h:116:20 */
type U_int64_t = X__uint64_t /* types.h:117:20 */

// quads, deprecated in favor of 64 bit int types
type Quad_t = X__int64_t    /* types.h:120:19 */
type U_quad_t = X__uint64_t /* types.h:121:20 */

// VM system types
type Vaddr_t = X__vaddr_t /* types.h:125:19 */
type Paddr_t = X__paddr_t /* types.h:126:19 */
type Vsize_t = X__vsize_t /* types.h:127:19 */
type Psize_t = X__psize_t /* types.h:128:19 */

// Standard system types
type Blkcnt_t = X__blkcnt_t       /* types.h:132:20 */ // blocks allocated for file
type Blksize_t = X__blksize_t     /* types.h:133:21 */ // optimal blocksize for I/O
type Caddr_t = uintptr            /* types.h:134:14 */ // core address
type Daddr32_t = X__int32_t       /* types.h:135:19 */ // 32-bit disk address
type Daddr_t = X__int64_t         /* types.h:136:19 */ // 64-bit disk address
type Dev_t = X__dev_t             /* types.h:137:18 */ // device number
type Fixpt_t = X__fixpt_t         /* types.h:138:19 */ // fixed point number
type Gid_t = X__gid_t             /* types.h:139:18 */ // group id
type Id_t = X__id_t               /* types.h:140:17 */ // may contain pid, uid or gid
type Ino_t = X__ino_t             /* types.h:141:18 */ // inode number
type Key_t = X__key_t             /* types.h:142:18 */ // IPC key (for Sys V IPC)
type Mode_t = X__mode_t           /* types.h:143:18 */ // permissions
type Nlink_t = X__nlink_t         /* types.h:144:19 */ // link count
type Rlim_t = X__rlim_t           /* types.h:145:18 */ // resource limit
type Segsz_t = X__segsz_t         /* types.h:146:19 */ // segment size
type Uid_t = X__uid_t             /* types.h:147:18 */ // user id
type Useconds_t = X__useconds_t   /* types.h:148:22 */ // microseconds
type Suseconds_t = X__suseconds_t /* types.h:149:23 */ // microseconds (signed)
type Fsblkcnt_t = X__fsblkcnt_t   /* types.h:150:22 */ // file system block count
type Fsfilcnt_t = X__fsfilcnt_t   /* types.h:151:22 */ // file system file count

// The following types may be defined in multiple header files.
type Clock_t = X__clock_t /* types.h:158:19 */

type Clockid_t = X__clockid_t /* types.h:163:21 */

type Pid_t = X__pid_t /* types.h:168:18 */

type Ssize_t = X__ssize_t /* types.h:178:19 */

type Time_t = X__time_t /* types.h:183:18 */

type Timer_t = X__timer_t /* types.h:188:19 */

type Off_t = X__off_t /* types.h:193:18 */

// Major, minor numbers, dev_t's.

// <sys/_endian.h> is pulled in by <sys/types.h>

type Sa_family_t = X__sa_family_t /* in.h:58:23 */ // sockaddr address family type

type In_addr_t = X__in_addr_t /* in.h:63:21 */ // base type for internet address
type In_port_t = X__in_port_t /* in.h:64:21 */ // IP port type

// Protocols

// Only used internally, so it can be outside the range of valid IP protocols

// From FreeBSD:
//
// Local port number conventions:
//
// When a user does a bind(2) or connect(2) with a port number of zero,
// a non-conflicting local port address is chosen.
// The default range is IPPORT_RESERVED through
// IPPORT_USERRESERVED, although that is settable by sysctl.
//
// A user may set the IPPROTO_IP option IP_PORTRANGE to change this
// default assignment range.
//
// The value IP_PORTRANGE_DEFAULT causes the default behavior.
//
// The value IP_PORTRANGE_HIGH changes the range of candidate port numbers
// into the "high" range.  These are reserved for client outbound connections
// which do not want to be filtered by any firewalls.
//
// The value IP_PORTRANGE_LOW changes the range to the "low" are
// that is (by convention) restricted to privileged processes.  This
// convention is based on "vouchsafe" principles only.  It is only secure
// if you trust the remote host to restrict these ports.
//
// The default range of ports and the high range can be changed by
// sysctl(3).  (net.inet.ip.port{hi}{first,last})
//
// Changing those values has bad security implications if you are
// using a a stateless firewall that is allowing packets outside of that
// range in order to allow transparent outgoing connections.
//
// Such a firewall configuration will generally depend on the use of these
// default values.  If you change them, you may find your Security
// Administrator looking for you with a heavy object.

// Ports < IPPORT_RESERVED are reserved for
// privileged processes (e.g. root).
// Ports > IPPORT_USERRESERVED are reserved
// for servers, not necessarily privileged.

// Default local port range to use by setting IP_PORTRANGE_HIGH

// IP Version 4 Internet address (a structure for historical reasons)
type In_addr = struct{ Fs_addr In_addr_t } /* in.h:167:1 */

// last return value of *_input(), meaning "all job for this pkt is done".

// Definitions of bits in internet address integers.
// On subnets, the decomposition of addresses to host and net parts
// is done according to subnet mask, not the masks here.
//
// By byte-swapping the constants, we avoid ever having to byte-swap IP
// addresses inside the kernel.  Unfortunately, user-level programs rely
// on these macros not doing byte-swapping.

// These ones aren't really net and host fields, but routing needn't know.

// IP Version 4 socket address.
type Sockaddr_in = struct {
	Fsin_len    U_int8_t
	Fsin_family Sa_family_t
	Fsin_port   In_port_t
	Fsin_addr   struct{ Fs_addr In_addr_t }
	Fsin_zero   [8]Int8_t
} /* in.h:258:1 */

// Structure used to describe IP options.
// Used to store options internally, to pass them to a process,
// or to restore options retrieved earlier.
// The ip_dst is used for the first-hop gateway when using a source route
// (this gets put into the header proper).
type Ip_opts = struct {
	Fip_dst  struct{ Fs_addr In_addr_t }
	Fip_opts [40]Int8_t
} /* in.h:273:1 */

// Options for use with [gs]etsockopt at the IP level.
// First word of comment is data type; bool is stored in int.
// source address to use

// Security levels - IPsec, not IPSO

// Defaults and limits for options
// The imo_membership vector for each socket starts at IP_MIN_MEMBERSHIPS
// and is dynamically allocated at run-time, bounded by IP_MAX_MEMBERSHIPS,
// and is reallocated when needed, sized according to a power-of-two increment.

// Argument structure for IP_ADD_MEMBERSHIP and IP_DROP_MEMBERSHIP.
type Ip_mreq = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_interface struct{ Fs_addr In_addr_t }
} /* in.h:359:1 */

type Ip_mreqn = struct {
	Fimr_multiaddr struct{ Fs_addr In_addr_t }
	Fimr_address   struct{ Fs_addr In_addr_t }
	Fimr_ifindex   int32
} /* in.h:364:1 */

// Argument for IP_PORTRANGE:
// - which range to search when port is unspecified at bind() or connect()

// Buffer lengths for strings containing printable IP addresses

// Definitions for inet sysctl operations.
//
// Third level is protocol number.
// Fourth level is desired variable within that protocol.

// Names for IP sysctl objects

// INET6 stuff
//	$OpenBSD: in6.h,v 1.109 2021/06/02 00:20:50 dlg Exp $
//	$KAME: in6.h,v 1.83 2001/03/29 02:55:07 jinmei Exp $

// Copyright (C) 1995, 1996, 1997, and 1998 WIDE Project.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the project nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE PROJECT AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE PROJECT OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// Copyright (c) 1982, 1986, 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)in.h	8.3 (Berkeley) 1/3/94

// Identification of the network protocol stack
// for *BSD-current/release: http://www.kame.net/dev/cvsweb2.cgi/kame/COVERAGE
// has the table of implementation/integration differences.

// IPv6 address
type In6_addr = struct {
	F__u6_addr struct {
		F__ccgo_pad1 [0]uint32
		F__u6_addr8  [16]U_int8_t
	}
} /* in6.h:81:1 */

// Socket address for IPv6
type Sockaddr_in6 = struct {
	Fsin6_len      U_int8_t
	Fsin6_family   Sa_family_t
	Fsin6_port     In_port_t
	Fsin6_flowinfo U_int32_t
	Fsin6_addr     struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]U_int8_t
		}
	}
	Fsin6_scope_id U_int32_t
} /* in6.h:106:1 */

// IPv6 route structure
type Route_in6 = struct {
	Fro_rt      uintptr
	Fro_tableid U_long
	Fro_dst     struct {
		Fsin6_len      U_int8_t
		Fsin6_family   Sa_family_t
		Fsin6_port     In_port_t
		Fsin6_flowinfo U_int32_t
		Fsin6_addr     struct {
			F__u6_addr struct {
				F__ccgo_pad1 [0]uint32
				F__u6_addr8  [16]U_int8_t
			}
		}
		Fsin6_scope_id U_int32_t
	}
} /* in6.h:150:1 */

// Definition of some useful macros to handle IP6 addresses

// Macros started with IPV6_ADDR is KAME local

// Unspecified

// Loopback

// IPv4 compatible

// Mapped

// Unicast Scope
// Note that we must check topmost 10 bits only, not 16 bits (see RFC2373).

// Multicast

// Options for use with [gs]etsockopt at the IPV6 level.
// First word of comment is data type; bool is stored in int.

// new socket options introduced in RFC3542

// More new socket options introduced in RFC3542

// to define items, should talk with KAME guys first, for *BSD compatibility

// Defaults and limits for options

// Argument structure for IPV6_JOIN_GROUP and IPV6_LEAVE_GROUP.
type Ipv6_mreq = struct {
	Fipv6mr_multiaddr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]U_int8_t
		}
	}
	Fipv6mr_interface uint32
} /* in6.h:366:1 */

// IPV6_PKTINFO: Packet information(RFC3542 sec 6)
type In6_pktinfo = struct {
	Fipi6_addr struct {
		F__u6_addr struct {
			F__ccgo_pad1 [0]uint32
			F__u6_addr8  [16]U_int8_t
		}
	}
	Fipi6_ifindex uint32
} /* in6.h:374:1 */

// Control structure for IPV6_RECVPATHMTU socket option.
// XXX Not allowed here by POSIX, but required by RFC 3542, so go
// XXX with the code on the pavement.
type Ip6_mtuinfo = struct {
	Fip6m_addr struct {
		Fsin6_len      U_int8_t
		Fsin6_family   Sa_family_t
		Fsin6_port     In_port_t
		Fsin6_flowinfo U_int32_t
		Fsin6_addr     struct {
			F__u6_addr struct {
				F__ccgo_pad1 [0]uint32
				F__u6_addr8  [16]U_int8_t
			}
		}
		Fsin6_scope_id U_int32_t
	}
	Fip6m_mtu U_int32_t
} /* in6.h:384:1 */

// Argument for IPV6_PORTRANGE:
// - which range to search when port is unspecified at bind() or connect()

type Socklen_t = X__socklen_t /* in6.h:401:21 */

// Structures returned by network data base library.  All addresses are
// supplied in host order, and returned in network order (suitable for
// use in system calls).
type Hostent = struct {
	Fh_name      uintptr
	Fh_aliases   uintptr
	Fh_addrtype  int32
	Fh_length    int32
	Fh_addr_list uintptr
} /* netdb.h:108:1 */

// Assumption here is that a network number
// fits in an in_addr_t -- probably a poor one.
type Netent = struct {
	Fn_name     uintptr
	Fn_aliases  uintptr
	Fn_addrtype int32
	Fn_net      In_addr_t
} /* netdb.h:121:1 */

type Servent = struct {
	Fs_name    uintptr
	Fs_aliases uintptr
	Fs_port    int32
	Fs_proto   uintptr
} /* netdb.h:128:1 */

type Protoent = struct {
	Fp_name    uintptr
	Fp_aliases uintptr
	Fp_proto   int32
} /* netdb.h:135:1 */

// Error return codes from gethostbyname() and gethostbyaddr()
// (left in extern int h_errno).

// Values for getaddrinfo() and getnameinfo()
// valid flags for addrinfo

// #define NI_NUMERICSCOPE	32	 return the scope number, not the name

// Scope delimit character (KAME hack)

type Addrinfo = struct {
	Fai_flags     int32
	Fai_family    int32
	Fai_socktype  int32
	Fai_protocol  int32
	Fai_addrlen   Socklen_t
	Fai_addr      uintptr
	Fai_canonname uintptr
	Fai_next      uintptr
} /* netdb.h:203:1 */

// Flags for getrrsetbyname()

// Return codes for getrrsetbyname()

// Structures used by getrrsetbyname() and freerrset()
type Rdatainfo = struct {
	Frdi_length uint32
	Frdi_data   uintptr
} /* netdb.h:233:1 */

type Rrsetinfo = struct {
	Frri_flags   uint32
	Frri_rdclass uint32
	Frri_rdtype  uint32
	Frri_ttl     uint32
	Frri_nrdatas uint32
	Frri_nsigs   uint32
	Frri_name    uintptr
	Frri_rdatas  uintptr
	Frri_sigs    uintptr
} /* netdb.h:238:1 */

type Servent_data = struct {
	Ffp         uintptr
	Faliases    uintptr
	Fmaxaliases int32
	Fstayopen   int32
	Fline       uintptr
} /* netdb.h:250:1 */

type Protoent_data = struct {
	Ffp         uintptr
	Faliases    uintptr
	Fmaxaliases int32
	Fstayopen   int32
	Fline       uintptr
} /* netdb.h:258:1 */

var _ int8 /* gen.c:2:13: */
