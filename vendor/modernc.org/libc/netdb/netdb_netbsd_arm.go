// Code generated by 'ccgo netdb/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o netdb/netdb_netbsd_arm.go -pkgname netdb', DO NOT EDIT.

package netdb

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	AI_ADDRCONFIG                 = 0x00000400             // netdb.h:269:1:
	AI_CANONNAME                  = 0x00000002             // netdb.h:266:1:
	AI_MASK                       = 3087                   // netdb.h:273:1:
	AI_NUMERICHOST                = 0x00000004             // netdb.h:267:1:
	AI_NUMERICSERV                = 0x00000008             // netdb.h:268:1:
	AI_PASSIVE                    = 0x00000001             // netdb.h:265:1:
	AI_SRV                        = 0x00000800             // netdb.h:272:1:
	EAI_ADDRFAMILY                = 1                      // netdb.h:243:1:
	EAI_AGAIN                     = 2                      // netdb.h:244:1:
	EAI_BADFLAGS                  = 3                      // netdb.h:245:1:
	EAI_BADHINTS                  = 12                     // netdb.h:254:1:
	EAI_FAIL                      = 4                      // netdb.h:246:1:
	EAI_FAMILY                    = 5                      // netdb.h:247:1:
	EAI_MAX                       = 15                     // netdb.h:257:1:
	EAI_MEMORY                    = 6                      // netdb.h:248:1:
	EAI_NODATA                    = 7                      // netdb.h:249:1:
	EAI_NONAME                    = 8                      // netdb.h:250:1:
	EAI_OVERFLOW                  = 14                     // netdb.h:256:1:
	EAI_PROTOCOL                  = 13                     // netdb.h:255:1:
	EAI_SERVICE                   = 9                      // netdb.h:251:1:
	EAI_SOCKTYPE                  = 10                     // netdb.h:252:1:
	EAI_SYSTEM                    = 11                     // netdb.h:253:1:
	HOST_NOT_FOUND                = 1                      // netdb.h:230:1:
	INT16_MAX                     = 32767                  // common_int_limits.h:53:1:
	INT16_MIN                     = -32768                 // common_int_limits.h:47:1:
	INT32_MAX                     = 2147483647             // common_int_limits.h:54:1:
	INT32_MIN                     = -2147483648            // common_int_limits.h:48:1:
	INT64_MAX                     = 9223372036854775807    // common_int_limits.h:55:1:
	INT64_MIN                     = -9223372036854775808   // common_int_limits.h:49:1:
	INT8_MAX                      = 127                    // common_int_limits.h:52:1:
	INT8_MIN                      = -128                   // common_int_limits.h:46:1:
	INTMAX_MAX                    = 9223372036854775807    // common_int_limits.h:111:1:
	INTMAX_MIN                    = -9223372036854775808   // common_int_limits.h:110:1:
	INTPTR_MAX                    = 2147483647             // common_int_limits.h:105:1:
	INTPTR_MIN                    = -2147483648            // common_int_limits.h:104:1:
	INT_FAST16_MAX                = 2147483647             // common_int_limits.h:93:1:
	INT_FAST16_MIN                = -2147483648            // common_int_limits.h:87:1:
	INT_FAST32_MAX                = 2147483647             // common_int_limits.h:94:1:
	INT_FAST32_MIN                = -2147483648            // common_int_limits.h:88:1:
	INT_FAST64_MAX                = 9223372036854775807    // common_int_limits.h:95:1:
	INT_FAST64_MIN                = -9223372036854775808   // common_int_limits.h:89:1:
	INT_FAST8_MAX                 = 2147483647             // common_int_limits.h:92:1:
	INT_FAST8_MIN                 = -2147483648            // common_int_limits.h:86:1:
	INT_LEAST16_MAX               = 32767                  // common_int_limits.h:73:1:
	INT_LEAST16_MIN               = -32768                 // common_int_limits.h:67:1:
	INT_LEAST32_MAX               = 2147483647             // common_int_limits.h:74:1:
	INT_LEAST32_MIN               = -2147483648            // common_int_limits.h:68:1:
	INT_LEAST64_MAX               = 9223372036854775807    // common_int_limits.h:75:1:
	INT_LEAST64_MIN               = -9223372036854775808   // common_int_limits.h:69:1:
	INT_LEAST8_MAX                = 127                    // common_int_limits.h:72:1:
	INT_LEAST8_MIN                = -128                   // common_int_limits.h:66:1:
	NETDB_INTERNAL                = -1                     // netdb.h:226:1:
	NETDB_SUCCESS                 = 0                      // netdb.h:227:1:
	NI_DGRAM                      = 0x00000010             // netdb.h:300:1:
	NI_MAXHOST                    = 1025                   // netdb.h:289:1:
	NI_MAXSERV                    = 32                     // netdb.h:290:1:
	NI_NAMEREQD                   = 0x00000004             // netdb.h:298:1:
	NI_NOFQDN                     = 0x00000001             // netdb.h:296:1:
	NI_NUMERICHOST                = 0x00000002             // netdb.h:297:1:
	NI_NUMERICSCOPE               = 0x00000040             // netdb.h:302:1:
	NI_NUMERICSERV                = 0x00000008             // netdb.h:299:1:
	NI_WITHSCOPEID                = 0x00000020             // netdb.h:301:1:
	NO_ADDRESS                    = 4                      // netdb.h:235:1:
	NO_DATA                       = 4                      // netdb.h:233:1:
	NO_RECOVERY                   = 3                      // netdb.h:232:1:
	PRIX16                        = "X"                    // int_fmtio.h:186:1:
	PRIX32                        = "X"                    // int_fmtio.h:187:1:
	PRIX64                        = "llX"                  // int_fmtio.h:189:1:
	PRIX8                         = "X"                    // int_fmtio.h:185:1:
	PRIXFAST16                    = "X"                    // int_fmtio.h:202:1:
	PRIXFAST32                    = "X"                    // int_fmtio.h:203:1:
	PRIXFAST64                    = "llX"                  // int_fmtio.h:205:1:
	PRIXFAST8                     = "X"                    // int_fmtio.h:201:1:
	PRIXLEAST16                   = "X"                    // int_fmtio.h:194:1:
	PRIXLEAST32                   = "X"                    // int_fmtio.h:195:1:
	PRIXLEAST64                   = "llX"                  // int_fmtio.h:197:1:
	PRIXLEAST8                    = "X"                    // int_fmtio.h:193:1:
	PRIXMAX                       = "llX"                  // int_fmtio.h:206:1:
	PRIXPTR                       = "lX"                   // int_fmtio.h:211:1:
	PRId16                        = "d"                    // int_fmtio.h:44:1:
	PRId32                        = "d"                    // int_fmtio.h:45:1:
	PRId64                        = "lld"                  // int_fmtio.h:47:1:
	PRId8                         = "d"                    // int_fmtio.h:43:1:
	PRIdFAST16                    = "d"                    // int_fmtio.h:60:1:
	PRIdFAST32                    = "d"                    // int_fmtio.h:61:1:
	PRIdFAST64                    = "lld"                  // int_fmtio.h:63:1:
	PRIdFAST8                     = "d"                    // int_fmtio.h:59:1:
	PRIdLEAST16                   = "d"                    // int_fmtio.h:52:1:
	PRIdLEAST32                   = "d"                    // int_fmtio.h:53:1:
	PRIdLEAST64                   = "lld"                  // int_fmtio.h:55:1:
	PRIdLEAST8                    = "d"                    // int_fmtio.h:51:1:
	PRIdMAX                       = "lld"                  // int_fmtio.h:64:1:
	PRIdPTR                       = "ld"                   // int_fmtio.h:69:1:
	PRIi16                        = "i"                    // int_fmtio.h:72:1:
	PRIi32                        = "i"                    // int_fmtio.h:73:1:
	PRIi64                        = "lli"                  // int_fmtio.h:75:1:
	PRIi8                         = "i"                    // int_fmtio.h:71:1:
	PRIiFAST16                    = "i"                    // int_fmtio.h:88:1:
	PRIiFAST32                    = "i"                    // int_fmtio.h:89:1:
	PRIiFAST64                    = "lli"                  // int_fmtio.h:91:1:
	PRIiFAST8                     = "i"                    // int_fmtio.h:87:1:
	PRIiLEAST16                   = "i"                    // int_fmtio.h:80:1:
	PRIiLEAST32                   = "i"                    // int_fmtio.h:81:1:
	PRIiLEAST64                   = "lli"                  // int_fmtio.h:83:1:
	PRIiLEAST8                    = "i"                    // int_fmtio.h:79:1:
	PRIiMAX                       = "lli"                  // int_fmtio.h:92:1:
	PRIiPTR                       = "li"                   // int_fmtio.h:97:1:
	PRIo16                        = "o"                    // int_fmtio.h:102:1:
	PRIo32                        = "o"                    // int_fmtio.h:103:1:
	PRIo64                        = "llo"                  // int_fmtio.h:105:1:
	PRIo8                         = "o"                    // int_fmtio.h:101:1:
	PRIoFAST16                    = "o"                    // int_fmtio.h:118:1:
	PRIoFAST32                    = "o"                    // int_fmtio.h:119:1:
	PRIoFAST64                    = "llo"                  // int_fmtio.h:121:1:
	PRIoFAST8                     = "o"                    // int_fmtio.h:117:1:
	PRIoLEAST16                   = "o"                    // int_fmtio.h:110:1:
	PRIoLEAST32                   = "o"                    // int_fmtio.h:111:1:
	PRIoLEAST64                   = "llo"                  // int_fmtio.h:113:1:
	PRIoLEAST8                    = "o"                    // int_fmtio.h:109:1:
	PRIoMAX                       = "llo"                  // int_fmtio.h:122:1:
	PRIoPTR                       = "lo"                   // int_fmtio.h:127:1:
	PRIu16                        = "u"                    // int_fmtio.h:130:1:
	PRIu32                        = "u"                    // int_fmtio.h:131:1:
	PRIu64                        = "llu"                  // int_fmtio.h:133:1:
	PRIu8                         = "u"                    // int_fmtio.h:129:1:
	PRIuFAST16                    = "u"                    // int_fmtio.h:146:1:
	PRIuFAST32                    = "u"                    // int_fmtio.h:147:1:
	PRIuFAST64                    = "llu"                  // int_fmtio.h:149:1:
	PRIuFAST8                     = "u"                    // int_fmtio.h:145:1:
	PRIuLEAST16                   = "u"                    // int_fmtio.h:138:1:
	PRIuLEAST32                   = "u"                    // int_fmtio.h:139:1:
	PRIuLEAST64                   = "llu"                  // int_fmtio.h:141:1:
	PRIuLEAST8                    = "u"                    // int_fmtio.h:137:1:
	PRIuMAX                       = "llu"                  // int_fmtio.h:150:1:
	PRIuPTR                       = "lu"                   // int_fmtio.h:155:1:
	PRIx16                        = "x"                    // int_fmtio.h:158:1:
	PRIx32                        = "x"                    // int_fmtio.h:159:1:
	PRIx64                        = "llx"                  // int_fmtio.h:161:1:
	PRIx8                         = "x"                    // int_fmtio.h:157:1:
	PRIxFAST16                    = "x"                    // int_fmtio.h:174:1:
	PRIxFAST32                    = "x"                    // int_fmtio.h:175:1:
	PRIxFAST64                    = "llx"                  // int_fmtio.h:177:1:
	PRIxFAST8                     = "x"                    // int_fmtio.h:173:1:
	PRIxLEAST16                   = "x"                    // int_fmtio.h:166:1:
	PRIxLEAST32                   = "x"                    // int_fmtio.h:167:1:
	PRIxLEAST64                   = "llx"                  // int_fmtio.h:169:1:
	PRIxLEAST8                    = "x"                    // int_fmtio.h:165:1:
	PRIxMAX                       = "llx"                  // int_fmtio.h:178:1:
	PRIxPTR                       = "lx"                   // int_fmtio.h:183:1:
	PTRDIFF_MAX                   = 2147483647             // common_int_limits.h:121:1:
	PTRDIFF_MIN                   = -2147483648            // common_int_limits.h:120:1:
	SCNd16                        = "hd"                   // int_fmtio.h:216:1:
	SCNd32                        = "d"                    // int_fmtio.h:217:1:
	SCNd64                        = "lld"                  // int_fmtio.h:219:1:
	SCNd8                         = "hhd"                  // int_fmtio.h:215:1:
	SCNdFAST16                    = "d"                    // int_fmtio.h:232:1:
	SCNdFAST32                    = "d"                    // int_fmtio.h:233:1:
	SCNdFAST64                    = "lld"                  // int_fmtio.h:235:1:
	SCNdFAST8                     = "d"                    // int_fmtio.h:231:1:
	SCNdLEAST16                   = "hd"                   // int_fmtio.h:224:1:
	SCNdLEAST32                   = "d"                    // int_fmtio.h:225:1:
	SCNdLEAST64                   = "lld"                  // int_fmtio.h:227:1:
	SCNdLEAST8                    = "hhd"                  // int_fmtio.h:223:1:
	SCNdMAX                       = "lld"                  // int_fmtio.h:236:1:
	SCNdPTR                       = "ld"                   // int_fmtio.h:241:1:
	SCNi16                        = "hi"                   // int_fmtio.h:244:1:
	SCNi32                        = "i"                    // int_fmtio.h:245:1:
	SCNi64                        = "lli"                  // int_fmtio.h:247:1:
	SCNi8                         = "hhi"                  // int_fmtio.h:243:1:
	SCNiFAST16                    = "i"                    // int_fmtio.h:260:1:
	SCNiFAST32                    = "i"                    // int_fmtio.h:261:1:
	SCNiFAST64                    = "lli"                  // int_fmtio.h:263:1:
	SCNiFAST8                     = "i"                    // int_fmtio.h:259:1:
	SCNiLEAST16                   = "hi"                   // int_fmtio.h:252:1:
	SCNiLEAST32                   = "i"                    // int_fmtio.h:253:1:
	SCNiLEAST64                   = "lli"                  // int_fmtio.h:255:1:
	SCNiLEAST8                    = "hhi"                  // int_fmtio.h:251:1:
	SCNiMAX                       = "lli"                  // int_fmtio.h:264:1:
	SCNiPTR                       = "li"                   // int_fmtio.h:269:1:
	SCNo16                        = "ho"                   // int_fmtio.h:274:1:
	SCNo32                        = "o"                    // int_fmtio.h:275:1:
	SCNo64                        = "llo"                  // int_fmtio.h:277:1:
	SCNo8                         = "hho"                  // int_fmtio.h:273:1:
	SCNoFAST16                    = "o"                    // int_fmtio.h:290:1:
	SCNoFAST32                    = "o"                    // int_fmtio.h:291:1:
	SCNoFAST64                    = "llo"                  // int_fmtio.h:293:1:
	SCNoFAST8                     = "o"                    // int_fmtio.h:289:1:
	SCNoLEAST16                   = "ho"                   // int_fmtio.h:282:1:
	SCNoLEAST32                   = "o"                    // int_fmtio.h:283:1:
	SCNoLEAST64                   = "llo"                  // int_fmtio.h:285:1:
	SCNoLEAST8                    = "hho"                  // int_fmtio.h:281:1:
	SCNoMAX                       = "llo"                  // int_fmtio.h:294:1:
	SCNoPTR                       = "lo"                   // int_fmtio.h:299:1:
	SCNu16                        = "hu"                   // int_fmtio.h:302:1:
	SCNu32                        = "u"                    // int_fmtio.h:303:1:
	SCNu64                        = "llu"                  // int_fmtio.h:305:1:
	SCNu8                         = "hhu"                  // int_fmtio.h:301:1:
	SCNuFAST16                    = "u"                    // int_fmtio.h:318:1:
	SCNuFAST32                    = "u"                    // int_fmtio.h:319:1:
	SCNuFAST64                    = "llu"                  // int_fmtio.h:321:1:
	SCNuFAST8                     = "u"                    // int_fmtio.h:317:1:
	SCNuLEAST16                   = "hu"                   // int_fmtio.h:310:1:
	SCNuLEAST32                   = "u"                    // int_fmtio.h:311:1:
	SCNuLEAST64                   = "llu"                  // int_fmtio.h:313:1:
	SCNuLEAST8                    = "hhu"                  // int_fmtio.h:309:1:
	SCNuMAX                       = "llu"                  // int_fmtio.h:322:1:
	SCNuPTR                       = "lu"                   // int_fmtio.h:327:1:
	SCNx16                        = "hx"                   // int_fmtio.h:330:1:
	SCNx32                        = "x"                    // int_fmtio.h:331:1:
	SCNx64                        = "llx"                  // int_fmtio.h:333:1:
	SCNx8                         = "hhx"                  // int_fmtio.h:329:1:
	SCNxFAST16                    = "x"                    // int_fmtio.h:346:1:
	SCNxFAST32                    = "x"                    // int_fmtio.h:347:1:
	SCNxFAST64                    = "llx"                  // int_fmtio.h:349:1:
	SCNxFAST8                     = "x"                    // int_fmtio.h:345:1:
	SCNxLEAST16                   = "hx"                   // int_fmtio.h:338:1:
	SCNxLEAST32                   = "x"                    // int_fmtio.h:339:1:
	SCNxLEAST64                   = "llx"                  // int_fmtio.h:341:1:
	SCNxLEAST8                    = "hhx"                  // int_fmtio.h:337:1:
	SCNxMAX                       = "llx"                  // int_fmtio.h:350:1:
	SCNxPTR                       = "lx"                   // int_fmtio.h:355:1:
	SCOPE_DELIMITER               = 37                     // netdb.h:308:1:
	SIG_ATOMIC_MAX                = 2147483647             // common_int_limits.h:125:1:
	SIG_ATOMIC_MIN                = -2147483648            // common_int_limits.h:124:1:
	SIZE_MAX                      = 4294967295             // common_int_limits.h:128:1:
	TRY_AGAIN                     = 2                      // netdb.h:231:1:
	UINT16_MAX                    = 65535                  // common_int_limits.h:59:1:
	UINT32_MAX                    = 4294967295             // common_int_limits.h:60:1:
	UINT64_MAX                    = 18446744073709551615   // common_int_limits.h:61:1:
	UINT8_MAX                     = 255                    // common_int_limits.h:58:1:
	UINTMAX_MAX                   = 18446744073709551615   // common_int_limits.h:112:1:
	UINTPTR_MAX                   = 4294967295             // common_int_limits.h:106:1:
	UINT_FAST16_MAX               = 4294967295             // common_int_limits.h:99:1:
	UINT_FAST32_MAX               = 4294967295             // common_int_limits.h:100:1:
	UINT_FAST64_MAX               = 18446744073709551615   // common_int_limits.h:101:1:
	UINT_FAST8_MAX                = 4294967295             // common_int_limits.h:98:1:
	UINT_LEAST16_MAX              = 65535                  // common_int_limits.h:79:1:
	UINT_LEAST32_MAX              = 4294967295             // common_int_limits.h:80:1:
	UINT_LEAST64_MAX              = 18446744073709551615   // common_int_limits.h:81:1:
	UINT_LEAST8_MAX               = 255                    // common_int_limits.h:78:1:
	WCHAR_MAX                     = 2147483647             // wchar_limits.h:50:1:
	WCHAR_MIN                     = -2147483648            // wchar_limits.h:42:1:
	WINT_MAX                      = 2147483647             // wchar_limits.h:68:1:
	WINT_MIN                      = -2147483648            // wchar_limits.h:60:1:
	X_ARM_ARCH_4T                 = 0                      // cdefs.h:44:1:
	X_ARM_ARCH_5                  = 0                      // cdefs.h:40:1:
	X_ARM_ARCH_5T                 = 0                      // cdefs.h:36:1:
	X_ARM_ARCH_6                  = 0                      // cdefs.h:31:1:
	X_ARM_ARCH_7                  = 0                      // cdefs.h:20:1:
	X_ARM_ARCH_DWORD_OK           = 0                      // cdefs.h:51:1:
	X_ARM_ARCH_T2                 = 0                      // cdefs.h:24:1:
	X_ARM_CDEFS_H_                = 0                      // cdefs.h:4:1:
	X_ARM_INT_CONST_H_            = 0                      // int_const.h:33:1:
	X_ARM_INT_FMTIO_H_            = 0                      // int_fmtio.h:33:1:
	X_ARM_INT_LIMITS_H_           = 0                      // int_limits.h:33:1:
	X_ARM_INT_MWGWTYPES_H_        = 0                      // int_mwgwtypes.h:33:1:
	X_ARM_INT_TYPES_H_            = 0                      // int_types.h:33:1:
	X_ARM_WCHAR_LIMITS_H_         = 0                      // wchar_limits.h:33:1:
	X_BSD_INT16_T_                = 0                      // stdint.h:50:1:
	X_BSD_INT32_T_                = 0                      // stdint.h:60:1:
	X_BSD_INT64_T_                = 0                      // stdint.h:70:1:
	X_BSD_INT8_T_                 = 0                      // stdint.h:40:1:
	X_BSD_INTPTR_T_               = 0                      // stdint.h:80:1:
	X_BSD_UINT16_T_               = 0                      // stdint.h:55:1:
	X_BSD_UINT32_T_               = 0                      // stdint.h:65:1:
	X_BSD_UINT64_T_               = 0                      // stdint.h:75:1:
	X_BSD_UINT8_T_                = 0                      // stdint.h:45:1:
	X_BSD_UINTPTR_T_              = 0                      // stdint.h:85:1:
	X_FILE_OFFSET_BITS            = 64                     // <builtin>:25:1:
	X_INTTYPES_H_                 = 0                      // inttypes.h:33:1:
	X_NETBSD_SOURCE               = 1                      // featuretest.h:70:1:
	X_NETDB_H_                    = 0                      // netdb.h:91:1:
	X_PATH_HEQUIV                 = "/etc/hosts.equiv"     // netdb.h:113:1:
	X_PATH_HOSTS                  = "/etc/hosts"           // netdb.h:116:1:
	X_PATH_NETWORKS               = "/etc/networks"        // netdb.h:119:1:
	X_PATH_PROTOCOLS              = "/etc/protocols"       // netdb.h:122:1:
	X_PATH_SERVICES               = "/etc/services"        // netdb.h:125:1:
	X_PATH_SERVICES_CDB           = "/var/db/services.cdb" // netdb.h:128:1:
	X_PATH_SERVICES_DB            = "/var/db/services.db"  // netdb.h:131:1:
	X_SYS_ANSI_H_                 = 0                      // ansi.h:33:1:
	X_SYS_CDEFS_ELF_H_            = 0                      // cdefs_elf.h:31:1:
	X_SYS_CDEFS_H_                = 0                      // cdefs.h:37:1:
	X_SYS_COMMON_ANSI_H_          = 0                      // common_ansi.h:33:1:
	X_SYS_COMMON_INT_LIMITS_H_    = 0                      // common_int_limits.h:33:1:
	X_SYS_COMMON_INT_MWGWTYPES_H_ = 0                      // common_int_mwgwtypes.h:33:1:
	X_SYS_COMMON_INT_TYPES_H_     = 0                      // common_int_types.h:33:1:
	X_SYS_INTTYPES_H_             = 0                      // inttypes.h:33:1:
	X_SYS_STDINT_H_               = 0                      // stdint.h:33:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

// return true if value 'a' fits in type 't'

// $NetBSD: endian_machdep.h,v 1.9 2014/01/29 01:03:13 matt Exp $

// __ARMEB__ or __AARCH64EB__ is predefined when building big-endian ARM.
//	$NetBSD: ansi.h,v 1.14 2011/07/17 20:54:54 joerg Exp $

// -
// Copyright (c) 2000, 2001, 2002 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Jun-ichiro itojun Hagino and by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: ansi.h,v 1.18 2019/05/07 03:49:26 kamil Exp $

//	$NetBSD: common_ansi.h,v 1.1 2014/08/19 07:27:31 matt Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_types.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.1 Exact-width integer types

type X__int8_t = int8     /* common_int_types.h:45:27 */
type X__uint8_t = uint8   /* common_int_types.h:46:27 */
type X__int16_t = int16   /* common_int_types.h:47:27 */
type X__uint16_t = uint16 /* common_int_types.h:48:27 */
type X__int32_t = int32   /* common_int_types.h:49:27 */
type X__uint32_t = uint32 /* common_int_types.h:50:27 */
type X__int64_t = int64   /* common_int_types.h:51:27 */
type X__uint64_t = uint64 /* common_int_types.h:52:27 */

// ******** Integer types capable of holding object pointers

type X__intptr_t = int32   /* common_int_types.h:58:27 */
type X__uintptr_t = uint32 /* common_int_types.h:59:26 */

// Types which are fundamental to the implementation and may appear in
// more than one standard header are defined here.  Standard headers
// then use:
//	#ifdef	_BSD_SIZE_T_
//	typedef	_BSD_SIZE_T_ size_t;
//	#undef	_BSD_SIZE_T_
//	#endif

type X__caddr_t = uintptr        /* ansi.h:37:14 */ // core address
type X__gid_t = X__uint32_t      /* ansi.h:38:20 */ // group id
type X__in_addr_t = X__uint32_t  /* ansi.h:39:20 */ // IP(v4) address
type X__in_port_t = X__uint16_t  /* ansi.h:40:20 */ // "Internet" port number
type X__mode_t = X__uint32_t     /* ansi.h:41:20 */ // file permissions
type X__off_t = X__int64_t       /* ansi.h:42:19 */ // file offset
type X__pid_t = X__int32_t       /* ansi.h:43:19 */ // process id
type X__sa_family_t = X__uint8_t /* ansi.h:44:19 */ // socket address family
type X__socklen_t = uint32       /* ansi.h:45:22 */ // socket-related datum length
type X__uid_t = X__uint32_t      /* ansi.h:46:20 */ // user id
type X__fsblkcnt_t = X__uint64_t /* ansi.h:47:20 */ // fs block count (statvfs)
type X__fsfilcnt_t = X__uint64_t /* ansi.h:48:20 */
type X__wctrans_t = uintptr      /* ansi.h:51:32 */
type X__wctype_t = uintptr       /* ansi.h:54:31 */

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions.  The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__mbstateL  X__int64_t
	F__ccgo_pad1 [120]byte
} /* ansi.h:63:3 */

type X__va_list = X__builtin_va_list /* ansi.h:72:27 */

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: inttypes.h,v 1.11 2015/01/16 18:35:59 christos Exp $

// -
// Copyright (c) 2001 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: featuretest.h,v 1.10 2013/04/26 18:29:06 christos Exp $

// Written by Klaus Klein <<EMAIL>>, February 2, 1998.
// Public domain.
//
// NOTE: Do not protect this header against multiple inclusion.  Doing
// so can have subtle side-effects due to header file inclusion order
// and testing of e.g. _POSIX_SOURCE vs. _POSIX_C_SOURCE.  Instead,
// protect each CPP macro that we want to supply.

// Feature-test macros are defined by several standards, and allow an
// application to specify what symbols they want the system headers to
// expose, and hence what standard they want them to conform to.
// There are two classes of feature-test macros.  The first class
// specify complete standards, and if one of these is defined, header
// files will try to conform to the relevant standard.  They are:
//
// ANSI macros:
// _ANSI_SOURCE			ANSI C89
//
// POSIX macros:
// _POSIX_SOURCE == 1		IEEE Std 1003.1 (version?)
// _POSIX_C_SOURCE == 1		IEEE Std 1003.1-1990
// _POSIX_C_SOURCE == 2		IEEE Std 1003.2-1992
// _POSIX_C_SOURCE == 199309L	IEEE Std 1003.1b-1993
// _POSIX_C_SOURCE == 199506L	ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L	IEEE Std 1003.1-2001
// _POSIX_C_SOURCE == 200809L   IEEE Std 1003.1-2008
//
// X/Open macros:
// _XOPEN_SOURCE		System Interfaces and Headers, Issue 4, Ver 2
// _XOPEN_SOURCE_EXTENDED == 1	XSH4.2 UNIX extensions
// _XOPEN_SOURCE == 500		System Interfaces and Headers, Issue 5
// _XOPEN_SOURCE == 520		Networking Services (XNS), Issue 5.2
// _XOPEN_SOURCE == 600		IEEE Std 1003.1-2001, XSI option
// _XOPEN_SOURCE == 700		IEEE Std 1003.1-2008, XSI option
//
// NetBSD macros:
// _NETBSD_SOURCE == 1		Make all NetBSD features available.
//
// If more than one of these "major" feature-test macros is defined,
// then the set of facilities provided (and namespace used) is the
// union of that specified by the relevant standards, and in case of
// conflict, the earlier standard in the above list has precedence (so
// if both _POSIX_C_SOURCE and _NETBSD_SOURCE are defined, the version
// of rename() that's used is the POSIX one).  If none of the "major"
// feature-test macros is defined, _NETBSD_SOURCE is assumed.
//
// There are also "minor" feature-test macros, which enable extra
// functionality in addition to some base standard.  They should be
// defined along with one of the "major" macros.  The "minor" macros
// are:
//
// _REENTRANT
// _ISOC99_SOURCE
// _ISOC11_SOURCE
// _LARGEFILE_SOURCE		Large File Support
//		<http://ftp.sas.com/standards/large.file/x_open.20Mar96.html>

//	$NetBSD: inttypes.h,v 1.6 2013/04/22 21:26:48 joerg Exp $

// -
// Copyright (c) 1998, 2000 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus J. Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.8  Format conversion of integer types

//	$NetBSD: stdint.h,v 1.8 2018/11/06 16:26:44 maya Exp $

// -
// Copyright (c) 2001, 2004 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Klaus Klein.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: cdefs.h,v 1.141 2019/02/21 21:34:05 christos Exp $

// * Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.8 (Berkeley) 1/9/95

//	$NetBSD: int_types.h,v 1.17 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

type Int8_t = X__int8_t /* stdint.h:39:18 */

type Uint8_t = X__uint8_t /* stdint.h:44:19 */

type Int16_t = X__int16_t /* stdint.h:49:19 */

type Uint16_t = X__uint16_t /* stdint.h:54:20 */

type Int32_t = X__int32_t /* stdint.h:59:19 */

type Uint32_t = X__uint32_t /* stdint.h:64:20 */

type Int64_t = X__int64_t /* stdint.h:69:19 */

type Uint64_t = X__uint64_t /* stdint.h:74:20 */

type Intptr_t = X__intptr_t /* stdint.h:79:20 */

type Uintptr_t = X__uintptr_t /* stdint.h:84:21 */

// $NetBSD: int_mwgwtypes.h,v 1.7 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Matt Thomas of 3am Software Foundry.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

//	$NetBSD: common_int_mwgwtypes.h,v 1.1 2014/07/25 21:43:13 joerg Exp $

// -
// Copyright (c) 2014 The NetBSD Foundation, Inc.
// All rights reserved.
//
// This code is derived from software contributed to The NetBSD Foundation
// by Joerg Sonnenberger.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
// BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// 7.18.1 Integer types

// 7.18.1.2 Minimum-width integer types

type Int_least8_t = int8     /* common_int_mwgwtypes.h:45:32 */
type Uint_least8_t = uint8   /* common_int_mwgwtypes.h:46:32 */
type Int_least16_t = int16   /* common_int_mwgwtypes.h:47:32 */
type Uint_least16_t = uint16 /* common_int_mwgwtypes.h:48:32 */
type Int_least32_t = int32   /* common_int_mwgwtypes.h:49:32 */
type Uint_least32_t = uint32 /* common_int_mwgwtypes.h:50:32 */
type Int_least64_t = int64   /* common_int_mwgwtypes.h:51:32 */
type Uint_least64_t = uint64 /* common_int_mwgwtypes.h:52:32 */

// 7.18.1.3 Fastest minimum-width integer types
type Int_fast8_t = int32    /* common_int_mwgwtypes.h:55:32 */
type Uint_fast8_t = uint32  /* common_int_mwgwtypes.h:56:32 */
type Int_fast16_t = int32   /* common_int_mwgwtypes.h:57:32 */
type Uint_fast16_t = uint32 /* common_int_mwgwtypes.h:58:32 */
type Int_fast32_t = int32   /* common_int_mwgwtypes.h:59:32 */
type Uint_fast32_t = uint32 /* common_int_mwgwtypes.h:60:32 */
type Int_fast64_t = int64   /* common_int_mwgwtypes.h:61:32 */
type Uint_fast64_t = uint64 /* common_int_mwgwtypes.h:62:32 */

// ******** Greatest-width integer types

type Intmax_t = int64   /* common_int_mwgwtypes.h:66:33 */
type Uintmax_t = uint64 /* common_int_mwgwtypes.h:67:32 */

type Imaxdiv_t = struct {
	Fquot Intmax_t
	Frem  Intmax_t
} /* inttypes.h:60:3 */

type Locale_t = uintptr /* inttypes.h:66:25 */

// Data types
type Socklen_t = X__socklen_t /* netdb.h:102:21 */

// %
// Structures returned by network data base library.  All addresses are
// supplied in host order, and returned in network order (suitable for
// use in system calls).
type Hostent = struct {
	Fh_name      uintptr
	Fh_aliases   uintptr
	Fh_addrtype  int32
	Fh_length    int32
	Fh_addr_list uintptr
} /* netdb.h:148:1 */

// %
// Assumption here is that a network number
// fits in an unsigned long -- probably a poor one.
type Netent = struct {
	Fn_name     uintptr
	Fn_aliases  uintptr
	Fn_addrtype int32
	Fn_net      Uint32_t
} /* netdb.h:161:1 */

type Servent = struct {
	Fs_name    uintptr
	Fs_aliases uintptr
	Fs_port    int32
	Fs_proto   uintptr
} /* netdb.h:176:1 */

type Protoent = struct {
	Fp_name    uintptr
	Fp_aliases uintptr
	Fp_proto   int32
} /* netdb.h:183:1 */

// Note: ai_addrlen used to be a size_t, per RFC 2553.
// In XNS5.2, and subsequently in POSIX-2001 and
// draft-ietf-ipngwg-rfc2553bis-02.txt it was changed to a socklen_t.
// To accommodate for this while preserving binary compatibility with the
// old interface, we prepend or append 32 bits of padding, depending on
// the (LP64) architecture's endianness.
//
// This should be deleted the next time the libc major number is
// incremented.
type Addrinfo = struct {
	Fai_flags     int32
	Fai_family    int32
	Fai_socktype  int32
	Fai_protocol  int32
	Fai_addrlen   X__socklen_t
	Fai_canonname uintptr
	Fai_addr      uintptr
	Fai_next      uintptr
} /* netdb.h:202:1 */

var _ uint8 /* gen.c:2:13: */
