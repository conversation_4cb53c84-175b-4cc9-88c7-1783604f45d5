// Code generated by 'ccgo pthread/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o pthread/pthread_windows_386.go -pkgname pthread', DO NOT EDIT.

package pthread

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	CHAR_BIT                                        = 8                    // limits.h:64:1:
	CHAR_MAX                                        = 127                  // limits.h:99:1:
	CHAR_MIN                                        = -128                 // limits.h:97:1:
	DUMMYSTRUCTNAME                                 = 0                    // _mingw.h:519:1:
	DUMMYSTRUCTNAME1                                = 0                    // _mingw.h:520:1:
	DUMMYSTRUCTNAME2                                = 0                    // _mingw.h:521:1:
	DUMMYSTRUCTNAME3                                = 0                    // _mingw.h:522:1:
	DUMMYSTRUCTNAME4                                = 0                    // _mingw.h:523:1:
	DUMMYSTRUCTNAME5                                = 0                    // _mingw.h:524:1:
	DUMMYUNIONNAME                                  = 0                    // _mingw.h:497:1:
	DUMMYUNIONNAME1                                 = 0                    // _mingw.h:498:1:
	DUMMYUNIONNAME2                                 = 0                    // _mingw.h:499:1:
	DUMMYUNIONNAME3                                 = 0                    // _mingw.h:500:1:
	DUMMYUNIONNAME4                                 = 0                    // _mingw.h:501:1:
	DUMMYUNIONNAME5                                 = 0                    // _mingw.h:502:1:
	DUMMYUNIONNAME6                                 = 0                    // _mingw.h:503:1:
	DUMMYUNIONNAME7                                 = 0                    // _mingw.h:504:1:
	DUMMYUNIONNAME8                                 = 0                    // _mingw.h:505:1:
	DUMMYUNIONNAME9                                 = 0                    // _mingw.h:506:1:
	E2BIG                                           = 7                    // errno.h:31:1:
	EACCES                                          = 13                   // errno.h:37:1:
	EADDRINUSE                                      = 100                  // errno.h:86:1:
	EADDRNOTAVAIL                                   = 101                  // errno.h:90:1:
	EAFNOSUPPORT                                    = 102                  // errno.h:82:1:
	EAGAIN                                          = 11                   // errno.h:35:1:
	EALREADY                                        = 103                  // errno.h:106:1:
	EBADF                                           = 9                    // errno.h:33:1:
	EBADMSG                                         = 104                  // errno.h:182:1:
	EBUSY                                           = 16                   // errno.h:39:1:
	ECANCELED                                       = 105                  // errno.h:154:1:
	ECHILD                                          = 10                   // errno.h:34:1:
	ECONNABORTED                                    = 106                  // errno.h:102:1:
	ECONNREFUSED                                    = 107                  // errno.h:110:1:
	ECONNRESET                                      = 108                  // errno.h:114:1:
	EDEADLK                                         = 36                   // errno.h:55:1:
	EDEADLOCK                                       = 36                   // errno.h:71:1:
	EDESTADDRREQ                                    = 109                  // errno.h:118:1:
	EDOM                                            = 33                   // errno.h:54:1:
	EEXIST                                          = 17                   // errno.h:40:1:
	EFAULT                                          = 14                   // errno.h:38:1:
	EFBIG                                           = 27                   // errno.h:48:1:
	EHOSTUNREACH                                    = 110                  // errno.h:122:1:
	EIDRM                                           = 111                  // errno.h:186:1:
	EILSEQ                                          = 42                   // errno.h:66:1:
	EINPROGRESS                                     = 112                  // errno.h:158:1:
	EINTR                                           = 4                    // errno.h:28:1:
	EINVAL                                          = 22                   // errno.h:64:1:
	EIO                                             = 5                    // errno.h:29:1:
	EISCONN                                         = 113                  // errno.h:94:1:
	EISDIR                                          = 21                   // errno.h:44:1:
	ELOOP                                           = 114                  // errno.h:227:1:
	EMFILE                                          = 24                   // errno.h:46:1:
	EMLINK                                          = 31                   // errno.h:52:1:
	EMSGSIZE                                        = 115                  // errno.h:126:1:
	ENAMETOOLONG                                    = 38                   // errno.h:56:1:
	ENETDOWN                                        = 116                  // errno.h:130:1:
	ENETRESET                                       = 117                  // errno.h:134:1:
	ENETUNREACH                                     = 118                  // errno.h:138:1:
	ENFILE                                          = 23                   // errno.h:45:1:
	ENOBUFS                                         = 119                  // errno.h:98:1:
	ENODATA                                         = 120                  // errno.h:190:1:
	ENODEV                                          = 19                   // errno.h:42:1:
	ENOENT                                          = 2                    // errno.h:25:1:
	ENOEXEC                                         = 8                    // errno.h:32:1:
	ENOFILE                                         = 2                    // errno.h:26:1:
	ENOLCK                                          = 39                   // errno.h:57:1:
	ENOLINK                                         = 121                  // errno.h:194:1:
	ENOMEM                                          = 12                   // errno.h:36:1:
	ENOMSG                                          = 122                  // errno.h:198:1:
	ENOPROTOOPT                                     = 123                  // errno.h:142:1:
	ENOSPC                                          = 28                   // errno.h:49:1:
	ENOSR                                           = 124                  // errno.h:202:1:
	ENOSTR                                          = 125                  // errno.h:206:1:
	ENOSYS                                          = 40                   // errno.h:58:1:
	ENOTCONN                                        = 126                  // errno.h:150:1:
	ENOTDIR                                         = 20                   // errno.h:43:1:
	ENOTEMPTY                                       = 41                   // errno.h:59:1:
	ENOTRECOVERABLE                                 = 127                  // errno.h:210:1:
	ENOTSOCK                                        = 128                  // errno.h:146:1:
	ENOTSUP                                         = 129                  // errno.h:76:1:
	ENOTTY                                          = 25                   // errno.h:47:1:
	ENXIO                                           = 6                    // errno.h:30:1:
	EOPNOTSUPP                                      = 130                  // errno.h:162:1:
	EOVERFLOW                                       = 132                  // errno.h:235:1:
	EOWNERDEAD                                      = 133                  // errno.h:170:1:
	EPERM                                           = 1                    // errno.h:24:1:
	EPIPE                                           = 32                   // errno.h:53:1:
	EPROTO                                          = 134                  // errno.h:174:1:
	EPROTONOSUPPORT                                 = 135                  // errno.h:178:1:
	EPROTOTYPE                                      = 136                  // errno.h:231:1:
	ERANGE                                          = 34                   // errno.h:65:1:
	EROFS                                           = 30                   // errno.h:51:1:
	ESPIPE                                          = 29                   // errno.h:50:1:
	ESRCH                                           = 3                    // errno.h:27:1:
	ETIME                                           = 137                  // errno.h:214:1:
	ETIMEDOUT                                       = 138                  // errno.h:223:1:
	ETXTBSY                                         = 139                  // errno.h:218:1:
	EWOULDBLOCK                                     = 140                  // errno.h:166:1:
	EXDEV                                           = 18                   // errno.h:41:1:
	GENERIC_ERRORCHECK_INITIALIZER                  = -2                   // pthread.h:279:1:
	GENERIC_INITIALIZER                             = -1                   // pthread.h:278:1:
	GENERIC_NORMAL_INITIALIZER                      = -1                   // pthread.h:281:1:
	GENERIC_RECURSIVE_INITIALIZER                   = -3                   // pthread.h:280:1:
	INT_MAX                                         = 2147483647           // limits.h:120:1:
	INT_MIN                                         = -2147483648          // limits.h:118:1:
	LLONG_MAX                                       = 9223372036854775807  // limits.h:142:1:
	LLONG_MIN                                       = -9223372036854775808 // limits.h:140:1:
	LONG_LONG_MAX                                   = 9223372036854775807  // limits.h:154:1:
	LONG_LONG_MIN                                   = -9223372036854775808 // limits.h:152:1:
	LONG_MAX                                        = 2147483647           // limits.h:131:1:
	LONG_MIN                                        = -2147483648          // limits.h:129:1:
	MAX_READ_LOCKS                                  = 2147483646           // pthread.h:168:1:
	MB_LEN_MAX                                      = 5                    // limits.h:35:1:
	MINGW_DDK_H                                     = 0                    // _mingw_ddk.h:2:1:
	MINGW_HAS_DDK_H                                 = 1                    // _mingw_ddk.h:4:1:
	MINGW_HAS_SECURE_API                            = 1                    // _mingw.h:602:1:
	MINGW_SDK_INIT                                  = 0                    // _mingw.h:598:1:
	NSIG                                            = 23                   // signal.h:21:1:
	OLD_P_OVERLAY                                   = 2                    // process.h:149:1:
	PATH_MAX                                        = 260                  // limits.h:20:1:
	PTHREAD_BARRIER_SERIAL_THREAD                   = 1                    // pthread.h:165:1:
	PTHREAD_CANCEL_ASYNCHRONOUS                     = 0x02                 // pthread.h:117:1:
	PTHREAD_CANCEL_DEFERRED                         = 0                    // pthread.h:116:1:
	PTHREAD_CANCEL_DISABLE                          = 0                    // pthread.h:113:1:
	PTHREAD_CANCEL_ENABLE                           = 0x01                 // pthread.h:114:1:
	PTHREAD_CREATE_DETACHED                         = 0x04                 // pthread.h:120:1:
	PTHREAD_CREATE_JOINABLE                         = 0                    // pthread.h:119:1:
	PTHREAD_DEFAULT_ATTR                            = 1                    // pthread.h:128:1:
	PTHREAD_DESTRUCTOR_ITERATIONS                   = 256                  // pthread.h:136:1:
	PTHREAD_EXPLICIT_SCHED                          = 0                    // pthread.h:122:1:
	PTHREAD_INHERIT_SCHED                           = 0x08                 // pthread.h:123:1:
	PTHREAD_KEYS_MAX                                = 1048576              // pthread.h:137:1:
	PTHREAD_MUTEX_ADAPTIVE_NP                       = 0                    // pthread.h:156:1:
	PTHREAD_MUTEX_DEFAULT                           = 0                    // pthread.h:142:1:
	PTHREAD_MUTEX_ERRORCHECK                        = 1                    // pthread.h:275:1:
	PTHREAD_MUTEX_ERRORCHECK_NP                     = 1                    // pthread.h:157:1:
	PTHREAD_MUTEX_FAST_NP                           = 0                    // pthread.h:154:1:
	PTHREAD_MUTEX_NORMAL                            = 0                    // pthread.h:274:1:
	PTHREAD_MUTEX_PRIVATE                           = 0                    // pthread.h:145:1:
	PTHREAD_MUTEX_RECURSIVE                         = 2                    // pthread.h:276:1:
	PTHREAD_MUTEX_RECURSIVE_NP                      = 2                    // pthread.h:158:1:
	PTHREAD_MUTEX_SHARED                            = 1                    // pthread.h:144:1:
	PTHREAD_MUTEX_TIMED_NP                          = 0                    // pthread.h:155:1:
	PTHREAD_ONCE_INIT                               = 0                    // pthread.h:134:1:
	PTHREAD_PRIO_INHERIT                            = 8                    // pthread.h:148:1:
	PTHREAD_PRIO_MULT                               = 32                   // pthread.h:150:1:
	PTHREAD_PRIO_NONE                               = 0                    // pthread.h:147:1:
	PTHREAD_PRIO_PROTECT                            = 16                   // pthread.h:149:1:
	PTHREAD_PROCESS_PRIVATE                         = 0                    // pthread.h:152:1:
	PTHREAD_PROCESS_SHARED                          = 1                    // pthread.h:151:1:
	PTHREAD_SCOPE_PROCESS                           = 0                    // pthread.h:125:1:
	PTHREAD_SCOPE_SYSTEM                            = 0x10                 // pthread.h:126:1:
	PTHREAD_THREADS_MAX                             = 2019                 // pthread.h:437:1:
	P_DETACH                                        = 4                    // process.h:151:1:
	P_NOWAIT                                        = 1                    // process.h:147:1:
	P_NOWAITO                                       = 3                    // process.h:150:1:
	P_OVERLAY                                       = 2                    // process.h:148:1:
	P_WAIT                                          = 0                    // process.h:146:1:
	RWLS_PER_THREAD                                 = 8                    // pthread.h:98:1:
	SCHAR_MAX                                       = 127                  // limits.h:75:1:
	SCHAR_MIN                                       = -128                 // limits.h:73:1:
	SCHED_FIFO                                      = 1                    // pthread.h:234:1:
	SCHED_MAX                                       = 2                    // pthread.h:237:1:
	SCHED_MIN                                       = 0                    // pthread.h:236:1:
	SCHED_OTHER                                     = 0                    // pthread.h:233:1:
	SCHED_RR                                        = 2                    // pthread.h:235:1:
	SEM_NSEMS_MAX                                   = 1024                 // pthread.h:443:1:
	SHRT_MAX                                        = 32767                // limits.h:106:1:
	SHRT_MIN                                        = -32768               // limits.h:104:1:
	SIGABRT                                         = 22                   // signal.h:30:1:
	SIGABRT2                                        = 22                   // signal.h:31:1:
	SIGABRT_COMPAT                                  = 6                    // signal.h:25:1:
	SIGBREAK                                        = 21                   // signal.h:29:1:
	SIGFPE                                          = 8                    // signal.h:26:1:
	SIGILL                                          = 4                    // signal.h:24:1:
	SIGINT                                          = 2                    // signal.h:23:1:
	SIGSEGV                                         = 11                   // signal.h:27:1:
	SIGTERM                                         = 15                   // signal.h:28:1:
	SIG_BLOCK                                       = 0                    // pthread.h:419:1:
	SIG_SETMASK                                     = 2                    // pthread.h:425:1:
	SIG_UNBLOCK                                     = 1                    // pthread.h:422:1:
	SIZE_MAX                                        = 4294967295           // limits.h:78:1:
	SSIZE_MAX                                       = 2147483647           // limits.h:86:1:
	STRUNCATE                                       = 80                   // errno.h:67:1:
	UCHAR_MAX                                       = 255                  // limits.h:82:1:
	UINT_MAX                                        = 4294967295           // limits.h:124:1:
	ULLONG_MAX                                      = 18446744073709551615 // limits.h:146:1:
	ULONG_LONG_MAX                                  = 18446744073709551615 // limits.h:158:1:
	ULONG_MAX                                       = 4294967295           // limits.h:135:1:
	UNALIGNED                                       = 0                    // _mingw.h:384:1:
	USE___UUIDOF                                    = 0                    // _mingw.h:77:1:
	USHRT_MAX                                       = 65535                // limits.h:113:1:
	WAIT_CHILD                                      = 0                    // process.h:152:1:
	WAIT_GRANDCHILD                                 = 1                    // process.h:153:1:
	WIN32                                           = 1                    // <predefined>:258:1:
	WINNT                                           = 1                    // <predefined>:306:1:
	WINPTHREAD_API                                  = 0                    // pthread.h:92:1:
	WIN_PTHREADS_H                                  = 0                    // pthread.h:60:1:
	WIN_PTHREADS_PTHREAD_COMPAT_H                   = 0                    // pthread_compat.h:61:1:
	WIN_PTHREADS_SIGNAL_H                           = 0                    // pthread_signal.h:24:1:
	WIN_PTHREADS_UNISTD_H                           = 0                    // pthread_unistd.h:24:1:
	X_AGLOBAL                                       = 0                    // _mingw.h:346:1:
	X_ANONYMOUS_STRUCT                              = 0                    // _mingw.h:474:1:
	X_ANONYMOUS_UNION                               = 0                    // _mingw.h:473:1:
	X_ANSI_STDDEF_H                                 = 0                    // stddef.h:52:1:
	X_ARGMAX                                        = 100                  // _mingw.h:402:1:
	X_CONST_RETURN                                  = 0                    // _mingw.h:377:1:
	X_CRTNOALIAS                                    = 0                    // corecrt.h:29:1:
	X_CRTRESTRICT                                   = 0                    // corecrt.h:33:1:
	X_CRT_ALTERNATIVE_IMPORTED                      = 0                    // _mingw.h:313:1:
	X_CRT_ERRNO_DEFINED                             = 0                    // stddef.h:17:1:
	X_CRT_GETPID_DEFINED                            = 0                    // process.h:157:1:
	X_CRT_MANAGED_HEAP_DEPRECATE                    = 0                    // _mingw.h:361:1:
	X_CRT_PACKING                                   = 8                    // corecrt.h:14:1:
	X_CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES          = 0                    // _mingw_secapi.h:34:1:
	X_CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES_MEMORY   = 0                    // _mingw_secapi.h:35:1:
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES        = 0                    // _mingw_secapi.h:36:1:
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT  = 0                    // _mingw_secapi.h:37:1:
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_MEMORY = 0                    // _mingw_secapi.h:38:1:
	X_CRT_SYSTEM_DEFINED                            = 0                    // process.h:91:1:
	X_CRT_TERMINATE_DEFINED                         = 0                    // process.h:41:1:
	X_CRT_USE_WINAPI_FAMILY_DESKTOP_APP             = 0                    // corecrt.h:501:1:
	X_CRT_WSYSTEM_DEFINED                           = 0                    // process.h:120:1:
	X_DEV_T_DEFINED                                 = 0                    // types.h:50:1:
	X_DLL                                           = 0                    // _mingw.h:326:1:
	X_ERRCODE_DEFINED                               = 0                    // corecrt.h:117:1:
	X_FILE_OFFSET_BITS                              = 64                   // <builtin>:25:1:
	X_FILE_OFFSET_BITS_SET_OFFT                     = 0                    // _mingw_off_t.h:21:1:
	X_GCC_LIMITS_H_                                 = 0                    // limits.h:30:1:
	X_GCC_MAX_ALIGN_T                               = 0                    // stddef.h:419:1:
	X_GTHREAD_USE_MUTEX_INIT_FUNC                   = 1                    // pthread.h:686:1:
	X_I16_MAX                                       = 32767                // limits.h:54:1:
	X_I16_MIN                                       = -32768               // limits.h:53:1:
	X_I32_MAX                                       = 2147483647           // limits.h:58:1:
	X_I32_MIN                                       = -2147483648          // limits.h:57:1:
	X_I64_MAX                                       = 9223372036854775807  // limits.h:71:1:
	X_I64_MIN                                       = -9223372036854775808 // limits.h:70:1:
	X_I8_MAX                                        = 127                  // limits.h:50:1:
	X_I8_MIN                                        = -128                 // limits.h:49:1:
	X_ILP32                                         = 1                    // <predefined>:211:1:
	X_INC_CORECRT                                   = 0                    // corecrt.h:8:1:
	X_INC_CORECRT_STARTUP                           = 0                    // corecrt_startup.h:8:1:
	X_INC_CRTDEFS                                   = 0                    // crtdefs.h:8:1:
	X_INC_CRTDEFS_MACRO                             = 0                    // _mingw_mac.h:8:1:
	X_INC_ERRNO                                     = 0                    // errno.h:7:1:
	X_INC_LIMITS                                    = 0                    // limits.h:9:1:
	X_INC_MINGW_SECAPI                              = 0                    // _mingw_secapi.h:8:1:
	X_INC_PROCESS                                   = 0                    // process.h:7:1:
	X_INC_SIGNAL                                    = 0                    // signal.h:7:1:
	X_INC_STDDEF                                    = 0                    // stddef.h:10:1:
	X_INC_TYPES                                     = 0                    // types.h:7:1:
	X_INC_VADEFS                                    = 0                    // vadefs.h:7:1:
	X_INC__MINGW_H                                  = 0                    // _mingw.h:8:1:
	X_INO_T_DEFINED                                 = 0                    // types.h:42:1:
	X_INT128_DEFINED                                = 0                    // _mingw.h:237:1:
	X_INTEGRAL_MAX_BITS                             = 64                   // <predefined>:320:1:
	X_INTPTR_T_DEFINED                              = 0                    // corecrt.h:62:1:
	X_LIMITS_H___                                   = 0                    // limits.h:60:1:
	X_MODE_T_                                       = 0                    // types.h:73:1:
	X_MT                                            = 0                    // _mingw.h:330:1:
	X_M_IX86                                        = 600                  // _mingw_mac.h:54:1:
	X_OFF64_T_DEFINED                               = 0                    // _mingw_off_t.h:12:1:
	X_OFF_T_                                        = 0                    // _mingw_off_t.h:4:1:
	X_OFF_T_DEFINED                                 = 0                    // _mingw_off_t.h:2:1:
	X_OLD_P_OVERLAY                                 = 2                    // process.h:23:1:
	X_PGLOBAL                                       = 0                    // _mingw.h:342:1:
	X_PID_T_                                        = 0                    // types.h:58:1:
	X_POSIX_BARRIERS                                = 200112               // pthread_unistd.h:130:1:
	X_POSIX_CLOCK_SELECTION                         = 200112               // pthread_unistd.h:173:1:
	X_POSIX_READER_WRITER_LOCKS                     = 200112               // pthread_unistd.h:101:1:
	X_POSIX_SEMAPHORES                              = 200112               // pthread_unistd.h:190:1:
	X_POSIX_SEM_NSEMS_MAX                           = 256                  // pthread.h:440:1:
	X_POSIX_SPIN_LOCKS                              = 200112               // pthread_unistd.h:115:1:
	X_POSIX_THREADS                                 = 200112               // pthread_unistd.h:81:1:
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS            = 256                  // pthread.h:431:1:
	X_POSIX_THREAD_KEYS_MAX                         = 1048576              // pthread.h:434:1:
	X_POSIX_TIMEOUTS                                = 200112               // pthread_unistd.h:145:1:
	X_PTRDIFF_T_                                    = 0                    // corecrt.h:90:1:
	X_PTRDIFF_T_DEFINED                             = 0                    // corecrt.h:88:1:
	X_P_DETACH                                      = 4                    // process.h:25:1:
	X_P_NOWAIT                                      = 1                    // process.h:22:1:
	X_P_NOWAITO                                     = 3                    // process.h:24:1:
	X_P_OVERLAY                                     = 2                    // process.h:26:1:
	X_P_WAIT                                        = 0                    // process.h:21:1:
	X_RSIZE_T_DEFINED                               = 0                    // corecrt.h:58:1:
	X_SECURECRT_ERRCODE_VALUES_DEFINED              = 0                    // errno.h:63:1:
	X_SECURECRT_FILL_BUFFER_PATTERN                 = 0xFD                 // _mingw.h:349:1:
	X_SIGSET_T_                                     = 0                    // types.h:101:1:
	X_SIG_ATOMIC_T_DEFINED                          = 0                    // signal.h:17:1:
	X_SIZE_T_DEFINED                                = 0                    // corecrt.h:37:1:
	X_SPAWNV_DEFINED                                = 0                    // process.h:83:1:
	X_SSIZE_T_DEFINED                               = 0                    // corecrt.h:47:1:
	X_STDDEF_H                                      = 0                    // stddef.h:49:1:
	X_STDDEF_H_                                     = 0                    // stddef.h:50:1:
	X_TAGLC_ID_DEFINED                              = 0                    // corecrt.h:447:1:
	X_THREADLOCALEINFO                              = 0                    // corecrt.h:456:1:
	X_TIME32_T_DEFINED                              = 0                    // corecrt.h:122:1:
	X_TIME64_T_DEFINED                              = 0                    // corecrt.h:127:1:
	X_TIMEB_DEFINED                                 = 0                    // timeb.h:51:1:
	X_TIMEB_H_                                      = 0                    // timeb.h:7:1:
	X_TIMEB_H_S                                     = 0                    // timeb_s.h:8:1:
	X_TIMESPEC_DEFINED                              = 0                    // types.h:88:1:
	X_TIME_T_DEFINED                                = 0                    // corecrt.h:139:1:
	X_UI16_MAX                                      = 0xffff               // limits.h:55:1:
	X_UI32_MAX                                      = 0xffffffff           // limits.h:59:1:
	X_UI64_MAX                                      = 0xffffffffffffffff   // limits.h:72:1:
	X_UI8_MAX                                       = 0xff                 // limits.h:51:1:
	X_UINTPTR_T_DEFINED                             = 0                    // corecrt.h:75:1:
	X_USE_32BIT_TIME_T                              = 0                    // _mingw.h:372:1:
	X_VA_LIST_DEFINED                               = 0                    // <builtin>:55:1:
	X_W64                                           = 0                    // _mingw.h:296:1:
	X_WAIT_CHILD                                    = 0                    // process.h:28:1:
	X_WAIT_GRANDCHILD                               = 1                    // process.h:29:1:
	X_WCHAR_T_DEFINED                               = 0                    // corecrt.h:101:1:
	X_WCTYPE_T_DEFINED                              = 0                    // corecrt.h:108:1:
	X_WEXEC_DEFINED                                 = 0                    // process.h:96:1:
	X_WIN32                                         = 1                    // <predefined>:164:1:
	X_WIN32_WINNT                                   = 0x502                // _mingw.h:233:1:
	X_WINT_T                                        = 0                    // corecrt.h:110:1:
	X_WSPAWN_DEFINED                                = 0                    // process.h:108:1:
	X_X86_                                          = 1                    // <predefined>:169:1:
	I386                                            = 1                    // <predefined>:171:1:
)

type Ptrdiff_t = int32 /* <builtin>:3:26 */

type Size_t = uint32 /* <builtin>:9:23 */

type Wchar_t = uint16 /* <builtin>:15:24 */

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

type Va_list = X__builtin_va_list /* <builtin>:50:27 */

//
//    Copyright (c) 2011-2016 mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Parts of this library are derived by:
//
// Posix Threads library for Microsoft Windows
//
// Use at own risk, there is no implied warranty to this code.
// It uses undocumented features of Microsoft Windows that can change
// at any time in the future.
//
// (C) 2010 Lockless Inc.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//
//  * Redistributions of source code must retain the above copyright notice,
//    this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.
//  * Neither the name of Lockless Inc. nor the names of its contributors may be
//    used to endorse or promote products derived from this software without
//    specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AN
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
// INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
// BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
// LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
// OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
// OF THE POSSIBILITY OF SUCH DAMAGE.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// This macro holds an monotonic increasing value, which indicates
//    a specific fix/patch is present on trunk.  This value isn't related to
//    minor/major version-macros.  It is increased on demand, if a big
//    fix was applied to trunk.  This macro gets just increased on trunk.  For
//    other branches its value won't be modified.

// mingw.org's version macros: these make gcc to define
//    MINGW32_SUPPORTS_MT_EH and to use the _CRT_MT global
//    and the __mingwthr_key_dtor() function from the MinGW
//    CRT in its private gthr-win32.h header.

// Set VC specific compiler target macros.

// For x86 we have always to prefix by underscore.

// Special case nameless struct/union.

// MinGW-w64 has some additional C99 printf/scanf feature support.
//    So we add some helper macros to ease recognition of them.

// If _FORTIFY_SOURCE is enabled, some inline functions may use
//    __builtin_va_arg_pack().  GCC may report an error if the address
//    of such a function is used.  Set _FORTIFY_VA_ARG=0 in this case.

// Enable workaround for ABI incompatibility on affected platforms

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// http://msdn.microsoft.com/en-us/library/ms175759%28v=VS.100%29.aspx
// Templates won't work in C, will break if secure API is not enabled, disabled

// https://blogs.msdn.com/b/sdl/archive/2010/02/16/vc-2010-and-memcpy.aspx?Redirected=true
// fallback on default implementation if we can't know the size of the destination

// Include _cygwin.h if we're building a Cygwin application.

// Target specific macro replacement for type "long".  In the Windows API,
//    the type long is always 32 bit, even if the target is 64 bit (LLP64).
//    On 64 bit Cygwin, the type long is 64 bit (LP64).  So, to get the right
//    sized definitions and declarations, all usage of type long in the Windows
//    headers have to be replaced by the below defined macro __LONG32.

// C/C++ specific language defines.

// Note the extern. This is needed to work around GCC's
// limitations in handling dllimport attribute.

// Attribute `nonnull' was valid as of gcc 3.3.  We don't use GCC's
//    variadiac macro facility, because variadic macros cause syntax
//    errors with  --traditional-cpp.

//  High byte is the major version, low byte is the minor.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// for backward compatibility

type X__gnuc_va_list = X__builtin_va_list /* vadefs.h:24:29 */

type Ssize_t = int32 /* corecrt.h:52:13 */

type Rsize_t = Size_t /* corecrt.h:57:16 */

type Intptr_t = int32 /* corecrt.h:69:13 */

type Uintptr_t = uint32 /* corecrt.h:82:22 */

type Wint_t = uint16   /* corecrt.h:111:24 */
type Wctype_t = uint16 /* corecrt.h:112:24 */

type Errno_t = int32 /* corecrt.h:118:13 */

type X__time32_t = int32 /* corecrt.h:123:14 */

type X__time64_t = int64 /* corecrt.h:128:35 */

type Time_t = X__time32_t /* corecrt.h:141:20 */

type Threadlocaleinfostruct = struct {
	Frefcount      int32
	Flc_codepage   uint32
	Flc_collate_cp uint32
	Flc_handle     [6]uint32
	Flc_id         [6]LC_ID
	Flc_category   [6]struct {
		Flocale    uintptr
		Fwlocale   uintptr
		Frefcount  uintptr
		Fwrefcount uintptr
	}
	Flc_clike            int32
	Fmb_cur_max          int32
	Flconv_intl_refcount uintptr
	Flconv_num_refcount  uintptr
	Flconv_mon_refcount  uintptr
	Flconv               uintptr
	Fctype1_refcount     uintptr
	Fctype1              uintptr
	Fpctype              uintptr
	Fpclmap              uintptr
	Fpcumap              uintptr
	Flc_time_curr        uintptr
} /* corecrt.h:435:1 */

type Pthreadlocinfo = uintptr /* corecrt.h:437:39 */
type Pthreadmbcinfo = uintptr /* corecrt.h:438:36 */

type Localeinfo_struct = struct {
	Flocinfo Pthreadlocinfo
	Fmbcinfo Pthreadmbcinfo
} /* corecrt.h:441:9 */

type X_locale_tstruct = Localeinfo_struct /* corecrt.h:444:3 */
type X_locale_t = uintptr                 /* corecrt.h:444:19 */

type TagLC_ID = struct {
	FwLanguage uint16
	FwCountry  uint16
	FwCodePage uint16
} /* corecrt.h:435:1 */

type LC_ID = TagLC_ID  /* corecrt.h:452:3 */
type LPLC_ID = uintptr /* corecrt.h:452:9 */

type Threadlocinfo = Threadlocaleinfostruct /* corecrt.h:487:3 */

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.
// <EMAIL> says the NeXT needs this.
// Irix 5.1 needs this.

// In 4.3bsd-net2, machine/ansi.h defines these symbols, which are
//    defined if the corresponding type is *not* defined.
//    FreeBSD-2.1 defines _MACHINE_ANSI_H_ instead of _ANSI_H_

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// If this symbol has done its job, get rid of it.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

//  In 4.3bsd-net2, leave these undefined to indicate that size_t, etc.
//     are already defined.
//  BSD/OS 3.1 and FreeBSD [23].x require the MACHINE_ANSI_H check here.

// A null pointer constant.

// Offset of member MEMBER in a struct of type TYPE.

// Type whose alignment is supported in every context and is at least
//
//	as great as that of any standard type not using alignment
//	specifiers.
type Max_align_t = struct {
	F__max_align_ll int64
	F__max_align_ld float64
} /* stddef.h:427:3 */

// Copyright (C) 1989-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>
// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// Posix thread extensions.

// Extension defined as by report VC 10+ defines error-numbers.

// Defined as WSAETIMEDOUT.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X_ino_t = uint16 /* types.h:43:24 */
type Ino_t = uint16   /* types.h:45:24 */

type X_dev_t = uint32 /* types.h:51:22 */
type Dev_t = uint32   /* types.h:53:22 */

type X_pid_t = int32 /* types.h:60:13 */

type Pid_t = X_pid_t /* types.h:68:16 */

type X_mode_t = uint16 /* types.h:74:24 */

type Mode_t = X_mode_t /* types.h:77:17 */

type X_off_t = int32 /* _mingw_off_t.h:5:16 */
type Off32_t = int32 /* _mingw_off_t.h:7:16 */

type X_off64_t = int64 /* _mingw_off_t.h:13:39 */
type Off64_t = int64   /* _mingw_off_t.h:15:39 */

type Off_t = Off64_t /* _mingw_off_t.h:24:17 */

type Useconds_t = uint32 /* types.h:84:22 */

type Timespec = struct {
	Ftv_sec  Time_t
	Ftv_nsec int32
} /* types.h:89:1 */

type Itimerspec = struct {
	Fit_interval struct {
		Ftv_sec  Time_t
		Ftv_nsec int32
	}
	Fit_value struct {
		Ftv_sec  Time_t
		Ftv_nsec int32
	}
} /* types.h:94:1 */

type X_sigset_t = uint32 /* types.h:106:23 */

type X_PVFV = uintptr /* corecrt_startup.h:20:14 */
type X_PIFV = uintptr /* corecrt_startup.h:21:13 */
type X_PVFI = uintptr /* corecrt_startup.h:22:14 */

type X_onexit_table_t1 = struct {
	F_first uintptr
	F_last  uintptr
	F_end   uintptr
} /* corecrt_startup.h:24:9 */

type X_onexit_table_t = X_onexit_table_t1 /* corecrt_startup.h:28:3 */

type X_onexit_t = uintptr /* corecrt_startup.h:30:13 */

// Includes a definition of _pid_t and pid_t
// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X_beginthread_proc_type = uintptr   /* process.h:32:16 */
type X_beginthreadex_proc_type = uintptr /* process.h:33:20 */

type X_tls_callback_type = uintptr /* process.h:61:16 */

// Copyright (C) 1992-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// This administrivia gets added to the beginning of limits.h
//    if the system has its own version of limits.h.

// We use _GCC_LIMITS_H_ because we want this not to match
//    any macros that the system's limits.h uses for its own purposes.

// Use "..." so that we find syslimits.h only in this same directory.
// syslimits.h stands for the system's own limits.h file.
//    If we can use it ok unmodified, then we install this text.
//    If fixincludes fixes it, then the fixed version is installed
//    instead of this text.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.
// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// File system limits
//
// NOTE: Apparently the actual size of PATH_MAX is 260, but a space is
//       required for the NUL. TODO: Test?
// NOTE: PATH_MAX is the POSIX equivalent for Microsoft's MAX_PATH; the two
//       are semantically identical, with a limit of 259 characters for the
//       path name, plus one for a terminating NUL, for a total of 260.

// Copyright (C) 1991-2020 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// Number of bits in a `char'.

// Maximum length of a multibyte character.

// Minimum and maximum values a `signed char' can hold.

// Maximum value an `unsigned char' can hold.  (Minimum is 0).

// Minimum and maximum values a `char' can hold.

// Minimum and maximum values a `signed short int' can hold.

// Maximum value an `unsigned short int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed int' can hold.

// Maximum value an `unsigned int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long int' can hold.
//    (Same as `int').

// Maximum value an `unsigned long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// This administrivia gets added to the end of limits.h
//    if the system has its own version of limits.h.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

//
//    Copyright (c) 2013-2016  mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Windows has rudimentary signals support.

type Sig_atomic_t = int32 /* signal.h:18:15 */

type X__p_sig_fn_t = uintptr /* signal.h:48:16 */

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X__timeb32 = struct {
	Ftime        X__time32_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:53:3 */

type Timeb = struct {
	Ftime        Time_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:61:3 */

type X__timeb64 = struct {
	Ftime        X__time64_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:69:3 */

// maximum number of times a read lock may be obtained

// No fork() in windows - so ignore this

// unsupported stuff:

type Pthread_once_t = int32          /* pthread.h:180:14 */
type Pthread_mutexattr_t = uint32    /* pthread.h:181:18 */
type Pthread_key_t = uint32          /* pthread.h:182:18 */
type Pthread_barrierattr_t = uintptr /* pthread.h:183:14 */
type Pthread_condattr_t = int32      /* pthread.h:184:13 */
type Pthread_rwlockattr_t = int32    /* pthread.h:185:13 */

//
// struct _pthread_v;
//
// typedef struct pthread_t {
//   struct _pthread_v *p;
//   int x;
// } pthread_t;

type Pthread_t = Uintptr_t /* pthread.h:196:19 */

type X_pthread_cleanup1 = struct {
	Ffunc uintptr
	Farg  uintptr
	Fnext uintptr
} /* pthread.h:198:9 */

type X_pthread_cleanup = X_pthread_cleanup1 /* pthread.h:198:33 */

// Note that if async cancelling is used, then there is a race here

// Windows doesn't have this, so declare it ourselves.

// Some POSIX realtime extensions, mostly stubbed

type Sched_param = struct{ Fsched_priority int32 } /* pthread.h:239:1 */

type Pthread_attr_t1 = struct {
	Fp_state uint32
	Fstack   uintptr
	Fs_size  Size_t
	Fparam   struct{ Fsched_priority int32 }
} /* pthread.h:251:9 */

type Pthread_attr_t = Pthread_attr_t1 /* pthread.h:251:31 */

// synchronization objects
type Pthread_spinlock_t = Intptr_t /* pthread.h:268:18 */
type Pthread_mutex_t = Intptr_t    /* pthread.h:269:18 */
type Pthread_cond_t = Intptr_t     /* pthread.h:270:18 */
type Pthread_rwlock_t = Intptr_t   /* pthread.h:271:18 */
type Pthread_barrier_t = uintptr   /* pthread.h:272:14 */

type Clockid_t = int32 /* pthread.h:389:13 */

//
//    Copyright (c) 2011-2016  mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Set defines described by the POSIX Threads Extension (1003.1c-1995)
// _SC_THREADS
//   Basic support for POSIX threads is available. The functions
//
//   pthread_atfork(),
//   pthread_attr_destroy(),
//   pthread_attr_getdetachstate(),
//   pthread_attr_getschedparam(),
//   pthread_attr_init(),
//   pthread_attr_setdetachstate(),
//   pthread_attr_setschedparam(),
//   pthread_cancel(),
//   pthread_cleanup_push(),
//   pthread_cleanup_pop(),
//   pthread_cond_broadcast(),
//   pthread_cond_destroy(),
//   pthread_cond_init(),
//   pthread_cond_signal(),
//   pthread_cond_timedwait(),
//   pthread_cond_wait(),
//   pthread_condattr_destroy(),
//   pthread_condattr_init(),
//   pthread_create(),
//   pthread_detach(),
//   pthread_equal(),
//   pthread_exit(),
//   pthread_getspecific(),
//   pthread_join(,
//   pthread_key_create(),
//   pthread_key_delete(),
//   pthread_mutex_destroy(),
//   pthread_mutex_init(),
//   pthread_mutex_lock(),
//   pthread_mutex_trylock(),
//   pthread_mutex_unlock(),
//   pthread_mutexattr_destroy(),
//   pthread_mutexattr_init(),
//   pthread_once(),
//   pthread_rwlock_destroy(),
//   pthread_rwlock_init(),
//   pthread_rwlock_rdlock(),
//   pthread_rwlock_tryrdlock(),
//   pthread_rwlock_trywrlock(),
//   pthread_rwlock_unlock(),
//   pthread_rwlock_wrlock(),
//   pthread_rwlockattr_destroy(),
//   pthread_rwlockattr_init(),
//   pthread_self(),
//   pthread_setcancelstate(),
//   pthread_setcanceltype(),
//   pthread_setspecific(),
//   pthread_testcancel()
//
//   are present.

// _SC_READER_WRITER_LOCKS
//   This option implies the _POSIX_THREADS option. Conversely, under
//   POSIX 1003.1-2001 the _POSIX_THREADS option implies this option.
//
//   The functions
//   pthread_rwlock_destroy(),
//   pthread_rwlock_init(),
//   pthread_rwlock_rdlock(),
//   pthread_rwlock_tryrdlock(),
//   pthread_rwlock_trywrlock(),
//   pthread_rwlock_unlock(),
//   pthread_rwlock_wrlock(),
//   pthread_rwlockattr_destroy(),
//   pthread_rwlockattr_init()
//
//   are present.

// _SC_SPIN_LOCKS
//   This option implies the _POSIX_THREADS and _POSIX_THREAD_SAFE_FUNCTIONS
//   options. The functions
//
//   pthread_spin_destroy(),
//   pthread_spin_init(),
//   pthread_spin_lock(),
//   pthread_spin_trylock(),
//   pthread_spin_unlock()
//
//   are present.

// _SC_BARRIERS
//   This option implies the _POSIX_THREADS and _POSIX_THREAD_SAFE_FUNCTIONS
//   options. The functions
//
//   pthread_barrier_destroy(),
//   pthread_barrier_init(),
//   pthread_barrier_wait(),
//   pthread_barrierattr_destroy(),
//   pthread_barrierattr_init()
//
//   are present.

// _SC_TIMEOUTS
//   The functions
//
//   mq_timedreceive(), - not supported
//   mq_timedsend(), - not supported
//   posix_trace_timedgetnext_event(), - not supported
//   pthread_mutex_timedlock(),
//   pthread_rwlock_timedrdlock(),
//   pthread_rwlock_timedwrlock(),
//   sem_timedwait(),
//
//   are present.

// _SC_TIMERS - not supported
//   The functions
//
//   clock_getres(),
//   clock_gettime(),
//   clock_settime(),
//   nanosleep(),
//   timer_create(),
//   timer_delete(),
//   timer_gettime(),
//   timer_getoverrun(),
//   timer_settime()
//
//   are present.
// #undef _POSIX_TIMERS

// _SC_CLOCK_SELECTION
//    This option implies the _POSIX_TIMERS option. The functions
//
//    pthread_condattr_getclock(),
//    pthread_condattr_setclock(),
//    clock_nanosleep()
//
//    are present.

// _SC_SEMAPHORES
//   The include file <semaphore.h> is present. The functions
//
//   sem_close(),
//   sem_destroy(),
//   sem_getvalue(),
//   sem_init(),
//   sem_open(),
//   sem_post(),
//   sem_trywait(),
//   sem_unlink(),
//   sem_wait()
//
//   are present.

// Wrap cancellation points.

// We deal here with a gcc issue for posix threading on Windows.
//    We would need to change here gcc's gthr-posix.h header, but this
//    got rejected.  So we deal it within this header.

var _ int8 /* gen.c:2:13: */
