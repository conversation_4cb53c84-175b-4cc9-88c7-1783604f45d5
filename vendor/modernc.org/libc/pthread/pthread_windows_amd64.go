// Code generated by 'ccgo pthread\gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -o pthread\pthread_windows_amd64.go -pkgname pthread', DO NOT EDIT.

package pthread

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	CHAR_BIT                                        = 8
	CHAR_MAX                                        = 127
	CHAR_MIN                                        = -128
	DUMMYSTRUCTNAME                                 = 0
	DUMMYSTRUCTNAME1                                = 0
	DUMMYSTRUCTNAME2                                = 0
	DUMMYSTRUCTNAME3                                = 0
	DUMMYSTRUCTNAME4                                = 0
	DUMMYSTRUCTNAME5                                = 0
	DUMMYUNIONNAME                                  = 0
	DUMMYUNIONNAME1                                 = 0
	DUMMYUNIONNAME2                                 = 0
	DUMMYUNIONNAME3                                 = 0
	DUMMYUNIONNAME4                                 = 0
	DUMMYUNIONNAME5                                 = 0
	DUMMYUNIONNAME6                                 = 0
	DUMMYUNIONNAME7                                 = 0
	DUMMYUNIONNAME8                                 = 0
	DUMMYUNIONNAME9                                 = 0
	E2BIG                                           = 7
	EACCES                                          = 13
	EADDRINUSE                                      = 100
	EADDRNOTAVAIL                                   = 101
	EAFNOSUPPORT                                    = 102
	EAGAIN                                          = 11
	EALREADY                                        = 103
	EBADF                                           = 9
	EBADMSG                                         = 104
	EBUSY                                           = 16
	ECANCELED                                       = 105
	ECHILD                                          = 10
	ECONNABORTED                                    = 106
	ECONNREFUSED                                    = 107
	ECONNRESET                                      = 108
	EDEADLK                                         = 36
	EDEADLOCK                                       = 36
	EDESTADDRREQ                                    = 109
	EDOM                                            = 33
	EEXIST                                          = 17
	EFAULT                                          = 14
	EFBIG                                           = 27
	EHOSTUNREACH                                    = 110
	EIDRM                                           = 111
	EILSEQ                                          = 42
	EINPROGRESS                                     = 112
	EINTR                                           = 4
	EINVAL                                          = 22
	EIO                                             = 5
	EISCONN                                         = 113
	EISDIR                                          = 21
	ELOOP                                           = 114
	EMFILE                                          = 24
	EMLINK                                          = 31
	EMSGSIZE                                        = 115
	ENAMETOOLONG                                    = 38
	ENETDOWN                                        = 116
	ENETRESET                                       = 117
	ENETUNREACH                                     = 118
	ENFILE                                          = 23
	ENOBUFS                                         = 119
	ENODATA                                         = 120
	ENODEV                                          = 19
	ENOENT                                          = 2
	ENOEXEC                                         = 8
	ENOFILE                                         = 2
	ENOLCK                                          = 39
	ENOLINK                                         = 121
	ENOMEM                                          = 12
	ENOMSG                                          = 122
	ENOPROTOOPT                                     = 123
	ENOSPC                                          = 28
	ENOSR                                           = 124
	ENOSTR                                          = 125
	ENOSYS                                          = 40
	ENOTCONN                                        = 126
	ENOTDIR                                         = 20
	ENOTEMPTY                                       = 41
	ENOTRECOVERABLE                                 = 127
	ENOTSOCK                                        = 128
	ENOTSUP                                         = 129
	ENOTTY                                          = 25
	ENXIO                                           = 6
	EOPNOTSUPP                                      = 130
	EOVERFLOW                                       = 132
	EOWNERDEAD                                      = 133
	EPERM                                           = 1
	EPIPE                                           = 32
	EPROTO                                          = 134
	EPROTONOSUPPORT                                 = 135
	EPROTOTYPE                                      = 136
	ERANGE                                          = 34
	EROFS                                           = 30
	ESPIPE                                          = 29
	ESRCH                                           = 3
	ETIME                                           = 137
	ETIMEDOUT                                       = 138
	ETXTBSY                                         = 139
	EWOULDBLOCK                                     = 140
	EXDEV                                           = 18
	INT_MAX                                         = 2147483647
	INT_MIN                                         = -2147483648
	LLONG_MAX                                       = 9223372036854775807
	LLONG_MIN                                       = -9223372036854775808
	LONG_LONG_MAX                                   = 9223372036854775807
	LONG_LONG_MIN                                   = -9223372036854775808
	LONG_MAX                                        = 2147483647
	LONG_MIN                                        = -2147483648
	MAX_READ_LOCKS                                  = 2147483646
	MB_LEN_MAX                                      = 5
	MINGW_DDK_H                                     = 0
	MINGW_DDRAW_VERSION                             = 7
	MINGW_HAS_DDK_H                                 = 1
	MINGW_HAS_DDRAW_H                               = 1
	MINGW_HAS_SECURE_API                            = 1
	MINGW_SDK_INIT                                  = 0
	NSIG                                            = 23
	OLD_P_OVERLAY                                   = 2
	PATH_MAX                                        = 260
	PTHREAD_BARRIER_SERIAL_THREAD                   = 1
	PTHREAD_CANCEL_ASYNCHRONOUS                     = 0x02
	PTHREAD_CANCEL_DEFERRED                         = 0
	PTHREAD_CANCEL_DISABLE                          = 0
	PTHREAD_CANCEL_ENABLE                           = 0x01
	PTHREAD_CREATE_DETACHED                         = 0x04
	PTHREAD_CREATE_JOINABLE                         = 0
	PTHREAD_DEFAULT_ATTR                            = 1
	PTHREAD_DESTRUCTOR_ITERATIONS                   = 256
	PTHREAD_EXPLICIT_SCHED                          = 0
	PTHREAD_INHERIT_SCHED                           = 0x08
	PTHREAD_KEYS_MAX                                = 1048576
	PTHREAD_MUTEX_ADAPTIVE_NP                       = 0
	PTHREAD_MUTEX_DEFAULT                           = 0
	PTHREAD_MUTEX_ERRORCHECK                        = 1
	PTHREAD_MUTEX_ERRORCHECK_NP                     = 1
	PTHREAD_MUTEX_FAST_NP                           = 0
	PTHREAD_MUTEX_NORMAL                            = 0
	PTHREAD_MUTEX_PRIVATE                           = 0
	PTHREAD_MUTEX_RECURSIVE                         = 2
	PTHREAD_MUTEX_RECURSIVE_NP                      = 2
	PTHREAD_MUTEX_SHARED                            = 1
	PTHREAD_MUTEX_TIMED_NP                          = 0
	PTHREAD_ONCE_INIT                               = 0
	PTHREAD_PRIO_INHERIT                            = 8
	PTHREAD_PRIO_MULT                               = 32
	PTHREAD_PRIO_NONE                               = 0
	PTHREAD_PRIO_PROTECT                            = 16
	PTHREAD_PROCESS_PRIVATE                         = 0
	PTHREAD_PROCESS_SHARED                          = 1
	PTHREAD_SCOPE_PROCESS                           = 0
	PTHREAD_SCOPE_SYSTEM                            = 0x10
	PTHREAD_THREADS_MAX                             = 2019
	P_DETACH                                        = 4
	P_NOWAIT                                        = 1
	P_NOWAITO                                       = 3
	P_OVERLAY                                       = 2
	P_WAIT                                          = 0
	RWLS_PER_THREAD                                 = 8
	SCHAR_MAX                                       = 127
	SCHAR_MIN                                       = -128
	SCHED_FIFO                                      = 1
	SCHED_MAX                                       = 2
	SCHED_MIN                                       = 0
	SCHED_OTHER                                     = 0
	SCHED_RR                                        = 2
	SEM_NSEMS_MAX                                   = 1024
	SHRT_MAX                                        = 32767
	SHRT_MIN                                        = -32768
	SIGABRT                                         = 22
	SIGABRT2                                        = 22
	SIGABRT_COMPAT                                  = 6
	SIGBREAK                                        = 21
	SIGFPE                                          = 8
	SIGILL                                          = 4
	SIGINT                                          = 2
	SIGSEGV                                         = 11
	SIGTERM                                         = 15
	SIG_BLOCK                                       = 0
	SIG_SETMASK                                     = 2
	SIG_UNBLOCK                                     = 1
	SIZE_MAX                                        = 18446744073709551615
	SSIZE_MAX                                       = 9223372036854775807
	STRUNCATE                                       = 80
	UCHAR_MAX                                       = 255
	UINT_MAX                                        = 4294967295
	ULLONG_MAX                                      = 18446744073709551615
	ULONG_LONG_MAX                                  = 18446744073709551615
	ULONG_MAX                                       = 4294967295
	UNALIGNED                                       = 0
	USE___UUIDOF                                    = 0
	USHRT_MAX                                       = 65535
	WAIT_CHILD                                      = 0
	WAIT_GRANDCHILD                                 = 1
	WIN32                                           = 1
	WIN64                                           = 1
	WINNT                                           = 1
	WINPTHREAD_API                                  = 0
	WIN_PTHREADS_H                                  = 0
	WIN_PTHREADS_PTHREAD_COMPAT_H                   = 0
	WIN_PTHREADS_SIGNAL_H                           = 0
	WIN_PTHREADS_UNISTD_H                           = 0
	X_AGLOBAL                                       = 0
	X_ANONYMOUS_STRUCT                              = 0
	X_ANONYMOUS_UNION                               = 0
	X_ANSI_STDDEF_H                                 = 0
	X_ARGMAX                                        = 100
	X_CONST_RETURN                                  = 0
	X_CRTNOALIAS                                    = 0
	X_CRTRESTRICT                                   = 0
	X_CRT_ALTERNATIVE_IMPORTED                      = 0
	X_CRT_ERRNO_DEFINED                             = 0
	X_CRT_GETPID_DEFINED                            = 0
	X_CRT_MANAGED_HEAP_DEPRECATE                    = 0
	X_CRT_PACKING                                   = 8
	X_CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES          = 0
	X_CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES_MEMORY   = 0
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES        = 0
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT  = 0
	X_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_MEMORY = 0
	X_CRT_SYSTEM_DEFINED                            = 0
	X_CRT_TERMINATE_DEFINED                         = 0
	X_CRT_WSYSTEM_DEFINED                           = 0
	X_DEV_T_DEFINED                                 = 0
	X_DLL                                           = 0
	X_ERRCODE_DEFINED                               = 0
	X_FILE_OFFSET_BITS                              = 64
	X_FILE_OFFSET_BITS_SET_OFFT                     = 0
	X_GCC_LIMITS_H_                                 = 0
	X_GCC_MAX_ALIGN_T                               = 0
	X_GTHREAD_USE_MUTEX_INIT_FUNC                   = 1
	X_I16_MAX                                       = 32767
	X_I16_MIN                                       = -32768
	X_I32_MAX                                       = 2147483647
	X_I32_MIN                                       = -2147483648
	X_I64_MAX                                       = 9223372036854775807
	X_I64_MIN                                       = -9223372036854775808
	X_I8_MAX                                        = 127
	X_I8_MIN                                        = -128
	X_INC_CORECRT_STARTUP                           = 0
	X_INC_CRTDEFS                                   = 0
	X_INC_CRTDEFS_MACRO                             = 0
	X_INC_ERRNO                                     = 0
	X_INC_LIMITS                                    = 0
	X_INC_MINGW_SECAPI                              = 0
	X_INC_PROCESS                                   = 0
	X_INC_SIGNAL                                    = 0
	X_INC_STDDEF                                    = 0
	X_INC_TYPES                                     = 0
	X_INC_VADEFS                                    = 0
	X_INC__MINGW_H                                  = 0
	X_INO_T_DEFINED                                 = 0
	X_INT128_DEFINED                                = 0
	X_INTEGRAL_MAX_BITS                             = 64
	X_INTPTR_T_DEFINED                              = 0
	X_LIMITS_H___                                   = 0
	X_MODE_T_                                       = 0
	X_MT                                            = 0
	X_M_AMD64                                       = 100
	X_M_X64                                         = 100
	X_OFF64_T_DEFINED                               = 0
	X_OFF_T_                                        = 0
	X_OFF_T_DEFINED                                 = 0
	X_OLD_P_OVERLAY                                 = 2
	X_PGLOBAL                                       = 0
	X_PID_T_                                        = 0
	X_POSIX_BARRIERS                                = 200112
	X_POSIX_CLOCK_SELECTION                         = 200112
	X_POSIX_READER_WRITER_LOCKS                     = 200112
	X_POSIX_SEMAPHORES                              = 200112
	X_POSIX_SEM_NSEMS_MAX                           = 256
	X_POSIX_SPIN_LOCKS                              = 200112
	X_POSIX_THREADS                                 = 200112
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS            = 256
	X_POSIX_THREAD_KEYS_MAX                         = 1048576
	X_POSIX_THREAD_SAFE_FUNCTIONS                   = 200112
	X_POSIX_TIMEOUTS                                = 200112
	X_PTRDIFF_T_                                    = 0
	X_PTRDIFF_T_DEFINED                             = 0
	X_P_DETACH                                      = 4
	X_P_NOWAIT                                      = 1
	X_P_NOWAITO                                     = 3
	X_P_OVERLAY                                     = 2
	X_P_WAIT                                        = 0
	X_REENTRANT                                     = 1
	X_RSIZE_T_DEFINED                               = 0
	X_SECURECRT_ERRCODE_VALUES_DEFINED              = 0
	X_SECURECRT_FILL_BUFFER_PATTERN                 = 0xFD
	X_SIGSET_T_                                     = 0
	X_SIG_ATOMIC_T_DEFINED                          = 0
	X_SIZE_T_DEFINED                                = 0
	X_SPAWNV_DEFINED                                = 0
	X_SSIZE_T_DEFINED                               = 0
	X_STDDEF_H                                      = 0
	X_STDDEF_H_                                     = 0
	X_TAGLC_ID_DEFINED                              = 0
	X_THREADLOCALEINFO                              = 0
	X_TIME32_T_DEFINED                              = 0
	X_TIME64_T_DEFINED                              = 0
	X_TIMEB_DEFINED                                 = 0
	X_TIMEB_H_                                      = 0
	X_TIMEB_H_S                                     = 0
	X_TIMESPEC_DEFINED                              = 0
	X_TIME_T_DEFINED                                = 0
	X_UI16_MAX                                      = 0xffff
	X_UI32_MAX                                      = 0xffffffff
	X_UI64_MAX                                      = 0xffffffffffffffff
	X_UI8_MAX                                       = 0xff
	X_UINTPTR_T_DEFINED                             = 0
	X_VA_LIST_DEFINED                               = 0
	X_W64                                           = 0
	X_WAIT_CHILD                                    = 0
	X_WAIT_GRANDCHILD                               = 1
	X_WCHAR_T_DEFINED                               = 0
	X_WCTYPE_T_DEFINED                              = 0
	X_WEXEC_DEFINED                                 = 0
	X_WIN32                                         = 1
	X_WIN32_WINNT                                   = 0x502
	X_WIN64                                         = 1
	X_WINT_T                                        = 0
	X_WSPAWN_DEFINED                                = 0
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = uint16 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

type Va_list = X__builtin_va_list /* <builtin>:50:27 */

//
//    Copyright (c) 2011-2016 mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Parts of this library are derived by:
//
// Posix Threads library for Microsoft Windows
//
// Use at own risk, there is no implied warranty to this code.
// It uses undocumented features of Microsoft Windows that can change
// at any time in the future.
//
// (C) 2010 Lockless Inc.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//
//  * Redistributions of source code must retain the above copyright notice,
//    this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.
//  * Neither the name of Lockless Inc. nor the names of its contributors may be
//    used to endorse or promote products derived from this software without
//    specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AN
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
// INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
// BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
// LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
// OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
// OF THE POSSIBILITY OF SUCH DAMAGE.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// This macro holds an monotonic increasing value, which indicates
//    a specific fix/patch is present on trunk.  This value isn't related to
//    minor/major version-macros.  It is increased on demand, if a big
//    fix was applied to trunk.  This macro gets just increased on trunk.  For
//    other branches its value won't be modified.

// mingw.org's version macros: these make gcc to define
//    MINGW32_SUPPORTS_MT_EH and to use the _CRT_MT global
//    and the __mingwthr_key_dtor() function from the MinGW
//    CRT in its private gthr-win32.h header.

// Set VC specific compiler target macros.

// MS does not prefix symbols by underscores for 64-bit.
// As we have to support older gcc version, which are using underscores
//       as symbol prefix for x64, we have to check here for the user label
//       prefix defined by gcc.

// Special case nameless struct/union.

// MinGW-w64 has some additional C99 printf/scanf feature support.
//    So we add some helper macros to ease recognition of them.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// http://msdn.microsoft.com/en-us/library/ms175759%28v=VS.100%29.aspx
// Templates won't work in C, will break if secure API is not enabled, disabled

// https://blogs.msdn.com/b/sdl/archive/2010/02/16/vc-2010-and-memcpy.aspx?Redirected=true
// fallback on default implementation if we can't know the size of the destination

// Include _cygwin.h if we're building a Cygwin application.

// Target specific macro replacement for type "long".  In the Windows API,
//    the type long is always 32 bit, even if the target is 64 bit (LLP64).
//    On 64 bit Cygwin, the type long is 64 bit (LP64).  So, to get the right
//    sized definitions and declarations, all usage of type long in the Windows
//    headers have to be replaced by the below defined macro __LONG32.

// C/C++ specific language defines.

// Note the extern. This is needed to work around GCC's
// limitations in handling dllimport attribute.

// Attribute `nonnull' was valid as of gcc 3.3.  We don't use GCC's
//    variadiac macro facility, because variadic macros cause syntax
//    errors with  --traditional-cpp.

//  High byte is the major version, low byte is the minor.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X__gnuc_va_list = X__builtin_va_list /* vadefs.h:24:29 */

type Ssize_t = int64 /* crtdefs.h:45:35 */

type Rsize_t = Size_t /* crtdefs.h:52:16 */

type Intptr_t = int64 /* crtdefs.h:62:35 */

type Uintptr_t = uint64 /* crtdefs.h:75:44 */

type Wint_t = uint16   /* crtdefs.h:106:24 */
type Wctype_t = uint16 /* crtdefs.h:107:24 */

type Errno_t = int32 /* crtdefs.h:113:13 */

type X__time32_t = int32 /* crtdefs.h:118:14 */

type X__time64_t = int64 /* crtdefs.h:123:35 */

type Time_t = X__time64_t /* crtdefs.h:138:20 */

type Threadlocaleinfostruct = struct {
	Frefcount      int32
	Flc_codepage   uint32
	Flc_collate_cp uint32
	Flc_handle     [6]uint32
	Flc_id         [6]LC_ID
	Flc_category   [6]struct {
		Flocale    uintptr
		Fwlocale   uintptr
		Frefcount  uintptr
		Fwrefcount uintptr
	}
	Flc_clike            int32
	Fmb_cur_max          int32
	Flconv_intl_refcount uintptr
	Flconv_num_refcount  uintptr
	Flconv_mon_refcount  uintptr
	Flconv               uintptr
	Fctype1_refcount     uintptr
	Fctype1              uintptr
	Fpctype              uintptr
	Fpclmap              uintptr
	Fpcumap              uintptr
	Flc_time_curr        uintptr
} /* crtdefs.h:422:1 */

type Pthreadlocinfo = uintptr /* crtdefs.h:424:39 */
type Pthreadmbcinfo = uintptr /* crtdefs.h:425:36 */

type Localeinfo_struct = struct {
	Flocinfo Pthreadlocinfo
	Fmbcinfo Pthreadmbcinfo
} /* crtdefs.h:428:9 */

type X_locale_tstruct = Localeinfo_struct /* crtdefs.h:431:3 */
type X_locale_t = uintptr                 /* crtdefs.h:431:19 */

type TagLC_ID = struct {
	FwLanguage uint16
	FwCountry  uint16
	FwCodePage uint16
} /* crtdefs.h:422:1 */

type LC_ID = TagLC_ID  /* crtdefs.h:439:3 */
type LPLC_ID = uintptr /* crtdefs.h:439:9 */

type Threadlocinfo = Threadlocaleinfostruct /* crtdefs.h:468:3 */

// ISO C Standard:  7.17  Common definitions  <stddef.h>

// Any one of these symbols __need_* means that GNU libc
//    wants us just to define one data type.  So don't define
//    the symbols that indicate this file's entire job has been done.
// <EMAIL> says the NeXT needs this.
// Irix 5.1 needs this.

// In 4.3bsd-net2, machine/ansi.h defines these symbols, which are
//    defined if the corresponding type is *not* defined.
//    FreeBSD-2.1 defines _MACHINE_ANSI_H_ instead of _ANSI_H_

// Sequent's header files use _PTRDIFF_T_ in some conflicting way.
//    Just ignore it.

// On VxWorks, <type/vxTypesBase.h> may have defined macros like
//    _TYPE_size_t which will typedef size_t.  fixincludes patched the
//    vxTypesBase.h so that this macro is only defined if _GCC_SIZE_T is
//    not defined, and so that defining this macro defines _GCC_SIZE_T.
//    If we find that the macros are still defined at this point, we must
//    invoke them so that the type is defined as expected.

// In case nobody has defined these types, but we aren't running under
//    GCC 2.00, make sure that __PTRDIFF_TYPE__, __SIZE_TYPE__, and
//    __WCHAR_TYPE__ have reasonable values.  This can happen if the
//    parts of GCC is compiled by an older compiler, that actually
//    include gstddef.h, such as collect2.

// Signed type of difference of two pointers.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// If this symbol has done its job, get rid of it.

// Unsigned type of `sizeof' something.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

// Wide character type.
//    Locale-writers should change this as necessary to
//    be big enough to hold unique values not between 0 and 127,
//    and not (wchar_t) -1, for each defined multibyte character.

// Define this type if we are doing the whole job,
//    or if we want this type in particular.

//  In 4.3bsd-net2, leave these undefined to indicate that size_t, etc.
//     are already defined.
//  BSD/OS 3.1 and FreeBSD [23].x require the MACHINE_ANSI_H check here.

// A null pointer constant.

// Offset of member MEMBER in a struct of type TYPE.

// Type whose alignment is supported in every context and is at least
//
//	as great as that of any standard type not using alignment
//	specifiers.
type Max_align_t = struct {
	F__max_align_ll int64
	F__max_align_ld float64
} /* stddef.h:426:3 */

// Copyright (C) 1989-2018 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation; either version 3, or (at your option)
// any later version.
//
// GCC is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C Standard:  7.17  Common definitions  <stddef.h>
// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// Posix thread extensions.

// Extension defined as by report VC 10+ defines error-numbers.

// Defined as WSAETIMEDOUT.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X_ino_t = uint16 /* types.h:43:24 */
type Ino_t = uint16   /* types.h:45:24 */

type X_dev_t = uint32 /* types.h:51:22 */
type Dev_t = uint32   /* types.h:53:22 */

type X_pid_t = int64 /* types.h:63:17 */

type Pid_t = X_pid_t /* types.h:68:16 */

type X_mode_t = uint16 /* types.h:74:24 */

type Mode_t = X_mode_t /* types.h:77:17 */

type X_off_t = int32 /* _mingw_off_t.h:5:16 */
type Off32_t = int32 /* _mingw_off_t.h:7:16 */

type X_off64_t = int64 /* _mingw_off_t.h:13:39 */
type Off64_t = int64   /* _mingw_off_t.h:15:39 */

type Off_t = Off64_t /* _mingw_off_t.h:24:17 */

type Useconds_t = uint32 /* types.h:84:22 */

type Timespec = struct {
	Ftv_sec      Time_t
	Ftv_nsec     int32
	F__ccgo_pad1 [4]byte
} /* types.h:89:1 */

type Itimerspec = struct {
	Fit_interval struct {
		Ftv_sec      Time_t
		Ftv_nsec     int32
		F__ccgo_pad1 [4]byte
	}
	Fit_value struct {
		Ftv_sec      Time_t
		Ftv_nsec     int32
		F__ccgo_pad1 [4]byte
	}
} /* types.h:94:1 */

type X_sigset_t = uint64 /* types.h:104:28 */

type X_PVFV = uintptr /* corecrt_startup.h:20:14 */
type X_PIFV = uintptr /* corecrt_startup.h:21:13 */
type X_PVFI = uintptr /* corecrt_startup.h:22:14 */

type X_onexit_table_t1 = struct {
	F_first uintptr
	F_last  uintptr
	F_end   uintptr
} /* corecrt_startup.h:24:9 */

type X_onexit_table_t = X_onexit_table_t1 /* corecrt_startup.h:28:3 */

type X_onexit_t = uintptr /* corecrt_startup.h:30:13 */

// Copyright (C) 1992-2018 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// This administrivia gets added to the beginning of limits.h
//    if the system has its own version of limits.h.

// We use _GCC_LIMITS_H_ because we want this not to match
//    any macros that the system's limits.h uses for its own purposes.

// Use "..." so that we find syslimits.h only in this same directory.
// syslimits.h stands for the system's own limits.h file.
//    If we can use it ok unmodified, then we install this text.
//    If fixincludes fixes it, then the fixed version is installed
//    instead of this text.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.
// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// File system limits
//
// NOTE: Apparently the actual size of PATH_MAX is 260, but a space is
//       required for the NUL. TODO: Test?
// NOTE: PATH_MAX is the POSIX equivalent for Microsoft's MAX_PATH; the two
//       are semantically identical, with a limit of 259 characters for the
//       path name, plus one for a terminating NUL, for a total of 260.

// Copyright (C) 1991-2018 Free Software Foundation, Inc.
//
// This file is part of GCC.
//
// GCC is free software; you can redistribute it and/or modify it under
// the terms of the GNU General Public License as published by the Free
// Software Foundation; either version 3, or (at your option) any later
// version.
//
// GCC is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or
// FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
// for more details.
//
// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.
//
// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// Number of bits in a `char'.

// Maximum length of a multibyte character.

// Minimum and maximum values a `signed char' can hold.

// Maximum value an `unsigned char' can hold.  (Minimum is 0).

// Minimum and maximum values a `char' can hold.

// Minimum and maximum values a `signed short int' can hold.

// Maximum value an `unsigned short int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed int' can hold.

// Maximum value an `unsigned int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long int' can hold.
//    (Same as `int').

// Maximum value an `unsigned long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// Minimum and maximum values a `signed long long int' can hold.

// Maximum value an `unsigned long long int' can hold.  (Minimum is 0).

// This administrivia gets added to the end of limits.h
//    if the system has its own version of limits.h.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

//
//    Copyright (c) 2013-2016  mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Windows has rudimentary signals support.

type Sig_atomic_t = int32 /* signal.h:18:15 */

type X__p_sig_fn_t = uintptr /* signal.h:48:16 */

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

// *
// This file has no copyright assigned and is placed in the Public Domain.
// This file is part of the mingw-w64 runtime package.
// No warranty is given; refer to the file DISCLAIMER.PD within this package.

type X__timeb32 = struct {
	Ftime        X__time32_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:53:3 */

type Timeb = struct {
	Ftime        Time_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:61:3 */

type X__timeb64 = struct {
	Ftime        X__time64_t
	Fmillitm     uint16
	Ftimezone    int16
	Fdstflag     int16
	F__ccgo_pad1 [2]byte
} /* timeb.h:69:3 */

// maximum number of times a read lock may be obtained

// No fork() in windows - so ignore this

// unsupported stuff:

type Pthread_once_t = int32          /* pthread.h:180:14 */
type Pthread_mutexattr_t = uint32    /* pthread.h:181:18 */
type Pthread_key_t = uint32          /* pthread.h:182:18 */
type Pthread_barrierattr_t = uintptr /* pthread.h:183:14 */
type Pthread_condattr_t = int32      /* pthread.h:184:13 */
type Pthread_rwlockattr_t = int32    /* pthread.h:185:13 */

//
// struct _pthread_v;
//
// typedef struct pthread_t {
//   struct _pthread_v *p;
//   int x;
// } pthread_t;

type Pthread_t = Uintptr_t /* pthread.h:196:19 */

type X_pthread_cleanup1 = struct {
	Ffunc uintptr
	Farg  uintptr
	Fnext uintptr
} /* pthread.h:198:9 */

type X_pthread_cleanup = X_pthread_cleanup1 /* pthread.h:198:33 */

// Note that if async cancelling is used, then there is a race here

// Windows doesn't have this, so declare it ourselves.

// Some POSIX realtime extensions, mostly stubbed

type Sched_param = struct{ Fsched_priority int32 } /* pthread.h:239:1 */

type Pthread_attr_t1 = struct {
	Fp_state     uint32
	F__ccgo_pad1 [4]byte
	Fstack       uintptr
	Fs_size      Size_t
	Fparam       struct{ Fsched_priority int32 }
	F__ccgo_pad2 [4]byte
} /* pthread.h:251:9 */

type Pthread_attr_t = Pthread_attr_t1 /* pthread.h:251:31 */

// synchronization objects
type Pthread_spinlock_t = uintptr /* pthread.h:268:14 */
type Pthread_mutex_t = uintptr    /* pthread.h:269:14 */
type Pthread_cond_t = uintptr     /* pthread.h:270:14 */
type Pthread_rwlock_t = uintptr   /* pthread.h:271:14 */
type Pthread_barrier_t = uintptr  /* pthread.h:272:14 */

type Clockid_t = int32 /* pthread.h:389:13 */

//
//    Copyright (c) 2011-2016  mingw-w64 project
//
//    Permission is hereby granted, free of charge, to any person obtaining a
//    copy of this software and associated documentation files (the "Software"),
//    to deal in the Software without restriction, including without limitation
//    the rights to use, copy, modify, merge, publish, distribute, sublicense,
//    and/or sell copies of the Software, and to permit persons to whom the
//    Software is furnished to do so, subject to the following conditions:
//
//    The above copyright notice and this permission notice shall be included in
//    all copies or substantial portions of the Software.
//
//    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
//    DEALINGS IN THE SOFTWARE.

// Set defines described by the POSIX Threads Extension (1003.1c-1995)
// _SC_THREADS
//   Basic support for POSIX threads is available. The functions
//
//   pthread_atfork(),
//   pthread_attr_destroy(),
//   pthread_attr_getdetachstate(),
//   pthread_attr_getschedparam(),
//   pthread_attr_init(),
//   pthread_attr_setdetachstate(),
//   pthread_attr_setschedparam(),
//   pthread_cancel(),
//   pthread_cleanup_push(),
//   pthread_cleanup_pop(),
//   pthread_cond_broadcast(),
//   pthread_cond_destroy(),
//   pthread_cond_init(),
//   pthread_cond_signal(),
//   pthread_cond_timedwait(),
//   pthread_cond_wait(),
//   pthread_condattr_destroy(),
//   pthread_condattr_init(),
//   pthread_create(),
//   pthread_detach(),
//   pthread_equal(),
//   pthread_exit(),
//   pthread_getspecific(),
//   pthread_join(,
//   pthread_key_create(),
//   pthread_key_delete(),
//   pthread_mutex_destroy(),
//   pthread_mutex_init(),
//   pthread_mutex_lock(),
//   pthread_mutex_trylock(),
//   pthread_mutex_unlock(),
//   pthread_mutexattr_destroy(),
//   pthread_mutexattr_init(),
//   pthread_once(),
//   pthread_rwlock_destroy(),
//   pthread_rwlock_init(),
//   pthread_rwlock_rdlock(),
//   pthread_rwlock_tryrdlock(),
//   pthread_rwlock_trywrlock(),
//   pthread_rwlock_unlock(),
//   pthread_rwlock_wrlock(),
//   pthread_rwlockattr_destroy(),
//   pthread_rwlockattr_init(),
//   pthread_self(),
//   pthread_setcancelstate(),
//   pthread_setcanceltype(),
//   pthread_setspecific(),
//   pthread_testcancel()
//
//   are present.

// _SC_READER_WRITER_LOCKS
//   This option implies the _POSIX_THREADS option. Conversely, under
//   POSIX 1003.1-2001 the _POSIX_THREADS option implies this option.
//
//   The functions
//   pthread_rwlock_destroy(),
//   pthread_rwlock_init(),
//   pthread_rwlock_rdlock(),
//   pthread_rwlock_tryrdlock(),
//   pthread_rwlock_trywrlock(),
//   pthread_rwlock_unlock(),
//   pthread_rwlock_wrlock(),
//   pthread_rwlockattr_destroy(),
//   pthread_rwlockattr_init()
//
//   are present.

// _SC_SPIN_LOCKS
//   This option implies the _POSIX_THREADS and _POSIX_THREAD_SAFE_FUNCTIONS
//   options. The functions
//
//   pthread_spin_destroy(),
//   pthread_spin_init(),
//   pthread_spin_lock(),
//   pthread_spin_trylock(),
//   pthread_spin_unlock()
//
//   are present.

// _SC_BARRIERS
//   This option implies the _POSIX_THREADS and _POSIX_THREAD_SAFE_FUNCTIONS
//   options. The functions
//
//   pthread_barrier_destroy(),
//   pthread_barrier_init(),
//   pthread_barrier_wait(),
//   pthread_barrierattr_destroy(),
//   pthread_barrierattr_init()
//
//   are present.

// _SC_THREAD_SAFE_FUNCTIONS
//   Affected functions are
//
//   readdir_r(),
//   getgrgid_r(),
//   getgrnam_r(),
//   getpwnam_r(),
//   getpwuid_r(),
//   flockfile(),
//   ftrylockfile(),
//   funlockfile(),
//   getc_unlocked(),
//   getchar_unlocked(),
//   putc_unlocked(),
//   putchar_unlocked(),
//   strerror_r(),

// _SC_TIMEOUTS
//   The functions
//
//   mq_timedreceive(), - not supported
//   mq_timedsend(), - not supported
//   posix_trace_timedgetnext_event(), - not supported
//   pthread_mutex_timedlock(),
//   pthread_rwlock_timedrdlock(),
//   pthread_rwlock_timedwrlock(),
//   sem_timedwait(),
//
//   are present.

// _SC_TIMERS - not supported
//   The functions
//
//   clock_getres(),
//   clock_gettime(),
//   clock_settime(),
//   nanosleep(),
//   timer_create(),
//   timer_delete(),
//   timer_gettime(),
//   timer_getoverrun(),
//   timer_settime()
//
//   are present.
// #undef _POSIX_TIMERS

// _SC_CLOCK_SELECTION
//    This option implies the _POSIX_TIMERS option. The functions
//
//    pthread_condattr_getclock(),
//    pthread_condattr_setclock(),
//    clock_nanosleep()
//
//    are present.

// _SC_SEMAPHORES
//   The include file <semaphore.h> is present. The functions
//
//   sem_close(),
//   sem_destroy(),
//   sem_getvalue(),
//   sem_init(),
//   sem_open(),
//   sem_post(),
//   sem_trywait(),
//   sem_unlink(),
//   sem_wait()
//
//   are present.

// Wrap cancellation points.

// We deal here with a gcc issue for posix threading on Windows.
//    We would need to change here gcc's gthr-posix.h header, but this
//    got rejected.  So we deal it within this header.

var _ int8 /* gen.c:2:13: */
