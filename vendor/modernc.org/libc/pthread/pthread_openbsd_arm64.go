// Code generated by 'ccgo pthread/gen.c -crt-import-path "" -export-defines "" -export-enums "" -export-externs X -export-fields F -export-structs "" -export-typedefs "" -header -hide _OSSwapInt16,_OSSwapInt32,_OSSwapInt64 -ignore-unsupported-alignment -o pthread/pthread_openbsd_arm64.go -pkgname pthread', DO NOT EDIT.

package pthread

import (
	"math"
	"reflect"
	"sync/atomic"
	"unsafe"
)

var _ = math.Pi
var _ reflect.Kind
var _ atomic.Value
var _ unsafe.Pointer

const (
	ARG_MAX                              = 524288               // syslimits.h:38:1:
	BC_BASE_MAX                          = 2147483647           // syslimits.h:51:1:
	BC_DIM_MAX                           = 65535                // syslimits.h:52:1:
	BC_SCALE_MAX                         = 2147483647           // syslimits.h:53:1:
	BC_STRING_MAX                        = 2147483647           // syslimits.h:54:1:
	BIG_ENDIAN                           = 4321                 // endian.h:45:1:
	BUS_ADRALN                           = 1                    // siginfo.h:83:1:
	BUS_ADRERR                           = 2                    // siginfo.h:84:1:
	BUS_OBJERR                           = 3                    // siginfo.h:85:1:
	BYTE_ORDER                           = 1234                 // endian.h:47:1:
	CHAR_BIT                             = 8                    // limits.h:36:1:
	CHAR_MAX                             = 0xff                 // limits.h:44:1:
	CHAR_MIN                             = 0                    // limits.h:43:1:
	CHILD_MAX                            = 80                   // syslimits.h:39:1:
	CLD_CONTINUED                        = 6                    // siginfo.h:105:1:
	CLD_DUMPED                           = 3                    // siginfo.h:102:1:
	CLD_EXITED                           = 1                    // siginfo.h:100:1:
	CLD_KILLED                           = 2                    // siginfo.h:101:1:
	CLD_STOPPED                          = 5                    // siginfo.h:104:1:
	CLD_TRAPPED                          = 4                    // siginfo.h:103:1:
	CLK_TCK                              = 100                  // time.h:68:1:
	CLOCKS_PER_SEC                       = 100                  // time.h:71:1:
	CLOCK_BOOTTIME                       = 6                    // _time.h:40:1:
	CLOCK_MONOTONIC                      = 3                    // _time.h:37:1:
	CLOCK_PROCESS_CPUTIME_ID             = 2                    // _time.h:36:1:
	CLOCK_REALTIME                       = 0                    // _time.h:35:1:
	CLOCK_THREAD_CPUTIME_ID              = 4                    // _time.h:38:1:
	CLOCK_UPTIME                         = 5                    // _time.h:39:1:
	COLL_WEIGHTS_MAX                     = 2                    // syslimits.h:55:1:
	DST_AUST                             = 2                    // time.h:78:1:
	DST_CAN                              = 6                    // time.h:82:1:
	DST_EET                              = 5                    // time.h:81:1:
	DST_MET                              = 4                    // time.h:80:1:
	DST_NONE                             = 0                    // time.h:76:1:
	DST_USA                              = 1                    // time.h:77:1:
	DST_WET                              = 3                    // time.h:79:1:
	EMT_TAGOVF                           = 1                    // siginfo.h:66:1:
	EXPR_NEST_MAX                        = 32                   // syslimits.h:56:1:
	FD_SETSIZE                           = 1024                 // select.h:62:1:
	FPE_FLTDIV                           = 3                    // siginfo.h:71:1:
	FPE_FLTINV                           = 7                    // siginfo.h:75:1:
	FPE_FLTOVF                           = 4                    // siginfo.h:72:1:
	FPE_FLTRES                           = 6                    // siginfo.h:74:1:
	FPE_FLTSUB                           = 8                    // siginfo.h:76:1:
	FPE_FLTUND                           = 5                    // siginfo.h:73:1:
	FPE_INTDIV                           = 1                    // siginfo.h:69:1:
	FPE_INTOVF                           = 2                    // siginfo.h:70:1:
	GID_MAX                              = 4294967295           // limits.h:84:1:
	HOST_NAME_MAX                        = 255                  // syslimits.h:76:1:
	ILL_BADSTK                           = 8                    // siginfo.h:63:1:
	ILL_COPROC                           = 7                    // siginfo.h:62:1:
	ILL_ILLADR                           = 3                    // siginfo.h:58:1:
	ILL_ILLOPC                           = 1                    // siginfo.h:56:1:
	ILL_ILLOPN                           = 2                    // siginfo.h:57:1:
	ILL_ILLTRP                           = 4                    // siginfo.h:59:1:
	ILL_PRVOPC                           = 5                    // siginfo.h:60:1:
	ILL_PRVREG                           = 6                    // siginfo.h:61:1:
	INT_MAX                              = 0x7fffffff           // limits.h:57:1:
	INT_MIN                              = -2147483648          // limits.h:58:1:
	IOV_MAX                              = 1024                 // syslimits.h:64:1:
	ITIMER_PROF                          = 2                    // time.h:146:1:
	ITIMER_REAL                          = 0                    // time.h:144:1:
	ITIMER_VIRTUAL                       = 1                    // time.h:145:1:
	LINE_MAX                             = 2048                 // syslimits.h:57:1:
	LINK_MAX                             = 32767                // syslimits.h:40:1:
	LITTLE_ENDIAN                        = 1234                 // endian.h:44:1:
	LLONG_MAX                            = 0x7fffffffffffffff   // limits.h:76:1:
	LLONG_MIN                            = -9223372036854775808 // limits.h:78:1:
	LOGIN_NAME_MAX                       = 32                   // syslimits.h:72:1:
	LONG_BIT                             = 64                   // limits.h:89:1:
	LONG_MAX                             = 0x7fffffffffffffff   // limits.h:63:1:
	LONG_MIN                             = -9223372036854775808 // limits.h:65:1:
	MAX_CANON                            = 255                  // syslimits.h:41:1:
	MAX_INPUT                            = 255                  // syslimits.h:42:1:
	MB_LEN_MAX                           = 4                    // limits.h:50:1:
	MINSIGSTKSZ                          = 12288                // signal.h:183:1:
	NAME_MAX                             = 255                  // syslimits.h:43:1:
	NBBY                                 = 8                    // select.h:111:1:
	NGROUPS_MAX                          = 16                   // syslimits.h:44:1:
	NL_ARGMAX                            = 9                    // limits.h:89:1:
	NL_LANGMAX                           = 14                   // limits.h:90:1:
	NL_MSGMAX                            = 32767                // limits.h:91:1:
	NL_SETMAX                            = 255                  // limits.h:92:1:
	NL_TEXTMAX                           = 255                  // limits.h:93:1:
	NSIG                                 = 33                   // signal.h:48:1:
	NSIGBUS                              = 3                    // siginfo.h:86:1:
	NSIGCLD                              = 6                    // siginfo.h:106:1:
	NSIGEMT                              = 1                    // siginfo.h:67:1:
	NSIGFPE                              = 8                    // siginfo.h:77:1:
	NSIGILL                              = 8                    // siginfo.h:64:1:
	NSIGSEGV                             = 2                    // siginfo.h:81:1:
	NSIGTRAP                             = 2                    // siginfo.h:95:1:
	NZERO                                = 20                   // syslimits.h:65:1:
	OPEN_MAX                             = 64                   // syslimits.h:45:1:
	PATH_MAX                             = 1024                 // syslimits.h:46:1:
	PDP_ENDIAN                           = 3412                 // endian.h:46:1:
	PIPE_BUF                             = 512                  // syslimits.h:47:1:
	PTHREAD_BARRIER_SERIAL_THREAD        = -1                   // pthread.h:88:1:
	PTHREAD_CANCEL_ASYNCHRONOUS          = 2                    // pthread.h:82:1:
	PTHREAD_CANCEL_DEFERRED              = 0                    // pthread.h:81:1:
	PTHREAD_CANCEL_DISABLE               = 1                    // pthread.h:80:1:
	PTHREAD_CANCEL_ENABLE                = 0                    // pthread.h:79:1:
	PTHREAD_CREATE_DETACHED              = 1                    // pthread.h:65:1:
	PTHREAD_CREATE_JOINABLE              = 0                    // pthread.h:66:1:
	PTHREAD_DESTRUCTOR_ITERATIONS        = 4                    // pthread.h:52:1:
	PTHREAD_DETACHED                     = 0x1                  // pthread.h:60:1:
	PTHREAD_DONE_INIT                    = 1                    // pthread.h:147:1:
	PTHREAD_EXPLICIT_SCHED               = 0                    // pthread.h:68:1:
	PTHREAD_INHERIT_SCHED                = 0x4                  // pthread.h:62:1:
	PTHREAD_KEYS_MAX                     = 256                  // pthread.h:53:1:
	PTHREAD_NEEDS_INIT                   = 0                    // pthread.h:146:1:
	PTHREAD_NOFLOAT                      = 0x8                  // pthread.h:63:1:
	PTHREAD_PRIO_INHERIT                 = 1                    // pthread.h:162:1:
	PTHREAD_PRIO_NONE                    = 0                    // pthread.h:161:1:
	PTHREAD_PRIO_PROTECT                 = 2                    // pthread.h:163:1:
	PTHREAD_PROCESS_PRIVATE              = 0                    // pthread.h:73:1:
	PTHREAD_PROCESS_SHARED               = 1                    // pthread.h:74:1:
	PTHREAD_SCOPE_PROCESS                = 0                    // pthread.h:67:1:
	PTHREAD_SCOPE_SYSTEM                 = 0x2                  // pthread.h:61:1:
	PTHREAD_STACK_MIN                    = 4096                 // pthread.h:54:1:
	PTHREAD_THREADS_MAX                  = 18446744073709551615 // pthread.h:55:1:
	QUAD_MAX                             = 0x7fffffffffffffff   // limits.h:51:1:
	QUAD_MIN                             = -9223372036854775808 // limits.h:52:1:
	RE_DUP_MAX                           = 255                  // syslimits.h:59:1:
	SA_NOCLDSTOP                         = 0x0008               // signal.h:132:1:
	SA_NOCLDWAIT                         = 0x0020               // signal.h:130:1:
	SA_NODEFER                           = 0x0010               // signal.h:129:1:
	SA_ONSTACK                           = 0x0001               // signal.h:126:1:
	SA_RESETHAND                         = 0x0004               // signal.h:128:1:
	SA_RESTART                           = 0x0002               // signal.h:127:1:
	SA_SIGINFO                           = 0x0040               // signal.h:134:1:
	SCHAR_MAX                            = 0x7f                 // limits.h:38:1:
	SCHAR_MIN                            = -128                 // limits.h:39:1:
	SCHED_FIFO                           = 1                    // sched.h:47:1:
	SCHED_OTHER                          = 2                    // sched.h:48:1:
	SCHED_RR                             = 3                    // sched.h:49:1:
	SEGV_ACCERR                          = 2                    // siginfo.h:80:1:
	SEGV_MAPERR                          = 1                    // siginfo.h:79:1:
	SEM_VALUE_MAX                        = 4294967295           // syslimits.h:60:1:
	SHRT_MAX                             = 0x7fff               // limits.h:53:1:
	SHRT_MIN                             = -32768               // limits.h:54:1:
	SIGABRT                              = 6                    // signal.h:56:1:
	SIGALRM                              = 14                   // signal.h:67:1:
	SIGBUS                               = 10                   // signal.h:63:1:
	SIGCHLD                              = 20                   // signal.h:73:1:
	SIGCONT                              = 19                   // signal.h:72:1:
	SIGEMT                               = 7                    // signal.h:59:1:
	SIGFPE                               = 8                    // signal.h:61:1:
	SIGHUP                               = 1                    // signal.h:51:1:
	SIGILL                               = 4                    // signal.h:54:1:
	SIGINFO                              = 29                   // signal.h:85:1:
	SIGINT                               = 2                    // signal.h:52:1:
	SIGIO                                = 23                   // signal.h:77:1:
	SIGIOT                               = 6                    // signal.h:58:1:
	SIGKILL                              = 9                    // signal.h:62:1:
	SIGPIPE                              = 13                   // signal.h:66:1:
	SIGPROF                              = 27                   // signal.h:82:1:
	SIGQUIT                              = 3                    // signal.h:53:1:
	SIGSEGV                              = 11                   // signal.h:64:1:
	SIGSTKSZ                             = 28672                // signal.h:185:1:
	SIGSTOP                              = 17                   // signal.h:70:1:
	SIGSYS                               = 12                   // signal.h:65:1:
	SIGTERM                              = 15                   // signal.h:68:1:
	SIGTHR                               = 32                   // signal.h:90:1:
	SIGTRAP                              = 5                    // signal.h:55:1:
	SIGTSTP                              = 18                   // signal.h:71:1:
	SIGTTIN                              = 21                   // signal.h:74:1:
	SIGTTOU                              = 22                   // signal.h:75:1:
	SIGURG                               = 16                   // signal.h:69:1:
	SIGUSR1                              = 30                   // signal.h:87:1:
	SIGUSR2                              = 31                   // signal.h:88:1:
	SIGVTALRM                            = 26                   // signal.h:81:1:
	SIGWINCH                             = 28                   // signal.h:84:1:
	SIGXCPU                              = 24                   // signal.h:79:1:
	SIGXFSZ                              = 25                   // signal.h:80:1:
	SIG_BLOCK                            = 1                    // signal.h:140:1:
	SIG_SETMASK                          = 3                    // signal.h:142:1:
	SIG_UNBLOCK                          = 2                    // signal.h:141:1:
	SIZE_MAX                             = 18446744073709551615 // limits.h:42:1:
	SIZE_T_MAX                           = 18446744073709551615 // limits.h:48:1:
	SI_LWP                               = -1                   // siginfo.h:47:1:
	SI_MAXSZ                             = 128                  // siginfo.h:127:1:
	SI_NOINFO                            = 32767                // siginfo.h:45:1:
	SI_QUEUE                             = -2                   // siginfo.h:48:1:
	SI_TIMER                             = -3                   // siginfo.h:49:1:
	SI_USER                              = 0                    // siginfo.h:46:1:
	SSIZE_MAX                            = 9223372036854775807  // limits.h:44:1:
	SS_DISABLE                           = 0x0004               // signal.h:182:1:
	SS_ONSTACK                           = 0x0001               // signal.h:181:1:
	SV_INTERRUPT                         = 2                    // signal.h:158:1:
	SV_ONSTACK                           = 1                    // signal.h:157:1:
	SV_RESETHAND                         = 4                    // signal.h:159:1:
	SYMLINK_MAX                          = 1024                 // syslimits.h:48:1:
	SYMLOOP_MAX                          = 32                   // syslimits.h:49:1:
	TIMER_ABSTIME                        = 0x1                  // _time.h:62:1:
	TIMER_RELTIME                        = 0x0                  // _time.h:61:1:
	TIME_UTC                             = 1                    // time.h:179:1:
	TRAP_BRKPT                           = 1                    // siginfo.h:93:1:
	TRAP_TRACE                           = 2                    // siginfo.h:94:1:
	TTY_NAME_MAX                         = 260                  // syslimits.h:71:1:
	UCHAR_MAX                            = 0xff                 // limits.h:41:1:
	UID_MAX                              = 4294967295           // limits.h:83:1:
	UINT_MAX                             = 0xffffffff           // limits.h:56:1:
	ULLONG_MAX                           = 0xffffffffffffffff   // limits.h:74:1:
	ULONG_MAX                            = 0xffffffffffffffff   // limits.h:61:1:
	UQUAD_MAX                            = 0xffffffffffffffff   // limits.h:50:1:
	USHRT_MAX                            = 0xffff               // limits.h:52:1:
	WORD_BIT                             = 32                   // limits.h:93:1:
	X_BIG_ENDIAN                         = 4321                 // _endian.h:43:1:
	X_BYTE_ORDER                         = 1234                 // endian.h:60:1:
	X_CLOCKID_T_DEFINED_                 = 0                    // types.h:162:1:
	X_CLOCK_T_DEFINED_                   = 0                    // types.h:157:1:
	X_FILE_OFFSET_BITS                   = 64                   // <builtin>:25:1:
	X_INT16_T_DEFINED_                   = 0                    // types.h:84:1:
	X_INT32_T_DEFINED_                   = 0                    // types.h:94:1:
	X_INT64_T_DEFINED_                   = 0                    // types.h:104:1:
	X_INT8_T_DEFINED_                    = 0                    // types.h:74:1:
	X_LIMITS_H_                          = 0                    // limits.h:36:1:
	X_LITTLE_ENDIAN                      = 1234                 // _endian.h:42:1:
	X_LOCALE_T_DEFINED_                  = 0                    // time.h:106:1:
	X_LP64                               = 1                    // <predefined>:1:1:
	X_MACHINE_CDEFS_H_                   = 0                    // cdefs.h:4:1:
	X_MACHINE_ENDIAN_H_                  = 0                    // endian.h:20:1:
	X_MACHINE_LIMITS_H_                  = 0                    // limits.h:36:1:
	X_MACHINE_SIGNAL_H_                  = 0                    // signal.h:37:1:
	X_MACHINE__TYPES_H_                  = 0                    // _types.h:35:1:
	X_MAXCOMLEN                          = 24                   // syslimits.h:79:1:
	X_MAX_PAGE_SHIFT                     = 12                   // _types.h:57:1:
	X_NSIG                               = 33                   // signal.h:45:1:
	X_OFF_T_DEFINED_                     = 0                    // types.h:192:1:
	X_PDP_ENDIAN                         = 3412                 // _endian.h:44:1:
	X_PID_T_DEFINED_                     = 0                    // types.h:167:1:
	X_POSIX2_BC_BASE_MAX                 = 99                   // limits.h:71:1:
	X_POSIX2_BC_DIM_MAX                  = 2048                 // limits.h:72:1:
	X_POSIX2_BC_SCALE_MAX                = 99                   // limits.h:73:1:
	X_POSIX2_BC_STRING_MAX               = 1000                 // limits.h:74:1:
	X_POSIX2_CHARCLASS_NAME_MAX          = 14                   // limits.h:79:1:
	X_POSIX2_COLL_WEIGHTS_MAX            = 2                    // limits.h:75:1:
	X_POSIX2_EXPR_NEST_MAX               = 32                   // limits.h:76:1:
	X_POSIX2_LINE_MAX                    = 2048                 // limits.h:77:1:
	X_POSIX2_RE_DUP_MAX                  = 255                  // limits.h:78:1:
	X_POSIX_ARG_MAX                      = 4096                 // limits.h:41:1:
	X_POSIX_CHILD_MAX                    = 25                   // limits.h:42:1:
	X_POSIX_CLOCKRES_MIN                 = 20000000             // limits.h:61:1:
	X_POSIX_HOST_NAME_MAX                = 255                  // limits.h:82:1:
	X_POSIX_LINK_MAX                     = 8                    // limits.h:43:1:
	X_POSIX_LOGIN_NAME_MAX               = 9                    // limits.h:83:1:
	X_POSIX_MAX_CANON                    = 255                  // limits.h:44:1:
	X_POSIX_MAX_INPUT                    = 255                  // limits.h:45:1:
	X_POSIX_NAME_MAX                     = 14                   // limits.h:46:1:
	X_POSIX_NGROUPS_MAX                  = 8                    // limits.h:62:1:
	X_POSIX_OPEN_MAX                     = 20                   // limits.h:63:1:
	X_POSIX_PATH_MAX                     = 256                  // limits.h:47:1:
	X_POSIX_PIPE_BUF                     = 512                  // limits.h:48:1:
	X_POSIX_RE_DUP_MAX                   = 255                  // limits.h:49:1:
	X_POSIX_SEM_NSEMS_MAX                = 256                  // limits.h:50:1:
	X_POSIX_SEM_VALUE_MAX                = 32767                // limits.h:51:1:
	X_POSIX_SSIZE_MAX                    = 32767                // limits.h:52:1:
	X_POSIX_STREAM_MAX                   = 8                    // limits.h:53:1:
	X_POSIX_SYMLINK_MAX                  = 255                  // limits.h:54:1:
	X_POSIX_SYMLOOP_MAX                  = 8                    // limits.h:55:1:
	X_POSIX_THREAD_DESTRUCTOR_ITERATIONS = 4                    // limits.h:56:1:
	X_POSIX_THREAD_KEYS_MAX              = 128                  // limits.h:57:1:
	X_POSIX_THREAD_THREADS_MAX           = 4                    // limits.h:58:1:
	X_POSIX_TTY_NAME_MAX                 = 9                    // limits.h:84:1:
	X_POSIX_TZNAME_MAX                   = 6                    // limits.h:64:1:
	X_PTHREAD_H_                         = 0                    // pthread.h:38:1:
	X_QUAD_HIGHWORD                      = 1                    // _endian.h:95:1:
	X_QUAD_LOWWORD                       = 0                    // _endian.h:96:1:
	X_RET_PROTECTOR                      = 1                    // <predefined>:2:1:
	X_SCHED_H_                           = 0                    // sched.h:40:1:
	X_SELECT_DEFINED_                    = 0                    // select.h:126:1:
	X_SIGSET_T_DEFINED_                  = 0                    // select.h:121:1:
	X_SIZE_T_DEFINED_                    = 0                    // types.h:172:1:
	X_SSIZE_T_DEFINED_                   = 0                    // types.h:177:1:
	X_STACKALIGNBYTES                    = 15                   // _types.h:54:1:
	X_SYS_CDEFS_H_                       = 0                    // cdefs.h:39:1:
	X_SYS_ENDIAN_H_                      = 0                    // endian.h:38:1:
	X_SYS_LIMITS_H_                      = 0                    // limits.h:27:1:
	X_SYS_SELECT_H_                      = 0                    // select.h:35:1:
	X_SYS_SIGINFO_H                      = 0                    // siginfo.h:29:1:
	X_SYS_SIGNAL_H_                      = 0                    // signal.h:41:1:
	X_SYS_TIME_H_                        = 0                    // time.h:36:1:
	X_SYS_TYPES_H_                       = 0                    // types.h:41:1:
	X_SYS__ENDIAN_H_                     = 0                    // _endian.h:34:1:
	X_SYS__TIME_H_                       = 0                    // _time.h:33:1:
	X_SYS__TYPES_H_                      = 0                    // _types.h:35:1:
	X_TIMER_T_DEFINED_                   = 0                    // types.h:187:1:
	X_TIMESPEC_DECLARED                  = 0                    // select.h:48:1:
	X_TIMEVAL_DECLARED                   = 0                    // select.h:40:1:
	X_TIME_H_                            = 0                    // time.h:42:1:
	X_TIME_T_DEFINED_                    = 0                    // types.h:182:1:
	X_UINT16_T_DEFINED_                  = 0                    // types.h:89:1:
	X_UINT32_T_DEFINED_                  = 0                    // types.h:99:1:
	X_UINT64_T_DEFINED_                  = 0                    // types.h:109:1:
	X_UINT8_T_DEFINED_                   = 0                    // types.h:79:1:
	X_XOPEN_IOV_MAX                      = 16                   // limits.h:106:1:
	X_XOPEN_NAME_MAX                     = 255                  // limits.h:107:1:
	X_XOPEN_PATH_MAX                     = 1024                 // limits.h:108:1:
	Unix                                 = 1                    // <predefined>:360:1:
)

// Flags for once initialization.

// Static once initialization values.

// Static initialization values.

// Mutex types.
const ( /* pthread.h:168:1: */
	PTHREAD_MUTEX_ERRORCHECK = 1 // Error checking mutex
	PTHREAD_MUTEX_RECURSIVE  = 2 // Recursive mutex
	PTHREAD_MUTEX_NORMAL     = 3 // No error checking
	PTHREAD_MUTEX_STRICT_NP  = 4 // Strict error checking
	PTHREAD_MUTEX_TYPE_MAX   = 5
)

type Ptrdiff_t = int64 /* <builtin>:3:26 */

type Size_t = uint64 /* <builtin>:9:23 */

type Wchar_t = int32 /* <builtin>:15:24 */

type X__int128_t = struct {
	Flo int64
	Fhi int64
} /* <builtin>:21:43 */ // must match modernc.org/mathutil.Int128
type X__uint128_t = struct {
	Flo uint64
	Fhi uint64
} /* <builtin>:22:44 */ // must match modernc.org/mathutil.Int128

type X__builtin_va_list = uintptr /* <builtin>:46:14 */
type X__float128 = float64        /* <builtin>:47:21 */

//	$OpenBSD: pthread.h,v 1.4 2018/03/05 01:15:26 deraadt Exp $

// Copyright (c) 1993, 1994 by Chris Provenzano, <EMAIL>
// Copyright (c) 1995-1998 by John Birrell <<EMAIL>>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//  This product includes software developed by Chris Provenzano.
// 4. The name of Chris Provenzano may not be used to endorse or promote
//	  products derived from this software without specific prior written
//	  permission.
//
// THIS SOFTWARE IS PROVIDED BY CHRIS PROVENZANO ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL CHRIS PROVENZANO BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
// SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
// CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
// $FreeBSD: pthread.h,v 1.13 1999/07/31 08:36:07 rse Exp $

// Header files.
//	$OpenBSD: types.h,v 1.49 2022/08/06 13:31:13 semarie Exp $
//	$NetBSD: types.h,v 1.29 1996/11/15 22:48:25 jtc Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: cdefs.h,v 1.1 2016/12/17 23:38:33 patrick Exp $

// Macro to test if we're using a specific version of gcc or later.

// The __CONCAT macro is used to concatenate parts of symbol names, e.g.
// with "#define OLD(foo) __CONCAT(old,foo)", OLD(foo) produces oldfoo.
// The __CONCAT macro is a bit tricky -- make sure you don't put spaces
// in between its arguments.  Do not use __CONCAT on double-quoted strings,
// such as those from the __STRING macro: to concatenate strings just put
// them next to each other.

// GCC1 and some versions of GCC2 declare dead (non-returning) and
// pure (no side effects) functions using "volatile" and "const";
// unfortunately, these then cause warnings under "-ansi -pedantic".
// GCC >= 2.5 uses the __attribute__((attrs)) style.  All of these
// work for GNU C++ (modulo a slight glitch in the C++ grammar in
// the distribution version of 2.5.5).

// __returns_twice makes the compiler not assume the function
// only returns once.  This affects registerisation of variables:
// even local variables need to be in memory across such a call.
// Example: setjmp()

// __only_inline makes the compiler only use this function definition
// for inlining; references that can't be inlined will be left as
// external references instead of generating a local copy.  The
// matching library should include a simple extern definition for
// the function to handle those references.  c.f. ctype.h

// GNU C version 2.96 adds explicit branch prediction so that
// the CPU back-end can hint the processor and also so that
// code blocks can be reordered such that the predicted path
// sees a more linear flow, thus improving cache behavior, etc.
//
// The following two macros provide us with a way to utilize this
// compiler feature.  Use __predict_true() if you expect the expression
// to evaluate to true, and __predict_false() if you expect the
// expression to evaluate to false.
//
// A few notes about usage:
//
//	* Generally, __predict_false() error condition checks (unless
//	  you have some _strong_ reason to do otherwise, in which case
//	  document it), and/or __predict_true() `no-error' condition
//	  checks, assuming you want to optimize for the no-error case.
//
//	* Other than that, if you don't know the likelihood of a test
//	  succeeding from empirical or other `hard' evidence, don't
//	  make predictions.
//
//	* These are meant to be used in places that are run `a lot'.
//	  It is wasteful to make predictions in code that is run
//	  seldomly (e.g. at subsystem initialization time) as the
//	  basic block reordering that this affects can often generate
//	  larger code.

// Delete pseudo-keywords wherever they are not available or needed.

// The __packed macro indicates that a variable or structure members
// should have the smallest possible alignment, despite any host CPU
// alignment requirements.
//
// The __aligned(x) macro specifies the minimum alignment of a
// variable or structure.
//
// These macros together are useful for describing the layout and
// alignment of messages exchanged with hardware or other systems.

// "The nice thing about standards is that there are so many to choose from."
// There are a number of "feature test macros" specified by (different)
// standards that determine which interfaces and types the header files
// should expose.
//
// Because of inconsistencies in these macros, we define our own
// set in the private name space that end in _VISIBLE.  These are
// always defined and so headers can test their values easily.
// Things can get tricky when multiple feature macros are defined.
// We try to take the union of all the features requested.
//
// The following macros are guaranteed to have a value after cdefs.h
// has been included:
//	__POSIX_VISIBLE
//	__XPG_VISIBLE
//	__ISO_C_VISIBLE
//	__BSD_VISIBLE

// X/Open Portability Guides and Single Unix Specifications.
// _XOPEN_SOURCE				XPG3
// _XOPEN_SOURCE && _XOPEN_VERSION = 4		XPG4
// _XOPEN_SOURCE && _XOPEN_SOURCE_EXTENDED = 1	XPG4v2
// _XOPEN_SOURCE == 500				XPG5
// _XOPEN_SOURCE == 520				XPG5v2
// _XOPEN_SOURCE == 600				POSIX 1003.1-2001 with XSI
// _XOPEN_SOURCE == 700				POSIX 1003.1-2008 with XSI
//
// The XPG spec implies a specific value for _POSIX_C_SOURCE.

// POSIX macros, these checks must follow the XOPEN ones above.
//
// _POSIX_SOURCE == 1		1003.1-1988 (superseded by _POSIX_C_SOURCE)
// _POSIX_C_SOURCE == 1		1003.1-1990
// _POSIX_C_SOURCE == 2		1003.2-1992
// _POSIX_C_SOURCE == 199309L	1003.1b-1993
// _POSIX_C_SOURCE == 199506L   1003.1c-1995, 1003.1i-1995,
//				and the omnibus ISO/IEC 9945-1:1996
// _POSIX_C_SOURCE == 200112L   1003.1-2001
// _POSIX_C_SOURCE == 200809L   1003.1-2008
//
// The POSIX spec implies a specific value for __ISO_C_VISIBLE, though
// this may be overridden by the _ISOC99_SOURCE macro later.

// _ANSI_SOURCE means to expose ANSI C89 interfaces only.
// If the user defines it in addition to one of the POSIX or XOPEN
// macros, assume the POSIX/XOPEN macro(s) should take precedence.

// _ISOC99_SOURCE, _ISOC11_SOURCE, __STDC_VERSION__, and __cplusplus
// override any of the other macros since they are non-exclusive.

// Finally deal with BSD-specific interfaces that are not covered
// by any standards.  We expose these when none of the POSIX or XPG
// macros is defined or if the user explicitly asks for them.

// Default values.

//	$OpenBSD: endian.h,v 1.25 2014/12/21 04:49:00 guenther Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Public definitions for little- and big-endian systems.
// This file should be included as <endian.h> in userspace and as
// <sys/endian.h> in the kernel.
//
// System headers that need endian information but that can't or don't
// want to export the public names here should include <sys/_endian.h>
// and use the internal names: _BYTE_ORDER, _*_ENDIAN, etc.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: _endian.h,v 1.8 2018/01/11 23:13:37 dlg Exp $

// -
// Copyright (c) 1997 Niklas Hallqvist.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Internal endianness macros.  This pulls in <machine/endian.h> to
// get the correct setting direction for the platform and sets internal
// ('__' prefix) macros appropriately.

//	$OpenBSD: _types.h,v 1.10 2022/08/06 13:31:13 semarie Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

// $OpenBSD: _types.h,v 1.4 2018/03/05 01:15:25 deraadt Exp $
// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94
//	@(#)ansi.h	8.2 (Berkeley) 1/4/94

// _ALIGN(p) rounds p (pointer or byte index) up to a correctly-aligned
// value for all data types (int, long, ...).   The result is an
// unsigned long and must be cast to any desired pointer type.
//
// _ALIGNED_POINTER is a boolean macro that checks whether an address
// is valid to fetch data elements of type t from on this architecture.
// This does not reflect the optimal alignment, just the possibility
// (within reasonable limits).

// ******** Exact-width integer types
type X__int8_t = int8     /* _types.h:60:22 */
type X__uint8_t = uint8   /* _types.h:61:24 */
type X__int16_t = int16   /* _types.h:62:17 */
type X__uint16_t = uint16 /* _types.h:63:25 */
type X__int32_t = int32   /* _types.h:64:15 */
type X__uint32_t = uint32 /* _types.h:65:23 */
// LONGLONG
type X__int64_t = int64 /* _types.h:67:20 */
// LONGLONG
type X__uint64_t = uint64 /* _types.h:69:28 */

// ******** Minimum-width integer types
type X__int_least8_t = X__int8_t     /* _types.h:72:19 */
type X__uint_least8_t = X__uint8_t   /* _types.h:73:20 */
type X__int_least16_t = X__int16_t   /* _types.h:74:20 */
type X__uint_least16_t = X__uint16_t /* _types.h:75:21 */
type X__int_least32_t = X__int32_t   /* _types.h:76:20 */
type X__uint_least32_t = X__uint32_t /* _types.h:77:21 */
type X__int_least64_t = X__int64_t   /* _types.h:78:20 */
type X__uint_least64_t = X__uint64_t /* _types.h:79:21 */

// 7.18.1.3 Fastest minimum-width integer types
type X__int_fast8_t = X__int32_t    /* _types.h:82:20 */
type X__uint_fast8_t = X__uint32_t  /* _types.h:83:21 */
type X__int_fast16_t = X__int32_t   /* _types.h:84:20 */
type X__uint_fast16_t = X__uint32_t /* _types.h:85:21 */
type X__int_fast32_t = X__int32_t   /* _types.h:86:20 */
type X__uint_fast32_t = X__uint32_t /* _types.h:87:21 */
type X__int_fast64_t = X__int64_t   /* _types.h:88:20 */
type X__uint_fast64_t = X__uint64_t /* _types.h:89:21 */

// 7.18.1.4 Integer types capable of holding object pointers
type X__intptr_t = int64   /* _types.h:104:16 */
type X__uintptr_t = uint64 /* _types.h:105:24 */

// 7.18.1.5 Greatest-width integer types
type X__intmax_t = X__int64_t   /* _types.h:108:20 */
type X__uintmax_t = X__uint64_t /* _types.h:109:21 */

// Register size
type X__register_t = int64 /* _types.h:112:16 */

// VM system types
type X__vaddr_t = uint64 /* _types.h:115:24 */
type X__paddr_t = uint64 /* _types.h:116:24 */
type X__vsize_t = uint64 /* _types.h:117:24 */
type X__psize_t = uint64 /* _types.h:118:24 */

// Standard system types
type X__double_t = float64           /* _types.h:121:18 */
type X__float_t = float32            /* _types.h:122:17 */
type X__ptrdiff_t = int64            /* _types.h:123:16 */
type X__size_t = uint64              /* _types.h:124:24 */
type X__ssize_t = int64              /* _types.h:125:16 */
type X__va_list = X__builtin_va_list /* _types.h:127:27 */

// Wide character support types
type X__wchar_t = int32     /* _types.h:137:15 */
type X__wint_t = int32      /* _types.h:140:15 */
type X__rune_t = int32      /* _types.h:141:15 */
type X__wctrans_t = uintptr /* _types.h:142:14 */
type X__wctype_t = uintptr  /* _types.h:143:14 */

type X__blkcnt_t = X__int64_t    /* _types.h:39:19 */ // blocks allocated for file
type X__blksize_t = X__int32_t   /* _types.h:40:19 */ // optimal blocksize for I/O
type X__clock_t = X__int64_t     /* _types.h:41:19 */ // ticks in CLOCKS_PER_SEC
type X__clockid_t = X__int32_t   /* _types.h:42:19 */ // CLOCK_* identifiers
type X__cpuid_t = uint64         /* _types.h:43:23 */ // CPU id
type X__dev_t = X__int32_t       /* _types.h:44:19 */ // device number
type X__fixpt_t = X__uint32_t    /* _types.h:45:20 */ // fixed point number
type X__fsblkcnt_t = X__uint64_t /* _types.h:46:20 */ // file system block count
type X__fsfilcnt_t = X__uint64_t /* _types.h:47:20 */ // file system file count
type X__gid_t = X__uint32_t      /* _types.h:48:20 */ // group id
type X__id_t = X__uint32_t       /* _types.h:49:20 */ // may contain pid, uid or gid
type X__in_addr_t = X__uint32_t  /* _types.h:50:20 */ // base type for internet address
type X__in_port_t = X__uint16_t  /* _types.h:51:20 */ // IP port type
type X__ino_t = X__uint64_t      /* _types.h:52:20 */ // inode number
type X__key_t = int64            /* _types.h:53:15 */ // IPC key (for Sys V IPC)
type X__mode_t = X__uint32_t     /* _types.h:54:20 */ // permissions
type X__nlink_t = X__uint32_t    /* _types.h:55:20 */ // link count
type X__off_t = X__int64_t       /* _types.h:56:19 */ // file offset or size
type X__pid_t = X__int32_t       /* _types.h:57:19 */ // process id
type X__rlim_t = X__uint64_t     /* _types.h:58:20 */ // resource limit
type X__sa_family_t = X__uint8_t /* _types.h:59:19 */ // sockaddr address family type
type X__segsz_t = X__int32_t     /* _types.h:60:19 */ // segment size
type X__socklen_t = X__uint32_t  /* _types.h:61:20 */ // length type for network syscalls
type X__suseconds_t = int64      /* _types.h:62:15 */ // microseconds (signed)
type X__time_t = X__int64_t      /* _types.h:63:19 */ // epoch time
type X__timer_t = X__int32_t     /* _types.h:64:19 */ // POSIX timer identifiers
type X__uid_t = X__uint32_t      /* _types.h:65:20 */ // user id
type X__useconds_t = X__uint32_t /* _types.h:66:20 */ // microseconds

// mbstate_t is an opaque object to keep conversion state, during multibyte
// stream conversions. The content must not be referenced by user programs.
type X__mbstate_t = struct {
	F__ccgo_pad1 [0]uint64
	F__mbstate8  [128]int8
} /* _types.h:75:3 */

// Tell sys/endian.h we have MD variants of the swap macros.

// Note that these macros evaluate their arguments several times.

// Public names

// These are specified to be function-like macros to match the spec

// POSIX names

// original BSD names

// these were exposed here before

// ancient stuff

type U_char = uint8   /* types.h:51:23 */
type U_short = uint16 /* types.h:52:24 */
type U_int = uint32   /* types.h:53:22 */
type U_long = uint64  /* types.h:54:23 */

type Unchar = uint8  /* types.h:56:23 */ // Sys V compatibility
type Ushort = uint16 /* types.h:57:24 */ // Sys V compatibility
type Uint = uint32   /* types.h:58:22 */ // Sys V compatibility
type Ulong = uint64  /* types.h:59:23 */ // Sys V compatibility

type Cpuid_t = X__cpuid_t       /* types.h:61:19 */ // CPU id
type Register_t = X__register_t /* types.h:62:22 */ // register-sized type

// XXX The exact-width bit types should only be exposed if __BSD_VISIBLE
//     but the rest of the includes are not ready for that yet.

type Int8_t = X__int8_t /* types.h:75:19 */

type Uint8_t = X__uint8_t /* types.h:80:20 */

type Int16_t = X__int16_t /* types.h:85:20 */

type Uint16_t = X__uint16_t /* types.h:90:21 */

type Int32_t = X__int32_t /* types.h:95:20 */

type Uint32_t = X__uint32_t /* types.h:100:21 */

type Int64_t = X__int64_t /* types.h:105:20 */

type Uint64_t = X__uint64_t /* types.h:110:21 */

// BSD-style unsigned bits types
type U_int8_t = X__uint8_t   /* types.h:114:19 */
type U_int16_t = X__uint16_t /* types.h:115:20 */
type U_int32_t = X__uint32_t /* types.h:116:20 */
type U_int64_t = X__uint64_t /* types.h:117:20 */

// quads, deprecated in favor of 64 bit int types
type Quad_t = X__int64_t    /* types.h:120:19 */
type U_quad_t = X__uint64_t /* types.h:121:20 */

// VM system types
type Vaddr_t = X__vaddr_t /* types.h:125:19 */
type Paddr_t = X__paddr_t /* types.h:126:19 */
type Vsize_t = X__vsize_t /* types.h:127:19 */
type Psize_t = X__psize_t /* types.h:128:19 */

// Standard system types
type Blkcnt_t = X__blkcnt_t       /* types.h:132:20 */ // blocks allocated for file
type Blksize_t = X__blksize_t     /* types.h:133:21 */ // optimal blocksize for I/O
type Caddr_t = uintptr            /* types.h:134:14 */ // core address
type Daddr32_t = X__int32_t       /* types.h:135:19 */ // 32-bit disk address
type Daddr_t = X__int64_t         /* types.h:136:19 */ // 64-bit disk address
type Dev_t = X__dev_t             /* types.h:137:18 */ // device number
type Fixpt_t = X__fixpt_t         /* types.h:138:19 */ // fixed point number
type Gid_t = X__gid_t             /* types.h:139:18 */ // group id
type Id_t = X__id_t               /* types.h:140:17 */ // may contain pid, uid or gid
type Ino_t = X__ino_t             /* types.h:141:18 */ // inode number
type Key_t = X__key_t             /* types.h:142:18 */ // IPC key (for Sys V IPC)
type Mode_t = X__mode_t           /* types.h:143:18 */ // permissions
type Nlink_t = X__nlink_t         /* types.h:144:19 */ // link count
type Rlim_t = X__rlim_t           /* types.h:145:18 */ // resource limit
type Segsz_t = X__segsz_t         /* types.h:146:19 */ // segment size
type Uid_t = X__uid_t             /* types.h:147:18 */ // user id
type Useconds_t = X__useconds_t   /* types.h:148:22 */ // microseconds
type Suseconds_t = X__suseconds_t /* types.h:149:23 */ // microseconds (signed)
type Fsblkcnt_t = X__fsblkcnt_t   /* types.h:150:22 */ // file system block count
type Fsfilcnt_t = X__fsfilcnt_t   /* types.h:151:22 */ // file system file count

// The following types may be defined in multiple header files.
type Clock_t = X__clock_t /* types.h:158:19 */

type Clockid_t = X__clockid_t /* types.h:163:21 */

type Pid_t = X__pid_t /* types.h:168:18 */

type Ssize_t = X__ssize_t /* types.h:178:19 */

type Time_t = X__time_t /* types.h:183:18 */

type Timer_t = X__timer_t /* types.h:188:19 */

type Off_t = X__off_t /* types.h:193:18 */

// Major, minor numbers, dev_t's.

//	$OpenBSD: time.h,v 1.62 2022/07/23 22:58:51 cheloha Exp $
//	$NetBSD: time.h,v 1.18 1996/04/23 10:29:33 mycroft Exp $

// Copyright (c) 1982, 1986, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)time.h	8.2 (Berkeley) 7/10/94

//	$OpenBSD: select.h,v 1.17 2016/09/12 19:41:20 guenther Exp $

// -
// Copyright (c) 1992, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)select.h	8.2 (Berkeley) 1/4/94

//	$OpenBSD: types.h,v 1.49 2022/08/06 13:31:13 semarie Exp $
//	$NetBSD: types.h,v 1.29 1996/11/15 22:48:25 jtc Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

type Timeval = struct {
	Ftv_sec  Time_t
	Ftv_usec Suseconds_t
} /* select.h:41:1 */

type Timespec = struct {
	Ftv_sec  Time_t
	Ftv_nsec int64
} /* select.h:49:1 */

// Select uses bit masks of file descriptors in longs.  These macros
// manipulate such bit fields (the filesystem macros use chars).
// FD_SETSIZE may be defined by the user, but the default here should
// be enough for most uses.

// We don't want to pollute the namespace with select(2) internals.
// Non-underscore versions are exposed later #if __BSD_VISIBLE
type X__fd_mask = Uint32_t /* select.h:70:18 */

type Fd_set1 = struct{ Ffds_bits [32]X__fd_mask } /* select.h:74:9 */

type Fd_set = Fd_set1 /* select.h:76:3 */

type Sigset_t = uint32 /* select.h:122:22 */

type Timezone = struct {
	Ftz_minuteswest int32
	Ftz_dsttime     int32
} /* time.h:72:1 */

// Operations on timevals.

// Operations on timespecs.

// Names of the interval timers, and structure
// defining a timer setting.

type Itimerval = struct {
	Fit_interval struct {
		Ftv_sec  Time_t
		Ftv_usec Suseconds_t
	}
	Fit_value struct {
		Ftv_sec  Time_t
		Ftv_usec Suseconds_t
	}
} /* time.h:148:1 */

// clock information structure for sysctl({CTL_KERN, KERN_CLOCKRATE})
type Clockinfo = struct {
	Fhz     int32
	Ftick   int32
	Fstathz int32
	Fprofhz int32
} /* time.h:157:1 */

//	$OpenBSD: time.h,v 1.31 2018/10/30 16:28:42 guenther Exp $
//	$NetBSD: time.h,v 1.9 1994/10/26 00:56:35 cgd Exp $

// Copyright (c) 1989 The Regents of the University of California.
// All rights reserved.
//
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)time.h	5.12 (Berkeley) 3/9/91

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: _null.h,v 1.2 2016/09/09 22:07:58 millert Exp $

// Written by Todd C. Miller, September 9, 2016
// Public domain.

//	$OpenBSD: _types.h,v 1.10 2022/08/06 13:31:13 semarie Exp $

// -
// Copyright (c) 1990, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.3 (Berkeley) 1/5/94

// Frequency of the clock ticks reported by times().  Deprecated - use
// sysconf(_SC_CLK_TCK) instead.  (Removed in 1003.1-2001.)

//	$OpenBSD: _time.h,v 1.9 2017/12/18 05:51:53 cheloha Exp $

// Copyright (c) 1982, 1986, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.

// Per-process and per-thread clocks encode the PID or TID into the
// high bits, with the type in the bottom bits

// Structure defined by POSIX 1003.1b to be like a itimerval,
// but with timespecs. Used in the timer_*() system calls.
type Itimerspec = struct {
	Fit_interval struct {
		Ftv_sec  Time_t
		Ftv_nsec int64
	}
	Fit_value struct {
		Ftv_sec  Time_t
		Ftv_nsec int64
	}
} /* _time.h:56:1 */

type Locale_t = uintptr /* time.h:107:14 */

type Tm = struct {
	Ftm_sec      int32
	Ftm_min      int32
	Ftm_hour     int32
	Ftm_mday     int32
	Ftm_mon      int32
	Ftm_year     int32
	Ftm_wday     int32
	Ftm_yday     int32
	Ftm_isdst    int32
	F__ccgo_pad1 [4]byte
	Ftm_gmtoff   int64
	Ftm_zone     uintptr
} /* time.h:111:1 */

//	$OpenBSD: signal.h,v 1.29 2018/04/18 16:05:20 deraadt Exp $
//	$NetBSD: signal.h,v 1.21 1996/02/09 18:25:32 christos Exp $

// Copyright (c) 1982, 1986, 1989, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)signal.h	8.2 (Berkeley) 1/21/94

// $OpenBSD: signal.h,v 1.2 2017/03/12 17:57:12 kettenis Exp $
// Copyright (c) 1992, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Ralph Campbell.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)signal.h	8.1 (Berkeley) 6/10/93

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

type Sig_atomic_t = int32 /* signal.h:41:13 */

// Information pushed on stack when a signal is delivered.
// This is used by the kernel to restore state following
// execution of the signal handler.  It is also made available
// to the handler to allow it to restore state properly if
// a non-standard exit is performed.
type Sigcontext = struct {
	F__sc_unused int32
	Fsc_mask     int32
	Fsc_sp       uint64
	Fsc_lr       uint64
	Fsc_elr      uint64
	Fsc_spsr     uint64
	Fsc_x        [30]uint64
	Fsc_cookie   int64
} /* signal.h:51:1 */

// Language spec says we must list exactly one parameter, even though we
// actually supply three.  Ugh!

//	$OpenBSD: siginfo.h,v 1.12 2017/04/07 04:48:44 guenther Exp $

// Copyright (c) 1997 Theo de Raadt
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
// IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
// OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
// IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
// NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
// THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

type Sigval = struct {
	F__ccgo_pad1 [0]uint64
	Fsival_int   int32
	F__ccgo_pad2 [4]byte
} /* siginfo.h:33:1 */

// Negative signal codes are reserved for future use for
// user generated signals.

// The machine dependent signal codes (SIGILL, SIGFPE,
// SIGSEGV, and SIGBUS)

// SIGTRAP signal codes

// SIGCHLD signal codes

//	$OpenBSD: time.h,v 1.62 2022/07/23 22:58:51 cheloha Exp $
//	$NetBSD: time.h,v 1.18 1996/04/23 10:29:33 mycroft Exp $

// Copyright (c) 1982, 1986, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)time.h	8.2 (Berkeley) 7/10/94

type Siginfo_t = struct {
	Fsi_signo    int32
	Fsi_code     int32
	Fsi_errno    int32
	F__ccgo_pad1 [4]byte
	F_data       struct {
		F__ccgo_pad1 [0]uint64
		F_pad        [29]int32
		F__ccgo_pad2 [4]byte
	}
} /* siginfo.h:173:3 */

// Signal vector "template" used in sigaction call.
type Sigaction = struct {
	F__sigaction_u struct{ F__sa_handler uintptr }
	Fsa_mask       Sigset_t
	Fsa_flags      int32
} /* signal.h:112:1 */

// if SA_SIGINFO is set, sa_sigaction is to be used instead of sa_handler.

// Flags for sigprocmask:

type Sig_t = uintptr /* signal.h:146:14 */ // type of signal function

// 4.3 compatibility:
// Signal vector "template" used in sigvec call.
type Sigvec = struct {
	Fsv_handler uintptr
	Fsv_mask    int32
	Fsv_flags   int32
} /* signal.h:152:1 */

// Macro for converting signal number to a mask suitable for
// sigblock().

// Structure used in sigaltstack call.
type Sigaltstack = struct {
	Fss_sp       uintptr
	Fss_size     Size_t
	Fss_flags    int32
	F__ccgo_pad1 [4]byte
} /* signal.h:176:9 */

// Macro for converting signal number to a mask suitable for
// sigblock().

// Structure used in sigaltstack call.
type Stack_t = Sigaltstack /* signal.h:180:3 */

type Ucontext_t = Sigcontext /* signal.h:190:27 */

//	$OpenBSD: limits.h,v 1.19 2015/01/20 22:09:50 tedu Exp $
//	$NetBSD: limits.h,v 1.7 1994/10/26 00:56:00 cgd Exp $

// Copyright (c) 1988 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)limits.h	5.9 (Berkeley) 4/3/91

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

// $OpenBSD: limits.h,v 1.10 2012/06/30 20:21:10 guenther Exp $
// Copyright (c) 2002 Marc Espie.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE OPENBSD PROJECT AND CONTRIBUTORS
// ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OPENBSD
// PROJECT OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

// Common definitions for limits.h.

// Legacy
//	$OpenBSD: limits.h,v 1.1 2016/12/17 23:38:33 patrick Exp $
//	$NetBSD: limits.h,v 1.4 2003/04/28 23:16:18 bjh21 Exp $

// Copyright (c) 1988 The Regents of the University of California.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	from: @(#)limits.h	7.2 (Berkeley) 6/28/90

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

// max value for unsigned long
// max value for a signed long
// min value for a signed long

// max value for unsigned long long
// max value for a signed long long
// min value for a signed long long

//	$OpenBSD: syslimits.h,v 1.15 2022/02/22 16:58:08 deraadt Exp $
//	$NetBSD: syslimits.h,v 1.12 1995/10/05 05:26:19 thorpej Exp $

// Copyright (c) 1988, 1993
//	The Regents of the University of California.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)syslimits.h	8.1 (Berkeley) 6/2/93

//	$OpenBSD: cdefs.h,v 1.43 2018/10/29 17:10:40 guenther Exp $
//	$NetBSD: cdefs.h,v 1.16 1996/04/03 20:46:39 christos Exp $

// Copyright (c) 1991, 1993
//	The Regents of the University of California.  All rights reserved.
//
// This code is derived from software contributed to Berkeley by
// Berkeley Software Design, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)cdefs.h	8.7 (Berkeley) 1/21/94

//	$OpenBSD: sched.h,v 1.1 2017/10/15 23:40:33 guenther Exp $

// sched.h: POSIX 1003.1b Process Scheduling header

// -
// Copyright (c) 1996, 1997
//	HD Associates, Inc.  All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. All advertising materials mentioning features or use of this software
//    must display the following acknowledgement:
//	This product includes software developed by HD Associates, Inc
//	and Jukka Antero Ukkonen.
// 4. Neither the name of the author nor the names of any co-contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY HD ASSOCIATES AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL HD ASSOCIATES OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//

//	$OpenBSD: types.h,v 1.49 2022/08/06 13:31:13 semarie Exp $
//	$NetBSD: types.h,v 1.29 1996/11/15 22:48:25 jtc Exp $

// -
// Copyright (c) 1982, 1986, 1991, 1993
//	The Regents of the University of California.  All rights reserved.
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)types.h	8.4 (Berkeley) 1/21/94

//	$OpenBSD: time.h,v 1.31 2018/10/30 16:28:42 guenther Exp $
//	$NetBSD: time.h,v 1.9 1994/10/26 00:56:35 cgd Exp $

// Copyright (c) 1989 The Regents of the University of California.
// All rights reserved.
//
// (c) UNIX System Laboratories, Inc.
// All or some portions of this file are derived from material licensed
// to the University of California by American Telephone and Telegraph
// Co. or Unix System Laboratories, Inc. and are reproduced herein with
// the permission of UNIX System Laboratories, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1. Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
// 3. Neither the name of the University nor the names of its contributors
//    may be used to endorse or promote products derived from this software
//    without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
// OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
// HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
// OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
// SUCH DAMAGE.
//
//	@(#)time.h	5.12 (Berkeley) 3/9/91

// Scheduling policies

type Sched_param = struct{ Fsched_priority int32 } /* sched.h:51:1 */

type Pthread_once = struct {
	Fstate       int32
	F__ccgo_pad1 [4]byte
	Fmutex       Pthread_mutex_t
} /* pthread.h:101:1 */

// Primitive system data type definitions required by P1003.1c.
//
// Note that P1003.1c specifies that there are no defined comparison
// or assignment operators for the types pthread_attr_t, pthread_cond_t,
// pthread_condattr_t, pthread_mutex_t, pthread_mutexattr_t.
type Pthread_t = uintptr             /* pthread.h:112:26 */
type Pthread_attr_t = uintptr        /* pthread.h:113:30 */
type Pthread_mutex_t = uintptr       /* pthread.h:114:39 */
type Pthread_mutexattr_t = uintptr   /* pthread.h:115:35 */
type Pthread_cond_t = uintptr        /* pthread.h:116:30 */
type Pthread_condattr_t = uintptr    /* pthread.h:117:34 */
type Pthread_key_t = int32           /* pthread.h:118:16 */
type Pthread_once_t = Pthread_once   /* pthread.h:119:30 */
type Pthread_rwlock_t = uintptr      /* pthread.h:120:32 */
type Pthread_rwlockattr_t = uintptr  /* pthread.h:121:35 */
type Pthread_barrier_t = uintptr     /* pthread.h:122:33 */
type Pthread_barrierattr_t = uintptr /* pthread.h:123:36 */
type Pthread_spinlock_t = uintptr    /* pthread.h:124:33 */

// Additional type definitions:
//
// Note that P1003.1c reserves the prefixes pthread_ and PTHREAD_ for
// use in header symbols.
type Pthread_addr_t = uintptr         /* pthread.h:132:14 */
type Pthread_startroutine_t = uintptr /* pthread.h:133:14 */

var _ int8 /* gen.c:2:13: */
