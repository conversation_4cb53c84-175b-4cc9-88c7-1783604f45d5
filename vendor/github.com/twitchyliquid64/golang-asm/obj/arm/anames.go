// Code generated by stringer -i a.out.go -o anames.go -p arm; DO NOT EDIT.

package arm

import "github.com/twitchyliquid64/golang-asm/obj"

var Anames = []string{
	obj.A_ARCHSPECIFIC: "AND",
	"EOR",
	"SUB",
	"RSB",
	"ADD",
	"ADC",
	"SBC",
	"RSC",
	"TST",
	"TEQ",
	"CMP",
	"CMN",
	"ORR",
	"BIC",
	"MVN",
	"BEQ",
	"BNE",
	"BCS",
	"BHS",
	"BCC",
	"BLO",
	"BMI",
	"BPL",
	"BVS",
	"BVC",
	"BHI",
	"BLS",
	"BGE",
	"BLT",
	"BGT",
	"BLE",
	"MOVWD",
	"MOVWF",
	"MOVDW",
	"MOVFW",
	"MOVFD",
	"MOVDF",
	"MOVF",
	"MOVD",
	"CMPF",
	"CMPD",
	"ADDF",
	"ADDD",
	"SU<PERSON>",
	"SUBD",
	"MULF",
	"MULD",
	"NMULF",
	"NMULD",
	"MULA<PERSON>",
	"MULAD",
	"NMULAF",
	"NMULAD",
	"MULSF",
	"MULSD",
	"NMULSF",
	"NMULSD",
	"FMULAF",
	"FMULAD",
	"FNMULAF",
	"FNMULAD",
	"FMULSF",
	"FMULSD",
	"FNMULSF",
	"FNMULSD",
	"DIVF",
	"DIVD",
	"SQRTF",
	"SQRTD",
	"ABSF",
	"ABSD",
	"NEGF",
	"NEGD",
	"SRL",
	"SRA",
	"SLL",
	"MULU",
	"DIVU",
	"MUL",
	"MMUL",
	"DIV",
	"MOD",
	"MODU",
	"DIVHW",
	"DIVUHW",
	"MOVB",
	"MOVBS",
	"MOVBU",
	"MOVH",
	"MOVHS",
	"MOVHU",
	"MOVW",
	"MOVM",
	"SWPBU",
	"SWPW",
	"RFE",
	"SWI",
	"MULA",
	"MULS",
	"MMULA",
	"MMULS",
	"WORD",
	"MULL",
	"MULAL",
	"MULLU",
	"MULALU",
	"BX",
	"BXRET",
	"DWORD",
	"LDREX",
	"STREX",
	"LDREXD",
	"STREXD",
	"DMB",
	"PLD",
	"CLZ",
	"REV",
	"REV16",
	"REVSH",
	"RBIT",
	"XTAB",
	"XTAH",
	"XTABU",
	"XTAHU",
	"BFX",
	"BFXU",
	"BFC",
	"BFI",
	"MULWT",
	"MULWB",
	"MULBB",
	"MULAWT",
	"MULAWB",
	"MULABB",
	"MRC",
	"LAST",
}
