// Code generated by stringer -i a.out.go -o anames.go -p mips; DO NOT EDIT.

package mips

import "github.com/twitchyliquid64/golang-asm/obj"

var Anames = []string{
	obj.A_ARCHSPECIFIC: "ABSD",
	"ABSF",
	"ABSW",
	"ADD",
	"ADDD",
	"ADDF",
	"ADDU",
	"ADDW",
	"AND",
	"BEQ",
	"BFPF",
	"BFPT",
	"BGEZ",
	"BGEZAL",
	"BGTZ",
	"BLEZ",
	"BLTZ",
	"BLTZAL",
	"BNE",
	"BREAK",
	"CLO",
	"CLZ",
	"CMOVF",
	"CMOVN",
	"CMOVT",
	"CMOVZ",
	"CMPEQD",
	"CMPEQF",
	"CMPGED",
	"CMPGEF",
	"CMPGTD",
	"CMPGTF",
	"DIV",
	"DIVD",
	"DIVF",
	"DIVU",
	"DIVW",
	"GOK",
	"LL",
	"LLV",
	"LUI",
	"MADD",
	"MOVB",
	"MOVB<PERSON>",
	"MOVD",
	"MOVDF",
	"MOVDW",
	"MOVF",
	"MOVFD",
	"MOVFW",
	"MOVH",
	"MOVHU",
	"MOVW",
	"MOVWD",
	"MOVWF",
	"MOVWL",
	"MOVWR",
	"MSUB",
	"MUL",
	"MULD",
	"MULF",
	"MULU",
	"MULW",
	"NEGD",
	"NEGF",
	"NEGW",
	"NEGV",
	"NOOP",
	"NOR",
	"OR",
	"REM",
	"REMU",
	"RFE",
	"SC",
	"SCV",
	"SGT",
	"SGTU",
	"SLL",
	"SQRTD",
	"SQRTF",
	"SRA",
	"SRL",
	"SUB",
	"SUBD",
	"SUBF",
	"SUBU",
	"SUBW",
	"SYNC",
	"SYSCALL",
	"TEQ",
	"TLBP",
	"TLBR",
	"TLBWI",
	"TLBWR",
	"TNE",
	"WORD",
	"XOR",
	"MOVV",
	"MOVVL",
	"MOVVR",
	"SLLV",
	"SRAV",
	"SRLV",
	"DIVV",
	"DIVVU",
	"REMV",
	"REMVU",
	"MULV",
	"MULVU",
	"ADDV",
	"ADDVU",
	"SUBV",
	"SUBVU",
	"TRUNCFV",
	"TRUNCDV",
	"TRUNCFW",
	"TRUNCDW",
	"MOVWU",
	"MOVFV",
	"MOVDV",
	"MOVVF",
	"MOVVD",
	"VMOVB",
	"VMOVH",
	"VMOVW",
	"VMOVD",
	"LAST",
}
