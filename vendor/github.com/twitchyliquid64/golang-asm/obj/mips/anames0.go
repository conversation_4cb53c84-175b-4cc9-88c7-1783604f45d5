// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package mips

var cnames0 = []string{
	"NONE",
	"REG",
	"FREG",
	"FCREG",
	"MREG",
	"WREG",
	"HI",
	"<PERSON>O",
	"ZCON",
	"<PERSON>ON",
	"UCON",
	"ADD0CON",
	"AND0CON",
	"ADDCON",
	"ANDCON",
	"LCON",
	"DCON",
	"SACON",
	"SECON",
	"LACON",
	"LECON",
	"DACON",
	"STCON",
	"SBRA",
	"LBRA",
	"SAUTO",
	"<PERSON>UTO",
	"SEXT",
	"LEXT",
	"ZOR<PERSON>",
	"SOREG",
	"LOREG",
	"GOK",
	"ADDR",
	"TLS",
	"TEXTSIZE",
	"NCLA<PERSON>",
}
