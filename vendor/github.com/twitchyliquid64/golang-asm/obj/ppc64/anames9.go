// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ppc64

var cnames9 = []string{
	"NONE",
	"REG",
	"FREG",
	"VREG",
	"VSREG",
	"CREG",
	"SP<PERSON>",
	"Z<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>ON",
	"ADDCON",
	"ANDCON",
	"LCON",
	"DCON",
	"SACON",
	"SECON",
	"LACON",
	"LECON",
	"DACON",
	"SBRA",
	"LBRA",
	"LBR<PERSON><PERSON>",
	"SAUTO",
	"LAUTO",
	"SEXT",
	"LEXT",
	"ZOR<PERSON>",
	"SOREG",
	"LOREG",
	"FPSCR",
	"MSR",
	"XER",
	"LR",
	"CTR",
	"ANY",
	"GOK",
	"ADDR",
	"GOTAD<PERSON>",
	"TOCADDR",
	"TLS_LE",
	"TLS_IE",
	"TEXTSIZ<PERSON>",
	"NCLASS",
}
