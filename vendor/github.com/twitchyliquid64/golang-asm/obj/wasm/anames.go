// Code generated by stringer -i a.out.go -o anames.go -p wasm; DO NOT EDIT.

package wasm

import "github.com/twitchyliquid64/golang-asm/obj"

var Anames = []string{
	obj.A_ARCHSPECIFIC: "CallImport",
	"Get",
	"Set",
	"Tee",
	"Not",
	"Unreachable",
	"Nop",
	"Block",
	"Loop",
	"If",
	"Else",
	"End",
	"Br",
	"BrIf",
	"BrTable",
	"Return",
	"Call",
	"CallIndirect",
	"Drop",
	"Select",
	"LocalGet",
	"LocalSet",
	"LocalTee",
	"GlobalGet",
	"GlobalSet",
	"I32Load",
	"I64Load",
	"F32Load",
	"F64Load",
	"I32Load8S",
	"I32Load8U",
	"I32Load16S",
	"I32Load16U",
	"I64Load8S",
	"I64Load8<PERSON>",
	"I64Load16S",
	"I<PERSON>Load16<PERSON>",
	"I64Load32S",
	"I64Load32U",
	"I32Store",
	"I64Store",
	"F32Store",
	"F64Store",
	"I32Store8",
	"I32Store16",
	"I64Store8",
	"I64Store16",
	"I64Store32",
	"CurrentMemory",
	"GrowMemory",
	"I32Const",
	"I64Const",
	"F32Const",
	"F64Const",
	"I32Eqz",
	"I32Eq",
	"I32Ne",
	"I32LtS",
	"I32LtU",
	"I32GtS",
	"I32GtU",
	"I32LeS",
	"I32LeU",
	"I32GeS",
	"I32GeU",
	"I64Eqz",
	"I64Eq",
	"I64Ne",
	"I64LtS",
	"I64LtU",
	"I64GtS",
	"I64GtU",
	"I64LeS",
	"I64LeU",
	"I64GeS",
	"I64GeU",
	"F32Eq",
	"F32Ne",
	"F32Lt",
	"F32Gt",
	"F32Le",
	"F32Ge",
	"F64Eq",
	"F64Ne",
	"F64Lt",
	"F64Gt",
	"F64Le",
	"F64Ge",
	"I32Clz",
	"I32Ctz",
	"I32Popcnt",
	"I32Add",
	"I32Sub",
	"I32Mul",
	"I32DivS",
	"I32DivU",
	"I32RemS",
	"I32RemU",
	"I32And",
	"I32Or",
	"I32Xor",
	"I32Shl",
	"I32ShrS",
	"I32ShrU",
	"I32Rotl",
	"I32Rotr",
	"I64Clz",
	"I64Ctz",
	"I64Popcnt",
	"I64Add",
	"I64Sub",
	"I64Mul",
	"I64DivS",
	"I64DivU",
	"I64RemS",
	"I64RemU",
	"I64And",
	"I64Or",
	"I64Xor",
	"I64Shl",
	"I64ShrS",
	"I64ShrU",
	"I64Rotl",
	"I64Rotr",
	"F32Abs",
	"F32Neg",
	"F32Ceil",
	"F32Floor",
	"F32Trunc",
	"F32Nearest",
	"F32Sqrt",
	"F32Add",
	"F32Sub",
	"F32Mul",
	"F32Div",
	"F32Min",
	"F32Max",
	"F32Copysign",
	"F64Abs",
	"F64Neg",
	"F64Ceil",
	"F64Floor",
	"F64Trunc",
	"F64Nearest",
	"F64Sqrt",
	"F64Add",
	"F64Sub",
	"F64Mul",
	"F64Div",
	"F64Min",
	"F64Max",
	"F64Copysign",
	"I32WrapI64",
	"I32TruncF32S",
	"I32TruncF32U",
	"I32TruncF64S",
	"I32TruncF64U",
	"I64ExtendI32S",
	"I64ExtendI32U",
	"I64TruncF32S",
	"I64TruncF32U",
	"I64TruncF64S",
	"I64TruncF64U",
	"F32ConvertI32S",
	"F32ConvertI32U",
	"F32ConvertI64S",
	"F32ConvertI64U",
	"F32DemoteF64",
	"F64ConvertI32S",
	"F64ConvertI32U",
	"F64ConvertI64S",
	"F64ConvertI64U",
	"F64PromoteF32",
	"I32ReinterpretF32",
	"I64ReinterpretF64",
	"F32ReinterpretI32",
	"F64ReinterpretI64",
	"I32Extend8S",
	"I32Extend16S",
	"I64Extend8S",
	"I64Extend16S",
	"I64Extend32S",
	"I32TruncSatF32S",
	"I32TruncSatF32U",
	"I32TruncSatF64S",
	"I32TruncSatF64U",
	"I64TruncSatF32S",
	"I64TruncSatF32U",
	"I64TruncSatF64S",
	"I64TruncSatF64U",
	"Last",
	"RESUMEPOINT",
	"CALLNORESUME",
	"RETUNWIND",
	"MOVB",
	"MOVH",
	"MOVW",
	"MOVD",
	"WORD",
	"LAST",
}
