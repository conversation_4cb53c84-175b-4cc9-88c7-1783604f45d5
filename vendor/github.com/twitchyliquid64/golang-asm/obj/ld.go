// Derived from Inferno utils/6l/obj.c and utils/6l/span.c
// https://bitbucket.org/inferno-os/inferno-os/src/master/utils/6l/obj.c
// https://bitbucket.org/inferno-os/inferno-os/src/master/utils/6l/span.c
//
//	Copyright © 1994-1999 Lucent Technologies Inc.  All rights reserved.
//	Portions Copyright © 1995-1997 C H Forsyth (<EMAIL>)
//	Portions Copyright © 1997-1999 Vita Nuova Limited
//	Portions Copyright © 2000-2007 Vita Nuova Holdings Limited (www.vitanuova.com)
//	Portions Copyright © 2004,2006 <PERSON> Ellis
//	Portions Copyright © 2005-2007 C <PERSON>syth (<EMAIL>)
//	Revisions Copyright © 2000-2007 Lucent Technologies Inc. and others
//	Portions Copyright © 2009 The Go Authors. All rights reserved.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

package obj

/*
 * add library to library list.
 *	srcref: src file referring to package
 *	objref: object file referring to package
 *	file: object file, e.g., /home/<USER>/go/pkg/container/vector.a
 *	pkg: package import path, e.g. container/vector
 */

const (
	LOG = 5
)

func mkfwd(sym *LSym) {
	var dwn [LOG]int32
	var cnt [LOG]int32
	var lst [LOG]*Prog

	for i := 0; i < LOG; i++ {
		if i == 0 {
			cnt[i] = 1
		} else {
			cnt[i] = LOG * cnt[i-1]
		}
		dwn[i] = 1
		lst[i] = nil
	}

	i := 0
	for p := sym.Func.Text; p != nil && p.Link != nil; p = p.Link {
		i--
		if i < 0 {
			i = LOG - 1
		}
		p.Forwd = nil
		dwn[i]--
		if dwn[i] <= 0 {
			dwn[i] = cnt[i]
			if lst[i] != nil {
				lst[i].Forwd = p
			}
			lst[i] = p
		}
	}
}

func Appendp(q *Prog, newprog ProgAlloc) *Prog {
	p := newprog()
	p.Link = q.Link
	q.Link = p
	p.Pos = q.Pos
	return p
}
