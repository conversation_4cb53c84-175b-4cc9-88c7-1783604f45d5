// Code generated by "stringer -type=SymKind"; DO NOT EDIT.

package objabi

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Sxxx-0]
	_ = x[STEXT-1]
	_ = x[SRODATA-2]
	_ = x[SNOPTRDATA-3]
	_ = x[SDATA-4]
	_ = x[SBSS-5]
	_ = x[SNOPTRBSS-6]
	_ = x[STLSBSS-7]
	_ = x[SDWARFCUINFO-8]
	_ = x[SDWARFCONST-9]
	_ = x[SDWARFFCN-10]
	_ = x[SDWARFABSFCN-11]
	_ = x[SDWARFTYPE-12]
	_ = x[SDWARFVAR-13]
	_ = x[SDWARFRANGE-14]
	_ = x[SDWARFLOC-15]
	_ = x[SDWARFLINES-16]
	_ = x[SABIALIAS-17]
	_ = x[SLIBFUZZER_EXTRA_COUNTER-18]
}

const _SymKind_name = "SxxxSTEXTSRODATASNOPTRDATASDATASBSSSNOPTRBSSSTLSBSSSDWARFCUINFOSDWARFCONSTSDWARFFCNSDWARFABSFCNSDWARFTYPESDWARFVARSDWARFRANGESDWARFLOCSDWARFLINESSABIALIASSLIBFUZZER_EXTRA_COUNTER"

var _SymKind_index = [...]uint8{0, 4, 9, 16, 26, 31, 35, 44, 51, 63, 74, 83, 95, 105, 114, 125, 134, 145, 154, 178}

func (i SymKind) String() string {
	if i >= SymKind(len(_SymKind_index)-1) {
		return "SymKind(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _SymKind_name[_SymKind_index[i]:_SymKind_index[i+1]]
}
