openapi: 3.0.0
info:
  title: Zero Ops Worker System API
  description: API documentation for the Zero Ops Worker System
  version: 1.0.0
servers:
  - url: /api/v1
    description: Worker API base URL
paths:
  /ping:
    get:
      summary: Health check endpoint
      description: Returns a simple response to verify the worker is running
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: pong

  # Worker Management
  /worker/health:
    get:
      summary: Worker health check
      description: Returns the health status of the worker node
      responses:
        '200':
          description: Worker health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  status: "AVAILABLE"
                  cpu_usage: 45.2
                  memory_usage: 67.8
                  disk_usage: 23.1
                  uptime: "2d 14h 32m"
                msg: "Worker health check successful"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Container Operations
  /worker/deploy:
    post:
      summary: Deploy a container
      description: Creates a deployment task record and queues it for processing
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkerDeployRequest'
      responses:
        '200':
          description: Deployment task queued successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  task_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                  status: "QUEUEING"
                msg: "部署任务已加入队列，任务将在后台处理，请稍后查询状态"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /worker/{service_id}/stop:
    put:
      summary: Stop a container
      description: Creates a stop task record and queues it for processing
      security:
        - basicAuth: []
      parameters:
        - name: service_id
          in: path
          required: true
          description: ID of the service to stop
          schema:
            type: string
        - name: nodeIP
          in: query
          description: IP address of the node where the container is running
          schema:
            type: string
      responses:
        '200':
          description: Stop task queued successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  task_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                  status: "QUEUEING"
                msg: "停止任务已加入队列，任务将在后台处理，请稍后查询状态"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /worker/{service_id}/restart:
    put:
      summary: Restart a container
      description: Creates a restart task record and queues it for processing
      security:
        - basicAuth: []
      parameters:
        - name: service_id
          in: path
          required: true
          description: ID of the service to restart
          schema:
            type: string
        - name: nodeIP
          in: query
          description: IP address of the node where the container is running
          schema:
            type: string
      responses:
        '200':
          description: Restart task queued successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  task_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                  status: "QUEUEING"
                msg: "重启任务已加入队列，任务将在后台处理，请稍后查询状态"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /worker/update:
    put:
      summary: Update a container
      description: Creates an update task record and queues it for processing. Stops the existing container and deploys a new one with the updated image. Only updates image name and image URL, other parameters remain unchanged.
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkerUpdateRequest'
      responses:
        '200':
          description: Update task queued successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  task_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                  status: "QUEUEING"
                msg: "升级任务已加入队列，任务将在后台处理，请稍后查询状态"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Status and Monitoring
  /worker/status:
    get:
      summary: Get deployment status
      description: Returns deployment status for one or more services
      parameters:
        - name: service_ids
          in: query
          required: true
          description: Comma-separated list of service IDs
          schema:
            type: string
          example: "svc-001,svc-002,svc-003"
      responses:
        '200':
          description: Deployment status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  records:
                    - service_id: "svc-001"
                      name: "my-service"
                      image_name: "nginx"
                      image_url: "nginx:latest"
                      domain_prefix: "my-service"
                      domain_suffix: ".example.com"
                      status: "RUNNING"
                      node_ip: "*************"
                      created_at: "2024-01-01T10:00:00Z"
                      updated_at: "2024-01-01T10:00:00Z"
                  not_found: ["svc-003"]
                msg: "获取部署状态成功"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /worker/container/status:
    get:
      summary: Get container status
      description: Returns the real-time status of a specific container
      parameters:
        - name: service_id
          in: query
          required: true
          description: Service ID
          schema:
            type: string
        - name: node_ip
          in: query
          required: true
          description: IP address of the node where the container is running
          schema:
            type: string
      responses:
        '200':
          description: Container status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerResponse'
              example:
                code: 200
                data:
                  container_id: "abc123def456"
                  status: "running"
                  image: "nginx:latest"
                  created: "2024-01-01T10:00:00Z"
                  ports: ["80/tcp", "443/tcp"]
                  cpu_usage: 12.5
                  memory_usage: 256
                msg: "获取容器状态成功"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication using username 'devops' and password '3-546-_iuhh5498'
  schemas:
    # Error Response
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message

    # Worker Response
    WorkerResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          description: Response data
        msg:
          type: string
          description: Response message

    # Worker Deploy Request
    WorkerDeployRequest:
      type: object
      properties:
        service_id:
          type: string
          description: Service ID
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        domain_prefix:
          type: string
          description: Domain prefix
        domain_suffix:
          type: string
          description: Domain suffix
        node_ip:
          type: string
          description: Target node IP address
        expiration:
          type: string
          description: Expiration date (YYYY-MM-DDTHH:mm:ss)
        duration_seconds:
          type: integer
          format: int64
          description: Duration in seconds
        api_replica:
          type: integer
          description: Number of API replicas
        api_cpu:
          type: number
          format: float
          description: CPU cores for API
        api_memory:
          type: integer
          description: Memory for API (MB)
        auto_replica:
          type: integer
          description: Number of Auto replicas
        auto_cpu:
          type: number
          format: float
          description: CPU cores for Auto
        auto_memory:
          type: integer
          description: Memory for Auto (MB)
        labels:
          type: array
          items:
            type: string
          description: Container labels
        customer_envs:
          type: array
          items:
            type: string
          description: Custom environment variables
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports
      required:
        - service_id
        - image_name
        - image_url
        - node_ip
      example:
        service_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
        image_name: "nginx"
        image_url: "nginx:latest"
        domain_prefix: "my-service"
        domain_suffix: ".example.com"
        node_ip: "*************"
        expiration: "2024-12-31T23:59:59"
        duration_seconds: 31536000
        api_replica: 1
        api_cpu: 1.0
        api_memory: 1024
        auto_replica: 0
        auto_cpu: 0.0
        auto_memory: 0
        labels: ["web", "nginx"]
        customer_envs: ["ENV=production", "DEBUG=false"]
        ports: ["80", "443"]

    # Worker Update Request (simplified for image updates only)
    WorkerUpdateRequest:
      type: object
      properties:
        service_id:
          type: string
          description: Service ID to update
        image_name:
          type: string
          description: New image name
        image_url:
          type: string
          description: New image URL
        node_ip:
          type: string
          description: Target node IP address
      required:
        - service_id
        - image_name
        - image_url
        - node_ip
      example:
        service_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
        image_name: "nginx"
        image_url: "nginx:1.21"
        node_ip: "*************"
