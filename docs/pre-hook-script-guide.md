# MySQL数据库初始化前置脚本使用指南

## 📋 **脚本概述**

我已经为您完善了前置脚本 `dbdata/entrypoint-pre.sh`，该脚本实现了以下功能：

1. **启动临时MySQL容器**：使用指定的MySQL镜像
2. **检查数据库表**：查看目标数据库中是否存在 `think_flow` 表
3. **条件执行SQL**：如果表不存在，则执行初始化SQL脚本
4. **自动清理**：执行完成后自动清理临时容器

## 🔧 **脚本功能特性**

### **1. 环境变量支持**
脚本支持环境变量挖槽替换，使用以下格式：
```bash
MYSQLHOST=_{MYSQL_HOST}_
MYSQLPORT=_{MYSQL_PORT}_
MYSQLPASSWD=_{MYSQL_PASSWORD}_
MYSQLUSER=_{MYSQL_USER}_
MYSQLDATABASE=_{MYSQL_DATABASE}_
```

### **2. 错误处理**
- ✅ 完整的错误检查和处理
- ✅ 连接重试机制（最多5次）
- ✅ 详细的日志输出
- ✅ 自动清理临时资源

### **3. 安全性**
- ✅ 使用临时容器，不影响现有环境
- ✅ 执行完成后自动清理
- ✅ 密码不会在日志中显示

## 📝 **脚本配置**

### **关键配置项**
```bash
# MySQL镜像
MYSQL_IMAGE="registry.yitaiyitai.com/library/mysql:8.0.36"

# 检查的表名
CHECK_TABLE="think_flow"

# SQL初始化文件
SQL_FILE="./hoocoopre-0527.sql"
```

### **环境变量要求**
脚本需要以下环境变量（通过挖槽替换获取）：
- `MYSQL_HOST`：MySQL服务器地址
- `MYSQL_PORT`：MySQL端口号
- `MYSQL_USER`：MySQL用户名
- `MYSQL_PASSWORD`：MySQL密码
- `MYSQL_DATABASE`：目标数据库名

## 🚀 **使用方法**

### **1. 准备文件**
确保以下文件存在于 `dbdata` 目录：
```
dbdata/
├── entrypoint-pre.sh      # 前置脚本
└── hoocoopre-0527.sql     # 初始化SQL文件
```

### **2. 配置环境变量**
在容器部署时，确保设置了正确的MySQL连接环境变量：
```bash
SYS_ENVS_TPL=MYSQL_HOST:**************|||MYSQL_PORT:33068|||MYSQL_USER:root|||MYSQL_PASSWORD:123456|||MYSQL_DATABASE:myapp_db
```

### **3. 配置前置脚本标签**
在容器标签中添加前置脚本配置：
```bash
SYS_PREHOOK=entrypoint-pre.sh
```

## 📊 **执行流程**

### **脚本执行步骤**
```
1. 检查环境变量
   ├── 验证MySQL连接信息是否完整
   └── 输出连接信息（不包含密码）

2. 检查SQL文件
   ├── 验证 hoocoopre-0527.sql 是否存在
   └── 确保文件可读

3. 启动临时MySQL容器
   ├── 使用指定的MySQL镜像
   ├── 使用host网络模式
   └── 等待容器启动完成

4. 测试MySQL连接
   ├── 连接目标MySQL服务器
   ├── 最多重试5次
   └── 每次重试间隔2秒

5. 检查数据库
   ├── 验证目标数据库是否存在
   └── 如果不存在则报错退出

6. 检查表是否存在
   ├── 查询 think_flow 表
   ├── 如果存在：跳过SQL执行，脚本结束
   └── 如果不存在：继续执行SQL

7. 执行SQL文件（仅在表不存在时）
   ├── 将SQL文件复制到容器
   ├── 执行初始化SQL
   ├── 验证表创建成功
   └── 输出执行结果

8. 清理资源
   ├── 删除临时容器
   └── 清理临时文件
```

## 📋 **日志输出示例**

### **成功执行（表不存在）**
```
=== 开始执行前置脚本 ===
[INFO] 2024-01-15 10:30:00 - === 开始执行前置脚本 ===
[INFO] 2024-01-15 10:30:00 - 服务ID: myapp-service
[INFO] 2024-01-15 10:30:00 - 检查表: think_flow
[INFO] 2024-01-15 10:30:00 - SQL文件: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:30:00 - 检查环境变量...
[INFO] 2024-01-15 10:30:00 - 环境变量检查通过
[INFO] 2024-01-15 10:30:00 - MySQL连接信息: root@**************:33068/myapp_db
[INFO] 2024-01-15 10:30:00 - 检查SQL文件: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:30:00 - SQL文件检查通过: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:30:01 - 启动临时MySQL容器进行数据库操作...
[INFO] 2024-01-15 10:30:02 - 临时MySQL容器启动成功
[INFO] 2024-01-15 10:30:02 - 测试MySQL连接...
[INFO] 2024-01-15 10:30:03 - MySQL连接测试成功
[INFO] 2024-01-15 10:30:03 - 检查数据库是否存在: myapp_db
[INFO] 2024-01-15 10:30:03 - 数据库存在: myapp_db
[INFO] 2024-01-15 10:30:03 - 检查表是否存在: think_flow
[INFO] 2024-01-15 10:30:03 - 表 think_flow 不存在，需要执行初始化SQL
[INFO] 2024-01-15 10:30:03 - 开始执行SQL文件: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:30:05 - SQL文件执行成功
[INFO] 2024-01-15 10:30:05 - 检查表是否存在: think_flow
[INFO] 2024-01-15 10:30:05 - 表 think_flow 已存在，跳过SQL执行
[INFO] 2024-01-15 10:30:05 - 表 think_flow 创建成功，初始化完成
[INFO] 2024-01-15 10:30:05 - === 前置脚本执行完成：数据库初始化成功 ===
[INFO] 2024-01-15 10:30:05 - 开始清理临时容器...
[INFO] 2024-01-15 10:30:06 - 临时容器 mysql_check_myapp-service_1705287000 已清理
```

### **跳过执行（表已存在）**
```
=== 开始执行前置脚本 ===
[INFO] 2024-01-15 10:35:00 - === 开始执行前置脚本 ===
[INFO] 2024-01-15 10:35:00 - 服务ID: myapp-service
[INFO] 2024-01-15 10:35:00 - 检查表: think_flow
[INFO] 2024-01-15 10:35:00 - SQL文件: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:35:00 - 检查环境变量...
[INFO] 2024-01-15 10:35:00 - 环境变量检查通过
[INFO] 2024-01-15 10:35:00 - MySQL连接信息: root@**************:33068/myapp_db
[INFO] 2024-01-15 10:35:00 - 检查SQL文件: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:35:00 - SQL文件检查通过: ./hoocoopre-0527.sql
[INFO] 2024-01-15 10:35:01 - 启动临时MySQL容器进行数据库操作...
[INFO] 2024-01-15 10:35:02 - 临时MySQL容器启动成功
[INFO] 2024-01-15 10:35:02 - 测试MySQL连接...
[INFO] 2024-01-15 10:35:03 - MySQL连接测试成功
[INFO] 2024-01-15 10:35:03 - 检查数据库是否存在: myapp_db
[INFO] 2024-01-15 10:35:03 - 数据库存在: myapp_db
[INFO] 2024-01-15 10:35:03 - 检查表是否存在: think_flow
[INFO] 2024-01-15 10:35:03 - 表 think_flow 已存在，跳过SQL执行
[INFO] 2024-01-15 10:35:03 - === 前置脚本执行完成：表已存在，无需初始化 ===
[INFO] 2024-01-15 10:35:03 - 开始清理临时容器...
[INFO] 2024-01-15 10:35:04 - 临时容器 mysql_check_myapp-service_1705287300 已清理
```

## 🔧 **自定义配置**

### **修改检查的表名**
如果需要检查其他表，修改脚本中的配置：
```bash
CHECK_TABLE="your_table_name"
```

### **修改SQL文件路径**
如果SQL文件在其他位置，修改配置：
```bash
SQL_FILE="./your-sql-file.sql"
```

### **修改MySQL镜像**
如果使用其他MySQL镜像，修改配置：
```bash
MYSQL_IMAGE="your-mysql-image:tag"
```

## ⚠️ **注意事项**

### **1. 网络配置**
- 脚本使用 `--network host` 模式启动临时容器
- 确保容器能够访问目标MySQL服务器

### **2. 权限要求**
- 脚本需要Docker执行权限
- MySQL用户需要有目标数据库的读写权限

### **3. 资源清理**
- 脚本会自动清理临时容器
- 如果脚本异常退出，可能需要手动清理

### **4. SQL文件要求**
- SQL文件必须存在且可读
- SQL语句应该是幂等的（可重复执行）

## 🎯 **最佳实践**

### **1. 环境变量管理**
```bash
# 推荐使用SYS_ENVS_TPL进行环境变量配置
SYS_ENVS_TPL=MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PORT:_{MYSQL_PORT}_|||MYSQL_USER:_{MYSQL_USER}_|||MYSQL_PASSWORD:_{MYSQL_PASSWORD}_|||MYSQL_DATABASE:_{MYSQL_DATABASE}_
```

### **2. 错误处理**
- 脚本会在遇到错误时立即退出
- 检查日志输出以诊断问题
- 确保MySQL服务器可访问

### **3. 测试建议**
- 在生产环境使用前，先在测试环境验证
- 确保SQL文件语法正确
- 验证环境变量配置正确

## 🎉 **总结**

这个完善的前置脚本提供了：

1. **完整的功能**：检查表存在性，条件执行SQL
2. **健壮的错误处理**：连接重试，详细日志，自动清理
3. **安全性**：临时容器，密码保护，资源清理
4. **易用性**：环境变量支持，详细日志，清晰的执行流程

现在您可以安全地使用这个脚本来实现MySQL数据库的条件初始化功能！🎉
