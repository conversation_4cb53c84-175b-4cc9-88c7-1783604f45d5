# 将标签处理方法移动到 utils 包

## 重构背景

在优化标签排重逻辑后，发现 `mergeLabelsWithDeduplication` 方法具有很强的通用性，不应该挂在 `Service` 结构体下。为了提高代码的复用性和架构的合理性，将其移动到 `utils` 包中。

## 重构原因

### 1. 通用性强
- **功能独立**：标签排重是一个独立的工具功能
- **无业务依赖**：不依赖于 Service 的任何业务逻辑
- **可复用**：其他包也可能需要标签排重功能

### 2. 架构优化
- **职责分离**：Service 专注业务逻辑，utils 提供工具函数
- **依赖清晰**：减少了 Service 的方法数量，接口更清晰
- **维护性好**：工具函数集中管理，便于维护和测试

### 3. 扩展性好
- **功能扩展**：可以在 utils 包中添加更多标签相关的工具函数
- **统一管理**：所有标签处理逻辑集中在一个地方
- **测试友好**：工具函数更容易进行单元测试

## 重构内容

### 1. 新增文件
创建了 `internal/pkg/utils/labels.go` 文件，包含以下工具函数：

#### 核心排重方法
```go
// MergeLabelsWithDeduplication 合并多个标签数组并进行排重
func MergeLabelsWithDeduplication(labelArrays ...[]string) []string
```

#### 辅助工具方法
```go
// DeduplicateLabels 对单个标签数组进行排重
func DeduplicateLabels(labels []string) []string

// FilterEmptyLabels 过滤掉空白标签
func FilterEmptyLabels(labels []string) []string

// SortLabels 对标签数组进行排序
func SortLabels(labels []string) []string

// ContainsLabel 检查标签数组中是否包含指定的标签
func ContainsLabel(labels []string, target string) bool

// AddLabelIfNotExists 如果标签不存在则添加到数组中
func AddLabelIfNotExists(labels []string, newLabel string) []string

// RemoveLabel 从标签数组中移除指定的标签
func RemoveLabel(labels []string, target string) []string
```

### 2. 修改调用方式
在 `internal/service/queue_processor.go` 中：

#### 修改前
```go
// 将 image.Labels 聚合到标签中，并进行排重处理
allLabels := s.mergeLabelsWithDeduplication(baseLabels, image.Labels, record.Labels)
record.Labels = allLabels
```

#### 修改后
```go
// 将 image.Labels 聚合到标签中，并进行排重处理
allLabels := utils.MergeLabelsWithDeduplication(baseLabels, image.Labels, record.Labels)
record.Labels = allLabels
```

### 3. 删除旧方法
从 `Service` 结构体中删除了 `mergeLabelsWithDeduplication` 方法，避免代码重复。

## 工具函数详解

### 1. MergeLabelsWithDeduplication
**功能**：合并多个标签数组并进行排重
**特点**：
- 支持可变参数，可以合并任意数量的标签数组
- 完全匹配排重，不拆分 key=value 格式
- 自动过滤空白标签
- 结果按字母顺序排序，保证一致性

**使用示例**：
```go
baseLabels := []string{"system=ops-system", "service-type=api"}
imageLabels := []string{"version=1.0", "env=dev"}
recordLabels := []string{"debug", "version=1.0"} // version=1.0 重复

result := utils.MergeLabelsWithDeduplication(baseLabels, imageLabels, recordLabels)
// 结果: ["debug", "env=dev", "service-type=api", "system=ops-system", "version=1.0"]
```

### 2. DeduplicateLabels
**功能**：对单个标签数组进行排重
**特点**：
- `MergeLabelsWithDeduplication` 的简化版本
- 适用于只需要对单个数组排重的场景

**使用示例**：
```go
labels := []string{"debug", "test", "debug", "production"}
result := utils.DeduplicateLabels(labels)
// 结果: ["debug", "production", "test"]
```

### 3. FilterEmptyLabels
**功能**：过滤掉空白标签
**特点**：
- 移除空字符串和只包含空白字符的标签
- 返回新数组，不修改原数组

**使用示例**：
```go
labels := []string{"debug", "", "  ", "test", "production"}
result := utils.FilterEmptyLabels(labels)
// 结果: ["debug", "test", "production"]
```

### 4. ContainsLabel
**功能**：检查标签数组中是否包含指定的标签
**特点**：
- 使用完全匹配进行比较
- 自动处理空白字符

**使用示例**：
```go
labels := []string{"debug", "test", "production"}
exists := utils.ContainsLabel(labels, "debug") // true
exists = utils.ContainsLabel(labels, "staging") // false
```

### 5. AddLabelIfNotExists
**功能**：如果标签不存在则添加到数组中
**特点**：
- 避免重复添加相同标签
- 返回新数组，不修改原数组

**使用示例**：
```go
labels := []string{"debug", "test"}
result := utils.AddLabelIfNotExists(labels, "production") // 添加新标签
result = utils.AddLabelIfNotExists(result, "debug")      // 不添加重复标签
// 结果: ["debug", "test", "production"]
```

### 6. RemoveLabel
**功能**：从标签数组中移除指定的标签
**特点**：
- 移除所有匹配的标签
- 返回新数组，不修改原数组

**使用示例**：
```go
labels := []string{"debug", "test", "debug", "production"}
result := utils.RemoveLabel(labels, "debug")
// 结果: ["test", "production"]
```

## 重构效果

### 1. 代码组织优化
- ✅ **职责清晰**：Service 专注业务逻辑，utils 提供工具函数
- ✅ **复用性强**：其他包可以直接使用标签处理工具
- ✅ **维护简化**：工具函数集中管理，便于维护

### 2. 功能扩展
- ✅ **工具丰富**：提供了完整的标签处理工具集
- ✅ **接口统一**：所有标签操作都有对应的工具函数
- ✅ **扩展友好**：可以轻松添加新的标签处理功能

### 3. 测试改进
- ✅ **单元测试**：工具函数更容易进行单元测试
- ✅ **测试覆盖**：可以独立测试每个工具函数
- ✅ **质量保证**：通过测试确保工具函数的正确性

## 使用建议

### 1. 标签排重
```go
// ✅ 推荐：使用 utils 包的方法
allLabels := utils.MergeLabelsWithDeduplication(baseLabels, imageLabels, recordLabels)

// ❌ 避免：手动实现排重逻辑
```

### 2. 标签检查
```go
// ✅ 推荐：使用工具函数
if utils.ContainsLabel(labels, "debug") {
    // 处理调试模式
}

// ❌ 避免：手动遍历检查
```

### 3. 标签操作
```go
// ✅ 推荐：使用工具函数进行标签操作
cleanLabels := utils.FilterEmptyLabels(rawLabels)
sortedLabels := utils.SortLabels(cleanLabels)
finalLabels := utils.AddLabelIfNotExists(sortedLabels, "production")
```

## 向后兼容性

- ✅ **接口不变**：原有的调用方式保持不变
- ✅ **功能增强**：在原有功能基础上增加了更多工具函数
- ✅ **性能一致**：重构后性能保持一致

## 相关文件

### 新增文件
- `internal/pkg/utils/labels.go` - 标签处理工具函数集

### 修改文件
- `internal/service/queue_processor.go` - 更新调用方式，删除旧方法

### 更新文档
- `docs/label-deduplication-optimization.md` - 更新排重逻辑说明
- `docs/move-labels-to-utils.md` - 新增重构说明文档

## 最佳实践

### 1. 工具函数设计原则
- **纯函数**：不修改输入参数，返回新的结果
- **错误处理**：合理处理边界情况和异常输入
- **性能优化**：使用高效的算法和数据结构

### 2. 包设计原则
- **单一职责**：每个包专注于特定的功能领域
- **依赖最小**：减少包之间的依赖关系
- **接口清晰**：提供简洁明了的公共接口

### 3. 代码复用原则
- **通用优先**：优先考虑通用性强的实现
- **配置灵活**：通过参数提供灵活的配置选项
- **文档完善**：提供详细的使用说明和示例

## 总结

这次重构成功地将标签处理逻辑从业务层移动到工具层，实现了：

1. **架构优化**：更清晰的代码组织和职责分离
2. **功能扩展**：提供了完整的标签处理工具集
3. **复用性提升**：其他包可以直接使用这些工具函数
4. **维护性改进**：集中管理，便于维护和测试

这种重构体现了良好的软件设计原则，为系统的长期维护和扩展奠定了坚实的基础。
