package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
)

// 定义各种配置结构
type HooksConfig struct {
	Pre  string `json:"pre,omitempty"`
	Post string `json:"post,omitempty"`
}

type VolumesConfig map[string]string

type LoggingConfig struct {
	Entrance string `json:"entrance,omitempty"`
	Format   string `json:"format,omitempty"`
	Level    string `json:"level,omitempty"`
}

type OSSConfig struct {
	Enabled  bool   `json:"enabled"`
	Endpoint string `json:"endpoint,omitempty"`
	Bucket   string `json:"bucket,omitempty"`
	Path     string `json:"path,omitempty"`
	Region   string `json:"region,omitempty"`
}

// 解析聚合标签的工具函数
func ParseAggregatedLabels(labels []string) map[string]interface{} {
	result := make(map[string]interface{})
	
	for _, label := range labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) != 2 {
			continue
		}
		
		key, value := parts[0], parts[1]
		
		switch key {
		case "SYS_HOOKS":
			var hooks HooksConfig
			if err := json.Unmarshal([]byte(value), &hooks); err == nil {
				result[key] = hooks
			}
		case "SYS_VOLUMES":
			var volumes VolumesConfig
			if err := json.Unmarshal([]byte(value), &volumes); err == nil {
				result[key] = volumes
			}
		case "SYS_LOGGING":
			var logging LoggingConfig
			if err := json.Unmarshal([]byte(value), &logging); err == nil {
				result[key] = logging
			}
		case "SYS_OSS":
			var oss OSSConfig
			if err := json.Unmarshal([]byte(value), &oss); err == nil {
				result[key] = oss
			}
		default:
			result[key] = value
		}
	}
	
	return result
}

// 生成聚合标签的工具函数
func GenerateAggregatedLabels() []string {
	// 定义各种配置
	hooks := HooksConfig{
		Pre:  "entrypoint-pre.sh",
		Post: "entrypoint-post.sh",
	}
	
	volumes := VolumesConfig{
		"nginx":                "/var/log/nginx/",
		"huanpingtong-server": "/var/www/huanpingtong-server/logs/",
		"bpmax-server":        "/var/www/bpmax-server/logs/",
	}
	
	logging := LoggingConfig{
		Entrance: "nginx/access.log",
		Format:   "combined",
		Level:    "info",
	}
	
	oss := OSSConfig{
		Enabled:  true,
		Endpoint: "oss-cn-shanghai.aliyuncs.com",
		Bucket:   "aggk-bpmax",
		Path:     "/hptfile",
		Region:   "cn-shanghai",
	}
	
	// 序列化为 JSON
	hooksJSON, _ := json.Marshal(hooks)
	volumesJSON, _ := json.Marshal(volumes)
	loggingJSON, _ := json.Marshal(logging)
	ossJSON, _ := json.Marshal(oss)
	
	return []string{
		"SYS_TYPE=standard",
		"SYS_CATEGORY=system",
		"SYS_VERSION=2025-05-24T20:38:21",
		"SYS_ENVS=worker-app.env",
		fmt.Sprintf("SYS_HOOKS=%s", string(hooksJSON)),
		fmt.Sprintf("SYS_VOLUMES=%s", string(volumesJSON)),
		fmt.Sprintf("SYS_LOGGING=%s", string(loggingJSON)),
		fmt.Sprintf("SYS_OSS=%s", string(ossJSON)),
	}
}

// 键值对格式解析（备选方案）
func ParseKeyValueFormat(value string) map[string]string {
	result := make(map[string]string)
	pairs := strings.Split(value, ",")
	
	for _, pair := range pairs {
		kv := strings.SplitN(pair, ":", 2)
		if len(kv) == 2 {
			result[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
		}
	}
	
	return result
}

func main() {
	// 生成标签示例
	labels := GenerateAggregatedLabels()
	
	fmt.Println("Generated Labels:")
	for _, label := range labels {
		fmt.Printf("  %s\n", label)
	}
	
	fmt.Println("\nParsed Results:")
	parsed := ParseAggregatedLabels(labels)
	
	// 输出解析结果
	for key, value := range parsed {
		switch v := value.(type) {
		case HooksConfig:
			fmt.Printf("  %s: Pre=%s, Post=%s\n", key, v.Pre, v.Post)
		case VolumesConfig:
			fmt.Printf("  %s: %+v\n", key, v)
		case LoggingConfig:
			fmt.Printf("  %s: Entrance=%s, Format=%s, Level=%s\n", key, v.Entrance, v.Format, v.Level)
		case OSSConfig:
			fmt.Printf("  %s: Enabled=%t, Endpoint=%s, Bucket=%s\n", key, v.Enabled, v.Endpoint, v.Bucket)
		default:
			fmt.Printf("  %s: %s\n", key, v)
		}
	}
	
	// 键值对格式示例
	fmt.Println("\nKey-Value Format Example:")
	kvExample := "enabled:true,endpoint:oss-cn-shanghai.aliyuncs.com,bucket:aggk-bpmax,path:/hptfile"
	kvParsed := ParseKeyValueFormat(kvExample)
	fmt.Printf("  Parsed: %+v\n", kvParsed)
}
