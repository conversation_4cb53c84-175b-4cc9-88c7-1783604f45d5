# deploy_record_w 表 visited_at 字段修复文档

## 问题描述

`deploy_record_w` 表增加了 `visited_at` 字段，但是 `deploy_record_w_repo.go` 文件的 `Create` 函数中对 `deploy_record_w` 表的操作没有对这个字段进行处理，导致：

1. **数据完整性问题**：新创建的记录缺少 `visited_at` 字段值
2. **查询错误**：SELECT 语句字段数量与 Scan 参数不匹配
3. **更新失败**：UPDATE 语句没有包含 `visited_at` 字段

## 修复内容

### 1. 模型层修复

#### 1.1 更新 `internal/models/service.go`
```go
// DeployRecord 结构体添加 visited_at 字段
type DeployRecord struct {
    // ... 其他字段
    CreatedAt       string   `json:"created_at,omitempty"`
    UpdatedAt       string   `json:"updated_at,omitempty"`
    VisitedAt       string   `json:"visited_at,omitempty"`     // 最后访问时间，用于活动检测
    // ... 其他字段
}
```

#### 1.2 更新 `internal/models/worker.go`
```go
// DeployRecordW 结构体添加 visited_at 字段
type DeployRecordW struct {
    // ... 其他字段
    CreatedAt       string   `json:"created_at,omitempty"`
    UpdatedAt       string   `json:"updated_at,omitempty"`
    VisitedAt       string   `json:"visited_at,omitempty"`     // 最后访问时间，用于活动检测
    // ... 其他字段
}
```

### 2. Repository 层修复

#### 2.1 `Create` 方法修复
- **时间戳初始化**：为 `visited_at` 字段设置默认值
- **INSERT 语句**：添加 `visited_at` 字段到插入语句
- **参数传递**：在 VALUES 中添加 `record.VisitedAt` 参数

```go
// 确保时间戳格式正确
if record.VisitedAt == "" {
    record.VisitedAt = utils.GetCSTTimeString()
}

// INSERT 语句包含 visited_at 字段
INSERT INTO deploy_record_w (
    service_id, name, image_name, image_url, domain_prefix, domain_suffix,
    expiration, duration_seconds, status, labels, customer_envs, remark, 
    node_ip, host_ip, created_at, updated_at, visited_at,  -- 添加 visited_at
    api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
    ports, ports_mapping
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

#### 2.2 查询方法修复
修复了以下方法的 SELECT 语句和 Scan 操作：

- `GetByID`
- `GetAll`
- `GetNextPendingRecord`
- `GetRecordsByStatus`

所有 SELECT 语句都添加了 `visited_at` 字段：
```sql
SELECT service_id, name, image_name, image_url, domain_prefix, domain_suffix,
       expiration, duration_seconds, status, labels, customer_envs, remark, 
       node_ip, host_ip, created_at, updated_at, visited_at,  -- 添加 visited_at
       api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
       ports, ports_mapping
FROM deploy_record_w
```

所有 Scan 操作都添加了 `&record.VisitedAt` 参数。

#### 2.3 `Update` 方法修复
UPDATE 语句添加了 `visited_at` 字段的更新：
```sql
UPDATE deploy_record_w
SET name = ?, image_name = ?, image_url = ?, domain_prefix = ?, domain_suffix = ?,
    expiration = ?, duration_seconds = ?, status = ?, labels = ?, customer_envs = ?, 
    remark = ?, node_ip = ?, host_ip = ?, updated_at = ?, visited_at = ?,  -- 添加 visited_at
    api_replica = ?, api_cpu = ?, api_memory = ?, auto_replica = ?, auto_cpu = ?, auto_memory = ?,
    ports = ?, ports_mapping = ?
WHERE service_id = ?
```

#### 2.4 新增 `UpdateVisitedAt` 方法
添加了专门用于更新访问时间的方法：
```go
// UpdateVisitedAt 更新部署记录的最后访问时间
func (r *DeployRecordWRepository) UpdateVisitedAt(serviceID string) error {
    result, err := r.db.Exec(`
UPDATE deploy_record_w
SET visited_at = ?, updated_at = ?
WHERE service_id = ?
`, utils.GetCSTTimeString(), utils.GetCSTTimeString(), serviceID)
    
    // 错误处理和行数检查...
}
```

## 字段用途说明

### visited_at 字段的作用
- **活动检测**：记录服务的最后访问时间
- **自动清理**：用于识别长时间未访问的服务
- **资源管理**：帮助系统自动回收闲置资源
- **监控统计**：提供服务使用情况的数据支持

### 使用场景
1. **服务访问时**：每次访问服务时更新 `visited_at`
2. **活动检查**：定期检查服务的活动状态
3. **自动停止**：停止长时间未访问的服务
4. **资源优化**：基于访问模式优化资源分配

## 验证方法

### 1. 数据库验证
```sql
-- 检查表结构是否包含 visited_at 字段
DESCRIBE deploy_record_w;

-- 检查新创建的记录是否有 visited_at 值
SELECT service_id, created_at, updated_at, visited_at 
FROM deploy_record_w 
ORDER BY created_at DESC 
LIMIT 5;
```

### 2. 代码验证
- 运行单元测试确保所有 CRUD 操作正常
- 检查日志确保没有字段相关的错误
- 验证新创建的记录包含完整的字段信息

### 3. 功能验证
- 创建新的部署记录
- 查询部署记录列表
- 更新部署记录
- 使用 `UpdateVisitedAt` 方法更新访问时间

## 注意事项

1. **数据库迁移**：如果数据库中已有数据，需要执行 ALTER TABLE 语句添加字段
2. **默认值处理**：新字段会自动设置为当前时间
3. **向后兼容**：修改保持了与现有代码的兼容性
4. **性能影响**：添加字段对查询性能影响很小

## 相关文件

- `internal/models/service.go` - Service 层 DeployRecord 模型
- `internal/models/worker.go` - Worker 层 DeployRecordW 模型  
- `internal/database/deploy_record_w_repo.go` - Worker 层数据库操作

修复完成后，`deploy_record_w` 表的所有操作都能正确处理 `visited_at` 字段。
