# Service DB 错误日志优化

## 📋 **修改概述**

将 `internal/database/service_db.go` 文件中的所有 `fmt.<PERSON>rro<PERSON>` 错误输出改为写入到 `log/error.log` 文件，使用 `utils/logger` 包提供的工具方法。

## 🔧 **修改内容**

### 1. **添加 logger 导入**
```go
import (
    "database/sql"
    "fmt"
    "os"
    "path/filepath"
    "sync"

    "github.com/zero-ops/service-system/internal/pkg/logger"  // ✅ 新增
    _ "github.com/mattn/go-sqlite3" // SQLite driver
)
```

### 2. **修改 NewServiceDB 方法中的错误处理**

#### 原代码：
```go
if err := os.MkdirAll(dbDir, 0755); err != nil {
    return nil, fmt.Errorf("failed to create database directory: %w", err)
}
```

#### 修改后：
```go
if err := os.MkdirAll(dbDir, 0755); err != nil {
    logger.Error("Failed to create database directory: %v, path: %s", err, dbDir)
    return nil, fmt.Erro<PERSON>("failed to create database directory: %w", err)
}
```

### 3. **所有错误处理点的修改**

| 错误类型 | 原代码 | 修改后 |
|----------|--------|--------|
| **目录创建失败** | `fmt.Errorf("failed to create database directory: %w", err)` | `logger.Error("Failed to create database directory: %v, path: %s", err, dbDir)` |
| **数据库打开失败** | `fmt.Errorf("failed to open database: %w", err)` | `logger.Error("Failed to open database: %v, path: %s", err, config.DBPath)` |
| **数据库连接失败** | `fmt.Errorf("failed to ping database: %w", err)` | `logger.Error("Failed to ping database: %v, path: %s", err, config.DBPath)` |
| **日志模式设置失败** | `fmt.Errorf("failed to set journal mode: %w", err)` | `logger.Error("Failed to set journal mode: %v, path: %s", err, config.DBPath)` |
| **同步模式设置失败** | `fmt.Errorf("failed to set synchronous mode: %w", err)` | `logger.Error("Failed to set synchronous mode: %v, path: %s", err, config.DBPath)` |
| **外键约束设置失败** | `fmt.Errorf("failed to disable foreign keys: %w", err)` | `logger.Error("Failed to disable foreign keys: %v, path: %s", err, config.DBPath)` |
| **workers表创建失败** | `fmt.Errorf("failed to create workers table: %w", err)` | `logger.Error("Failed to create workers table: %v", err)` |
| **deploy_record表创建失败** | `fmt.Errorf("failed to create deploy_record table: %w", err)` | `logger.Error("Failed to create deploy_record table: %v", err)` |
| **image_type表创建失败** | `fmt.Errorf("failed to create image_type table: %w", err)` | `logger.Error("Failed to create image_type table: %v", err)` |

## 📊 **修改统计**

### 修改的方法：
- ✅ `NewServiceDB()` - 6个错误处理点
- ✅ `InitSchema()` - 3个错误处理点

### 总计：
- **9个错误处理点**全部修改完成
- **保留原有的 fmt.Errorf 返回值**，确保接口兼容性
- **新增详细的错误日志记录**，包含错误信息和相关路径

## 🎯 **优化效果**

### 1. **错误追踪改进**
```go
// 修改前：只有返回错误，无日志记录
return nil, fmt.Errorf("failed to open database: %w", err)

// 修改后：既有日志记录，又有返回错误
logger.Error("Failed to open database: %v, path: %s", err, config.DBPath)
return nil, fmt.Errorf("failed to open database: %w", err)
```

### 2. **日志信息丰富**
- ✅ **错误详情**：记录具体的错误信息
- ✅ **上下文信息**：包含数据库路径等关键信息
- ✅ **时间戳**：logger 自动添加时间戳
- ✅ **调用位置**：logger 自动记录文件名和行号

### 3. **日志格式示例**
```
[2025-05-26 16:30:15.123] [ERROR] [service_db.go:37] Failed to create database directory: permission denied, path: /restricted/dbdata
[2025-05-26 16:30:15.124] [ERROR] [service_db.go:44] Failed to open database: no such file or directory, path: dbdata/service.db
[2025-05-26 16:30:15.125] [ERROR] [service_db.go:100] Failed to create workers table: syntax error
```

## 🔍 **logger 包功能**

### 可用的日志方法：
```go
logger.Info(format string, args ...interface{})   // 信息日志 -> log/out.log
logger.Warn(format string, args ...interface{})   // 警告日志 -> log/out.log  
logger.Error(format string, args ...interface{})  // 错误日志 -> log/error.log
```

### 自动功能：
- ✅ **自动初始化**：首次调用时自动创建日志目录和文件
- ✅ **线程安全**：使用 mutex 保证并发安全
- ✅ **信号处理**：支持 SIGHUP 信号重新打开日志文件
- ✅ **格式化输出**：自动添加时间戳、级别、调用位置

## 📁 **日志文件位置**

### 默认路径：
- **错误日志**：`log/error.log`
- **普通日志**：`log/out.log`

### 自定义路径：
```go
logger.SetLogPath("custom/out.log", "custom/error.log")
```

## 🚀 **使用建议**

### 1. **错误处理模式**
```go
// 推荐的错误处理模式
if err != nil {
    logger.Error("Operation failed: %v, context: %s", err, contextInfo)
    return fmt.Errorf("operation failed: %w", err)
}
```

### 2. **日志级别选择**
- **Error**：数据库连接失败、SQL执行失败等严重错误
- **Warn**：配置问题、性能警告等
- **Info**：正常的操作信息、状态变更等

### 3. **日志轮转**
```bash
# 发送 SIGHUP 信号进行日志轮转
kill -HUP <process_id>
```

## ✅ **验证方法**

### 1. **触发错误场景**
```bash
# 删除数据库文件权限，触发错误
chmod 000 dbdata/
./your-program

# 检查错误日志
cat log/error.log
```

### 2. **预期日志输出**
```
[2025-05-26 16:30:15.123] [ERROR] [service_db.go:37] Failed to create database directory: permission denied, path: dbdata
```

### 3. **确认功能正常**
- ✅ 错误信息写入 `log/error.log`
- ✅ 程序仍然正常返回错误
- ✅ 调用方能正常处理错误

## 📝 **总结**

这次修改成功实现了：

1. **双重错误处理**：既写入日志文件，又返回错误给调用方
2. **详细错误信息**：包含错误详情和上下文信息
3. **统一日志格式**：使用标准的 logger 包格式
4. **向后兼容**：保持原有的错误返回接口不变
5. **便于调试**：错误日志集中存储，便于问题排查

现在所有数据库相关的错误都会被记录到 `log/error.log` 文件中，同时保持原有的错误处理逻辑不变。
