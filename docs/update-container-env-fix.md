# UpdateContainer 方法环境变量处理修复

## 📋 **修复概述**

我已经成功修复了 `UpdateContainer` 方法中的环境变量处理问题，使其与 `DeployContainer` 方法保持一致。

## 🔍 **发现的问题**

### **1. 环境变量重复处理**
```go
// 问题：第847-855行已经处理了环境变量
processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
sysEnvVars = processedEnvString

// 问题：第904-921行又重复处理了 req.CustomerEnvs
envArgs := ""
for _, env := range req.CustomerEnvs {
    // ... 重复处理
}

// 问题：第930, 935, 940行使用了重复的环境变量
command = buildDockerRunCommand(..., sysEnvVars+envArgs, ...)  // ❌ 重复
```

### **2. 前置/后置脚本缺少挖槽替换**
```go
// 问题：没有使用 envMap 进行挖槽替换
scriptContent, err := utils.ReadFile(scriptPath)
// 直接执行，没有替换环境变量
cmd := exec.Command("/bin/bash", scriptPath)
```

## 🔧 **修复方案**

### **1. 移除重复的环境变量处理**

#### **修复前**
```go
// 第一次处理
processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
sysEnvVars = processedEnvString

// 第二次处理（重复）
envArgs := ""
for _, env := range req.CustomerEnvs {
    // 重复处理 CustomerEnvs
}

// 使用重复的环境变量
command = buildDockerRunCommand(..., sysEnvVars+envArgs, ...)
```

#### **修复后**
```go
// 只处理一次
processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
sysEnvVars = processedEnvString

// 注意：环境变量已经在 ProcessEnvironmentVariablesFromRequest 中处理完成
// sysEnvVars 包含了所有环境变量（SYS_ENVS_TPL + CustomerEnvs）
// 不需要再单独处理 req.CustomerEnvs

// 直接使用处理后的环境变量
command = buildDockerRunCommand(..., sysEnvVars, ...)
```

### **2. 添加脚本挖槽替换功能**

#### **前置脚本修复**
```go
// 修复前：直接执行脚本
scriptContent, err := utils.ReadFile(scriptPath)
cmd := exec.Command("/bin/bash", scriptPath)

// 修复后：支持挖槽替换
scriptContent, err := utils.ReadFile(scriptPath)
if len(envMap) > 0 {
    scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
    log.Printf("前置脚本已进行环境变量挖槽替换，替换了 %d 个变量", len(envMap))
}

// 创建临时脚本文件（包含替换后的内容）
tempScriptPath := fmt.Sprintf("/tmp/pre_hook_%s.sh", req.ServiceId)
err = utils.WriteFile(tempScriptPath, scriptContent)
cmd := exec.Command("/bin/bash", tempScriptPath)
// 执行后清理临时文件
_ = os.Remove(tempScriptPath)
```

#### **后置脚本修复**
```go
// 修复前：直接执行脚本
scriptContent, err := utils.ReadFile(scriptPath)
cmd := exec.Command("/bin/bash", scriptPath)

// 修复后：支持挖槽替换
scriptContent, err := utils.ReadFile(scriptPath)
if len(envMap) > 0 {
    scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
    log.Printf("后置脚本已进行环境变量挖槽替换，替换了 %d 个变量", len(envMap))
}

// 创建临时脚本文件（包含替换后的内容）
tempScriptPath := fmt.Sprintf("/tmp/post_hook_%s.sh", req.ServiceId)
err = utils.WriteFile(tempScriptPath, scriptContent)
cmd := exec.Command("/bin/bash", tempScriptPath)
// 执行后清理临时文件
_ = os.Remove(tempScriptPath)
```

## ✅ **修复效果**

### **1. 环境变量处理一致性**
- ✅ **统一处理**：与 `DeployContainer` 方法完全一致
- ✅ **避免重复**：移除了重复的环境变量处理逻辑
- ✅ **数据一致性**：Docker容器和脚本使用相同的环境变量

### **2. 脚本功能增强**
- ✅ **前置脚本**：支持环境变量挖槽替换
- ✅ **后置脚本**：支持环境变量挖槽替换
- ✅ **多种格式**：支持 `${VAR}`、`{{VAR}}`、`_{VAR}_` 三种挖槽格式

### **3. 性能优化**
- ✅ **减少重复计算**：环境变量只处理一次
- ✅ **临时文件管理**：自动清理临时脚本文件
- ✅ **内存优化**：避免了重复的字符串拼接

## 🔄 **修复对比**

### **DeployContainer vs UpdateContainer**

| 功能 | DeployContainer | UpdateContainer (修复前) | UpdateContainer (修复后) |
|------|-----------------|-------------------------|-------------------------|
| 环境变量处理 | ✅ 统一处理 | ❌ 重复处理 | ✅ 统一处理 |
| 前置脚本挖槽替换 | ✅ 支持 | ❌ 不支持 | ✅ 支持 |
| 后置脚本挖槽替换 | ✅ 支持 | ❌ 不支持 | ✅ 支持 |
| 临时文件管理 | ✅ 自动清理 | ❌ 无临时文件 | ✅ 自动清理 |
| 日志记录 | ✅ 详细日志 | ❌ 简单日志 | ✅ 详细日志 |

## 🎯 **实际应用场景**

### **容器更新流程**
```bash
# 1. 停止并移除现有容器
docker rm -f service-123

# 2. 执行前置脚本（支持环境变量替换）
#!/bin/bash
echo "更新服务: ${SERVICE_ID}"
echo "新镜像: {{IMAGE_URL}}"
echo "域名: _{DOMAIN}_"

# 备份数据
cp -r /user/data/${SERVICE_ID}/data /user/data/${SERVICE_ID}/backup-$(date +%Y%m%d)

# 3. 部署新容器（使用统一的环境变量）
docker run -d --name service-123 \
  --env MYSQL_HOST="**************" \
  --env DOMAIN="myapp.example.com" \
  --env SERVICE_ID="service-123" \
  new-image:latest

# 4. 执行后置脚本（支持环境变量替换）
#!/bin/bash
echo "服务更新完成: ${SERVICE_ID}"
echo "访问地址: https://_{DOMAIN}_"

# 验证服务健康状态
curl -f http://localhost/health || exit 1

# 发送更新通知
curl -X POST "{{NOTIFICATION_URL}}" \
  -d '{"service":"${SERVICE_ID}","status":"updated"}'
```

## 📝 **代码变更总结**

### **主要修改**
1. **移除重复的环境变量处理**（第904-932行）
2. **修复Docker命令构建**（第933, 938, 943行）
3. **增强前置脚本处理**（第857-907行）
4. **增强后置脚本处理**（第973-1023行）

### **保持不变**
- ✅ 容器停止和移除逻辑
- ✅ 资源计算逻辑
- ✅ 标签处理逻辑
- ✅ 卷映射处理逻辑
- ✅ 响应数据结构

## 🧪 **测试验证**

### **测试结果**
```bash
=== RUN   TestProcessEnvironmentVariablesFromRequest
--- PASS: TestProcessEnvironmentVariablesFromRequest (0.00s)
=== RUN   TestDockerCommandFormat
--- PASS: TestDockerCommandFormat (0.00s)
PASS
```

### **编译验证**
```bash
go build ./cmd/server
# 编译成功，无错误
```

## 🎯 **总结**

这次修复成功实现了：

1. **功能一致性**：`UpdateContainer` 与 `DeployContainer` 的环境变量处理完全一致
2. **避免重复**：移除了重复的环境变量处理逻辑
3. **功能增强**：前置/后置脚本支持环境变量挖槽替换
4. **性能优化**：减少了重复计算和内存使用
5. **代码质量**：提高了代码的可维护性和一致性

现在 `UpdateContainer` 方法的环境变量处理与 `DeployContainer` 方法完全一致，支持完整的环境变量处理和脚本挖槽替换功能！🎉
