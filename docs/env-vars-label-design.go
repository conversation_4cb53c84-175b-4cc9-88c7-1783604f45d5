package main

import (
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"
)

// 方案一：增强型键值对格式（推荐）
// 使用 | 作为分隔符，: 作为键值分隔符
// 对包含特殊字符的值进行 URL 编码
func EncodeEnvVarsKV(envs map[string]string) string {
	var parts []string
	for key, value := range envs {
		// 如果值包含特殊字符，进行 URL 编码
		if needsEncoding(value) {
			value = url.QueryEscape(value)
			key = key + "_ENCODED"
		}
		parts = append(parts, key+":"+value)
	}
	return strings.Join(parts, "|")
}

func DecodeEnvVarsKV(encoded string) map[string]string {
	result := make(map[string]string)
	parts := strings.Split(encoded, "|")
	
	for _, part := range parts {
		kv := strings.SplitN(part, ":", 2)
		if len(kv) == 2 {
			key, value := kv[0], kv[1]
			
			// 检查是否需要解码
			if strings.HasSuffix(key, "_ENCODED") {
				key = strings.TrimSuffix(key, "_ENCODED")
				if decoded, err := url.QueryUnescape(value); err == nil {
					value = decoded
				}
			}
			result[key] = value
		}
	}
	return result
}

// 方案二：Base64 编码格式
func EncodeEnvVarsB64(envs map[string]string) string {
	var lines []string
	for key, value := range envs {
		lines = append(lines, key+"="+value)
	}
	content := strings.Join(lines, "\n")
	return base64.StdEncoding.EncodeToString([]byte(content))
}

func DecodeEnvVarsB64(encoded string) map[string]string {
	result := make(map[string]string)
	
	decoded, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return result
	}
	
	lines := strings.Split(string(decoded), "\n")
	for _, line := range lines {
		if line = strings.TrimSpace(line); line != "" {
			kv := strings.SplitN(line, "=", 2)
			if len(kv) == 2 {
				result[kv[0]] = kv[1]
			}
		}
	}
	return result
}

// 方案三：混合格式（简单值用KV，复杂值用Base64）
func EncodeEnvVarsMixed(envs map[string]string) (string, string) {
	simpleVars := make(map[string]string)
	complexVars := make(map[string]string)
	
	for key, value := range envs {
		if isComplexValue(value) {
			complexVars[key] = value
		} else {
			simpleVars[key] = value
		}
	}
	
	kvPart := EncodeEnvVarsKV(simpleVars)
	b64Part := EncodeEnvVarsB64(complexVars)
	
	return kvPart, b64Part
}

// 辅助函数
func needsEncoding(value string) bool {
	// 检查是否包含需要编码的字符
	specialChars := "|:,=&?#[]{}\"' \t\n\r"
	for _, char := range specialChars {
		if strings.ContainsRune(value, char) {
			return true
		}
	}
	return false
}

func isComplexValue(value string) bool {
	// 判断是否为复杂值（多行、包含大量特殊字符等）
	return strings.Contains(value, "\n") || 
		   strings.Contains(value, "-----BEGIN") ||
		   len(value) > 100 ||
		   strings.Count(value, "\"") > 2
}

// 实际使用示例
func main() {
	// 测试环境变量
	envVars := map[string]string{
		"APP_NAME":     "myapp",
		"PORT":         "8080",
		"DEBUG":        "true",
		"DATABASE_URL": "mysql://user:pass@localhost:3306/db?charset=utf8",
		"JWT_SECRET":   "abc123!@#$%^&*()_+-=[]{}|;:,.<>?",
		"DESCRIPTION":  "My Application Description",
		"CONFIG_JSON":  `{"host":"localhost","port":3306,"ssl":true}`,
		"FEATURE_FLAGS": "feature1,feature2,feature3",
	}
	
	fmt.Println("=== 方案一：增强型键值对格式 ===")
	kvEncoded := EncodeEnvVarsKV(envVars)
	fmt.Printf("编码结果: %s\n", kvEncoded)
	kvDecoded := DecodeEnvVarsKV(kvEncoded)
	fmt.Printf("解码结果: %+v\n\n", kvDecoded)
	
	fmt.Println("=== 方案二：Base64 编码格式 ===")
	b64Encoded := EncodeEnvVarsB64(envVars)
	fmt.Printf("编码结果: %s\n", b64Encoded)
	b64Decoded := DecodeEnvVarsB64(b64Encoded)
	fmt.Printf("解码结果: %+v\n\n", b64Decoded)
	
	fmt.Println("=== 方案三：混合格式 ===")
	kvPart, b64Part := EncodeEnvVarsMixed(envVars)
	fmt.Printf("KV部分: %s\n", kvPart)
	fmt.Printf("B64部分: %s\n", b64Part)
	
	// 生成最终的标签
	fmt.Println("\n=== 推荐的标签格式 ===")
	labels := []string{
		"SYS_TYPE=standard",
		"SYS_CATEGORY=system",
		"SYS_VERSION=2025-05-24T20:38:21",
		fmt.Sprintf("SYS_ENVS=%s", kvEncoded),
		"SYS_HOOKS={\"pre\":\"entrypoint-pre.sh\",\"post\":\"entrypoint-post.sh\"}",
		"SYS_OSS={\"enabled\":true,\"endpoint\":\"oss-cn-shanghai.aliyuncs.com\",\"bucket\":\"aggk-bpmax\"}",
	}
	
	for _, label := range labels {
		fmt.Printf("  %s\n", label)
	}
}
