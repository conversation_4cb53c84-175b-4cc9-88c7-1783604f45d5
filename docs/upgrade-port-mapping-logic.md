# 升级操作端口映射逻辑优化

## 优化背景

在 Worker 层的 `queue_processor.go` 文件中，原有的升级操作（UPDATE）没有处理端口映射的逻辑。当服务升级时，新镜像可能有不同的端口配置，需要智能地处理端口映射：保留已有的端口映射，为新端口分配映射。

## 需求分析

### 升级场景的特点
1. **已有部署**：升级操作说明之前有过部署，记录中可能有 `ports_mapping` 的记录
2. **端口变化**：新镜像可能有不同的端口配置
3. **映射保留**：已有的端口映射应该保留，避免服务中断
4. **新端口分配**：新增的端口需要分配新的主机端口映射

### 处理逻辑
1. **检查现有映射**：解析 `record.PortsMapping` 中的现有端口映射
2. **端口对比**：检查当前 `record.Ports` 中的端口是否在现有映射中存在
3. **保留映射**：对于已存在的端口，保留现有的主机端口映射
4. **分配新映射**：对于新增的端口，通过 `mapPorts` 方法分配新的主机端口
5. **更新记录**：将重新整理后的 `ports_mapping` 存储到数据库

## 实现方案

### 1. 升级操作端口映射逻辑

```go
// 如果是升级操作且有端口需要映射，处理端口映射逻辑
if record.Remark == "UPDATE" && len(record.Ports) > 0 && record.NodeIP != "" {
    log.Printf("Processing port mapping for UPDATE operation on service %s with ports %v on node %s",
        record.ServiceID, record.Ports, record.NodeIP)

    // 因为是升级，说明之前有过部署，记录中可能有 ports_mapping 的记录
    log.Printf("Existing ports mapping for service %s: %v", record.ServiceID, record.PortsMapping)

    // 创建一个 map 来存储现有的端口映射 (containerPort -> hostPort)
    existingMappings := make(map[string]string)
    for _, mapping := range record.PortsMapping {
        // 解析映射格式 "hostPort:containerPort"
        parts := strings.Split(mapping, ":")
        if len(parts) == 2 {
            hostPort := parts[0]
            containerPort := parts[1]
            existingMappings[containerPort] = hostPort
            log.Printf("Found existing mapping: container port %s -> host port %s", containerPort, hostPort)
        }
    }

    // 检查当前 ports 字段中的端口，是否在 ports_mapping 记录中存在
    var finalPortsMapping []string
    var portsNeedMapping []string

    for _, port := range record.Ports {
        if hostPort, exists := existingMappings[port]; exists {
            // 端口已有映射，保留现有映射
            mapping := fmt.Sprintf("%s:%s", hostPort, port)
            finalPortsMapping = append(finalPortsMapping, mapping)
            log.Printf("Preserving existing mapping for port %s: %s", port, mapping)
        } else {
            // 端口没有映射，需要新分配
            portsNeedMapping = append(portsNeedMapping, port)
            log.Printf("Port %s needs new mapping", port)
        }
    }

    // 如果有端口需要新的映射，通过 mapPorts 获取
    if len(portsNeedMapping) > 0 {
        log.Printf("Need to allocate mappings for %d ports: %v", len(portsNeedMapping), portsNeedMapping)
        
        newMappings, err := w.mapPorts(record.NodeIP, portsNeedMapping)
        if err != nil {
            log.Printf("Failed to map new ports for service %s: %v", record.ServiceID, err)
            updateDeployRecordStatus(w, record.ServiceID, "FAILED")
            return
        }

        // 将新的映射添加到最终映射列表
        finalPortsMapping = append(finalPortsMapping, newMappings...)
        log.Printf("Allocated new mappings: %v", newMappings)
    }

    // 把重新整理后的 ports_mapping 存到 record.PortsMapping 中
    record.PortsMapping = finalPortsMapping
    log.Printf("Final ports mapping for service %s: %v", record.ServiceID, finalPortsMapping)

    // 更新端口映射到数据库
    if err := w.UpdateDeployRecordPortsMapping(record.ServiceID, finalPortsMapping); err != nil {
        log.Printf("Failed to update ports mapping in database for service %s: %v",
            record.ServiceID, err)
        // 不中断流程，继续执行升级
    }

    log.Printf("Port mapping update completed for service %s", record.ServiceID)
}
```

### 2. 核心处理步骤

#### 步骤1：解析现有端口映射
```go
existingMappings := make(map[string]string)
for _, mapping := range record.PortsMapping {
    parts := strings.Split(mapping, ":")
    if len(parts) == 2 {
        hostPort := parts[0]
        containerPort := parts[1]
        existingMappings[containerPort] = hostPort
    }
}
```

#### 步骤2：端口分类处理
```go
var finalPortsMapping []string
var portsNeedMapping []string

for _, port := range record.Ports {
    if hostPort, exists := existingMappings[port]; exists {
        // 保留现有映射
        mapping := fmt.Sprintf("%s:%s", hostPort, port)
        finalPortsMapping = append(finalPortsMapping, mapping)
    } else {
        // 需要新分配
        portsNeedMapping = append(portsNeedMapping, port)
    }
}
```

#### 步骤3：分配新端口映射
```go
if len(portsNeedMapping) > 0 {
    newMappings, err := w.mapPorts(record.NodeIP, portsNeedMapping)
    if err != nil {
        // 错误处理
        return
    }
    finalPortsMapping = append(finalPortsMapping, newMappings...)
}
```

#### 步骤4：更新记录
```go
record.PortsMapping = finalPortsMapping
w.UpdateDeployRecordPortsMapping(record.ServiceID, finalPortsMapping)
```

## 使用示例

### 场景1：端口完全相同
```go
// 原有映射
record.PortsMapping = ["30001:80", "30002:443"]
// 新镜像端口
record.Ports = ["80", "443"]

// 处理结果
finalPortsMapping = ["30001:80", "30002:443"]  // 完全保留
portsNeedMapping = []                          // 无需新分配
```

### 场景2：新增端口
```go
// 原有映射
record.PortsMapping = ["30001:80", "30002:443"]
// 新镜像端口
record.Ports = ["80", "443", "8080"]

// 处理结果
finalPortsMapping = ["30001:80", "30002:443", "30003:8080"]  // 保留+新增
portsNeedMapping = ["8080"]                                  // 需要新分配
```

### 场景3：端口变化
```go
// 原有映射
record.PortsMapping = ["30001:80", "30002:443"]
// 新镜像端口
record.Ports = ["80", "8080", "9090"]

// 处理结果
finalPortsMapping = ["30001:80", "30003:8080", "30004:9090"]  // 保留+新增
portsNeedMapping = ["8080", "9090"]                           // 需要新分配
```

## 优化效果

### 1. 服务连续性保障
- ✅ **端口保留**：已有端口映射保持不变，避免服务中断
- ✅ **平滑升级**：用户访问的端口保持一致
- ✅ **配置稳定**：负载均衡器和防火墙配置无需修改

### 2. 资源利用优化
- ✅ **端口复用**：避免不必要的端口重新分配
- ✅ **资源节约**：减少端口资源的浪费
- ✅ **冲突避免**：智能分配新端口，避免冲突

### 3. 运维友好性
- ✅ **日志详细**：完整记录端口映射的处理过程
- ✅ **错误处理**：完善的错误处理和状态更新
- ✅ **调试便利**：便于排查端口映射相关问题

## 日志输出示例

### 详细的处理过程
```
Processing port mapping for UPDATE operation on service app-123 with ports [80 443 8080] on node *************
Existing ports mapping for service app-123: [30001:80 30002:443]
Found existing mapping: container port 80 -> host port 30001
Found existing mapping: container port 443 -> host port 30002
Preserving existing mapping for port 80: 30001:80
Preserving existing mapping for port 443: 30002:443
Port 8080 needs new mapping
Need to allocate mappings for 1 ports: [8080]
Mapped container port 8080 to host port 30003
Allocated new mappings: [30003:8080]
Final ports mapping for service app-123: [30001:80 30002:443 30003:8080]
Port mapping update completed for service app-123
```

## 与部署操作的对比

### 部署操作（DEPLOY）
- **场景**：首次部署，没有现有端口映射
- **处理**：为所有端口分配新的主机端口映射
- **逻辑**：简单的端口分配

### 升级操作（UPDATE）
- **场景**：服务升级，可能有现有端口映射
- **处理**：保留现有映射，为新端口分配映射
- **逻辑**：智能的端口映射管理

## 错误处理

### 1. 端口分配失败
```go
if err != nil {
    log.Printf("Failed to map new ports for service %s: %v", record.ServiceID, err)
    updateDeployRecordStatus(w, record.ServiceID, "FAILED")
    return
}
```

### 2. 数据库更新失败
```go
if err := w.UpdateDeployRecordPortsMapping(record.ServiceID, finalPortsMapping); err != nil {
    log.Printf("Failed to update ports mapping in database for service %s: %v",
        record.ServiceID, err)
    // 不中断流程，继续执行升级
}
```

## 相关文件

### 修改的文件
- `internal/worker/queue_processor.go` - 添加升级操作端口映射逻辑

### 依赖的方法
- `w.mapPorts()` - 端口映射分配方法
- `w.UpdateDeployRecordPortsMapping()` - 数据库端口映射更新方法
- `updateDeployRecordStatus()` - 状态更新方法

### 相关文档
- `docs/add-ports-to-update-service.md` - Service 层端口处理文档
- `docs/visited_at_field_fix.md` - Worker 层数据库操作文档

## 最佳实践

### 1. 端口映射格式
- **标准格式**：`"hostPort:containerPort"`
- **示例**：`"30001:80"`
- **解析方式**：使用 `strings.Split(mapping, ":")`

### 2. 错误处理策略
- **端口分配失败**：标记任务为 FAILED，停止处理
- **数据库更新失败**：记录日志，但不中断升级流程
- **格式解析错误**：跳过无效的映射记录

### 3. 日志记录原则
- **详细记录**：记录每个端口的处理过程
- **状态跟踪**：记录映射的保留和新分配情况
- **错误日志**：详细记录错误信息和上下文

## 总结

这次优化成功实现了升级操作的智能端口映射逻辑：

1. **智能保留**：保留现有端口映射，确保服务连续性
2. **动态分配**：为新端口智能分配主机端口映射
3. **数据一致**：确保数据库中的端口映射信息准确
4. **错误处理**：完善的错误处理和状态管理

这个优化为服务升级提供了更加可靠和智能的端口管理能力，确保了升级过程中的服务连续性和端口资源的合理利用。
