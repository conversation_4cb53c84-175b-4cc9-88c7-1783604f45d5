# 环境变量处理逻辑重构指南

## 📋 **重构概述**

根据新的需求，我们重构了环境变量处理逻辑，实现了从 `deploy_record_w` 表获取数据并解析标签的完整流程。

## 🔧 **处理流程**

### 1. **数据获取**
从 `deploy_record_w` 表获取部署记录，包含：
- `labels` 字段：包含 `SYS_ENVS` 和 `SYS_ENVS_TPL` 标签
- `customer_envs` 字段：用户自定义环境变量数组

### 2. **标签解析**
#### SYS_ENVS 格式：
```
SYS_ENVS=KEY1:value1|||KEY2:value2|||KEY3:value3
```

#### SYS_ENVS_TPL 格式：
```
SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123
```

### 3. **挖槽替换**
使用 `SYS_ENVS` 解析的结果替换 `SYS_ENVS_TPL` 中的挖槽：
- `_{DOMAIN}_` → 完整域名（如：`myapp.example.com`）
- `_{DOMAIN_NO_DOT}_` → 无点域名（如：`myapp_example_com`）
- `_{SERVICE_ID}_` → 服务ID
- `_{CUSTOM_KEY}_` → SYS_ENVS 中定义的自定义值

### 4. **用户环境变量解析**
```json
["VARA=1456", "VARB=s d fd  24999", "PPORT=234"]
```

### 5. **最终拼接**
先拼接 `SYS_ENVS_TPL` 处理后的结果，再拼接 `customer_envs` 的结果。

## 🎯 **使用示例**

### **输入数据**
```go
record := &models.DeployRecordW{
    ServiceID:    "test-service-123",
    DomainPrefix: "myapp",
    DomainSuffix: ".example.com",
    Labels: []string{
        "SYS_ENVS=ES_SERVER:elasticsearch.example.com|||MYSQL_HOST:mysql.example.com|||REDIS_HOST:redis.example.com",
        "SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||MYSQL_HOST:_{MYSQL_HOST}_|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:",
    },
    CustomerEnvs: []string{
        "CUSTOM_VAR1=value1",
        "CUSTOM_VAR2=value with spaces",
        "DEBUG=true",
    },
}
```

### **处理结果**
```bash
--env ES_NAMESPACE="myapp_example_com" --env ES_SERVER="http://elasticsearch.example.com" --env MYSQL_DATABASE="myapp_example_com" --env MYSQL_HOST="mysql.example.com" --env REDIS_KEY_PREFIX="myapp_example_com:" --env CUSTOM_VAR1="value1" --env CUSTOM_VAR2="value with spaces" --env DEBUG="true"
```

## 📊 **实际应用场景**

### **场景1：微服务配置**
```
SYS_ENVS=KAFKA_BROKERS:kafka1.example.com:9092,kafka2.example.com:9092|||ES_SERVER:elasticsearch.example.com|||MYSQL_HOST:mysql.example.com

SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||KAFKA_BROKERS:_{KAFKA_BROKERS}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_USER:root|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_
```

### **场景2：OSS配置**
```
SYS_ENVS=OSSBUCKET:my-bucket|||OSSHOST:oss-cn-shanghai.aliyuncs.com|||OSSREGION:cn-shanghai

SYS_ENVS_TPL=OSSBUCKET:_{OSSBUCKET}_|||OSSHOST:_{OSSHOST}_|||OSSREGION:_{OSSREGION}_|||OSSUPLOADBUCKET:_{OSSBUCKET}_|||PLUGIN_OSS_BASE:
```

## 🔧 **API 接口**

### **主要函数**
```go
// 推荐使用：从WorkerDeployRequest处理环境变量，保持数据传递一致性
func ProcessEnvironmentVariablesFromRequest(req *models.WorkerDeployRequest) (string, error)

// 兼容接口：从DeployRecordW处理环境变量
func ProcessEnvironmentVariables(record *models.DeployRecordW) (string, error)
```

### **辅助函数**
```go
func parseSysEnvs(labels []string) (map[string]string, error)
func parseSysEnvsTpl(labels []string) (map[string]string, error)
func replacePlaceholdersFromRequest(tplEnvs map[string]string, sysEnvs map[string]string, req *models.WorkerDeployRequest) map[string]string
func replacePlaceholders(tplEnvs map[string]string, sysEnvs map[string]string, record *models.DeployRecordW) map[string]string
func parseCustomEnvs(customerEnvs []string) (map[string]string, error)
func buildEnvString(tplEnvs map[string]string, customEnvs map[string]string) string
```

## ✅ **特殊字符处理**

### **支持的特殊字符**
- URL：`mysql://user:pass@localhost:3306/db?charset=utf8`
- 符号：`abc123!@#$%^&*()_+-=[]{}|;:,.<>?`
- 引号：`value with "quotes" and 'apostrophes'`
- 管道：`cat file1 | grep pattern | sort`
- 等号：`key1=val1&key2=val2`

### **转义处理**
```go
// 自动转义双引号
value = strings.ReplaceAll(value, "\"", "\\\"")
envArg := fmt.Sprintf("--env %s=\"%s\"", key, escapedValue)
```

## 🔍 **错误处理**

### **容错机制**
1. **获取记录失败**：回退到原有的文件读取逻辑
2. **标签解析失败**：记录错误并继续处理其他部分
3. **挖槽替换失败**：保留原始模板值
4. **环境变量格式错误**：跳过无效的环境变量

### **日志记录**
```go
logger.Info("开始处理环境变量，ServiceID: %s", record.ServiceID)
logger.Info("解析到SYS_ENVS变量 %d 个", len(sysEnvs))
logger.Info("解析到SYS_ENVS_TPL变量 %d 个", len(sysEnvsTpl))
logger.Info("处理后的模板变量 %d 个", len(processedTplEnvs))
logger.Info("解析到custom_envs变量 %d 个", len(customEnvs))
logger.Error("解析SYS_ENVS失败: %v", err)
```

## 🧪 **测试覆盖**

### **测试用例**
- ✅ 完整流程测试
- ✅ SYS_ENVS 解析测试
- ✅ SYS_ENVS_TPL 解析测试
- ✅ 挖槽替换测试
- ✅ 用户环境变量解析测试
- ✅ 环境变量字符串构建测试
- ✅ 特殊字符处理测试

### **运行测试**
```bash
go test ./internal/pkg/utils -v -run "TestProcess|TestParse|TestReplace|TestBuild|TestSpecial"
```

## 🚀 **性能优化**

### **优化点**
1. **单次数据库查询**：避免重复获取部署记录
2. **内存复用**：使用 map 存储解析结果
3. **字符串构建**：使用 slice 拼接而非字符串连接
4. **错误处理**：快速失败，避免无效处理

### **内存使用**
- 解析结果缓存在 map 中
- 字符串拼接使用 slice
- 及时释放临时变量

## 📝 **迁移指南**

### **从旧逻辑迁移**
1. **保持向后兼容**：如果新逻辑失败，自动回退到文件读取
2. **渐进式迁移**：可以同时支持新旧两种格式
3. **数据验证**：确保标签格式正确

### **配置更新**
```json
{
  "old_format": "SYS_ENVS=worker-app.env",
  "new_format": "SYS_ENVS=KEY1:value1|||KEY2:value2"
}
```

## 🎯 **总结**

新的环境变量处理逻辑实现了：

1. **统一数据源**：从数据库获取所有配置
2. **灵活配置**：支持模板和挖槽替换
3. **特殊字符支持**：正确处理各种特殊字符
4. **错误容错**：多层次的错误处理机制
5. **性能优化**：高效的解析和构建算法
6. **测试覆盖**：完整的单元测试

这个重构大大提升了环境变量处理的灵活性和可维护性！🎉
