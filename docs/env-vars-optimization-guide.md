# ProcessEnvironmentVariablesFromRequest 函数优化指南

## 📋 **优化概述**

我们成功优化了 `ProcessEnvironmentVariablesFromRequest` 函数，新增了环境变量 map 返回值，使前置脚本和后置脚本能够进行动态挖槽替换。

## 🔧 **函数签名变更**

### **优化前**
```go
func ProcessEnvironmentVariablesFromRequest(req *models.WorkerDeployRequest) (string, error)
```

### **优化后**
```go
func ProcessEnvironmentVariablesFromRequest(req *models.WorkerDeployRequest) (string, map[string]string, error)
```

## 📊 **返回值说明**

| 返回值 | 类型 | 说明 |
|--------|------|------|
| `envString` | `string` | 用于Docker命令的环境变量字符串 |
| `envMap` | `map[string]string` | 包含所有环境变量的map，供脚本使用 |
| `error` | `error` | 错误信息 |

## 🎯 **使用示例**

### **基本调用**
```go
// 处理环境变量
envString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
if err != nil {
    log.Printf("处理环境变量失败: %v", err)
    return
}

// 用于Docker命令
dockerCmd := fmt.Sprintf("docker run -d --name %s%s %s", containerName, envString, image)

// 用于脚本挖槽替换
if len(envMap) > 0 {
    scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
}
```

### **环境变量map内容示例**
```go
envMap := map[string]string{
    "MYSQL_HOST":     "**************",
    "MYSQL_PORT":     "33068", 
    "MYSQL_DATABASE": "myapp_example_com",
    "ES_SERVER":      "http://**************:9200",
    "DOMAIN":         "myapp.example.com",
    "DOMAIN_NO_DOT":  "myapp_example_com",
    "SERVICE_ID":     "test-service-123",
    "CUSTOM_VAR1":    "value1",
    "CUSTOM_VAR2":    "value with spaces",
}
```

## 🔄 **脚本挖槽替换功能**

### **支持的挖槽格式**

#### 1. **标准Shell变量格式：`${VAR_NAME}`**
```bash
#!/bin/bash
echo "MySQL Host: ${MYSQL_HOST}"
echo "MySQL Port: ${MYSQL_PORT}"
mysql -h ${MYSQL_HOST} -P ${MYSQL_PORT} -D ${MYSQL_DATABASE}
```

#### 2. **模板变量格式：`{{VAR_NAME}}`**
```bash
#!/bin/bash
echo "Domain: {{DOMAIN}}"
echo "Service ID: {{SERVICE_ID}}"
curl -X GET {{ES_SERVER}}/_cluster/health
```

#### 3. **自定义挖槽格式：`_{VAR_NAME}_`**
```bash
#!/bin/bash
echo "API Key: _{API_KEY}_"
echo "Debug Mode: _{DEBUG_MODE}_"
export SECRET_KEY="_{SECRET_KEY}_"
```

### **混合使用示例**
```bash
#!/bin/bash
# 前置钩子脚本示例

# 使用不同格式的挖槽
echo "=== 服务部署前置检查 ==="
echo "域名: {{DOMAIN}}"
echo "服务ID: _{SERVICE_ID}_"
echo "MySQL服务器: ${MYSQL_HOST}:${MYSQL_PORT}"

# 检查MySQL连接
mysql -h ${MYSQL_HOST} -P ${MYSQL_PORT} -e "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "MySQL连接正常"
else
    echo "MySQL连接失败"
    exit 1
fi

# 检查ES服务
curl -s {{ES_SERVER}}/_cluster/health | grep -q "green\|yellow"
if [ $? -eq 0 ]; then
    echo "ES服务正常"
else
    echo "ES服务异常"
    exit 1
fi

# 创建必要的目录
mkdir -p /user/data/_{SERVICE_ID}_/logs
mkdir -p /user/data/_{SERVICE_ID}_/uploads

echo "前置检查完成"
```

## 🚀 **Worker层集成**

### **DeployContainer方法中的使用**
```go
// 处理环境变量
processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
if err != nil {
    fmt.Printf("处理环境变量失败: %v\n", err)
    logger.Error("处理环境变量失败: %v", err)
} else {
    sysEnvVars = processedEnvString
    fmt.Printf("环境变量map包含 %d 个变量，可用于脚本挖槽替换\n", len(envMap))
    logger.Info("成功处理环境变量，生成了 %d 个环境变量", len(envMap))
}

// 前置脚本处理
if sysPreHookScript != "" {
    scriptContent, err := utils.ReadFile(fmt.Sprintf("dbdata/%s", sysPreHookScript))
    if err == nil && envMap != nil && len(envMap) > 0 {
        scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
        // 创建临时脚本并执行...
    }
}

// 后置脚本处理
if sysPostHookScript != "" {
    scriptContent, err := utils.ReadFile(fmt.Sprintf("dbdata/%s", sysPostHookScript))
    if err == nil && envMap != nil && len(envMap) > 0 {
        scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
        // 创建临时脚本并执行...
    }
}
```

## ✅ **优化效果**

### **1. 数据一致性**
- ✅ 环境变量处理和脚本替换使用相同的数据源
- ✅ 避免了重复解析和不一致的问题
- ✅ 保证了Docker容器和脚本中的环境变量完全一致

### **2. 功能增强**
- ✅ 前置脚本可以使用所有环境变量进行动态配置
- ✅ 后置脚本可以使用环境变量进行清理和验证
- ✅ 支持多种挖槽格式，兼容不同的脚本风格

### **3. 性能优化**
- ✅ 一次解析，多处使用，避免重复计算
- ✅ 环境变量map直接传递，无需重新解析
- ✅ 临时脚本文件自动清理，避免磁盘占用

### **4. 错误处理**
- ✅ 统一的错误处理机制
- ✅ 脚本替换失败不影响容器部署
- ✅ 详细的日志记录便于调试

## 🧪 **测试验证**

### **单元测试覆盖**
```bash
# 测试环境变量处理
go test ./internal/pkg/utils -v -run TestProcessEnvironmentVariablesFromRequest

# 测试脚本挖槽替换
go test ./internal/pkg/utils -v -run TestReplaceScriptPlaceholders

# 测试Docker命令格式
go test ./internal/pkg/utils -v -run TestDockerCommandFormat
```

### **测试结果**
```
=== RUN   TestProcessEnvironmentVariablesFromRequest
--- PASS: TestProcessEnvironmentVariablesFromRequest (0.00s)
=== RUN   TestReplaceScriptPlaceholders  
--- PASS: TestReplaceScriptPlaceholders (0.00s)
=== RUN   TestDockerCommandFormat
--- PASS: TestDockerCommandFormat (0.00s)
PASS
```

## 📝 **迁移指南**

### **代码更新**
如果您在其他地方调用了 `ProcessEnvironmentVariablesFromRequest`，需要更新调用方式：

```go
// 旧的调用方式
envString, err := utils.ProcessEnvironmentVariablesFromRequest(req)

// 新的调用方式
envString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
// 如果不需要envMap，可以使用下划线忽略
envString, _, err := utils.ProcessEnvironmentVariablesFromRequest(req)
```

### **向后兼容**
- ✅ 原有的 `ProcessEnvironmentVariables` 函数保持不变
- ✅ 环境变量字符串格式保持不变
- ✅ Docker命令构建逻辑保持不变

## 🎯 **总结**

这次优化成功实现了：

1. **功能扩展**：新增环境变量map返回值
2. **脚本增强**：前置/后置脚本支持动态挖槽替换
3. **数据一致性**：统一的环境变量数据源
4. **性能优化**：一次解析，多处使用
5. **测试完整**：全面的单元测试覆盖

现在前置脚本和后置脚本可以使用所有的环境变量进行动态配置，大大提升了部署流程的灵活性和可维护性！🎉
