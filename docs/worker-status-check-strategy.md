# Worker 状态检查策略设计文档

## 概述

本文档描述了 Service 层调用 Worker 层时的状态检查策略，基于操作类型对资源的影响进行分类处理。

## 设计原则

### 核心理念
**只有会增加 Worker 资源占用的操作才需要检查 Worker 状态，不增加或减少资源占用的操作可以在任何状态下执行。**

### 状态同步策略
**Service 层在成功调用 Worker 层后，应立即更新部署记录状态，保证数据一致性和用户体验的及时性。**

#### 状态同步的重要性：
1. **及时性**：用户能立即看到操作结果，不需要等待定期轮询
2. **数据一致性**：避免用户层调用时获取到过时的状态信息
3. **用户体验**：提供实时的状态反馈
4. **系统响应性**：减少状态不一致的时间窗口

## 操作分类

### 🔴 需要状态检查的操作（资源增加型）

#### 创建/部署操作
- **操作**：`POST /service/create`
- **资源影响**：增加 CPU、内存、存储、网络等资源占用
- **状态要求**：Worker 必须处于 `AVAILABLE` 状态
- **检查逻辑**：
  ```go
  if workerInfo.Status != "AVAILABLE" {
      return error("Worker 不可用")
  }
  ```

### 🟢 无需状态检查的操作（资源不变/减少型）

#### 1. 升级操作
- **操作**：`PUT /service/update`
- **资源影响**：替换镜像，总体资源配置不变
- **状态更新**：`PROCESSING`（立即更新）
- **设计理由**：
  - 升级通常是为了修复问题或更新功能，不应被 Worker 状态阻塞
  - 即使 Worker 处于 `FREEZE` 状态，也可以安全执行镜像替换
  - 有助于在紧急情况下快速修复问题

#### 2. 重启操作
- **操作**：`PUT /service/{service_id}/restart`
- **资源影响**：临时释放后重新分配，总体资源不变
- **状态更新**：`PROCESSING`（立即更新）
- **设计理由**：
  - 重启是常见的故障恢复手段，不应被状态阻塞
  - 不会增加额外的资源占用
  - 可能有助于释放一些临时占用的资源

#### 3. 停止操作
- **操作**：`DELETE /service/{service_id}`
- **资源影响**：释放所有资源，资源减少
- **状态更新**：`STOPPED`（立即更新）
- **设计理由**：
  - 停止操作释放资源，有助于缓解 Worker 压力
  - 即使 Worker 处于 `FREEZE` 状态，停止操作也是安全的
  - 用户应该能够在任何时候停止服务

## 实现细节

### Service 层实现

```go
// 升级操作 - 无需状态检查，但需要状态同步
func (s *Service) UpdateService(ctx context.Context, req *models.UpdateServiceRequest) (*models.ServiceResponse, error) {
    // 对于升级操作，不需要检查 Worker 状态
    // 升级操作不会增加资源占用，只是替换镜像，即使 Worker 处于 FREEZE 状态也可以安全执行
    log.Printf("Update operation for service %s on worker %s (status: %s)", 
        req.ServiceInfo.ServiceID, workerID, workerInfo.Status)
    
    // 直接创建 worker 客户端，无需状态检查
    worker := NewWorkerHTTPClient(workerInfo.Host)
    
    // 调用 Worker 层
    workerResp, err := worker.UpdateContainer(ctx, workerReq)
    if err != nil {
        return nil, err
    }
    
    // 调用成功后，立即更新部署记录状态为 PROCESSING
    // 这样可以及时同步状态，保证用户层调用时获取到一致的状态信息
    if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
        log.Printf("Failed to update deploy record status: %v", updateErr)
        // 不中断流程，继续返回成功响应，但记录日志
    }
    
    return &models.ServiceResponse{
        Code: workerResp.Code,
        Data: workerResp.Data,
        Msg:  workerResp.Msg,
    }, nil
}
```

### 双重状态同步机制

1. **立即同步**：Service 层在 Worker 调用成功后立即更新状态
   - 目的：保证用户能及时看到状态变化
   - 时机：Worker HTTP 调用成功后
   - 状态：根据操作类型设置（PROCESSING、STOPPED 等）

2. **定期同步**：Service 层的 `updateServiceStatus` 定期查询 Worker 层
   - 目的：同步最终的执行结果状态
   - 时机：每 2 分钟轮询一次
   - 状态：从 Worker 层获取真实的容器状态（RUNNING、FAILED 等）

## 优势分析

### 1. 系统可用性提升
- 用户可以在 Worker 繁忙时执行管理操作
- 提高了系统的可操作性和灵活性
- 避免了因 Worker 状态而导致的操作阻塞

### 2. 业务连续性保障
- 升级操作不会被 Worker 状态阻塞，有利于快速修复问题
- 重启和停止操作可以在任何时候执行，提高故障恢复能力
- 支持紧急情况下的快速响应

### 3. 数据一致性保证
- 立即状态更新确保用户看到及时的反馈
- 定期状态同步确保最终一致性
- 减少了状态不一致的时间窗口

### 4. 用户体验改善
- 减少了操作被拒绝的情况
- 提供了更一致的操作体验
- 实时状态反馈提升了用户满意度

## 总结

这种设计策略的核心优势：

1. **精确控制**：只在真正需要的时候进行状态检查
2. **及时反馈**：立即状态更新保证用户体验
3. **最终一致性**：定期同步确保数据准确性
4. **提升可用性**：允许在 Worker 繁忙时执行管理操作
5. **符合直觉**：操作的限制与其对资源的影响相匹配

这种设计既保证了系统的稳定性，又提高了可用性和用户体验，是一个非常合理和实用的架构设计。
