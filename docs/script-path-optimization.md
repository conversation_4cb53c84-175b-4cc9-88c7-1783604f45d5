# 脚本路径优化方案

## 📋 **修改概述**

我已经根据您的建议，将临时脚本文件的存储路径从 `/tmp` 目录改为 `./dbdata` 相对路径，并添加了时间戳以避免并发冲突。

## 🔧 **修改内容**

### **修改前**
```go
// 使用系统临时目录
tempScriptPath := fmt.Sprintf("/tmp/pre_hook_%s.sh", req.ServiceId)
tempScriptPath := fmt.Sprintf("/tmp/post_hook_%s.sh", req.ServiceId)
```

### **修改后**
```go
// 使用相对路径 + 时间戳确保唯一性
timestamp := time.Now().UnixNano()
tempScriptPath := fmt.Sprintf("./dbdata/pre_hook_%s_%d.sh", req.ServiceId, timestamp)
tempScriptPath := fmt.Sprintf("./dbdata/post_hook_%s_%d.sh", req.ServiceId, timestamp)
```

## ✅ **优化特性**

### **1. 相对路径使用**
- ✅ 使用 `./dbdata/` 相对路径
- ✅ 脚本文件与原始脚本在同一目录
- ✅ 便于管理和调试

### **2. 文件名唯一性**
- ✅ 添加纳秒级时间戳 `time.Now().UnixNano()`
- ✅ 避免多个服务同时部署时的文件名冲突
- ✅ 确保并发安全

### **3. 自动清理**
- ✅ 脚本执行完成后自动删除临时文件
- ✅ 避免临时文件积累
- ✅ 保持目录整洁

## 📊 **文件命名示例**

### **前置脚本**
```bash
# 原始脚本
./dbdata/entrypoint-pre.sh

# 临时脚本（包含环境变量替换后的内容）
./dbdata/pre_hook_test-service-123_1705287123456789000.sh
```

### **后置脚本**
```bash
# 原始脚本
./dbdata/entrypoint-post.sh

# 临时脚本（包含环境变量替换后的内容）
./dbdata/post_hook_test-service-123_1705287123456789001.sh
```

## 🔄 **执行流程**

### **脚本处理流程**
```
1. 读取原始脚本
   ├── ./dbdata/entrypoint-pre.sh
   └── 获取脚本内容

2. 环境变量挖槽替换
   ├── 使用 envMap 替换 _{VAR}_ 格式
   └── 生成替换后的脚本内容

3. 创建临时脚本文件
   ├── 生成唯一文件名（包含时间戳）
   ├── ./dbdata/pre_hook_service-123_1705287123456789000.sh
   └── 写入替换后的内容

4. 设置执行权限
   ├── chmod +x tempScriptPath
   └── 确保脚本可执行

5. 执行脚本
   ├── /bin/bash tempScriptPath
   ├── 捕获输出和错误
   └── 记录执行结果

6. 清理临时文件
   ├── os.Remove(tempScriptPath)
   └── 删除临时脚本文件
```

## ⚠️ **注意事项**

### **1. 工作目录依赖**
```bash
# 相对路径依赖于程序启动时的工作目录
# 确保程序从正确的目录启动
cd /path/to/your/app && ./server

# 或者在程序中设置工作目录
os.Chdir("/path/to/your/app")
```

### **2. 目录权限**
```bash
# 确保 dbdata 目录有写权限
chmod 755 dbdata/

# 检查目录权限
ls -la dbdata/
# drwxr-xr-x  dbdata/  # 需要写权限
```

### **3. 磁盘空间**
```bash
# 定期清理可能残留的临时文件
find ./dbdata/ -name "*_hook_*" -type f -mtime +1 -delete

# 监控目录大小
du -sh ./dbdata/
```

## 🎯 **实际应用效果**

### **文件结构**
```
dbdata/
├── entrypoint-pre.sh                           # 原始前置脚本
├── entrypoint-post.sh                          # 原始后置脚本
├── hoocoopre-0527.sql                          # SQL初始化文件
├── nginx-default.tpl                           # Nginx配置模板
├── pre_hook_service-123_1705287123456789000.sh # 临时前置脚本（执行后删除）
└── post_hook_service-123_1705287123456789001.sh # 临时后置脚本（执行后删除）
```

### **日志输出示例**
```
[INFO] 前置脚本已进行环境变量挖槽替换，替换了 8 个变量
[INFO] 开始执行前置钩子脚本: ./dbdata/pre_hook_test-service-123_1705287123456789000.sh
[INFO] 前置钩子脚本执行成功
[INFO] 后置脚本已进行环境变量挖槽替换，替换了 8 个变量
[INFO] 开始执行后置钩子脚本: ./dbdata/post_hook_test-service-123_1705287123456789001.sh
[INFO] 后置钩子脚本执行成功
```

## 🔄 **对比分析**

### **使用 /tmp 目录（修改前）**
```
优点：
✅ 标准临时文件位置
✅ 系统自动清理
✅ 不依赖工作目录

缺点：
❌ 文件分散在不同位置
❌ 调试时不便查看
❌ 可能被系统清理程序删除
```

### **使用 ./dbdata 目录（修改后）**
```
优点：
✅ 文件集中管理
✅ 便于调试和查看
✅ 与原始脚本在同一位置
✅ 不会被系统意外清理

缺点：
❌ 依赖工作目录
❌ 需要确保目录权限
❌ 需要手动清理残留文件
```

## 💡 **最佳实践建议**

### **1. 目录权限检查**
```go
// 在程序启动时检查目录权限
func checkDBDataPermissions() error {
    testFile := "./dbdata/.write_test"
    err := ioutil.WriteFile(testFile, []byte("test"), 0644)
    if err != nil {
        return fmt.Errorf("dbdata directory is not writable: %v", err)
    }
    os.Remove(testFile)
    return nil
}
```

### **2. 定期清理**
```go
// 定期清理临时脚本文件
func cleanupTempScripts() {
    pattern := "./dbdata/*_hook_*"
    matches, _ := filepath.Glob(pattern)
    for _, file := range matches {
        if info, err := os.Stat(file); err == nil {
            if time.Since(info.ModTime()) > time.Hour {
                os.Remove(file)
            }
        }
    }
}
```

### **3. 错误处理**
```go
// 增强错误处理
if err := utils.WriteFile(tempScriptPath, scriptContent); err != nil {
    log.Printf("创建临时脚本失败: %v", err)
    log.Printf("检查 dbdata 目录权限: ls -la ./dbdata/")
    return
}
```

## 🎉 **总结**

这次路径优化实现了：

1. **相对路径使用**：按照您的建议使用 `./dbdata/` 相对路径
2. **文件唯一性**：添加纳秒级时间戳避免冲突
3. **自动清理**：执行完成后自动删除临时文件
4. **便于调试**：临时脚本与原始脚本在同一目录
5. **并发安全**：时间戳确保多个部署操作不会冲突

现在脚本文件将在 `./dbdata/` 目录中创建和执行，更便于管理和调试！🎉

## ⚠️ **重要提醒**

请确保：
1. 程序从包含 `dbdata` 目录的正确路径启动
2. `dbdata` 目录具有写权限
3. 定期检查是否有残留的临时文件

这样可以确保脚本路径功能正常工作。
