package interfaces

import (
	"context"

	"github.com/zero-ops/service-system/internal/models"
)

// WorkerClient 定义了 service 层需要调用的 worker 方法
// 这是一个接口，可以由 worker 层实现，也可以由其他组件实现（如测试模拟）
type WorkerClient interface {

	// DeployContainer 部署容器
	DeployContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error)

	// StopContainer 停止容器
	StopContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error)

	// RestartContainer 重启容器
	RestartContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error)

	// UpdateContainer 升级容器
	UpdateContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error)

	// HealthCheck 检查 worker 的健康状态
	HealthCheck(ctx context.Context) (*models.WorkerResponse, error)

	// GetDeployStatus 获取部署状态
	GetDeployStatus(ctx context.Context, serviceIDs []string) (*models.WorkerResponse, error)
}
