package middleware

import (
	"encoding/base64"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// BasicAuth 中间件实现基本的HTTP认证
func BasicAuth(username, password string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		auth := c.<PERSON>("Authorization")
		if auth == "" {
			// 如果没有Authorization头，返回401
			c.Header("WWW-Authenticate", "Basic realm=Authorization Required")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "未授权访问",
			})
			return
		}

		// 检查Authorization头格式
		if !strings.HasPrefix(auth, "Basic ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证格式错误",
			})
			return
		}

		// 解码Base64编码的凭证
		creds, err := base64.StdEncoding.DecodeString(auth[6:])
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证格式错误",
			})
			return
		}

		// 检查凭证格式
		pair := strings.SplitN(string(creds), ":", 2)
		if len(pair) != 2 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证格式错误",
			})
			return
		}

		// 验证用户名和密码
		if pair[0] != username || pair[1] != password {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "用户名或密码错误",
			})
			return
		}

		// 认证成功，继续处理请求
		c.Next()
	}
}
