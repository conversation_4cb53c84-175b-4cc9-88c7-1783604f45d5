package worker

import (
	"context"
	"log"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
)

// WorkerManager 处理 worker 节点的所有业务逻辑
type WorkerManager struct {
	worker *Worker
	db     *database.WorkerDB
	config Config
}

// NewWorkerManager 创建一个新的 WorkerManager 实例
func NewWorkerManager() *WorkerManager {
	return NewWorkerManagerWithConfig(DefaultConfig())
}

// NewWorkerManagerWithConfig 使用指定配置创建一个新的 WorkerManager 实例
func NewWorkerManagerWithConfig(config Config) *WorkerManager {
	manager := &WorkerManager{
		worker: &Worker{
			config: config,
		},
		config: config,
	}

	// 初始化数据库（如果配置了）
	if config.DB.DBPath != "" {
		db, err := database.NewWorkerDB(config.DB)
		if err != nil {
			log.Printf("Failed to initialize worker database: %v", err)
		} else {
			// 初始化数据库 schema
			if err := db.InitSchema(); err != nil {
				log.Printf("Failed to initialize worker database schema: %v", err)
			}

			manager.db = db
		}
	}

	return manager
}

// DeployContainer 部署容器
func (m *WorkerManager) DeployContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.DeployContainer(ctx, req)
}

// StopContainer 停止容器
func (m *WorkerManager) StopContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.StopContainer(ctx, req)
}

// RestartContainer 重启容器
func (m *WorkerManager) RestartContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.RestartContainer(ctx, req)
}

// CreateContainerViaSSH 通过SSH在远程服务器上创建容器
func (m *WorkerManager) CreateContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.CreateContainerViaSSH(ctx, req)
}

// StopContainerViaSSH 通过SSH停止远程服务器上的容器
func (m *WorkerManager) StopContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.StopContainerViaSSH(ctx, req)
}

// RestartContainerViaSSH 通过SSH重启远程服务器上的容器
func (m *WorkerManager) RestartContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	return m.worker.RestartContainerViaSSH(ctx, req)
}

// HealthCheck 获取当前 worker 的健康状态
func (m *WorkerManager) HealthCheck(ctx context.Context) (*models.WorkerResponse, error) {
	return m.worker.HealthCheck(ctx)
}

// GetDeployStatus 获取容器部署状态
func (m *WorkerManager) GetDeployStatus(ctx context.Context, serviceID, nodeIP string) (*models.ContainerStatus, error) {
	return m.worker.GetDeployStatus(ctx, serviceID, nodeIP)
}

// SetStatus 设置当前 worker 的状态
func (m *WorkerManager) SetStatus(status WorkerStatus) {
	SetWorkerStatus(status)
}
