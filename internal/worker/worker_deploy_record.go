package worker

import (
	"fmt"
	"log"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// CreateDeployRecord 创建一个新的部署记录
func (w *Worker) CreateDeployRecord(req *models.WorkerDeployRequest, taskType string) (models.DeployRecordW, error) {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return models.DeployRecordW{}, fmt.Errorf("database connection not available")
	}

	// 将 worker 状态设置为 FREEZE，因为新建部署任务、停止已部署服务、重启已部署服务
	// 都会对服务器的硬件资源产生长期的或短时的影响
	log.Println("Setting worker status to FREEZE during CreateDeployRecord")
	SetWorkerStatus(StatusFreeze)

	// 如果请求中已经有 ServiceId，则使用请求中的
	if req.ServiceId == "" {
		return models.DeployRecordW{}, fmt.Errorf("serviceId is empty")
	}

	// 检查 deploy_record_w 表里是否已经存在当前的 ServiceId
	existingRecord, err := w.deployRecordWRepo.GetByID(req.ServiceId)
	if err == nil {
		// 记录存在，检查状态
		// 如果状态不为 QUEUEING 或 PROCESSING 且（当状态为 RUNNING 时，remark 值等于 DEPLOY），则更新数据
		if existingRecord.Status != "QUEUEING" && existingRecord.Status != "PROCESSING" &&
			(existingRecord.Status != "RUNNING" || (existingRecord.Status == "RUNNING" && (taskType == "RESTART" || taskType == "STOP" || taskType == "UPDATE"))) {
			// 更新现有记录
			existingRecord.Status = "QUEUEING" // 重置状态为待处理
			existingRecord.UpdatedAt = utils.GetCSTTimeString()
			existingRecord.Remark = taskType // 更新任务类型

			// 目前升级仅支持“更换镜像”
			// 如果需要其他的升级，再做添加
			if taskType == "UPDATE" {
				existingRecord.ImageName = req.ImageName // 仅更新镜像名
				existingRecord.ImageURL = req.ImageURL   // 仅更新镜像地址
				existingRecord.Labels = req.Labels
				existingRecord.Ports = req.Ports

				// TODO: 扩展点 - 如需支持更多字段更新，可在此处添加
				// 例如：
				// existingRecord.DomainPrefix = req.DomainPrefix       // 域名前缀更新
				// existingRecord.DomainSuffix = req.DomainSuffix       // 域名后缀更新
				// existingRecord.Expiration = req.Expiration           // 过期时间更新
				// existingRecord.DurationSeconds = req.DurationSeconds // 持续时间更新
				// existingRecord.ApiReplica = req.ApiReplica           // API副本数更新
				// existingRecord.ApiCpu = req.ApiCpu                   // API CPU更新
				// existingRecord.ApiMemory = req.ApiMemory             // API内存更新
				// existingRecord.AutoReplica = req.AutoReplica         // Auto副本数更新
				// existingRecord.AutoCpu = req.AutoCpu                 // Auto CPU更新
				// existingRecord.AutoMemory = req.AutoMemory           // Auto内存更新
				// existingRecord.CustomerEnvs = req.CustomerEnvs       // 环境变量更新
			}

			// 更新数据库中的记录
			if err := w.deployRecordWRepo.Update(existingRecord); err != nil {
				return models.DeployRecordW{}, fmt.Errorf("failed to update existing deploy record: %w", err)
			}

			return existingRecord, nil
		} else {
			// 状态为 QUEUEING、PROCESSING 或 RUNNING，不允许更新
			return models.DeployRecordW{}, fmt.Errorf("cannot update record with status %s, please wait for the current operation to complete", existingRecord.Status)
		}
	}

	// 记录不存在或获取记录时出错，创建新记录
	record := models.DeployRecordW{
		ServiceID:       req.ServiceId,
		Name:            req.ServiceId,
		ImageName:       req.ImageName,
		ImageURL:        req.ImageURL,
		DomainPrefix:    req.DomainPrefix,
		DomainSuffix:    req.DomainSuffix,
		Expiration:      req.Expiration,
		DurationSeconds: req.DurationSeconds, // 设置持续时间（秒）
		Status:          "QUEUEING",          // 初始状态为待处理
		HostIP:          req.HostIP,
		NodeIP:          req.NodeIP,
		Labels:          req.Labels,
		CustomerEnvs:    req.CustomerEnvs,
		Ports:           req.Ports, // 设置端口列表
		ApiReplica:      req.ApiReplica,
		ApiCpu:          req.ApiCpu,
		ApiMemory:       req.ApiMemory,
		AutoReplica:     req.AutoReplica,
		AutoCpu:         req.AutoCpu,
		AutoMemory:      req.AutoMemory,
		CreatedAt:       utils.GetCSTTimeString(),
		UpdatedAt:       utils.GetCSTTimeString(),
	}

	// 将任务类型存储在 Remark 字段中
	record.Remark = taskType

	// 将部署记录保存到数据库
	if err := w.deployRecordWRepo.Create(&record); err != nil {
		return models.DeployRecordW{}, fmt.Errorf("failed to save deploy record to database: %w", err)
	}

	return record, nil
}

// GetDeployRecordByID 根据 ID 获取部署记录
// TODO: 需要重构，支持多值查询
func (w *Worker) GetDeployRecordByID(serviceID string) (models.DeployRecordW, error) {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return models.DeployRecordW{}, fmt.Errorf("database connection not available")
	}

	// 从数据库获取部署记录
	record, err := w.deployRecordWRepo.GetByID(serviceID)
	if err != nil {
		return models.DeployRecordW{}, fmt.Errorf("failed to get deploy record from database: %w", err)
	}

	return record, nil
}

// GetAllDeployRecords 获取所有部署记录
func (w *Worker) GetAllDeployRecords(filter map[string]interface{}) ([]models.DeployRecordW, error) {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// 从数据库获取部署记录列表
	records, err := w.deployRecordWRepo.GetAll(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get deploy records from database: %w", err)
	}

	return records, nil
}

// UpdateDeployRecordStatus 更新部署记录状态
func (w *Worker) UpdateDeployRecordStatus(serviceID, status string) error {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return fmt.Errorf("database connection not available")
	}

	// 更新部署记录状态
	if err := w.deployRecordWRepo.UpdateStatus(serviceID, status); err != nil {
		return fmt.Errorf("failed to update deploy record status: %w", err)
	}

	return nil
}

// UpdateDeployRecordPortsMapping 更新部署记录的端口映射
func (w *Worker) UpdateDeployRecordPortsMapping(serviceID string, portsMapping []string) error {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return fmt.Errorf("database connection not available")
	}

	// 更新部署记录的端口映射
	if err := w.deployRecordWRepo.UpdatePortsMapping(serviceID, portsMapping); err != nil {
		return fmt.Errorf("failed to update deploy record ports mapping: %w", err)
	}

	return nil
}

// UpdateDeployRecordRemark 更新部署记录的备注字段
func (w *Worker) UpdateDeployRecordRemark(serviceID, remark string) error {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return fmt.Errorf("database connection not available")
	}

	// 更新部署记录的备注字段
	if err := w.deployRecordWRepo.UpdateRemark(serviceID, remark); err != nil {
		return fmt.Errorf("failed to update deploy record remark: %w", err)
	}

	return nil
}

// DeleteDeployRecord 删除部署记录
func (w *Worker) DeleteDeployRecord(serviceID string) error {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return fmt.Errorf("database connection not available")
	}

	// 从数据库删除部署记录
	if err := w.deployRecordWRepo.Delete(serviceID); err != nil {
		return fmt.Errorf("failed to delete deploy record from database: %w", err)
	}

	return nil
}

// QueueDeployTask 将部署任务加入队列
func (w *Worker) QueueDeployTask(req *models.WorkerDeployRequest) (models.DeployRecordW, error) {
	return w.CreateDeployRecord(req, "DEPLOY")
}

// QueueStopTask 将停止任务加入队列
func (w *Worker) QueueStopTask(req *models.WorkerDeployRequest) (models.DeployRecordW, error) {
	return w.CreateDeployRecord(req, "STOP")
}

// QueueRestartTask 将重启任务加入队列
func (w *Worker) QueueRestartTask(req *models.WorkerDeployRequest) (models.DeployRecordW, error) {
	return w.CreateDeployRecord(req, "RESTART")
}

// QueueUpdateTask 将重启任务加入队列
func (w *Worker) QueueUpdateTask(req *models.WorkerDeployRequest) (models.DeployRecordW, error) {
	return w.CreateDeployRecord(req, "UPDATE")
}
