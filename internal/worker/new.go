package worker

import (
	"context"
	"log"
	"sync"

	"github.com/zero-ops/service-system/internal/database"
)

// NewWorker creates a new worker instance
func NewWorker() *Worker {
	config := DefaultConfig()
	return NewWorkerWithConfig(config)
}

// NewWorkerWithConfig creates a new worker instance with the given configuration
func NewWorkerWithConfig(config Config) *Worker {
	// Create worker instance
	worker := &Worker{
		config:               config,
		taskProcessorMu:      sync.Mutex{},
		taskProcessorRunning: false,
		taskProcessorCancel:  nil,
	}

	// Initialize database
	dbConfig := database.WorkerDBConfig{
		DBPath: config.DB.DBPath,
	}

	db, err := database.NewWorkerDB(dbConfig)
	if err != nil {
		log.Printf("Failed to initialize database: %v", err)
	} else {
		// Initialize schema
		if err := db.InitSchema(); err != nil {
			log.Printf("Failed to initialize database schema: %v", err)
		}

		// Initialize repositories
		worker.db = db
		worker.deployRecordWRepo = database.NewDeployRecordWRepository(db)

		// Start the task processor
		worker.StartTaskProcessor(context.Background())
		log.Println("Task processor started")
	}

	// Set initial worker status to AVAILABLE
	SetWorkerStatus(StatusAvailable)

	return worker
}

// Close closes the worker and releases resources
func (w *Worker) Close() error {
	// Stop the task processor
	w.StopTaskProcessor()

	// Close the database connection
	if w.db != nil {
		return w.db.Close()
	}

	return nil
}
