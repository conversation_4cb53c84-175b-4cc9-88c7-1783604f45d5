package worker

import (
	"bytes"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"golang.org/x/crypto/ssh"
)

// SSHClient 表示SSH客户端，用于远程执行命令
type SSHClient struct {
	Config *SSHConfig
	client *ssh.Client // 保存SSH连接以便重用
	mu     sync.Mutex  // 互斥锁，保护client字段
}

// SSHConfig SSH连接配置
type SSHConfig struct {
	User     string // SSH用户名
	ServerIP string // 服务器IP
	Port     int    // SSH端口
	Timeout  int    // 连接超时时间(秒)
	KeyPath  string // SSH私钥路径
}

// NewSSHClient 创建一个新的SSH客户端
func NewSSHClient(serverIP, user string) *SSHClient {
	// 使用默认配置
	return NewSSHClientWithConfig(serverIP, user, DefaultConfig())
}

// NewSSHClientWithConfig 使用指定配置创建一个新的SSH客户端
func NewSSHClientWithConfig(serverIP, user string, config Config) *SSHClient {
	// 如果未指定用户名，则使用配置中的默认用户名
	if user == "" {
		user = config.SSHDefaultUser
	}

	return &SSHClient{
		Config: &SSHConfig{
			User:     user,
			ServerIP: serverIP,
			Port:     config.SSHDefaultPort,
			Timeout:  config.SSHTimeout,
			KeyPath:  config.SSHDefaultKeyPath,
		},
		mu: sync.Mutex{},
	}
}

// Close 关闭SSH连接
func (c *SSHClient) Close() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.client != nil {
		c.client.Close()
		c.client = nil
	}
}

// getSSHClient 获取或创建SSH连接
func (c *SSHClient) getSSHClient() (*ssh.Client, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果已经有连接且连接有效，直接返回
	if c.client != nil {
		// 尝试发送一个保活消息来检查连接是否有效
		_, _, err := c.client.SendRequest("<EMAIL>", true, nil)
		if err == nil {
			return c.client, nil
		}
		// 连接已失效，关闭它
		c.client.Close()
		c.client = nil
	}

	// 读取SSH私钥
	keyBytes, err := os.ReadFile(c.Config.KeyPath)
	if err != nil {
		return nil, fmt.Errorf("读取SSH私钥失败: %w", err)
	}

	// 解析SSH私钥
	signer, err := ssh.ParsePrivateKey(keyBytes)
	if err != nil {
		return nil, fmt.Errorf("解析SSH私钥失败: %w", err)
	}

	// 创建SSH客户端配置
	clientConfig := &ssh.ClientConfig{
		User:            c.Config.User,
		Auth:            []ssh.AuthMethod{ssh.PublicKeys(signer)},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         time.Duration(c.Config.Timeout) * time.Second,
	}

	fmt.Println("Connecting to SSH server...", fmt.Sprintf("%s:%d", c.Config.ServerIP, c.Config.Port))
	// 连接到SSH服务器
	client, err := ssh.Dial("tcp", fmt.Sprintf("%s:%d", c.Config.ServerIP, c.Config.Port), clientConfig)
	if err != nil {
		fmt.Println("SSH连接失败:", err)
		return nil, fmt.Errorf("SSH连接失败: %w", err)
	}

	// 保存连接以便重用
	c.client = client
	return client, nil
}

// ExecuteCommand 在远程服务器上执行命令
func (c *SSHClient) ExecuteCommand(command string) (string, error) {
	// 获取或创建SSH连接
	client, err := c.getSSHClient()
	if err != nil {
		return "", err
	}

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		// 如果创建会话失败，可能是连接已断开，尝试重新连接
		c.mu.Lock()
		if c.client != nil {
			c.client.Close()
			c.client = nil
		}
		c.mu.Unlock()

		// 重新获取连接并创建会话
		client, err = c.getSSHClient()
		if err != nil {
			return "", fmt.Errorf("重新连接SSH失败: %w", err)
		}

		session, err = client.NewSession()
		if err != nil {
			return "", fmt.Errorf("创建SSH会话失败: %w", err)
		}
	}
	defer session.Close()

	// 执行命令
	var output bytes.Buffer
	session.Stdout = &output
	if err := session.Run(command); err != nil {
		return "", fmt.Errorf("执行命令失败: %w", err)
	}

	return output.String(), nil
}

// CreateContainer 在远程服务器上创建Docker容器
func (c *SSHClient) CreateContainer(image string, cpuCores float64, memoryMB float64, labels map[string]string, containerName string) (*models.DockerContainerInfo, error) {
	// 如果没有提供容器名称，则生成一个默认名称
	if containerName == "" {
		containerName = fmt.Sprintf("service-%d", time.Now().UnixNano())
	} else {
		// 将域名中的点(.)转换为下划线(_)
		containerName = strings.ReplaceAll(containerName, ".", "_")
	}

	// 确保labels不为nil
	if labels == nil {
		labels = make(map[string]string)
	}

	// 添加默认标签
	labels["ops-system"] = "true"

	// 构建标签参数
	labelArgs := ""
	for k, v := range labels {
		labelArgs += fmt.Sprintf(" --label %s=%s", k, v)
	}

	// 构建Docker运行命令，设置资源限制和标签
	command := fmt.Sprintf(
		"docker run -d --name %s --cpus=%.2f --memory=%dm%s %s",
		containerName,
		cpuCores,
		int(memoryMB),
		labelArgs,
		image,
	)

	// 执行Docker命令
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return nil, err
	}

	// 解析容器ID
	containerID := strings.TrimSpace(output)

	// 获取容器状态
	statusCmd := fmt.Sprintf("docker inspect --format='{{.State.Status}}' %s", containerID)
	status, err := c.ExecuteCommand(statusCmd)
	if err != nil {
		return nil, fmt.Errorf("获取容器状态失败: %w", err)
	}

	return &models.DockerContainerInfo{
		ContainerID: containerID,
		Image:       image,
		Status:      strings.TrimSpace(status),
		Name:        containerName,
		ServerIP:    c.Config.ServerIP,
		Labels:      labels,
	}, nil
}

// StopContainer 停止远程服务器上的Docker容器
func (c *SSHClient) StopContainer(containerID string) error {
	command := fmt.Sprintf("docker stop %s", containerID)
	_, err := c.ExecuteCommand(command)
	return err
}

// RemoveContainer 删除远程服务器上的Docker容器
func (c *SSHClient) RemoveContainer(containerID string) error {
	command := fmt.Sprintf("docker rm -f %s", containerID)
	_, err := c.ExecuteCommand(command)
	return err
}

// RestartContainer 重启远程服务器上的Docker容器
func (c *SSHClient) RestartContainer(containerID string) error {
	command := fmt.Sprintf("docker restart %s", containerID)
	_, err := c.ExecuteCommand(command)
	return err
}

// GetContainerStatus 获取远程服务器上Docker容器的状态
func (c *SSHClient) GetContainerStatus(containerID string) (string, error) {
	command := fmt.Sprintf("docker inspect --format='{{.State.Status}}' %s", containerID)
	status, err := c.ExecuteCommand(command)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(status), nil
}

// GetContainersByLabel 通过标签获取远程服务器上的Docker容器列表
func (c *SSHClient) GetContainersByLabel(labelFilter string) ([]*models.DockerContainerInfo, error) {
	// 构建Docker命令，使用标签过滤容器
	command := fmt.Sprintf("docker ps -a --filter \"label=%s\" --format \"{{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Names}}\"", labelFilter)

	// 执行Docker命令
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return nil, err
	}

	// 解析Docker容器列表输出
	lines := strings.Split(strings.TrimSpace(output), "\n")
	containers := make([]*models.DockerContainerInfo, 0, len(lines))

	for _, line := range lines {
		if line == "" {
			continue
		}

		parts := strings.Split(line, "\t")
		if len(parts) < 4 {
			continue
		}

		containers = append(containers, &models.DockerContainerInfo{
			ContainerID: parts[0],
			Image:       parts[1],
			Status:      parts[2],
			Name:        parts[3],
			ServerIP:    c.Config.ServerIP,
			Labels:      make(map[string]string), // 默认空标签，如果需要详细标签信息，需要额外调用docker inspect
		})
	}

	return containers, nil
}

// CheckPortAvailability 检查远程服务器上的端口是否可用
func (c *SSHClient) CheckPortAvailability(port int) (bool, error) {
	// 使用更可靠的方法检查端口是否被占用
	// 1. 使用 ss 命令（如果可用）
	// 2. 回退到 netstat 命令
	// 3. 尝试使用 lsof 命令

	// 尝试使用 ss 命令
	ssCommand := fmt.Sprintf("ss -tuln | grep ':%d'", port)
	ssOutput, ssErr := c.ExecuteCommand(ssCommand)

	// 如果 ss 命令成功执行
	if ssErr == nil {
		// 如果输出为空，则端口可用
		if strings.TrimSpace(ssOutput) == "" {
			return true, nil
		}
		// 否则端口被占用
		return false, nil
	}

	// 如果 ss 命令不可用，尝试使用 netstat 命令
	netstatCommand := fmt.Sprintf("netstat -tuln | grep ':%d'", port)
	netstatOutput, netstatErr := c.ExecuteCommand(netstatCommand)

	// 如果 netstat 命令成功执行或者错误是因为没有找到匹配项
	if netstatErr == nil || strings.Contains(netstatErr.Error(), "exit status 1") {
		// 如果输出为空，则端口可用
		if strings.TrimSpace(netstatOutput) == "" {
			return true, nil
		}
		// 否则端口被占用
		return false, nil
	}

	// 如果 netstat 命令也不可用，尝试使用 lsof 命令
	lsofCommand := fmt.Sprintf("lsof -i :%d", port)
	lsofOutput, lsofErr := c.ExecuteCommand(lsofCommand)

	// 如果 lsof 命令成功执行或者错误是因为没有找到匹配项
	if lsofErr == nil || strings.Contains(lsofErr.Error(), "exit status 1") {
		// 如果输出为空，则端口可用
		if strings.TrimSpace(lsofOutput) == "" {
			return true, nil
		}
		// 否则端口被占用
		return false, nil
	}

	// 如果所有命令都失败，假设端口可用（宁可错误地认为端口可用，也不要错误地认为端口不可用）
	return true, nil
}

// GetAvailablePorts 获取指定范围内的可用端口
func (c *SSHClient) GetAvailablePorts(startPort, endPort, count int) ([]int, error) {
	// 直接检查每个端口是否可用，而不是先获取所有已使用的端口
	// 这样更可靠，因为不依赖于特定命令的输出格式

	// 记录日志
	fmt.Printf("Checking for available ports in range %d-%d, need %d ports\n", startPort, endPort, count)

	// 找出可用端口
	availablePorts := make([]int, 0, count)
	for port := startPort; port <= endPort && len(availablePorts) < count; port++ {
		// 检查端口是否可用
		available, err := c.CheckPortAvailability(port)
		if err != nil {
			fmt.Printf("Error checking port %d: %v, assuming it's available\n", port, err)
			// 如果检查出错，假设端口可用
			availablePorts = append(availablePorts, port)
			continue
		}

		if available {
			fmt.Printf("Port %d is available\n", port)
			availablePorts = append(availablePorts, port)
		} else {
			fmt.Printf("Port %d is in use\n", port)
		}
	}

	// 如果找不到足够的可用端口，返回错误
	if len(availablePorts) < count {
		return availablePorts, fmt.Errorf("找不到足够的可用端口，需要 %d 个，但只找到 %d 个", count, len(availablePorts))
	}

	fmt.Printf("Found %d available ports: %v\n", len(availablePorts), availablePorts)
	return availablePorts, nil
}
