package worker

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/models"
)

// 错误类型常量
const (
	ErrInvalidParam = "参数错误"
	ErrServerError  = "服务器错误"
)

// WorkerError 封装Worker错误信息
type WorkerError struct {
	Code    int    // HTTP状态码
	Message string // 错误消息
	Context string // 错误上下文
}

// Error 实现error接口
func (e *WorkerError) Error() string {
	if e.Context != "" {
		return fmt.Sprintf("%s: %s", e.Context, e.Message)
	}
	return e.Message
}

// NewParamError 创建参数错误
func NewParamError(context, message string) *WorkerError {
	return &WorkerError{
		Code:    400,
		Message: message,
		Context: context,
	}
}

// NewServerError 创建服务器错误
func NewServerError(context string, err error) *WorkerError {
	return &WorkerError{
		Code:    500,
		Message: err.Error(),
		Context: context,
	}
}

// BuildErrorResponse 构建统一的错误响应
func BuildErrorResponse(operation string, err error) (*models.WorkerResponse, error) {
	// 如果是WorkerError类型，直接使用其中的信息
	if workerErr, ok := err.(*WorkerError); ok {
		return &models.WorkerResponse{
			Code: workerErr.Code,
			Data: gin.H{"error": workerErr.Message},
			Msg:  fmt.Sprintf("Worker: %s失败: %s", operation, workerErr.Error()),
		}, err
	}

	// 普通错误，默认作为服务器错误处理
	return &models.WorkerResponse{
		Code: 500,
		Data: gin.H{"error": err.Error()},
		Msg:  fmt.Sprintf("Worker: %s失败: %v", operation, err),
	}, err
}

// BuildSuccessResponse 构建统一的成功响应
func BuildSuccessResponse(operation string, data gin.H, detail string) *models.WorkerResponse {
	return &models.WorkerResponse{
		Code: 200,
		Data: data,
		Msg:  fmt.Sprintf("Worker: %s成功%s", operation, detail),
	}
}
