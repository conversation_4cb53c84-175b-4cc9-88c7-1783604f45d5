package worker

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/logger"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// Worker represents the worker node that runs on servers
type Worker struct {
	// 配置信息
	config Config

	// 数据库仓库
	db                *database.WorkerDB
	deployRecordWRepo *database.DeployRecordWRepository

	// 任务处理器状态
	taskProcessorMu      sync.Mutex
	taskProcessorRunning bool
	taskProcessorCancel  context.CancelFunc

	// 全局查询锁，用于保护数据库查询操作
	queryMutex sync.Mutex
}

// DeployContainer deploys a container on the worker node
func (w *Worker) DeployContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if err := w.validateDeployParams(req); err != nil {
		return BuildErrorResponse("部署", err)
	}

	// 确定使用镜像
	image := req.ImageURL

	// 获取SSH客户端
	user := "root" // 使用配置中的默认用户

	// 计算资源需求
	// 处理 API 服务的资源需求
	apiReplica := req.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1 // 默认值为 1
	}
	apiCPU := req.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求

	// 处理 Auto 服务的资源需求
	autoReplica := req.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
	}
	autoCPU := req.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU // 总 CPU 需求
	if totalCPU <= 0 {
		totalCPU = 0.5 // 默认至少 1 核
	}

	// 计算内存需求
	apiMemory := req.ApiMemory * apiReplica        // API 服务总内存需求
	autoMemory := req.AutoMemory * autoReplica     // Auto 服务总内存需求
	totalMemory := float64(apiMemory + autoMemory) // 总内存需求
	if totalMemory <= 0 {
		totalMemory = 1024.0 // 默认至少 1024MB 内存
	}

	// 处理容器名称
	containerName := req.ServiceId

	// 处理特殊标签
	var sysPreHookScript string
	var sysPostHookScript string
	var sysEnvVars string
	var sysVolumes string

	// 读取 req 数据中的 labels
	for _, label := range req.Labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value := parts[1]

		// 检查是否是前置钩子脚本
		if key == "SYS_PREHOOK" && strings.HasSuffix(value, ".sh") {
			sysPreHookScript = value
			fmt.Printf("检测到前置钩子脚本: %s\n", sysPreHookScript)
			logger.Info("检测到前置钩子脚本: %s", sysPreHookScript)
		}

		// 检查是否是后置钩子脚本
		if key == "SYS_POSTHOOK" && strings.HasSuffix(value, ".sh") {
			sysPostHookScript = value
			fmt.Printf("检测到后置钩子脚本: %s\n", sysPostHookScript)
			logger.Info("检测到后置钩子脚本: %s", sysPostHookScript)
		}

		// 检查是否是卷映射标签
		if key == "SYS_VOL" {
			// 处理卷映射
			volumeMappings := strings.Split(value, ",")
			for _, mapping := range volumeMappings {
				mapping = strings.TrimSpace(mapping)
				if mapping == "" {
					continue
				}

				parts := strings.Split(mapping, ":")
				if len(parts) != 2 {
					fmt.Printf("无效的卷映射格式: %s，应为 '宿主机路径:容器内路径'\n", mapping)
					logger.Warn("无效的卷映射格式: %s，应为 '宿主机路径:容器内路径'", mapping)
					continue
				}

				hostPath := parts[0]
				containerPath := parts[1]

				// 为宿主机路径添加前缀
				hostPathWithPrefix := fmt.Sprintf("/user/data/%s/%s", req.ServiceId, hostPath)

				// 添加到卷映射参数
				sysVolumes += fmt.Sprintf(" -v %s:%s", hostPathWithPrefix, containerPath)

				fmt.Printf("添加卷映射: %s -> %s\n", hostPathWithPrefix, containerPath)
				logger.Info("添加卷映射: %s -> %s", hostPathWithPrefix, containerPath)
			}
		}

		// 可以在这里添加其他特殊标签的检查

	}

	// 使用新的环境变量处理逻辑
	// 保持数据传递的一致性，所有部署信息都从req参数中获取
	processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		fmt.Printf("处理环境变量失败: %v\n", err)
		logger.Error("处理环境变量失败: %v", err)
		// 处理失败时，sysEnvVars 保持为空字符串，后续会使用 req.CustomerEnvs
	} else {
		sysEnvVars = processedEnvString
		fmt.Printf("处理后的环境变量: %s\n", sysEnvVars)
		fmt.Printf("环境变量map包含 %d 个变量，可用于脚本挖槽替换\n", len(envMap))
		logger.Info("成功处理环境变量，生成了 %d 个环境变量", len(envMap))
	}

	// 执行前置钩子脚本
	if sysPreHookScript != "" {
		scriptPath := fmt.Sprintf("dbdata/%s", sysPreHookScript)
		scriptContent, err := utils.ReadFile(scriptPath)
		if err != nil {
			fmt.Printf("读取前置钩子脚本失败: %v\n", err)
			logger.Error("读取前置钩子脚本失败: %v", err)
		} else {
			// 使用环境变量map进行挖槽替换
			if envMap != nil && len(envMap) > 0 {
				scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
				fmt.Printf("前置脚本已进行环境变量挖槽替换，替换了 %d 个变量\n", len(envMap))
				logger.Info("前置脚本已进行环境变量挖槽替换，替换了 %d 个变量", len(envMap))
			}

			// 创建临时脚本文件（包含替换后的内容）
			tempScriptPath := fmt.Sprintf("/tmp/pre_hook_%s.sh", req.ServiceId)
			err = utils.WriteFile(tempScriptPath, scriptContent)
			if err != nil {
				fmt.Printf("创建临时前置脚本失败: %v\n", err)
				logger.Error("创建临时前置脚本失败: %v", err)
			} else {
				// 设置脚本可执行权限
				chmodCmd := exec.Command("chmod", "+x", tempScriptPath)
				if chmodErr := chmodCmd.Run(); chmodErr != nil {
					fmt.Printf("设置前置钩子脚本执行权限失败: %v\n", chmodErr)
					logger.Error("设置前置钩子脚本执行权限失败: %v", chmodErr)
				} else {
					// 执行脚本
					fmt.Printf("开始执行前置钩子脚本: %s\n", tempScriptPath)
					logger.Info("开始执行前置钩子脚本: %s", tempScriptPath)

					// 创建命令
					cmd := exec.Command("/bin/bash", tempScriptPath)

					// 捕获标准输出和标准错误
					var stdout, stderr bytes.Buffer
					cmd.Stdout = &stdout
					cmd.Stderr = &stderr

					// 执行命令
					execErr := cmd.Run()
					if execErr != nil {
						fmt.Printf("执行前置钩子脚本失败: %v\n", execErr)
						fmt.Printf("错误输出: %s\n", stderr.String())
						logger.Error("执行前置钩子脚本失败: %v, 错误输出: %s", execErr, stderr.String())
					} else {
						output := stdout.String()
						fmt.Printf("前置钩子脚本执行成功，输出: %s\n", output)
						logger.Info("前置钩子脚本执行成功")
					}
				}

				// 清理临时文件
				_ = os.Remove(tempScriptPath)
			}
		}
	}

	// 转换标签格式
	labelsMap := make(map[string]string)
	for _, label := range req.Labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) == 2 {
			labelsMap[parts[0]] = parts[1]
		} else if len(parts) == 1 {
			labelsMap[parts[0]] = ""
		}
	}

	// 打印标签信息，方便调试
	fmt.Println("标签信息:")
	for k, v := range labelsMap {
		fmt.Printf("  %s: %s\n", k, v)
	}

	// 注意：环境变量已经在 ProcessEnvironmentVariablesFromRequest 中处理完成
	// sysEnvVars 包含了所有环境变量（SYS_ENVS_TPL + CustomerEnvs）
	// 不需要再单独处理 req.CustomerEnvs

	fmt.Printf("=====%+v========\n", req)
	fmt.Printf("最终环境变量参数: %s\n", sysEnvVars)

	// 部署容器
	var containerInfo *models.DockerContainerInfo

	// 创建 SSH 客户端
	sshClient := w.getSSHClient(req.NodeIP, user)
	defer sshClient.Close() // 确保在函数返回时关闭SSH连接

	// 获取部署记录以检查是否有端口映射
	var command string
	record, recordErr := w.GetDeployRecordByID(req.ServiceId)
	if recordErr != nil {
		// 获取部署记录失败，记录错误但继续使用默认方式部署
		fmt.Printf("Failed to get deploy record: %v, will deploy without port mappings\n", recordErr)
		// 使用处理后的环境变量和卷映射
		command = buildDockerRunCommand(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars, sysVolumes, image)
	} else if len(record.PortsMapping) > 0 {
		// 使用端口映射构建 Docker 运行命令
		fmt.Printf("Using port mappings for deployment: %v\n", record.PortsMapping)
		// 使用处理后的环境变量和卷映射
		command = buildDockerRunCommandWithPorts(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars, sysVolumes, record.PortsMapping, image)
	} else {
		// 没有端口映射，使用默认方式构建 Docker 运行命令
		fmt.Printf("No port mappings found for deployment\n")
		// 使用处理后的环境变量和卷映射
		command = buildDockerRunCommand(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars, sysVolumes, image)
	}

	// 执行 Docker 命令，带重试机制
	fmt.Println("++++++ output docker command ++++++", command)
	output, err := w.executeSSHCommandWithRetry(sshClient, command, "部署容器", 2, 3*time.Second)
	if err != nil {
		return BuildErrorResponse("部署容器", NewServerError("部署容器", err))
	}

	// 解析容器 ID
	containerID := output

	// 获取容器状态
	statusCmd := fmt.Sprintf("docker inspect --format='{{.State.Status}}' %s", containerID)
	status, err := w.executeSSHCommand(sshClient, statusCmd, "获取容器状态")
	if err != nil {
		return BuildErrorResponse("获取容器状态", NewServerError("获取容器状态", err))
	}

	// 创建容器信息
	containerInfo = &models.DockerContainerInfo{
		ContainerID: containerID,
		Image:       image,
		Status:      strings.TrimSpace(status),
		Name:        containerName,
		ServerIP:    req.NodeIP,
		Labels:      labelsMap,
	}

	// 执行后置钩子脚本
	if sysPostHookScript != "" {
		scriptPath := fmt.Sprintf("dbdata/%s", sysPostHookScript)
		scriptContent, err := utils.ReadFile(scriptPath)
		if err != nil {
			fmt.Printf("读取后置钩子脚本失败: %v\n", err)
			logger.Error("读取后置钩子脚本失败: %v", err)
		} else {
			// 使用环境变量map进行挖槽替换
			if envMap != nil && len(envMap) > 0 {
				scriptContent = utils.ReplaceScriptPlaceholders(scriptContent, envMap)
				fmt.Printf("后置脚本已进行环境变量挖槽替换，替换了 %d 个变量\n", len(envMap))
				logger.Info("后置脚本已进行环境变量挖槽替换，替换了 %d 个变量", len(envMap))
			}

			// 创建临时脚本文件（包含替换后的内容）
			tempScriptPath := fmt.Sprintf("/tmp/post_hook_%s.sh", req.ServiceId)
			err = utils.WriteFile(tempScriptPath, scriptContent)
			if err != nil {
				fmt.Printf("创建临时后置脚本失败: %v\n", err)
				logger.Error("创建临时后置脚本失败: %v", err)
			} else {
				// 设置脚本可执行权限
				chmodCmd := exec.Command("chmod", "+x", tempScriptPath)
				if chmodErr := chmodCmd.Run(); chmodErr != nil {
					fmt.Printf("设置后置钩子脚本执行权限失败: %v\n", chmodErr)
					logger.Error("设置后置钩子脚本执行权限失败: %v", chmodErr)
				} else {
					// 执行脚本
					fmt.Printf("开始执行后置钩子脚本: %s\n", tempScriptPath)
					logger.Info("开始执行后置钩子脚本: %s", tempScriptPath)

					// 创建命令
					cmd := exec.Command("/bin/bash", tempScriptPath)

					// 捕获标准输出和标准错误
					var stdout, stderr bytes.Buffer
					cmd.Stdout = &stdout
					cmd.Stderr = &stderr

					// 执行命令
					execErr := cmd.Run()
					if execErr != nil {
						fmt.Printf("执行后置钩子脚本失败: %v\n", execErr)
						fmt.Printf("错误输出: %s\n", stderr.String())
						logger.Error("执行后置钩子脚本失败: %v, 错误输出: %s", execErr, stderr.String())
					} else {
						output := stdout.String()
						fmt.Printf("后置钩子脚本执行成功，输出: %s\n", output)
						logger.Info("后置钩子脚本执行成功")
					}
				}

				// 清理临时文件
				_ = os.Remove(tempScriptPath)
			}
		}
	}

	// 返回部署成功的响应
	data := gin.H{
		"deploymentId": containerInfo.ContainerID,
		"containerInfo": gin.H{
			"containerId": containerInfo.ContainerID,
			"image":       containerInfo.Image,
			"status":      containerInfo.Status,
			"name":        containerInfo.Name,
			"serverIP":    containerInfo.ServerIP,
		},
		"resources": gin.H{
			"cpu":    totalCPU,
			"memory": totalMemory,
		},
	}

	// 如果有端口映射，添加到响应中
	if len(record.PortsMapping) > 0 {
		data["portMappings"] = record.PortsMapping
	}
	detail := fmt.Sprintf("容器ID: %s", containerInfo.ContainerID)

	// 完成容器部署后，解决流量转发问题
	// 1. 流量入口固定在 host_ip 对应的节点
	// 2. 使用 dbdata/nginx-default.tpl 生成配置文件
	if req.HostIP != "" && len(record.PortsMapping) > 0 {
		// 获取域名
		domain := req.DomainPrefix + req.DomainSuffix

		// 获取第一个端口映射的主机端口
		var hostPort string
		if len(record.PortsMapping) > 0 {
			portMapping := record.PortsMapping[0]
			parts := strings.Split(portMapping, ":")
			if len(parts) >= 1 {
				hostPort = parts[0]
			}
		}

		if domain != "" && hostPort != "" {
			// 读取 Nginx 配置模板
			templateContent, err := utils.ReadFile("dbdata/nginx-default.tpl")
			if err != nil {
				fmt.Printf("Warning: Failed to read Nginx template: %v\n", err)
			} else {
				// 替换模板中的变量
				nginxConfig := strings.ReplaceAll(templateContent, "{{domain}}", domain)
				nginxConfig = strings.ReplaceAll(nginxConfig, "{{ip}}", req.NodeIP)
				nginxConfig = strings.ReplaceAll(nginxConfig, "{{port}}", hostPort)

				// 创建临时文件
				tempFilePath := fmt.Sprintf("/tmp/%s.conf", req.ServiceId)
				err = utils.WriteFile(tempFilePath, nginxConfig)
				if err != nil {
					fmt.Printf("Warning: Failed to create temporary Nginx config file: %v\n", err)
				} else {
					// 创建 SSH 客户端连接到 host_ip 服务器
					hostSSHClient := NewSSHClientWithConfig(req.HostIP, user, w.config)

					// 上传配置文件到 host_ip 服务器
					uploadCmd := fmt.Sprintf("cat > /etc/nginx/conf.d/%s.conf << 'EOL'\n%s\nEOL", req.ServiceId, nginxConfig)
					_, err = hostSSHClient.ExecuteCommand(uploadCmd)
					if err != nil {
						fmt.Printf("Warning: Failed to upload Nginx config to host server: %v\n", err)
					} else {
						// 测试 Nginx 配置
						_, err = hostSSHClient.ExecuteCommand("nginx -t")
						if err != nil {
							fmt.Printf("Warning: Nginx configuration test failed: %v\n", err)

							// 如果测试失败，删除配置文件
							_, _ = hostSSHClient.ExecuteCommand(fmt.Sprintf("rm -f /etc/nginx/conf.d/%s.conf", req.ServiceId))
						} else {
							// 重启 Nginx
							_, err = hostSSHClient.ExecuteCommand("nginx -s reload")
							if err != nil {
								fmt.Printf("Warning: Failed to reload Nginx: %v\n", err)
							} else {
								fmt.Printf("Successfully configured Nginx for service %s on host %s\n", req.ServiceId, req.HostIP)

								// 添加域名信息到响应
								data["domain"] = domain
								data["accessUrl"] = fmt.Sprintf("https://%s", domain)
							}
						}
					}

					// 清理临时文件
					_ = os.Remove(tempFilePath)
				}
			}
		}
	}

	return BuildSuccessResponse("服务部署", data, detail), nil
}

// buildDockerRunCommand 构建 Docker 运行命令
func buildDockerRunCommand(containerName string, cpuCores float64, memoryMB float64, labels map[string]string, envArgs string, volumeArgs string, image string) string {
	// 将域名中的点(.)转换为下划线(_)
	containerName = strings.ReplaceAll(containerName, ".", "_")

	// 构建标签参数
	labelArgs := ""
	for k, v := range labels {
		// 对值部分进行转义，确保特殊字符不会影响命令解析
		// 使用双引号包裹值部分，允许 Docker 正确解析值中的特殊字符
		// 如果值中包含双引号，需要进行转义以避免命令解析错误
		v = strings.ReplaceAll(v, "\"", "\\\"")
		labelArgs += fmt.Sprintf(" --label %s=\"%s\"", k, v)
	}

	// 打印标签参数，方便调试
	fmt.Printf("标签参数: %s\n", labelArgs)

	// 打印卷映射参数，方便调试
	if volumeArgs != "" {
		fmt.Printf("卷映射参数: %s\n", volumeArgs)
	}

	// 构建 Docker 运行命令，设置资源限制、标签、卷映射和环境变量
	return fmt.Sprintf(
		"docker run -d --name %s --cpus=%.2f --memory=%dm%s%s%s %s",
		containerName,
		cpuCores,
		int(memoryMB),
		labelArgs,
		envArgs,
		volumeArgs,
		image,
	)
}

// buildDockerRunCommandWithPorts 构建带有端口映射的 Docker 运行命令
func buildDockerRunCommandWithPorts(containerName string, cpuCores float64, memoryMB float64, labels map[string]string, envArgs string, volumeArgs string, portsMapping []string, image string) string {
	// 将域名中的点(.)转换为下划线(_)
	containerName = strings.ReplaceAll(containerName, ".", "_")

	// 构建标签参数
	labelArgs := ""
	for k, v := range labels {
		// 对值部分进行转义，确保特殊字符不会影响命令解析
		// 使用双引号包裹值部分，允许 Docker 正确解析值中的特殊字符
		// 如果值中包含双引号，需要进行转义以避免命令解析错误
		v = strings.ReplaceAll(v, "\"", "\\\"")
		labelArgs += fmt.Sprintf(" --label %s=\"%s\"", k, v)
	}

	// 打印标签参数，方便调试
	fmt.Printf("标签参数(带端口): %s\n", labelArgs)

	// 构建端口映射参数
	portArgs := ""
	for _, portMapping := range portsMapping {
		// 端口映射格式为 "hostPort:containerPort"，直接用于 Docker 的 -p 参数
		portArgs += fmt.Sprintf(" -p %s", portMapping)
	}

	// 打印卷映射参数，方便调试
	if volumeArgs != "" {
		fmt.Printf("卷映射参数(带端口): %s\n", volumeArgs)
	}

	// 构建 Docker 运行命令，设置资源限制、标签、端口映射、卷映射和环境变量
	return fmt.Sprintf(
		"docker run -d --name %s --cpus=%.2f --memory=%dm%s%s%s%s %s",
		containerName,
		cpuCores,
		int(memoryMB),
		labelArgs,
		portArgs,
		volumeArgs,
		envArgs,
		image,
	)
}

// StopContainer stops a running container on the worker node
func (w *Worker) StopContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if err := w.validateServiceParams(req.ServiceId, req.NodeIP, "停止容器"); err != nil {
		return BuildErrorResponse("停止服务", err)
	}

	// 获取SSH客户端连接到容器所在节点
	sshClient := w.getSSHClient(req.NodeIP, "")
	defer sshClient.Close() // 确保在函数返回时关闭SSH连接

	// 停止容器
	command := fmt.Sprintf("docker stop %s", req.ServiceId)
	_, err := w.executeSSHCommandWithRetry(sshClient, command, "停止容器", 1, 2*time.Second)
	if err != nil {
		return BuildErrorResponse("停止容器", NewServerError("停止容器", err))
	}

	// 获取部署记录以获取 host_ip
	record, err := w.GetDeployRecordByID(req.ServiceId)
	if err != nil {
		fmt.Printf("获取部署记录失败: %v, 将使用请求中的节点IP\n", err)
		// 如果获取部署记录失败，使用请求中的节点IP作为 host_ip
		record = models.DeployRecordW{
			HostIP: req.NodeIP,
		}
	}

	// 如果 host_ip 存在且不为空，则删除 nginx 配置文件并重启 nginx
	if record.HostIP != "" {
		// 创建连接到 host_ip 的 SSH 客户端
		hostSSHClient := w.getSSHClient(record.HostIP, "")
		defer hostSSHClient.Close()

		// 删除 nginx 配置文件
		nginxConfPath := fmt.Sprintf("/etc/nginx/conf.d/%s.conf", req.ServiceId)
		rmCommand := fmt.Sprintf("rm -f %s", nginxConfPath)

		fmt.Printf("正在删除 Nginx 配置文件: %s 在服务器 %s\n", nginxConfPath, record.HostIP)
		logger.Info("正在删除 Nginx 配置文件: %s 在服务器 %s", nginxConfPath, record.HostIP)

		_, rmErr := w.executeSSHCommand(hostSSHClient, rmCommand, "删除Nginx配置文件")
		if rmErr != nil {
			fmt.Printf("删除 Nginx 配置文件失败: %v\n", rmErr)
			logger.Error("删除 Nginx 配置文件失败: %v", rmErr)
			// 继续执行，不因为删除配置文件失败而中断流程
		} else {
			fmt.Printf("成功删除 Nginx 配置文件: %s\n", nginxConfPath)
			logger.Info("成功删除 Nginx 配置文件: %s", nginxConfPath)
		}

		// 验证 nginx 配置
		fmt.Println("正在验证 Nginx 配置")
		logger.Info("正在验证 Nginx 配置")

		_, testErr := w.executeSSHCommand(hostSSHClient, "nginx -t", "验证Nginx配置")
		if testErr != nil {
			fmt.Printf("Nginx 配置验证失败: %v\n", testErr)
			logger.Error("Nginx 配置验证失败: %v", testErr)
			// 如果验证失败，不执行重启
			fmt.Println("由于 Nginx 配置验证失败，跳过重启 Nginx")
			logger.Warn("由于 Nginx 配置验证失败，跳过重启 Nginx")
		} else {
			fmt.Println("Nginx 配置验证成功")
			logger.Info("Nginx 配置验证成功")

			// 只有在配置验证成功时才重启 nginx
			fmt.Println("正在重启 Nginx")
			logger.Info("正在重启 Nginx")

			_, restartErr := w.executeSSHCommand(hostSSHClient, "systemctl reload nginx || systemctl restart nginx", "重启Nginx")
			if restartErr != nil {
				fmt.Printf("重启 Nginx 失败: %v\n", restartErr)
				logger.Error("重启 Nginx 失败: %v", restartErr)
			} else {
				fmt.Println("成功重启 Nginx")
				logger.Info("成功重启 Nginx")
			}
		}
	} else {
		fmt.Println("没有找到 host_ip，跳过 Nginx 配置清理")
		logger.Warn("没有找到 host_ip，跳过 Nginx 配置清理")
	}

	// 返回停止成功的响应
	data := gin.H{
		"serviceId": req.ServiceId,
		"nodeIP":    req.NodeIP,
		"hostIP":    record.HostIP,
	}
	detail := fmt.Sprintf("，服务ID: %s", req.ServiceId)
	return BuildSuccessResponse("服务已停止", data, detail), nil
}

// RestartContainer restarts a container on the worker node
func (w *Worker) RestartContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if err := w.validateServiceParams(req.ServiceId, req.NodeIP, "重启容器"); err != nil {
		return BuildErrorResponse("重启服务", err)
	}

	// 获取SSH客户端
	sshClient := w.getSSHClient(req.NodeIP, "")
	defer sshClient.Close() // 确保在函数返回时关闭SSH连接

	// 重启容器
	command := fmt.Sprintf("docker restart %s", req.ServiceId)
	_, err := w.executeSSHCommandWithRetry(sshClient, command, "重启容器", 1, 2*time.Second)
	if err != nil {
		return BuildErrorResponse("重启容器", NewServerError("重启容器", err))
	}

	// 获取容器状态
	statusCmd := fmt.Sprintf("docker inspect --format='{{.State.Status}}' %s", req.ServiceId)
	status, err := w.executeSSHCommand(sshClient, statusCmd, "获取容器状态")
	if err != nil {
		// 即使获取状态失败，也认为重启成功，只是状态未知
		fmt.Printf("获取重启后容器状态失败: %v, 将使用默认状态 'unknown'\n", err)
		status = "unknown"
	}

	// 获取部署记录以获取 host_ip
	record, err := w.GetDeployRecordByID(req.ServiceId)
	if err != nil {
		fmt.Printf("获取部署记录失败: %v, 将跳过 Nginx 配置验证\n", err)
	} else if record.HostIP != "" {
		// 创建连接到 host_ip 的 SSH 客户端
		hostSSHClient := w.getSSHClient(record.HostIP, "")
		defer hostSSHClient.Close()

		// 验证 nginx 配置
		fmt.Printf("正在验证 Nginx 配置在服务器 %s\n", record.HostIP)
		logger.Info("正在验证 Nginx 配置在服务器 %s", record.HostIP)

		_, testErr := w.executeSSHCommand(hostSSHClient, "nginx -t", "验证Nginx配置")
		if testErr != nil {
			fmt.Printf("Nginx 配置验证失败: %v\n", testErr)
			logger.Error("Nginx 配置验证失败: %v", testErr)
			// 如果验证失败，不执行重启
			fmt.Println("由于 Nginx 配置验证失败，跳过重启 Nginx")
			logger.Warn("由于 Nginx 配置验证失败，跳过重启 Nginx")
		} else {
			fmt.Println("Nginx 配置验证成功")
			logger.Info("Nginx 配置验证成功")

			// 只有在配置验证成功时才重启 nginx
			fmt.Println("正在重启 Nginx")
			logger.Info("正在重启 Nginx")

			_, restartErr := w.executeSSHCommand(hostSSHClient, "systemctl reload nginx || systemctl restart nginx", "重启Nginx")
			if restartErr != nil {
				fmt.Printf("重启 Nginx 失败: %v\n", restartErr)
				logger.Error("重启 Nginx 失败: %v", restartErr)
			} else {
				fmt.Println("成功重启 Nginx")
				logger.Info("成功重启 Nginx")
			}
		}
	}

	// 返回重启成功的响应
	hostIP := ""
	if err == nil {
		hostIP = record.HostIP
	}

	data := gin.H{
		"serviceId": req.ServiceId,
		"nodeIP":    req.NodeIP,
		"hostIP":    hostIP,
		"status":    status,
	}
	detail := fmt.Sprintf("，服务ID: %s", req.ServiceId)
	return BuildSuccessResponse("服务已重启", data, detail), nil
}

// UpdateContainer updates a container by stopping the existing one and deploying a new one
// without modifying Nginx configuration
func (w *Worker) UpdateContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	log.Printf("Starting container update for service %s with new image %s", req.ServiceId, req.ImageURL)

	// 验证必要参数
	if err := w.validateServiceParams(req.ServiceId, req.NodeIP, "更新容器"); err != nil {
		log.Printf("Parameter validation failed for service %s: %v", req.ServiceId, err)
		return BuildErrorResponse("更新服务", err)
	}

	// 获取SSH客户端连接到容器所在节点
	sshClient := w.getSSHClient(req.NodeIP, "")
	defer sshClient.Close() // 确保在函数返回时关闭SSH连接

	// 第一步：停止并移除现有容器
	log.Printf("Stopping and removing container %s before update", req.ServiceId)

	// 使用 docker rm -f 命令，它会先停止容器再移除，更加高效
	removeCommand := fmt.Sprintf("docker rm -f %s", req.ServiceId)
	_, err := w.executeSSHCommandWithRetry(sshClient, removeCommand, "停止并移除容器", 2, 2*time.Second)
	if err != nil {
		log.Printf("Failed to stop and remove container %s: %v", req.ServiceId, err)
		// 如果容器不存在，这是正常的，继续部署新容器
		// 如果是其他错误，也继续尝试，因为可能是容器已经停止但移除失败
	}

	// 短暂等待确保容器完全清理
	time.Sleep(10 * time.Second)

	// 第二步：部署新容器（参考 DeployContainer 方法）
	log.Printf("Deploying new container for %s", req.ServiceId)

	// 确定使用镜像
	image := req.ImageURL

	// 计算资源需求
	// 处理 API 服务的资源需求
	apiReplica := req.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1 // 默认值为 1
	}
	apiCPU := req.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求

	// 处理 Auto 服务的资源需求
	autoReplica := req.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
	}
	autoCPU := req.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU // 总 CPU 需求
	if totalCPU <= 0 {
		totalCPU = 0.5 // 默认至少 0.5 核
	}

	// 计算内存需求
	apiMemory := req.ApiMemory * apiReplica        // API 服务总内存需求
	autoMemory := req.AutoMemory * autoReplica     // Auto 服务总内存需求
	totalMemory := float64(apiMemory + autoMemory) // 总内存需求
	if totalMemory <= 0 {
		totalMemory = 1024.0 // 默认至少 1024MB 内存
	}

	// 处理容器名称
	containerName := req.ServiceId

	// 处理特殊标签
	var sysEnvsFile string
	var sysPreHookScript string
	var sysPostHookScript string
	var sysEnvVars string
	var sysVolumes string

	// 读取 req 数据中的 labels
	for _, label := range req.Labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value := parts[1]

		// 检查是否是系统环境变量标签
		if key == "SYS_ENVS" && strings.HasSuffix(value, ".env") {
			sysEnvsFile = value
			log.Printf("检测到系统环境变量文件: %s", sysEnvsFile)
		}

		// 检查是否是前置钩子脚本
		if key == "SYS_PREHOOK" && strings.HasSuffix(value, ".sh") {
			sysPreHookScript = value
			log.Printf("检测到前置钩子脚本: %s", sysPreHookScript)
		}

		// 检查是否是后置钩子脚本
		if key == "SYS_POSTHOOK" && strings.HasSuffix(value, ".sh") {
			sysPostHookScript = value
			log.Printf("检测到后置钩子脚本: %s", sysPostHookScript)
		}

		// 检查是否是卷映射标签
		if key == "SYS_VOL" {
			// 处理卷映射
			volumeMappings := strings.Split(value, "|||")
			for _, mapping := range volumeMappings {
				mapping = strings.TrimSpace(mapping)
				if mapping == "" {
					continue
				}

				parts := strings.Split(mapping, ":")
				if len(parts) != 2 {
					log.Printf("无效的卷映射格式: %s，应为 '宿主机路径:容器内路径'", mapping)
					continue
				}

				hostPath := parts[0]
				containerPath := parts[1]

				// 为宿主机路径添加前缀
				hostPathWithPrefix := fmt.Sprintf("/user/data/%s/%s", req.ServiceId, hostPath)

				// 添加到卷映射参数
				sysVolumes += fmt.Sprintf(" -v %s:%s", hostPathWithPrefix, containerPath)

				log.Printf("添加卷映射: %s -> %s", hostPathWithPrefix, containerPath)
			}
		}
	}

	// 使用新的环境变量处理逻辑
	// 保持数据传递的一致性，所有部署信息都从req参数中获取
	processedEnvString, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		log.Printf("处理环境变量失败: %v", err)
		// 处理失败时，sysEnvVars 保持为空字符串，后续会使用 req.CustomerEnvs
	} else {
		sysEnvVars = processedEnvString
		log.Printf("处理后的环境变量: %s", sysEnvVars)
		log.Printf("环境变量map包含 %d 个变量，可用于脚本挖槽替换", len(envMap))
	}

	// 执行前置钩子脚本
	if sysPreHookScript != "" {
		scriptPath := fmt.Sprintf("dbdata/%s", sysPreHookScript)
		_, err := utils.ReadFile(scriptPath)
		if err != nil {
			log.Printf("读取前置钩子脚本失败: %v", err)
		} else {
			// 设置脚本可执行权限
			chmodCmd := exec.Command("chmod", "+x", scriptPath)
			if chmodErr := chmodCmd.Run(); chmodErr != nil {
				log.Printf("设置前置钩子脚本执行权限失败: %v", chmodErr)
			} else {
				// 执行脚本
				log.Printf("开始执行前置钩子脚本: %s", scriptPath)

				// 创建命令
				cmd := exec.Command("/bin/bash", scriptPath)

				// 捕获标准输出和标准错误
				var stdout, stderr bytes.Buffer
				cmd.Stdout = &stdout
				cmd.Stderr = &stderr

				// 执行命令
				execErr := cmd.Run()
				if execErr != nil {
					log.Printf("执行前置钩子脚本失败: %v", execErr)
					log.Printf("错误输出: %s", stderr.String())
				} else {
					output := stdout.String()
					log.Printf("前置钩子脚本执行成功，输出: %s", output)
				}
			}
		}
	}

	// 转换标签格式
	labelsMap := make(map[string]string)
	for _, label := range req.Labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) == 2 {
			labelsMap[parts[0]] = parts[1]
		} else if len(parts) == 1 {
			labelsMap[parts[0]] = ""
		}
	}

	// 添加环境变量
	envArgs := ""
	for _, env := range req.CustomerEnvs {
		// 分离键和值
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 {
			key := parts[0]
			value := parts[1]
			// 对值部分进行转义，确保特殊字符不会影响命令解析
			value = strings.ReplaceAll(value, "\"", "\\\"")
			envArgs += fmt.Sprintf(" --env %s=\"%s\"", key, value)
		} else {
			// 如果环境变量没有 = 号，则整个字符串作为键，值为空
			envArgs += fmt.Sprintf(" --env %s=\"\"", parts[0])
		}
	}

	log.Printf("环境变量参数: %s", envArgs)

	// 获取部署记录以检查是否有端口映射
	var command string

	if err != nil {
		// 获取部署记录失败，记录错误但继续使用默认方式部署
		log.Printf("Failed to get deploy record: %v, will deploy without port mappings", err)
		// 添加系统环境变量（优先级低于用户环境变量）和卷映射
		command = buildDockerRunCommand(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars+envArgs, sysVolumes, image)
	} else if len(req.PortsMapping) > 0 {
		// 使用端口映射构建 Docker 运行命令
		log.Printf("Using port mappings for deployment: %v", req.PortsMapping)
		// 添加系统环境变量（优先级低于用户环境变量）和卷映射
		command = buildDockerRunCommandWithPorts(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars+envArgs, sysVolumes, req.PortsMapping, image)
	} else {
		// 没有端口映射，使用默认方式构建 Docker 运行命令
		log.Printf("No port mappings found for deployment")
		// 添加系统环境变量（优先级低于用户环境变量）和卷映射
		command = buildDockerRunCommand(containerName, totalCPU, totalMemory, labelsMap, sysEnvVars+envArgs, sysVolumes, image)
	}

	// 执行 Docker 命令，带重试机制
	log.Printf("Executing docker command: %s", command)
	output, err := w.executeSSHCommandWithRetry(sshClient, command, "部署容器", 2, 3*time.Second)
	if err != nil {
		return BuildErrorResponse("更新容器", NewServerError("部署容器", err))
	}

	// 解析容器 ID
	containerID := output

	// 获取容器状态
	statusCmd := fmt.Sprintf("docker inspect --format='{{.State.Status}}' %s", containerID)
	status, err := w.executeSSHCommand(sshClient, statusCmd, "获取容器状态")
	if err != nil {
		return BuildErrorResponse("获取容器状态", NewServerError("获取容器状态", err))
	}

	// 创建容器信息
	containerInfo := &models.DockerContainerInfo{
		ContainerID: containerID,
		Image:       image,
		Status:      strings.TrimSpace(status),
		Name:        containerName,
		ServerIP:    req.NodeIP,
		Labels:      labelsMap,
	}

	// 执行后置钩子脚本
	if sysPostHookScript != "" {
		scriptPath := fmt.Sprintf("dbdata/%s", sysPostHookScript)
		_, err := utils.ReadFile(scriptPath)
		if err != nil {
			log.Printf("读取后置钩子脚本失败: %v", err)
		} else {
			// 设置脚本可执行权限
			chmodCmd := exec.Command("chmod", "+x", scriptPath)
			if chmodErr := chmodCmd.Run(); chmodErr != nil {
				log.Printf("设置后置钩子脚本执行权限失败: %v", chmodErr)
			} else {
				// 执行脚本
				log.Printf("开始执行后置钩子脚本: %s", scriptPath)

				// 创建命令
				cmd := exec.Command("/bin/bash", scriptPath)

				// 捕获标准输出和标准错误
				var stdout, stderr bytes.Buffer
				cmd.Stdout = &stdout
				cmd.Stderr = &stderr

				// 执行命令
				execErr := cmd.Run()
				if execErr != nil {
					log.Printf("执行后置钩子脚本失败: %v", execErr)
					log.Printf("错误输出: %s", stderr.String())
				} else {
					output := stdout.String()
					log.Printf("后置钩子脚本执行成功，输出: %s", output)
				}
			}
		}
	}

	// 返回更新成功的响应
	data := gin.H{
		"deploymentId": containerInfo.ContainerID,
		"containerInfo": gin.H{
			"containerId": containerInfo.ContainerID,
			"image":       containerInfo.Image,
			"status":      containerInfo.Status,
			"name":        containerInfo.Name,
			"serverIP":    containerInfo.ServerIP,
		},
		"resources": gin.H{
			"cpu":    totalCPU,
			"memory": totalMemory,
		},
	}

	// 如果有端口映射，添加到响应中
	if len(req.PortsMapping) > 0 {
		data["portMappings"] = req.PortsMapping
	}
	detail := fmt.Sprintf("容器ID: %s", containerInfo.ContainerID)

	return BuildSuccessResponse("服务已更新", data, detail), nil

}

// GetDeployStatus returns the deploy status of a service on a worker node
func (w *Worker) GetDeployStatus(ctx context.Context, serviceID, nodeIP string) (*models.ContainerStatus, error) {
	// 创建容器状态对象
	containerStatus := &models.ContainerStatus{
		ServiceID: serviceID,
		NodeIP:    nodeIP,
		Status:    "UNKNOWN",
	}

	// 验证必要参数
	if err := w.validateServiceParams(serviceID, nodeIP, "获取部署状态"); err != nil {
		containerStatus.Message = err.Error()
		return containerStatus, nil
	}

	// 获取SSH客户端
	sshClient := w.getSSHClient(nodeIP, "")
	defer sshClient.Close() // 确保在函数返回时关闭SSH连接

	// 将容器名中的点(.)转换为下划线(_)，与部署时保持一致
	containerName := strings.ReplaceAll(serviceID, ".", "_")

	// 构建 docker ps 命令，过滤出指定容器
	command := fmt.Sprintf("docker ps -a --filter \"name=%s\" --format \"{{.ID}}|{{.Status}}|{{.Image}}|{{.Names}}\"", containerName)

	// 执行命令，带重试
	output, err := w.executeSSHCommandWithRetry(sshClient, command, "获取容器状态", 1, 1*time.Second)
	if err != nil {
		containerStatus.Message = fmt.Sprintf("执行 docker ps 命令失败: %v", err)
		fmt.Printf("获取容器状态失败: %v, 服务ID: %s, 节点IP: %s\n", err, serviceID, nodeIP)
		return containerStatus, nil
	}

	// 处理输出结果
	if output == "" {
		// 容器不存在
		containerStatus.Status = "NOT_FOUND"
		containerStatus.Message = "容器不存在"
		return containerStatus, nil
	}

	// 解析输出结果
	// 格式: containerID|Status|Image|Names
	parts := strings.Split(output, "|")
	if len(parts) < 2 {
		containerStatus.Message = fmt.Sprintf("解析 docker ps 输出失败: 意外的输出格式: %s", output)
		return containerStatus, nil
	}

	containerID := parts[0]
	statusFull := parts[1]

	// 解析状态
	status := "UNKNOWN"
	if strings.HasPrefix(statusFull, "Up") {
		status = "RUNNING"
	} else if strings.HasPrefix(statusFull, "Exited") {
		status = "STOPPED"
	} else if strings.HasPrefix(statusFull, "Created") {
		status = "CREATED"
	} else if strings.HasPrefix(statusFull, "Restarting") {
		status = "RUNNING"
	} else if strings.HasPrefix(statusFull, "Removing") {
		status = "STOPPED"
	} else if strings.HasPrefix(statusFull, "Paused") {
		status = "STOPPED"
	} else if strings.HasPrefix(statusFull, "Dead") {
		status = "STOPPED"
	}

	// 更新容器状态
	containerStatus.Status = status
	containerStatus.ContainerID = containerID
	containerStatus.StatusDetail = statusFull

	// 如果容器状态是 RUNNING，一次性获取所有需要的信息
	if status == "RUNNING" {
		// 构建一个命令，一次性获取所有需要的信息
		// 1. 获取容器详细信息
		// 2. 获取容器资源使用情况
		combinedCmd := fmt.Sprintf(`
CONTAINER_ID="%s"
# 获取容器详细信息
INSPECT_OUTPUT=$(docker inspect --format '{{json .}}' $CONTAINER_ID)
echo "===INSPECT_OUTPUT_START==="
echo "$INSPECT_OUTPUT"
echo "===INSPECT_OUTPUT_END==="

# 获取容器资源使用情况
STATS_OUTPUT=$(docker stats $CONTAINER_ID --no-stream --format "{{.CPUPerc}}|{{.MemUsage}}|{{.NetIO}}|{{.BlockIO}}")
echo "===STATS_OUTPUT_START==="
echo "$STATS_OUTPUT"
echo "===STATS_OUTPUT_END==="
`, containerID)

		// 执行组合命令，不使用重试，因为这是一个复杂命令
		combinedOutput, err := w.executeSSHCommand(sshClient, combinedCmd, "获取容器详细信息")
		if err == nil {
			// 解析组合输出
			// 提取 INSPECT_OUTPUT
			inspectStart := strings.Index(combinedOutput, "===INSPECT_OUTPUT_START===")
			inspectEnd := strings.Index(combinedOutput, "===INSPECT_OUTPUT_END===")
			if inspectStart >= 0 && inspectEnd > inspectStart {
				inspectOutput := combinedOutput[inspectStart+len("===INSPECT_OUTPUT_START===") : inspectEnd]
				inspectOutput = strings.TrimSpace(inspectOutput)
				if inspectOutput != "" {
					containerStatus.InspectData = inspectOutput
				}
			}

			// 提取 STATS_OUTPUT
			statsStart := strings.Index(combinedOutput, "===STATS_OUTPUT_START===")
			statsEnd := strings.Index(combinedOutput, "===STATS_OUTPUT_END===")
			if statsStart >= 0 && statsEnd > statsStart {
				statsOutput := combinedOutput[statsStart+len("===STATS_OUTPUT_START===") : statsEnd]
				statsOutput = strings.TrimSpace(statsOutput)
				if statsOutput != "" {
					statsParts := strings.Split(statsOutput, "|")
					if len(statsParts) >= 2 {
						containerStatus.CPUUsage = statsParts[0]
						containerStatus.MemoryUsage = statsParts[1]
						if len(statsParts) >= 4 {
							containerStatus.NetworkIO = statsParts[2]
							containerStatus.DiskIO = statsParts[3]
						}
					}
				}
			}
		} else {
			// 如果组合命令失败，记录错误并回退到单独执行命令
			fmt.Printf("组合命令执行失败，将尝试单独执行命令: %v\n", err)

			// 获取更详细的容器信息
			inspectCmd := fmt.Sprintf("docker inspect --format '{{json .}}' %s", containerID)
			inspectOutput, inspectErr := w.executeSSHCommand(sshClient, inspectCmd, "获取容器详细信息")
			if inspectErr == nil {
				containerStatus.InspectData = inspectOutput
			} else {
				fmt.Printf("获取容器详细信息失败: %v\n", inspectErr)
			}

			// 获取容器资源使用情况
			statsCmd := fmt.Sprintf("docker stats %s --no-stream --format \"{{.CPUPerc}}|{{.MemUsage}}|{{.NetIO}}|{{.BlockIO}}\"", containerID)
			statsOutput, statsErr := w.executeSSHCommand(sshClient, statsCmd, "获取容器资源使用情况")
			if statsErr == nil {
				statsParts := strings.Split(statsOutput, "|")
				if len(statsParts) >= 2 {
					containerStatus.CPUUsage = statsParts[0]
					containerStatus.MemoryUsage = statsParts[1]
					if len(statsParts) >= 4 {
						containerStatus.NetworkIO = statsParts[2]
						containerStatus.DiskIO = statsParts[3]
					}
				}
			} else {
				fmt.Printf("获取容器资源使用情况失败: %v\n", statsErr)
			}
		}

		// 尝试获取部署记录中的端口映射信息
		record, recordErr := w.GetDeployRecordByID(serviceID)
		if recordErr == nil && len(record.PortsMapping) > 0 {
			containerStatus.PortMappings = record.PortsMapping
		} else if recordErr != nil {
			fmt.Printf("获取部署记录失败: %v, 服务ID: %s\n", recordErr, serviceID)
		}
	} else {
		// 对于非运行状态的容器，只获取详细信息
		inspectCmd := fmt.Sprintf("docker inspect --format '{{json .}}' %s", containerID)
		inspectOutput, inspectErr := w.executeSSHCommand(sshClient, inspectCmd, "获取非运行容器详细信息")
		if inspectErr == nil {
			containerStatus.InspectData = inspectOutput
		} else {
			fmt.Printf("获取非运行容器详细信息失败: %v, 容器ID: %s\n", inspectErr, containerID)
		}
	}

	fmt.Printf("成功获取容器状态: 服务ID=%s, 状态=%s\n", serviceID, containerStatus.Status)

	return containerStatus, nil
}

// HealthCheck returns the current status of the worker
func (w *Worker) HealthCheck(ctx context.Context) (*models.WorkerResponse, error) {
	// 获取当前 worker 的状态
	status := GetWorkerStatus()

	// 记录健康检查日志
	fmt.Printf("Worker健康检查: 当前状态=%s\n", status)

	data := gin.H{
		"status": string(status),
		"time":   time.Now().Format(time.RFC3339),
	}

	return BuildSuccessResponse("健康状态", data, ""), nil
}
