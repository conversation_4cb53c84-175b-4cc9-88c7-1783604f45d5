package worker

import (
	"fmt"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/models"
)

// SSHCommandResult 表示SSH命令执行的结果
type SSHCommandResult struct {
	Output  string
	Error   error
	Command string
}

// sanitizeCommand 对命令进行脱敏处理，移除敏感信息
func sanitizeCommand(command string) string {
	// 这里可以实现更复杂的脱敏逻辑，例如移除密码等敏感信息
	// 简单示例：将环境变量值替换为 [REDACTED]
	sanitized := command
	// 查找环境变量参数
	if strings.Contains(sanitized, "--env") {
		parts := strings.Split(sanitized, "--env")
		for i := 1; i < len(parts); i++ {
			// 找到等号后的值部分
			valuePart := strings.SplitN(parts[i], "=", 2)
			if len(valuePart) > 1 {
				// 替换值部分为 [REDACTED]
				parts[i] = valuePart[0] + "=[REDACTED]" + strings.SplitN(valuePart[1], " ", 2)[1]
			}
		}
		sanitized = strings.Join(parts, "--env")
	}
	return sanitized
}

// executeSSHCommand 执行SSH命令并统一处理错误
func (w *Worker) executeSSHCommand(sshClient *SSHClient, command, operation string) (string, error) {
	// 记录将要执行的命令（脱敏敏感信息）
	sanitizedCmd := sanitizeCommand(command)
	fmt.Printf("执行SSH命令: %s\n", sanitizedCmd)

	// 执行命令
	output, err := sshClient.ExecuteCommand(command)
	if err != nil {
		// 记录详细错误信息
		fmt.Printf("SSH命令执行失败: %s, 错误: %v\n", sanitizedCmd, err)
		return "", fmt.Errorf("%s失败: %w", operation, err)
	}

	return strings.TrimSpace(output), nil
}

// executeSSHCommandWithRetry 带重试机制的SSH命令执行
func (w *Worker) executeSSHCommandWithRetry(sshClient *SSHClient, command, operation string, maxRetries int, retryDelay time.Duration) (string, error) {
	var output string
	var err error

	sanitizedCmd := sanitizeCommand(command)

	for i := 0; i <= maxRetries; i++ {
		output, err = sshClient.ExecuteCommand(command)
		if err == nil {
			if i > 0 {
				fmt.Printf("SSH命令执行成功(尝试 %d/%d): %s\n", i+1, maxRetries+1, sanitizedCmd)
			}
			return strings.TrimSpace(output), nil
		}

		fmt.Printf("SSH命令执行失败(尝试 %d/%d): %s, 错误: %v\n", i+1, maxRetries+1, sanitizedCmd, err)

		if i < maxRetries {
			time.Sleep(retryDelay)
		}
	}

	return "", fmt.Errorf("%s失败(已重试%d次): %w", operation, maxRetries, err)
}

// getSSHClient 获取SSH客户端，统一处理用户名默认值
func (w *Worker) getSSHClient(nodeIP, user string) *SSHClient {
	if user == "" {
		user = "root" // 使用配置中的默认用户
	}
	return NewSSHClientWithConfig(nodeIP, user, w.config)
}

// validateDeployParams 验证部署参数
func (w *Worker) validateDeployParams(req *models.WorkerDeployRequest) error {
	if req.ImageURL == "" {
		return NewParamError("部署容器", "镜像URL不能为空")
	}
	if req.NodeIP == "" {
		return NewParamError("部署容器", "服务器IP不能为空")
	}
	return nil
}

// validateServiceParams 验证服务ID和节点IP参数
func (w *Worker) validateServiceParams(serviceID, nodeIP, operation string) error {
	if serviceID == "" {
		return NewParamError(operation, "服务ID不能为空")
	}
	if nodeIP == "" {
		return NewParamError(operation, "服务器IP不能为空")
	}
	return nil
}
