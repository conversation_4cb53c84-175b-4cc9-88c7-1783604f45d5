package worker

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/models"
)

// CreateContainerViaSSH 通过SSH在远程服务器上创建容器
func (w *Worker) CreateContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if req.ImageName == "" && req.ImageURL == "" {
		err := NewParamError("通过SSH部署容器", "镜像名称和镜像URL不能同时为空")
		return BuildErrorResponse("部署", err)
	}

	if req.NodeIP == "" {
		err := NewParamError("通过SSH部署容器", "服务器IP不能为空")
		return BuildErrorResponse("部署", err)
	}

	// 确定使用哪个镜像
	image := req.ImageURL
	if image == "" {
		image = req.ImageName
	}

	// 设置默认用户
	user := "root" // 使用配置中的默认用户

	// 计算资源需求
	// 处理 API 服务的资源需求
	apiReplica := req.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1 // 默认值为 1
	}
	apiCPU := req.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求

	// 处理 Auto 服务的资源需求
	autoReplica := req.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
	}
	autoCPU := req.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU // 总 CPU 需求
	if totalCPU <= 0 {
		totalCPU = 1.0 // 默认至少 1 核
	}

	// 计算内存需求
	apiMemory := req.ApiMemory * apiReplica        // API 服务总内存需求
	autoMemory := req.AutoMemory * autoReplica     // Auto 服务总内存需求
	totalMemory := float64(apiMemory + autoMemory) // 总内存需求
	if totalMemory <= 0 {
		totalMemory = 1024.0 // 默认至少 1024MB 内存
	}

	// 处理容器名称
	containerName := req.ServiceId

	// 转换标签格式
	labelsMap := make(map[string]string)
	for _, label := range req.Labels {
		parts := strings.SplitN(label, "=", 2)
		if len(parts) == 2 {
			labelsMap[parts[0]] = parts[1]
		} else if len(parts) == 1 {
			labelsMap[parts[0]] = ""
		}
	}

	// 创建SSH客户端
	sshClient := NewSSHClientWithConfig(req.NodeIP, user, w.config)

	// 部署容器
	containerInfo, err := sshClient.CreateContainer(image, totalCPU, totalMemory, labelsMap, containerName)
	if err != nil {
		return BuildErrorResponse("通过SSH部署容器", NewServerError("通过SSH部署容器", err))
	}

	// 返回部署成功的响应
	data := gin.H{
		"deploymentId": containerInfo.ContainerID,
		"containerInfo": gin.H{
			"containerId": containerInfo.ContainerID,
			"image":       containerInfo.Image,
			"status":      containerInfo.Status,
			"name":        containerInfo.Name,
			"serverIP":    containerInfo.ServerIP,
		},
		"resources": gin.H{
			"cpu":    totalCPU,
			"memory": totalMemory,
		},
	}
	detail := fmt.Sprintf("，容器ID: %s", containerInfo.ContainerID)
	return BuildSuccessResponse("通过SSH服务部署", data, detail), nil
}

// StopContainerViaSSH 通过SSH停止远程服务器上的容器
func (w *Worker) StopContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if req.ServiceId == "" {
		err := NewParamError("通过SSH停止容器", "服务ID不能为空")
		return BuildErrorResponse("停止服务", err)
	}

	if req.NodeIP == "" {
		err := NewParamError("通过SSH停止容器", "服务器IP不能为空")
		return BuildErrorResponse("停止服务", err)
	}

	// 设置默认用户
	user := "root" // 使用配置中的默认用户

	// 创建SSH客户端
	sshClient := NewSSHClientWithConfig(req.NodeIP, user, w.config)

	// 停止容器
	err := sshClient.StopContainer(req.ServiceId)
	if err != nil {
		return BuildErrorResponse("通过SSH停止容器", NewServerError("通过SSH停止容器", err))
	}

	// 返回停止成功的响应
	data := gin.H{
		"serviceId": req.ServiceId,
		"nodeIP":    req.NodeIP,
	}
	detail := fmt.Sprintf("，服务ID: %s", req.ServiceId)
	return BuildSuccessResponse("通过SSH服务已停止", data, detail), nil
}

// RestartContainerViaSSH 通过SSH重启远程服务器上的容器
func (w *Worker) RestartContainerViaSSH(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 验证必要参数
	if req.ServiceId == "" {
		err := NewParamError("通过SSH重启容器", "服务ID不能为空")
		return BuildErrorResponse("重启服务", err)
	}

	if req.NodeIP == "" {
		err := NewParamError("通过SSH重启容器", "服务器IP不能为空")
		return BuildErrorResponse("重启服务", err)
	}

	// 设置默认用户
	user := "root" // 使用配置中的默认用户

	// 创建SSH客户端
	sshClient := NewSSHClientWithConfig(req.NodeIP, user, w.config)

	// 重启容器
	err := sshClient.RestartContainer(req.ServiceId)
	if err != nil {
		return BuildErrorResponse("通过SSH重启容器", NewServerError("通过SSH重启容器", err))
	}

	// 获取容器状态
	status, err := sshClient.GetContainerStatus(req.ServiceId)
	if err != nil {
		// 即使获取状态失败，也认为重启成功，只是状态未知
		status = "unknown"
	}

	// 返回重启成功的响应
	data := gin.H{
		"serviceId": req.ServiceId,
		"nodeIP":    req.NodeIP,
		"status":    status,
	}
	detail := fmt.Sprintf("，服务ID: %s", req.ServiceId)
	return BuildSuccessResponse("通过SSH服务已重启", data, detail), nil
}
