package worker

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// StartTaskProcessor 启动任务处理器，使用独立的 goroutine 处理任务队列和更新状态
func (w *Worker) StartTaskProcessor(ctx context.Context) {
	// 使用互斥锁保护状态检查和修改
	w.taskProcessorMu.Lock()
	defer w.taskProcessorMu.Unlock()

	// 检查是否已经在运行
	if w.taskProcessorRunning {
		log.Println("Task processor is already running, ignoring start request")
		return
	}

	log.Println("Starting task processor...")

	// 创建可取消的上下文
	processorCtx, cancel := context.WithCancel(ctx)
	w.taskProcessorCancel = cancel
	w.taskProcessorRunning = true

	// 启动一个独立的协程来管理任务处理和状态更新
	go func() {
		// 创建一个等待组，用于等待所有 goroutine 完成
		var wg sync.WaitGroup

		// 启动任务处理 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 任务处理器每 1 分钟运行一次
			taskTicker := time.NewTicker(1 * time.Minute)
			defer taskTicker.Stop()

			// 立即处理一次任务队列
			w.processTaskQueue(processorCtx)

			// 然后每 1 分钟处理一次
			for {
				select {
				case <-taskTicker.C:
					w.processTaskQueue(processorCtx)
				case <-processorCtx.Done():
					log.Println("Task queue processor stopped")
					return
				}
			}
		}()

		// 启动状态更新 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 状态更新器每 2 分钟运行一次
			statusTicker := time.NewTicker(2 * time.Minute)
			defer statusTicker.Stop()

			// 立即更新一次状态
			w.updateDeployStatus(processorCtx)

			// 然后每 2 分钟更新一次
			for {
				select {
				case <-statusTicker.C:
					w.updateDeployStatus(processorCtx)
				case <-processorCtx.Done():
					log.Println("Status updater stopped")
					return
				}
			}
		}()

		// 启动应用活跃性检查 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 创建一个停止通道
			stopChan := make(chan struct{})

			// 启动一个独立的 goroutine 来处理停止信号
			go func() {
				<-processorCtx.Done()
				close(stopChan)
			}()

			// 立即执行一次应用活跃性检查
			w.checkApplicationActivity(processorCtx)

			// 启动定时检查
			w.startActivityChecker(processorCtx, stopChan)

			log.Println("Application activity checker stopped")
		}()

		// 等待上下文取消
		<-processorCtx.Done()
		log.Println("Task processor context canceled, waiting for goroutines to complete")

		// 等待所有 goroutine 完成
		wg.Wait()

		// 更新状态标志
		w.taskProcessorMu.Lock()
		w.taskProcessorRunning = false
		w.taskProcessorCancel = nil
		w.taskProcessorMu.Unlock()

		log.Println("Task processor stopped completely")
	}()
}

// StopTaskProcessor 停止任务处理器
func (w *Worker) StopTaskProcessor() {
	w.taskProcessorMu.Lock()
	defer w.taskProcessorMu.Unlock()

	if !w.taskProcessorRunning {
		log.Println("Task processor is not running, ignoring stop request")
		return
	}

	log.Println("Stopping task processor...")

	// 调用取消函数来停止处理器
	if w.taskProcessorCancel != nil {
		w.taskProcessorCancel()
		// 状态更新会在 goroutine 退出时完成
	}
}

// IsTaskProcessorRunning 返回任务处理器是否正在运行
func (w *Worker) IsTaskProcessorRunning() bool {
	w.taskProcessorMu.Lock()
	defer w.taskProcessorMu.Unlock()

	return w.taskProcessorRunning
}

// processTaskQueue 处理任务队列中的任务
func (w *Worker) processTaskQueue(ctx context.Context) {
	log.Println("Processing task queue...")

	// 如果数据库连接不可用，直接返回
	if w.deployRecordWRepo == nil {
		log.Println("Database connection not available, skipping task processing")
		return
	}

	// 使用全局锁保护数据库操作
	w.queryMutex.Lock()
	defer w.queryMutex.Unlock()

	// 获取队列中的下一个待处理的部署记录
	record, err := w.deployRecordWRepo.GetNextPendingRecord()
	if err != nil {
		log.Printf("No queueing deploy records found or error: %v", err)
		return
	}

	// 将 worker 状态设置为 FREEZE，防止在执行容器操作期间被分配新任务
	// 不需要保存之前的状态，因为 updateDeployStatus 方法会根据任务状态决定是否恢复
	log.Println("Setting worker status to FREEZE during container operations")
	SetWorkerStatus(StatusFreeze)

	log.Printf("Processing deploy record: %s (Task type: %s)", record.ServiceID, record.Remark)

	// 更新部署记录状态为处理中
	record.Status = "PROCESSING"
	if err = w.deployRecordWRepo.Update(record); err != nil {
		log.Printf("Failed to update deploy record status: %v", err)
		return
	}

	// 如果是部署任务且有端口需要映射，处理端口映射
	if record.Remark == "DEPLOY" && len(record.Ports) > 0 && record.NodeIP != "" {
		log.Printf("Processing port mapping for service %s with ports %v on node %s",
			record.ServiceID, record.Ports, record.NodeIP)

		// 处理端口映射
		portsMapping, err := w.mapPorts(record.NodeIP, record.Ports)
		if err != nil {
			log.Printf("Failed to map ports for service %s: %v", record.ServiceID, err)
			updateDeployRecordStatus(w, record.ServiceID, "FAILED")
			return
		}

		// 更新端口映射到数据库
		log.Printf("Updating port mappings in database for service %s: %v",
			record.ServiceID, portsMapping)
		if err = w.UpdateDeployRecordPortsMapping(record.ServiceID, portsMapping); err != nil {
			log.Printf("Failed to update ports mapping in database for service %s: %v",
				record.ServiceID, err)
			// 不中断流程，继续执行部署
		}

		// 更新记录的端口映射
		record.PortsMapping = portsMapping
		log.Printf("Port mapping completed for service %s: %v", record.ServiceID, portsMapping)
	}

	// 如果是升级操作且有端口需要映射，处理端口映射逻辑
	if record.Remark == "UPDATE" && len(record.Ports) > 0 && record.NodeIP != "" {
		log.Printf("Processing port mapping for UPDATE operation on service %s with ports %v on node %s",
			record.ServiceID, record.Ports, record.NodeIP)

		// 因为是升级，说明之前有过部署，记录中可能有 ports_mapping 的记录
		log.Printf("Existing ports mapping for service %s: %v", record.ServiceID, record.PortsMapping)

		// 创建一个 map 来存储现有的端口映射 (containerPort -> hostPort)
		existingMappings := make(map[string]string)
		for _, mapping := range record.PortsMapping {
			// 解析映射格式 "hostPort:containerPort"
			parts := strings.Split(mapping, ":")
			if len(parts) == 2 {
				hostPort := parts[0]
				containerPort := parts[1]
				existingMappings[containerPort] = hostPort
				log.Printf("Found existing mapping: container port %s -> host port %s", containerPort, hostPort)
			}
		}

		// 检查当前 ports 字段中的端口，是否在 ports_mapping 记录中存在
		var finalPortsMapping []string
		var portsNeedMapping []string

		for _, port := range record.Ports {
			if hostPort, exists := existingMappings[port]; exists {
				// 端口已有映射，保留现有映射
				mapping := fmt.Sprintf("%s:%s", hostPort, port)
				finalPortsMapping = append(finalPortsMapping, mapping)
				log.Printf("Preserving existing mapping for port %s: %s", port, mapping)
			} else {
				// 端口没有映射，需要新分配
				portsNeedMapping = append(portsNeedMapping, port)
				log.Printf("Port %s needs new mapping", port)
			}
		}

		// 如果有端口需要新的映射，通过 mapPorts 获取
		if len(portsNeedMapping) > 0 {
			log.Printf("Need to allocate mappings for %d ports: %v", len(portsNeedMapping), portsNeedMapping)

			newMappings, err := w.mapPorts(record.NodeIP, portsNeedMapping)
			if err != nil {
				log.Printf("Failed to map new ports for service %s: %v", record.ServiceID, err)
				updateDeployRecordStatus(w, record.ServiceID, "FAILED")
				return
			}

			// 将新的映射添加到最终映射列表
			finalPortsMapping = append(finalPortsMapping, newMappings...)
			log.Printf("Allocated new mappings: %v", newMappings)
		}

		// 把重新整理后的 ports_mapping 存到 record.PortsMapping 中
		record.PortsMapping = finalPortsMapping
		log.Printf("Final ports mapping for service %s: %v", record.ServiceID, finalPortsMapping)

	}

	// 创建部署请求
	req := &models.WorkerDeployRequest{
		ServiceId:       record.ServiceID,
		ImageName:       record.ImageName,
		ImageURL:        record.ImageURL,
		DomainPrefix:    record.DomainPrefix,
		DomainSuffix:    record.DomainSuffix,
		NodeIP:          record.NodeIP,
		HostIP:          record.HostIP,
		Expiration:      record.Expiration,
		DurationSeconds: record.DurationSeconds, // 设置持续时间（秒）
		ApiCpu:          record.ApiCpu,
		ApiMemory:       record.ApiMemory,
		ApiReplica:      record.ApiReplica,
		AutoCpu:         record.AutoCpu,
		AutoMemory:      record.AutoMemory,
		AutoReplica:     record.AutoReplica,
		Ports:           record.Ports,
		Labels:          record.Labels,       // 添加标签
		CustomerEnvs:    record.CustomerEnvs, // 添加用户自定义环境变量
	}

	// 打印请求信息，方便调试
	log.Printf("Created deploy request with Envs && Labels: %v, CustomerEnvs: %v", record.Labels, record.CustomerEnvs)

	taskType := record.Remark // 任务类型存储在 Remark 字段中
	// 根据任务类型执行不同的操作
	switch taskType {
	case "DEPLOY":
		// 执行部署操作
		_, err = w.DeployContainer(ctx, req)
		if err != nil {
			log.Printf("Failed to deploy container: %v", err)
			updateDeployRecordStatus(w, record.ServiceID, "FAILED")
			return
		}

	case "STOP":
		// 执行停止操作
		_, err = w.StopContainer(ctx, req)
		if err != nil {
			log.Printf("Failed to stop container: %v", err)
			updateDeployRecordStatus(w, record.ServiceID, "FAILED")
			return
		}

	case "UPDATE":
		// 执行更新操作（停止旧容器并部署新容器）
		_, err = w.UpdateContainer(ctx, req)
		if err != nil {
			log.Printf("Failed to update container: %v", err)
			updateDeployRecordStatus(w, record.ServiceID, "FAILED")
			return
		}

	case "RESTART":
		// 执行重启操作
		_, err = w.RestartContainer(ctx, req)
		if err != nil {
			log.Printf("Failed to restart container: %v", err)
			updateDeployRecordStatus(w, record.ServiceID, "FAILED")
			return
		}

	default:
		log.Printf("Unknown task type: %s", taskType)
		updateDeployRecordStatus(w, record.ServiceID, "FAILED")
		return
	}

	// 更新部署记录状态为完成
	if err = w.deployRecordWRepo.UpdateStatus(record.ServiceID, "PROCESSING"); err != nil {
		log.Printf("Failed to update deploy record status: %v", err)
	}

	log.Printf("Successfully processed deploy record: %s", record.ServiceID)

	// 注意：不在这里恢复 worker 状态，而是由 updateDeployStatus 方法根据任务状态决定是否恢复
}

// 辅助函数：更新部署记录状态
func updateDeployRecordStatus(w *Worker, serviceID, status string) {
	if err := w.deployRecordWRepo.UpdateStatus(serviceID, status); err != nil {
		log.Printf("Failed to update deploy record status: %v", err)
	}
}

// mapPorts 为容器端口映射到主机端口
// 1. 检查远程服务器上 30000-30500 范围内的可用端口
// 2. 将容器端口映射到可用的主机端口
// 3. 返回端口映射列表，格式为 ["hostPort:containerPort", ...]
func (w *Worker) mapPorts(nodeIP string, containerPorts []string) ([]string, error) {
	log.Printf("Starting port mapping process for node %s with container ports: %v", nodeIP, containerPorts)

	// 如果没有端口需要映射，直接返回空列表
	if len(containerPorts) == 0 {
		log.Printf("No ports to map, returning empty list")
		return []string{}, nil
	}

	// 设置默认用户
	user := "root" // 使用配置中的默认用户

	// 创建 SSH 客户端
	sshClient := NewSSHClientWithConfig(nodeIP, user, w.config)
	log.Printf("Created SSH client for node %s", nodeIP)

	// 检查端口范围 30000-30500 内的可用端口
	log.Printf("Checking for available ports in range 30000-30500 on node %s", nodeIP)
	availablePorts, err := w.getAvailablePorts(sshClient, 30000, 30500, len(containerPorts))
	if err != nil {
		log.Printf("Failed to get available ports on node %s: %v", nodeIP, err)
		return nil, fmt.Errorf("failed to get available ports: %w", err)
	}
	log.Printf("Found %d available ports on node %s: %v", len(availablePorts), nodeIP, availablePorts)

	// 如果可用端口数量不足，返回错误
	if len(availablePorts) < len(containerPorts) {
		log.Printf("Not enough available ports on node %s: need %d, got %d",
			nodeIP, len(containerPorts), len(availablePorts))
		return nil, fmt.Errorf("not enough available ports: need %d, got %d", len(containerPorts), len(availablePorts))
	}

	// 创建端口映射 (格式: "hostPort:containerPort")
	var portsMapping []string
	for i, containerPort := range containerPorts {
		hostPort := availablePorts[i]
		mapping := fmt.Sprintf("%d:%s", hostPort, containerPort)
		portsMapping = append(portsMapping, mapping)
		log.Printf("Mapped container port %s to host port %d", containerPort, hostPort)
	}

	log.Printf("Created port mappings for node %s: %v", nodeIP, portsMapping)
	return portsMapping, nil
}

// getAvailablePorts 获取指定范围内的可用端口
func (w *Worker) getAvailablePorts(sshClient *SSHClient, startPort, endPort, count int) ([]int, error) {
	// 使用 SSHClient 的 GetAvailablePorts 方法获取可用端口
	return sshClient.GetAvailablePorts(startPort, endPort, count)
}

// updateDeployStatus 更新部署记录的状态
// 1. 遍历 deploy_record_w 表的部署记录，获取 status 为 PROCESSING 或 RUNNING 的数据
// 2. 调用 Worker.go 的 GetDeployStatus 方法，获取容器的运行状态
// 3. 更新到deploy_record_w表
func (w *Worker) updateDeployStatus(ctx context.Context) {
	log.Println("Updating deploy status...")

	// 如果数据库连接不可用，直接返回
	if w.deployRecordWRepo == nil {
		log.Println("Database connection not available, skipping deploy status update")
		return
	}

	// 使用全局锁保护数据库操作
	w.queryMutex.Lock()
	defer w.queryMutex.Unlock()

	// 获取所有状态为 PROCESSING 或 RUNNING 的部署记录
	records, err := w.deployRecordWRepo.GetRecordsByStatus([]string{"PROCESSING", "RUNNING"})
	if err != nil {
		log.Printf("Failed to get deploy records: %v", err)
		return
	}

	// 如果 records 中不包含 status=="PROCESSING" 的数据，则恢复worker为AVAILABLE状态
	hasProcessingTasks := false
	for _, record := range records {
		if record.Status == "PROCESSING" {
			hasProcessingTasks = true
			break
		}
	}

	// 如果没有正在处理中的任务，将 worker 状态设置为 AVAILABLE
	if !hasProcessingTasks && GetWorkerStatus() == StatusFreeze {
		log.Println("No PROCESSING tasks found, setting worker status to AVAILABLE")
		SetWorkerStatus(StatusAvailable)
	}

	if len(records) == 0 {
		log.Println("No PROCESSING or RUNNING deploy records found")
		return
	}

	log.Printf("Found %d PROCESSING or RUNNING deploy records", len(records))

	// 遍历每个部署记录
	for _, record := range records {
		// 如果节点IP为空，跳过
		if record.NodeIP == "" {
			log.Printf("Deploy record %s has no node IP, skipping", record.ServiceID)
			continue
		}

		// 调用 GetDeployStatus 方法获取容器状态
		containerStatus, err := w.GetDeployStatus(ctx, record.ServiceID, record.NodeIP)
		if err != nil {
			log.Printf("Failed to get deploy status for %s: %v", record.ServiceID, err)
			continue
		}

		// 根据容器状态更新部署记录状态
		var newStatus string
		switch containerStatus.Status {
		case "RUNNING":
			newStatus = "RUNNING"
		case "CREATED":
			newStatus = "PROCESSING"
		case "STOPPED", "REMOVING", "PAUSED", "DEAD":
			newStatus = "STOPPED"
		case "RESTARTING":
			newStatus = "RUNNING"
		case "NOT_FOUND":
			// 如果容器不存在，但记录状态是 RUNNING，则更新为 STOPPED
			if record.Status == "RUNNING" {
				newStatus = "STOPPED"
			} else {
				// 否则保持原状态
				newStatus = record.Status
			}
		default:
			// 未知状态，保持原状态
			newStatus = record.Status
		}

		// 如果是 RUNNING，需要做过期判断
		if newStatus == "RUNNING" {
			// 解析 created_at 时间
			createdAt, err := time.Parse(time.RFC3339, record.CreatedAt)
			if err != nil {
				log.Printf("Failed to parse created_at time for service %s: %v", record.ServiceID, err)
			} else {
				// 计算过期时间 = 创建时间 + 持续时间
				expirationTime := createdAt.Add(time.Duration(record.DurationSeconds) * time.Second)

				// 获取当前时间
				now := time.Now()

				// 如果当前时间大于或等于过期时间，则服务已过期
				if now.After(expirationTime) || now.Equal(expirationTime) {
					log.Printf("Service %s has expired (created: %s, duration: %d seconds, expired: %s)",
						record.ServiceID, createdAt.Format(time.RFC3339), record.DurationSeconds, expirationTime.Format(time.RFC3339))

					// 更新状态为 QUEUEING，并设置 Remark 为 STOP
					if err := w.deployRecordWRepo.UpdateStatus(record.ServiceID, "QUEUEING"); err != nil {
						log.Printf("Failed to update expired service status to QUEUEING: %v", err)
					}

					if err := w.deployRecordWRepo.UpdateRemark(record.ServiceID, "STOP"); err != nil {
						log.Printf("Failed to update expired service remark to STOP: %v", err)
					}

					// 已经更新了状态，不需要再执行下面的状态更新
					continue
				} else {
					// 记录剩余时间
					remainingTime := expirationTime.Sub(now)
					log.Printf("Service %s will expire in %s (at %s)",
						record.ServiceID, remainingTime.String(), expirationTime.Format(time.RFC3339))
				}
			}
		}

		// 如果状态有变化，更新数据库
		if newStatus != record.Status {
			log.Printf("Updating deploy record %s status from %s to %s", record.ServiceID, record.Status, newStatus)
			if err := w.deployRecordWRepo.UpdateStatus(record.ServiceID, newStatus); err != nil {
				log.Printf("Failed to update deploy record status: %v", err)
			}
		}

		// 如果 newStatus == "RUNNING", 则更新 remark 字段为 “”
		if newStatus == "RUNNING" {
			if err := w.deployRecordWRepo.UpdateRemark(record.ServiceID, ""); err != nil {
				log.Printf("Failed to update deploy record remark: %v", err)
			}
		}
	}

	log.Println("Deploy status update completed")
}

// checkApplicationActivity 检查应用活跃性
// 1. 从 deploy_record_w 表中获取 status 为 RUNNING 的记录
// 2. 获取 labels，检查是否包含 "SYS_ENTRANCE_LOG=xxx" 的值
// 3. 检查日志文件最后一行的时间，如果超过 12 小时无访问，则标记为停止
func (w *Worker) checkApplicationActivity(_ context.Context) {
	log.Println("Checking application activity...")

	// 如果数据库连接不可用，直接返回
	if w.deployRecordWRepo == nil {
		log.Println("Database connection not available, skipping application activity check")
		return
	}

	// 使用全局锁保护数据库操作
	w.queryMutex.Lock()
	defer w.queryMutex.Unlock()

	// 1. 从 deploy_record_w 表中获取 status 为 RUNNING 的记录
	records, err := w.deployRecordWRepo.GetRecordsByStatus([]string{"RUNNING"})
	if err != nil {
		log.Printf("Failed to get running records: %v", err)
		return
	}

	log.Printf("Found %d running services to check for activity", len(records))

	// 2. 遍历记录
	for _, record := range records {
		// 检查是否有 SYS_ENTRANCE_LOG 标签
		logPath := w.getEntranceLogPath(record)
		if logPath == "" {
			continue // 没有指定入口日志，跳过
		}

		// 3. 检查日志文件最后一行的时间
		isInactive, err := w.checkLogFileActivity(record.ServiceID, record.NodeIP, logPath)
		if err != nil {
			log.Printf("Failed to check log file activity for service %s: %v", record.ServiceID, err)
			continue
		}

		// 4. 如果不活跃，更新状态为 QUEUEING，备注为 STOP
		if isInactive {
			log.Printf("Service %s is inactive, marking for stop", record.ServiceID)
			if err := w.deployRecordWRepo.UpdateStatus(record.ServiceID, "QUEUEING"); err != nil {
				log.Printf("Failed to update status for service %s: %v", record.ServiceID, err)
			}

			if err := w.deployRecordWRepo.UpdateRemark(record.ServiceID, "STOP"); err != nil {
				log.Printf("Failed to update remark for service %s: %v", record.ServiceID, err)
			}
		}
	}

	log.Println("Application activity check completed")
}

// getEntranceLogPath 从标签中获取入口日志路径
func (w *Worker) getEntranceLogPath(record models.DeployRecordW) string {
	for _, label := range record.Labels {
		if strings.HasPrefix(label, "SYS_ENTRANCE_LOG=") {
			parts := strings.SplitN(label, "=", 2)
			if len(parts) == 2 && parts[1] != "" {
				// 返回日志文件路径
				return fmt.Sprintf("/user/data/%s/%s", record.ServiceID, parts[1])
			}
		}
	}
	return ""
}

// checkLogFileActivity 检查日志文件的活跃性并同步访问时间到数据库
// 返回 true 表示不活跃（超过12小时无访问）
func (w *Worker) checkLogFileActivity(serviceID, nodeIP, logPath string) (bool, error) {
	// 创建 SSH 客户端
	sshClient := NewSSHClientWithConfig(nodeIP, "root", w.config)

	// 1. 检查文件是否存在且是文件
	checkFileCmd := fmt.Sprintf("test -f %s && echo 'FILE' || echo 'NOT_FILE'", logPath)
	output, err := sshClient.ExecuteCommand(checkFileCmd)
	if err != nil {
		return false, fmt.Errorf("failed to check if log file exists: %w", err)
	}

	if strings.TrimSpace(output) != "FILE" {
		log.Printf("Log file %s is not a regular file or does not exist", logPath)
		return false, nil
	}

	// 2. 获取文件最后一行
	tailCmd := fmt.Sprintf("tail -n 1 %s", logPath)
	lastLine, err := sshClient.ExecuteCommand(tailCmd)
	if err != nil {
		return false, fmt.Errorf("failed to get last line of log file: %w", err)
	}

	if lastLine == "" {
		log.Printf("Log file %s is empty", logPath)
		return false, nil
	}

	// 3. 解析时间
	lastAccessTime, err := w.parseAccessLogTime(lastLine)
	if err != nil {
		log.Printf("Failed to parse time from log line: %v", err)
		return false, nil
	}

	// 4. 同步访问时间到数据库
	// 将解析到的最后访问时间更新到 deploy_record_w 表的 visited_at 字段
	if err := w.updateVisitedAtFromLogTime(serviceID, lastAccessTime); err != nil {
		log.Printf("Failed to update visited_at for service %s: %v", serviceID, err)
		// 不中断流程，继续执行活跃性检查
	} else {
		log.Printf("Successfully updated visited_at for service %s to %s",
			serviceID, utils.FormatCSTTime(lastAccessTime))
	}

	// 5. 检查是否超过12小时 TODO
	inactiveThreshold := 720 * time.Minute
	now := time.Now()

	// 确保 now 也是东八区的时间
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err == nil {
		now = now.In(loc)
	} else {
		// 如果加载时区失败，使用固定的 UTC+8 偏移
		now = now.In(time.FixedZone("CST", 8*60*60))
	}

	if now.Sub(lastAccessTime) > inactiveThreshold {
		log.Printf("Service %s has been inactive for more than 12 hours (last access: %s, current: %s)",
			serviceID, utils.FormatCSTTime(lastAccessTime), utils.FormatCSTTime(now))
		return true, nil
	}

	log.Printf("Service %s is active (last access: %s, current: %s)",
		serviceID, utils.FormatCSTTime(lastAccessTime), utils.FormatCSTTime(now))
	return false, nil
}

// parseAccessLogTime 解析访问日志中的时间并转换为东八区
// 格式如 [20/May/2025:06:56:46 +0000]
func (w *Worker) parseAccessLogTime(logLine string) (time.Time, error) {
	// 使用正则表达式匹配时间格式
	re := regexp.MustCompile(`\[(\d{2}/\w{3}/\d{4}:\d{2}:\d{2}:\d{2} [+-]\d{4})\]`)
	matches := re.FindStringSubmatch(logLine)

	if len(matches) < 2 {
		return time.Time{}, fmt.Errorf("no timestamp found in log line")
	}

	// 解析时间，保留原始时区
	timestamp := matches[1]
	t, err := time.Parse("02/Jan/2006:15:04:05 -0700", timestamp)
	if err != nil {
		return time.Time{}, err
	}

	// 转换为东八区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 如果加载时区失败，使用固定的 UTC+8 偏移
		return t.In(time.FixedZone("CST", 8*60*60)), nil
	}

	return t.In(loc), nil
}

// updateVisitedAtFromLogTime 将从日志解析的访问时间更新到数据库
func (w *Worker) updateVisitedAtFromLogTime(serviceID string, lastAccessTime time.Time) error {
	// 如果数据库连接不可用，直接返回错误
	if w.deployRecordWRepo == nil {
		return fmt.Errorf("database connection not available")
	}

	// 使用系统标准的东八区时间格式化方法，确保时区一致性
	// utils.FormatCSTTime 会将时间转换为东八区并格式化为 RFC3339 格式
	visitedAtStr := utils.FormatCSTTime(lastAccessTime)

	// 调用 repository 的方法更新 visited_at 字段
	// 这里我们需要创建一个新的方法来只更新 visited_at 字段，而不影响 updated_at
	if err := w.deployRecordWRepo.UpdateVisitedAtWithTime(serviceID, visitedAtStr); err != nil {
		return fmt.Errorf("failed to update visited_at in database: %w", err)
	}

	return nil
}

// startActivityChecker 启动应用活跃性定时检查
// 使用独立的 goroutine 实现定时检查，同时能够快速响应停止信号
func (w *Worker) startActivityChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting activity checker with 60-minute interval")

	// 创建一个 60 分钟的定时器 TODO
	ticker := time.NewTicker(60 * time.Minute)
	defer ticker.Stop()

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			// 执行应用活跃性检查
			log.Println("Ticker triggered, performing application activity check")
			w.checkApplicationActivity(ctx)
		case <-stopChan:
			// 收到停止信号，立即退出
			log.Println("Received stop signal, stopping activity checker")
			return
		}
	}
}
