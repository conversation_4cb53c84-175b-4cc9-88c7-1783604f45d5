package worker

import (
	"sync"

	"github.com/zero-ops/service-system/internal/database"
)

// WorkerStatus 表示 worker 的状态
type WorkerStatus string

const (
	// StatusAvailable 表示 worker 可用
	StatusAvailable WorkerStatus = "AVAILABLE"
	// StatusFreeze 表示 worker 冻结
	StatusFreeze WorkerStatus = "FREEZE"
)

// 全局状态变量，默认为冻结状态
var (
	workerStatus     WorkerStatus = StatusFreeze
	workerStatusLock sync.RWMutex
)

// Config 保存 worker 的配置
type Config struct {
	// worker 特定的配置项
	LogDir string // 日志目录

	// 数据库配置
	DB database.WorkerDBConfig

	// SSH配置
	SSHDefaultUser    string // SSH默认用户名
	SSHDefaultPort    int    // SSH默认端口
	SSHTimeout        int    // SSH连接超时时间(秒)
	SSHDefaultKeyPath string // SSH默认密钥路径
}

// DefaultConfig 返回默认的 worker 配置
func DefaultConfig() Config {
	return Config{
		LogDir:            "log",
		DB:                database.DefaultWorkerDBConfig(),
		SSHDefaultUser:    "root",
		SSHDefaultPort:    22,
		SSHTimeout:        10,
		SSHDefaultKeyPath: "dbdata/rsa.pem",
	}
}

// GetWorkerStatus 获取当前 worker 的状态
func GetWorkerStatus() WorkerStatus {
	workerStatusLock.RLock()
	defer workerStatusLock.RUnlock()
	return workerStatus
}

// SetWorkerStatus 设置当前 worker 的状态
func SetWorkerStatus(status WorkerStatus) {
	workerStatusLock.Lock()
	defer workerStatusLock.Unlock()
	workerStatus = status
}
