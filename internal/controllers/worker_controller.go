package controllers

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/worker"
)

// WorkerController 处理 worker 相关的 API 请求
type WorkerController struct {
	manager *worker.WorkerManager
	worker  *worker.Worker
}

// NewWorkerController 创建一个新的 WorkerController
func NewWorkerController(config worker.Config) *WorkerController {
	w := worker.NewWorkerWithConfig(config)
	return &WorkerController{
		manager: worker.NewWorkerManagerWithConfig(config),
		worker:  w,
	}
}

// DeployContainer 部署容器
func (c *WorkerController) DeployContainer(ctx *gin.Context) {
	var req models.WorkerDeployRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.manager.DeployContainer(context.Background(), &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// DeployRecord 创建部署任务记录
func (c *WorkerController) DeployRecord(ctx *gin.Context) {
	var req models.WorkerDeployRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// 将部署请求加入队列
	record, err := c.worker.QueueDeployTask(&req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 返回成功响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "部署任务已加入队列，任务将在后台处理，请稍后查询状态",
		Data: gin.H{
			"task_id": record.ServiceID,
			"status":  record.Status,
		},
	}

	ctx.JSON(200, resp)
}

// StopContainer 停止容器
func (c *WorkerController) StopContainer(ctx *gin.Context) {
	service_id := ctx.Param("service_id")

	// 创建请求对象
	req := &models.WorkerDeployRequest{
		ServiceId: service_id,
		NodeIP:    ctx.Query("nodeIP"),
	}

	resp, err := c.manager.StopContainer(context.Background(), req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// StopRecord 创建停止任务记录
func (c *WorkerController) StopRecord(ctx *gin.Context) {
	service_id := ctx.Param("service_id")

	// 创建请求对象
	req := &models.WorkerDeployRequest{
		ServiceId: service_id,
		NodeIP:    ctx.Query("nodeIP"),
	}

	// 将停止请求加入队列
	record, err := c.worker.QueueStopTask(req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 返回成功响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "停止任务已加入队列，任务将在后台处理，请稍后查询状态",
		Data: gin.H{
			"task_id": record.ServiceID,
			"status":  record.Status,
		},
	}

	ctx.JSON(200, resp)
}

// RestartContainer 重启容器
func (c *WorkerController) RestartContainer(ctx *gin.Context) {
	service_id := ctx.Param("service_id")

	// 创建请求对象
	req := &models.WorkerDeployRequest{
		ServiceId: service_id,
		NodeIP:    ctx.Query("nodeIP"),
	}

	resp, err := c.manager.RestartContainer(context.Background(), req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// RestartRecord 创建重启任务记录
func (c *WorkerController) RestartRecord(ctx *gin.Context) {
	service_id := ctx.Param("service_id")

	// 创建请求对象
	req := &models.WorkerDeployRequest{
		ServiceId: service_id,
		NodeIP:    ctx.Query("nodeIP"),
	}

	// 将重启请求加入队列
	record, err := c.worker.QueueRestartTask(req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 返回成功响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "重启任务已加入队列，任务将在后台处理，请稍后查询状态",
		Data: gin.H{
			"task_id": record.ServiceID,
			"status":  record.Status,
		},
	}

	ctx.JSON(200, resp)
}

// UpdateRecord 创建重启任务记录
func (c *WorkerController) UpdateRecord(ctx *gin.Context) {

	var req models.WorkerDeployRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// 将升级请求加入队列
	record, err := c.worker.QueueUpdateTask(&req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 返回成功响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "升级任务已加入队列，任务将在后台处理，请稍后查询状态",
		Data: gin.H{
			"task_id": record.ServiceID,
			"status":  record.Status,
		},
	}

	ctx.JSON(200, resp)
}

// GetDeployStatus 获取部署状态（从查询参数获取多个 service_id）
func (c *WorkerController) GetDeployStatus(ctx *gin.Context) {
	// 从查询参数中获取 service_id
	serviceIDsStr := ctx.Query("service_ids")
	if serviceIDsStr == "" {
		ctx.JSON(400, gin.H{"error": "service_ids query parameter is required"})
		return
	}

	// 解析逗号分隔的 service_id 列表
	serviceIDs := strings.Split(serviceIDsStr, ",")
	if len(serviceIDs) == 0 {
		ctx.JSON(400, gin.H{"error": "service_ids cannot be empty"})
		return
	}

	// 不再区分单个和多个 service_id 的情况，统一处理

	// 统一处理所有 service_id
	var deployRecords []gin.H
	var notFoundIDs []string

	for _, serviceID := range serviceIDs {
		record, err := c.worker.GetDeployRecordByID(serviceID)
		if err != nil {
			// 记录未找到的 service_id
			notFoundIDs = append(notFoundIDs, serviceID)
			continue
		}

		// 构建记录数据
		deployData := gin.H{
			"service_id":    record.ServiceID,
			"name":          record.Name,
			"image_name":    record.ImageName,
			"image_url":     record.ImageURL,
			"domain_prefix": record.DomainPrefix,
			"domain_suffix": record.DomainSuffix,
			"status":        record.Status,
			"node_ip":       record.NodeIP,
			"created_at":    record.CreatedAt,
			"updated_at":    record.UpdatedAt,
			"api_replica":   record.ApiReplica,
			"api_cpu":       record.ApiCpu,
			"api_memory":    record.ApiMemory,
			"auto_replica":  record.AutoReplica,
			"auto_cpu":      record.AutoCpu,
			"auto_memory":   record.AutoMemory,
		}

		deployRecords = append(deployRecords, deployData)
	}

	// 构建响应
	responseData := gin.H{
		"records": deployRecords,
	}

	// 如果有未找到的记录，添加到响应中
	if len(notFoundIDs) > 0 {
		responseData["not_found"] = notFoundIDs
	}

	// 返回响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "获取部署状态成功",
		Data: responseData,
	}

	ctx.JSON(200, resp)
}

// HealthCheck 获取当前 worker 的健康状态
func (c *WorkerController) HealthCheck(ctx *gin.Context) {
	resp, err := c.manager.HealthCheck(context.Background())
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// GetContainerStatus 获取容器状态
func (c *WorkerController) GetContainerStatus(ctx *gin.Context) {
	// 从查询参数中获取 service_id 和 node_ip
	serviceID := ctx.Query("service_id")
	nodeIP := ctx.Query("node_ip")

	// 验证参数
	if serviceID == "" {
		ctx.JSON(400, gin.H{"error": "service_id is required"})
		return
	}

	if nodeIP == "" {
		ctx.JSON(400, gin.H{"error": "node_ip is required"})
		return
	}

	// 调用 worker 方法获取容器状态
	containerStatus, err := c.worker.GetDeployStatus(context.Background(), serviceID, nodeIP)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 构建响应
	resp := &models.WorkerResponse{
		Code: 200,
		Msg:  "获取容器状态成功",
		Data: containerStatus,
	}

	ctx.JSON(200, resp)
}
