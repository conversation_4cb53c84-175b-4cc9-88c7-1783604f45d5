package routes

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/zero-ops/service-system/internal/controllers"
	"github.com/zero-ops/service-system/internal/middleware"
	"github.com/zero-ops/service-system/internal/pkg/logger"
	"github.com/zero-ops/service-system/internal/service"
	"github.com/zero-ops/service-system/internal/worker"

	"github.com/gin-gonic/gin"
)

// Router wraps a gin.Engine to provide additional functionality
type Router struct {
	Engine *gin.Engine
}

// setupCommonMiddleware sets up middleware common to all router types
func setupCommonMiddleware(r *gin.Engine) {

	// 自定义日志中间件
	r.Use(func(c *gin.Context) {
		// 请求开始前
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 请求结束后
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		// 记录请求日志
		logMsg := fmt.Sprintf("%s - \"%s %s\" %d %s",
			clientIP,
			method,
			path,
			statusCode,
			latency,
		)

		// 根据状态码选择日志级别
		if statusCode >= 500 {
			logger.Error("%s", logMsg)
		} else if statusCode >= 400 {
			logger.Warn("%s", logMsg)
		} else {
			logger.Info("%s", logMsg)
		}

		// 记录错误日志
		if len(c.Errors) > 0 {
			for _, e := range c.Errors {
				logger.Error("%s - Error: %v", logMsg, e)
			}
		}
	})

	// 添加恢复中间件，防止程序崩溃
	r.Use(gin.Recovery())
}

// setupHealthEndpoint adds a health check endpoint
func setupHealthEndpoint(r *gin.Engine) {
	r.GET("/ping", func(c *gin.Context) {
		logger.Info("Health check request received")
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
		})
	})
}

// SetupServiceRouter initializes a router for the service node (management layer)
func SetupServiceRouter() *Router {
	r := gin.New() // 使用 New 而不是 Default，因为我们自定义了日志中间件
	setupCommonMiddleware(r)
	setupHealthEndpoint(r)

	// Initialize service instance with database support
	config := service.DefaultConfig()
	config.DBPath = "dbdata/service.db" // Set the database path
	svcInstance := service.NewServiceWithNewConfig(config)

	// Initialize service controller
	serviceCtrl := controllers.NewServiceController(svcInstance)

	pwd, _ := os.Getwd()
	log.Printf("Go程序工作目录: %s", pwd)

	api := r.Group("/api/v1")
	{
		service := api.Group("/service")
		{
			// 使用固定的用户名和密码进行基本认证
			authMiddleware := middleware.BasicAuth("devops", "3-546-_iuhh5498")

			// User service management
			service.POST("/create", authMiddleware, serviceCtrl.CreateService)
			service.GET("/list", serviceCtrl.GetServiceList) // 支持 ?service_id=xxx,yyy，不需要认证
			service.DELETE("/:service_id", authMiddleware, serviceCtrl.DeleteService)
			service.PUT("/:service_id/restart", authMiddleware, serviceCtrl.RestartService)
			service.PUT("/update", authMiddleware, serviceCtrl.UpdateService)

			// Image type
			service.POST("/image/create", authMiddleware, serviceCtrl.CreateImageTypes) // 支持单个和批量创建镜像类型
			service.GET("/image/list", serviceCtrl.GetImageTypeList)                    // 不需要认证
			service.DELETE("/image/:id", authMiddleware, serviceCtrl.DeleteImageType)
			service.PUT("/image/:id", authMiddleware, serviceCtrl.UpdateImageType)

			// Deployment machines
			service.GET("/worker/list", serviceCtrl.GetWorkerList) // 不需要认证
			service.POST("/worker/register", authMiddleware, serviceCtrl.RegisterWorker)
			service.DELETE("/worker/:worker_id", authMiddleware, serviceCtrl.RemoveWorker)
			service.PUT("/worker/:worker_id", authMiddleware, serviceCtrl.UpdateWorker)
		}

	}

	logger.Info("Service router initialized")
	return &Router{Engine: r}
}

// SetupWorkerRouterWithConfig initializes a router for the worker node with the given configuration
func SetupWorkerRouterWithConfig(config worker.Config) *Router {
	r := gin.New() // 使用 New 而不是 Default，因为我们自定义了日志中间件
	setupCommonMiddleware(r)
	setupHealthEndpoint(r)

	// 创建 worker 控制器
	workerCtrl := controllers.NewWorkerController(config)

	api := r.Group("/api/v1")
	{
		// Worker layer - Operations on this worker node
		worker := api.Group("/worker")
		{
			// 使用固定的用户名和密码进行基本认证
			authMiddleware := middleware.BasicAuth("devops", "3-546-_iuhh5498")

			// 检查worker可用性 - 不需要认证
			worker.GET("/health", workerCtrl.HealthCheck)

			// Container operations - 需要认证的敏感操作
			worker.POST("/deploy", authMiddleware, workerCtrl.DeployRecord)
			worker.PUT("/:service_id/stop", authMiddleware, workerCtrl.StopRecord)
			worker.PUT("/:service_id/restart", authMiddleware, workerCtrl.RestartRecord)
			worker.PUT("/update", authMiddleware, workerCtrl.UpdateRecord)

			// 查询操作 - 不需要认证
			worker.GET("/status", workerCtrl.GetDeployStatus)              // 支持 ?service_id=xxx,yyy
			worker.GET("/container/status", workerCtrl.GetContainerStatus) // 支持 ?service_id=xxx&node_ip=xxx
		}
	}

	logger.Info("Worker router initialized with config: %+v", config)
	return &Router{Engine: r}
}

// SetupRouter is kept for backward compatibility
func SetupRouter() *gin.Engine {
	return SetupServiceRouter().Engine
}
