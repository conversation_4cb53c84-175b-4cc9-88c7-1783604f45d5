package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// DeployRecordWRepository 处理 worker 部署记录的数据库操作
type DeployRecordWRepository struct {
	db *WorkerDB
}

// NewDeployRecordWRepository 创建一个新的 DeployRecordWRepository 实例
func NewDeployRecordWRepository(db *WorkerDB) *DeployRecordWRepository {
	return &DeployRecordWRepository{
		db: db,
	}
}

// Create 创建一个新的部署记录
func (r *DeployRecordWRepository) Create(record *models.DeployRecord) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 确保时间戳格式正确
	if record.CreatedAt == "" {
		record.CreatedAt = utils.GetCSTTimeString()
	}
	if record.UpdatedAt == "" {
		record.UpdatedAt = utils.GetCSTTimeString()
	}

	// 将 Labels 和 CustomerEnvs 转换为 JSON 字符串
	labelsJSON, err := json.Marshal(record.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	customerEnvsJSON, err := json.Marshal(record.CustomerEnvs)
	if err != nil {
		return fmt.Errorf("failed to marshal customer_envs: %w", err)
	}

	// 将 Ports 和 PortsMapping 转换为 JSON 字符串
	portsJSON, err := json.Marshal(record.Ports)
	if err != nil {
		return fmt.Errorf("failed to marshal ports: %w", err)
	}

	portsMappingJSON, err := json.Marshal(record.PortsMapping)
	if err != nil {
		return fmt.Errorf("failed to marshal ports_mapping: %w", err)
	}

	// 插入部署记录
	_, err = r.db.Exec(`
INSERT INTO deploy_record_w (
    service_id, name, image_name, image_url, domain_prefix, domain_suffix,
    expiration, duration_seconds, status, labels, customer_envs, remark, node_ip, host_ip, created_at, updated_at,
    api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
    ports, ports_mapping
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`,
		record.ServiceID, record.Name, record.ImageName, record.ImageURL, record.DomainPrefix, record.DomainSuffix,
		record.Expiration, record.DurationSeconds, record.Status, string(labelsJSON), string(customerEnvsJSON), record.Remark, record.NodeIP, record.HostIP, record.CreatedAt, record.UpdatedAt,
		record.ApiReplica, record.ApiCpu, record.ApiMemory, record.AutoReplica, record.AutoCpu, record.AutoMemory,
		string(portsJSON), string(portsMappingJSON),
	)

	if err != nil {
		return fmt.Errorf("failed to create deploy record: %w", err)
	}

	return nil
}

// GetByID 根据 ID 获取部署记录
func (r *DeployRecordWRepository) GetByID(serviceID string) (models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var record models.DeployRecord

	var labelsJSON, customerEnvsJSON, portsJSON, portsMappingJSON string
	err := r.db.QueryRow(`
SELECT service_id, name, image_name, image_url, domain_prefix, domain_suffix,
       expiration, duration_seconds, status, labels, customer_envs, remark, node_ip, host_ip, created_at, updated_at,
       api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
       ports, ports_mapping
FROM deploy_record_w
WHERE service_id = ?
`, serviceID).Scan(
		&record.ServiceID, &record.Name, &record.ImageName, &record.ImageURL, &record.DomainPrefix, &record.DomainSuffix,
		&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &record.NodeIP, &record.HostIP, &record.CreatedAt, &record.UpdatedAt,
		&record.ApiReplica, &record.ApiCpu, &record.ApiMemory, &record.AutoReplica, &record.AutoCpu, &record.AutoMemory,
		&portsJSON, &portsMappingJSON,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return models.DeployRecord{}, fmt.Errorf("deploy record not found: %s", serviceID)
		}
		return models.DeployRecord{}, fmt.Errorf("failed to get deploy record: %w", err)
	}

	// 解析 Labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// 解析 CustomerEnvs JSON
	if customerEnvsJSON != "" {
		if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
		}
	}

	// 解析 Ports JSON
	if portsJSON != "" {
		if err := json.Unmarshal([]byte(portsJSON), &record.Ports); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal ports: %w", err)
		}
	}

	// 解析 PortsMapping JSON
	if portsMappingJSON != "" {
		if err := json.Unmarshal([]byte(portsMappingJSON), &record.PortsMapping); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal ports_mapping: %w", err)
		}
	}

	return record, nil
}

// GetAll 获取所有部署记录，可选过滤条件
func (r *DeployRecordWRepository) GetAll(filter map[string]interface{}) ([]models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建查询语句
	query := `
SELECT service_id, name, image_name, image_url, domain_prefix, domain_suffix,
       expiration, duration_seconds, status, labels, customer_envs, remark, node_ip, host_ip, created_at, updated_at,
       api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
       ports, ports_mapping
FROM deploy_record_w
`

	// 添加过滤条件
	var args []interface{}
	if len(filter) > 0 {
		whereClause := "WHERE 1=1"

		if status, ok := filter["status"].(string); ok && status != "" {
			whereClause += " AND status = ?"
			args = append(args, status)
		}

		if imageType, ok := filter["image_name"].(string); ok && imageType != "" {
			whereClause += " AND image_name = ?"
			args = append(args, imageType)
		}

		query += whereClause
	}

	// 添加排序
	query += " ORDER BY updated_at DESC"

	// 执行查询
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query deploy records: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var records []models.DeployRecord
	for rows.Next() {
		var record models.DeployRecord
		var labelsJSON, customerEnvsJSON, portsJSON, portsMappingJSON string
		if err := rows.Scan(
			&record.ServiceID, &record.Name, &record.ImageName, &record.ImageURL, &record.DomainPrefix, &record.DomainSuffix,
			&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &record.NodeIP, &record.HostIP, &record.CreatedAt, &record.UpdatedAt,
			&record.ApiReplica, &record.ApiCpu, &record.ApiMemory, &record.AutoReplica, &record.AutoCpu, &record.AutoMemory,
			&portsJSON, &portsMappingJSON,
		); err != nil {
			return nil, fmt.Errorf("failed to scan deploy record: %w", err)
		}

		// 解析 Labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// 解析 CustomerEnvs JSON
		if customerEnvsJSON != "" {
			if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
			}
		}

		// 解析 Ports JSON
		if portsJSON != "" {
			if err := json.Unmarshal([]byte(portsJSON), &record.Ports); err != nil {
				return nil, fmt.Errorf("failed to unmarshal ports: %w", err)
			}
		}

		// 解析 PortsMapping JSON
		if portsMappingJSON != "" {
			if err := json.Unmarshal([]byte(portsMappingJSON), &record.PortsMapping); err != nil {
				return nil, fmt.Errorf("failed to unmarshal ports_mapping: %w", err)
			}
		}

		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating deploy records: %w", err)
	}

	return records, nil
}

// Update 更新部署记录
func (r *DeployRecordWRepository) Update(record models.DeployRecord) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 确保更新时间戳
	record.UpdatedAt = utils.GetCSTTimeString()

	// 将 Labels 和 CustomerEnvs 转换为 JSON 字符串
	labelsJSON, err := json.Marshal(record.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	customerEnvsJSON, err := json.Marshal(record.CustomerEnvs)
	if err != nil {
		return fmt.Errorf("failed to marshal customer_envs: %w", err)
	}

	// 将 Ports 和 PortsMapping 转换为 JSON 字符串
	portsJSON, err := json.Marshal(record.Ports)
	if err != nil {
		return fmt.Errorf("failed to marshal ports: %w", err)
	}

	portsMappingJSON, err := json.Marshal(record.PortsMapping)
	if err != nil {
		return fmt.Errorf("failed to marshal ports_mapping: %w", err)
	}

	// 更新部署记录
	result, err := r.db.Exec(`
UPDATE deploy_record_w
SET name = ?, image_name = ?, image_url = ?, domain_prefix = ?, domain_suffix = ?,
    expiration = ?, duration_seconds = ?, status = ?, labels = ?, customer_envs = ?, remark = ?, node_ip = ?, host_ip = ?, updated_at = ?,
    api_replica = ?, api_cpu = ?, api_memory = ?, auto_replica = ?, auto_cpu = ?, auto_memory = ?,
    ports = ?, ports_mapping = ?
WHERE service_id = ?
`,
		record.Name, record.ImageName, record.ImageURL, record.DomainPrefix, record.DomainSuffix,
		record.Expiration, record.DurationSeconds, record.Status, string(labelsJSON), string(customerEnvsJSON), record.Remark, record.NodeIP, record.HostIP, record.UpdatedAt,
		record.ApiReplica, record.ApiCpu, record.ApiMemory, record.AutoReplica, record.AutoCpu, record.AutoMemory,
		string(portsJSON), string(portsMappingJSON),
		record.ServiceID,
	)

	if err != nil {
		return fmt.Errorf("failed to update deploy record: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("deploy record not found: %s", record.ServiceID)
	}

	return nil
}

// UpdateStatus 更新部署记录状态
func (r *DeployRecordWRepository) UpdateStatus(serviceID, status string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 更新部署记录状态
	result, err := r.db.Exec(`
UPDATE deploy_record_w
SET status = ?, updated_at = ?
WHERE service_id = ?
`,
		status, utils.GetCSTTimeString(), serviceID,
	)

	if err != nil {
		return fmt.Errorf("failed to update deploy record status: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("deploy record not found: %s", serviceID)
	}

	return nil
}

// UpdatePortsMapping 更新部署记录的端口映射
func (r *DeployRecordWRepository) UpdatePortsMapping(serviceID string, portsMapping []string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 将 PortsMapping 转换为 JSON 字符串
	portsMappingJSON, err := json.Marshal(portsMapping)
	if err != nil {
		return fmt.Errorf("failed to marshal ports_mapping: %w", err)
	}

	// 更新部署记录的端口映射
	result, err := r.db.Exec(`
UPDATE deploy_record_w
SET ports_mapping = ?, updated_at = ?
WHERE service_id = ?
`,
		string(portsMappingJSON), utils.GetCSTTimeString(), serviceID,
	)

	if err != nil {
		return fmt.Errorf("failed to update deploy record ports_mapping: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("deploy record not found: %s", serviceID)
	}

	return nil
}

// Delete 删除部署记录
func (r *DeployRecordWRepository) Delete(serviceID string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 删除部署记录
	result, err := r.db.Exec(`
DELETE FROM deploy_record_w
WHERE service_id = ?
`, serviceID)

	if err != nil {
		return fmt.Errorf("failed to delete deploy record: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("deploy record not found: %s", serviceID)
	}

	return nil
}

// UpdateRemark 更新部署记录的备注字段
func (r *DeployRecordWRepository) UpdateRemark(serviceID, remark string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 更新部署记录的备注字段
	result, err := r.db.Exec(`
UPDATE deploy_record_w
SET remark = ?, updated_at = ?
WHERE service_id = ?
`,
		remark, utils.GetCSTTimeString(), serviceID,
	)

	if err != nil {
		return fmt.Errorf("failed to update deploy record remark: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("deploy record not found: %s", serviceID)
	}

	return nil
}

// GetNextPendingRecord 获取下一个待处理的部署记录
func (r *DeployRecordWRepository) GetNextPendingRecord() (models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var record models.DeployRecord

	var labelsJSON, customerEnvsJSON, portsJSON, portsMappingJSON string
	err := r.db.QueryRow(`
SELECT service_id, name, image_name, image_url, domain_prefix, domain_suffix,
       expiration, duration_seconds, status, labels, customer_envs, remark, node_ip, host_ip, created_at, updated_at,
       api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
       ports, ports_mapping
FROM deploy_record_w
WHERE status = 'QUEUEING'
ORDER BY updated_at ASC
LIMIT 1
`).Scan(
		&record.ServiceID, &record.Name, &record.ImageName, &record.ImageURL, &record.DomainPrefix, &record.DomainSuffix,
		&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &record.NodeIP, &record.HostIP, &record.CreatedAt, &record.UpdatedAt,
		&record.ApiReplica, &record.ApiCpu, &record.ApiMemory, &record.AutoReplica, &record.AutoCpu, &record.AutoMemory,
		&portsJSON, &portsMappingJSON,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return models.DeployRecord{}, fmt.Errorf("no queueing deploy records found")
		}
		return models.DeployRecord{}, fmt.Errorf("failed to get next queueing deploy record: %w", err)
	}

	// 解析 Labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// 解析 CustomerEnvs JSON
	if customerEnvsJSON != "" {
		if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
		}
	}

	// 解析 Ports JSON
	if portsJSON != "" {
		if err := json.Unmarshal([]byte(portsJSON), &record.Ports); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal ports: %w", err)
		}
	}

	// 解析 PortsMapping JSON
	if portsMappingJSON != "" {
		if err := json.Unmarshal([]byte(portsMappingJSON), &record.PortsMapping); err != nil {
			return models.DeployRecord{}, fmt.Errorf("failed to unmarshal ports_mapping: %w", err)
		}
	}

	return record, nil
}

// GetRecordsByStatus 获取指定状态的部署记录
func (r *DeployRecordWRepository) GetRecordsByStatus(statuses []string) ([]models.DeployRecord, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建查询语句
	query := `
SELECT service_id, name, image_name, image_url, domain_prefix, domain_suffix,
       expiration, duration_seconds, status, labels, customer_envs, remark, node_ip, host_ip, created_at, updated_at,
       api_replica, api_cpu, api_memory, auto_replica, auto_cpu, auto_memory,
       ports, ports_mapping
FROM deploy_record_w
WHERE status IN (`

	// 构建参数占位符
	placeholders := make([]string, len(statuses))
	args := make([]interface{}, len(statuses))
	for i, status := range statuses {
		placeholders[i] = "?"
		args[i] = status
	}
	query += strings.Join(placeholders, ", ") + ")"

	// 执行查询
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query deploy records by status: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var records []models.DeployRecord
	for rows.Next() {
		var record models.DeployRecord
		var labelsJSON, customerEnvsJSON, portsJSON, portsMappingJSON string

		err := rows.Scan(
			&record.ServiceID, &record.Name, &record.ImageName, &record.ImageURL, &record.DomainPrefix, &record.DomainSuffix,
			&record.Expiration, &record.DurationSeconds, &record.Status, &labelsJSON, &customerEnvsJSON, &record.Remark, &record.NodeIP, &record.HostIP, &record.CreatedAt, &record.UpdatedAt,
			&record.ApiReplica, &record.ApiCpu, &record.ApiMemory, &record.AutoReplica, &record.AutoCpu, &record.AutoMemory,
			&portsJSON, &portsMappingJSON,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan deploy record: %w", err)
		}

		// 解析 Labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &record.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// 解析 CustomerEnvs JSON
		if customerEnvsJSON != "" {
			if err := json.Unmarshal([]byte(customerEnvsJSON), &record.CustomerEnvs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal customer_envs: %w", err)
			}
		}

		// 解析 Ports JSON
		if portsJSON != "" {
			if err := json.Unmarshal([]byte(portsJSON), &record.Ports); err != nil {
				return nil, fmt.Errorf("failed to unmarshal ports: %w", err)
			}
		}

		// 解析 PortsMapping JSON
		if portsMappingJSON != "" {
			if err := json.Unmarshal([]byte(portsMappingJSON), &record.PortsMapping); err != nil {
				return nil, fmt.Errorf("failed to unmarshal ports_mapping: %w", err)
			}
		}

		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating deploy records: %w", err)
	}

	return records, nil
}
