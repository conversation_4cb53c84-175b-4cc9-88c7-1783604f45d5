package database

import (
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// WorkerDB represents a database connection for the worker node
type WorkerDB struct {
	*sql.DB
	mu sync.Mutex // Mutex for thread-safe operations
}

// WorkerDBConfig holds configuration for the worker database
type WorkerDBConfig struct {
	DBPath string // Path to the database file
}

// DefaultWorkerDBConfig returns the default worker database configuration
func DefaultWorkerDBConfig() WorkerDBConfig {
	return WorkerDBConfig{
		DBPath: "dbdata/worker.db",
	}
}

// NewWorkerDB creates a new worker database connection
func NewWorkerDB(config WorkerDBConfig) (*WorkerDB, error) {
	// Create the database connection
	db, err := sql.Open("sqlite3", config.DBPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Set connection pool parameters
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set pragmas for better performance (WAL mode)
	if _, err := db.Exec("PRAGMA journal_mode=DELETE;"); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to set journal mode: %w", err)
	}

	if _, err := db.Exec("PRAGMA synchronous=NORMAL;"); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to set synchronous mode: %w", err)
	}

	// Explicitly disable foreign key constraints
	if _, err := db.Exec("PRAGMA foreign_keys=OFF;"); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to disable foreign keys: %w", err)
	}

	return &WorkerDB{DB: db, mu: sync.Mutex{}}, nil
}

// InitSchema initializes the database schema for the worker node
func (db *WorkerDB) InitSchema() error {
	db.mu.Lock()
	defer db.mu.Unlock()

	// Create deploy_record_w table
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS deploy_record_w (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			service_id TEXT NOT NULL, -- 为用户层创建的唯一id
			name TEXT NOT NULL,
			image_name TEXT NOT NULL,
			image_url TEXT,
			domain_prefix TEXT NOT NULL,
			domain_suffix TEXT NOT NULL,
			expiration TEXT NOT NULL, -- YYYY-MM-DDTHH:mm:ss
			duration_seconds INTEGER NOT NULL, -- 服务持续时间（秒）
			status TEXT NOT NULL, -- QUEUEING排队中 | PROCESSING 操作中|RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			labels TEXT, -- 存储为JSON格式的标签数组
			customer_envs TEXT, -- 用户自定义环境变量 存储为JSON格式的数组 ["VARA='some value'", "VARB=123"]
			remark TEXT, -- 额外信息
			host_ip TEXT,
			node_ip TEXT,
			ports TEXT, -- 存储为JSON格式的端口数组 [] ["8080", "9000"]
			ports_mapping TEXT, -- 	存储为JSON格式的端口映射数组 [] ["30001:8080", "30002:9000"] (格式: "主机端口:容器端口")
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			visited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			api_replica INTEGER,
			api_cpu REAL,
			api_memory INTEGER,
			auto_replica INTEGER,
			auto_cpu REAL,
			auto_memory INTEGER
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create deploy_record_w table: %w", err)
	}

	return nil
}

// Close closes the database connection
func (db *WorkerDB) Close() error {
	return db.DB.Close()
}
