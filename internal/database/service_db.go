package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	_ "github.com/mattn/go-sqlite3" // SQLite driver
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// ServiceDB represents a database connection for the service node
type ServiceDB struct {
	*sql.DB
	mu sync.Mutex // Mutex for thread-safe operations
}

// ServiceDBConfig holds configuration for the service database
type ServiceDBConfig struct {
	DBPath string // Path to the database file
}

// DefaultServiceDBConfig returns the default service database configuration
func DefaultServiceDBConfig() ServiceDBConfig {
	return ServiceDBConfig{
		DBPath: filepath.Join("dbdata", "service.db"),
	}
}

// NewServiceDB creates a new service database connection
func NewServiceDB(config ServiceDBConfig) (*ServiceDB, error) {
	// Ensure the directory exists
	dbDir := filepath.Dir(config.DBPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		logger.Error("Failed to create database directory: %v, path: %s", err, dbDir)
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open the database
	db, err := sql.Open("sqlite3", config.DBPath)
	if err != nil {
		logger.Error("Failed to open database: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		logger.Error("Failed to ping database: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set pragmas for better performance (WAL mode)
	if _, err := db.Exec("PRAGMA journal_mode=DELETE;"); err != nil {
		db.Close()
		logger.Error("Failed to set journal mode: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to set journal mode: %w", err)
	}

	if _, err := db.Exec("PRAGMA synchronous=NORMAL;"); err != nil {
		db.Close()
		logger.Error("Failed to set synchronous mode: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to set synchronous mode: %w", err)
	}

	// Explicitly disable foreign key constraints
	if _, err := db.Exec("PRAGMA foreign_keys=OFF;"); err != nil {
		db.Close()
		logger.Error("Failed to disable foreign keys: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to disable foreign keys: %w", err)
	}

	return &ServiceDB{DB: db, mu: sync.Mutex{}}, nil
}

// InitSchema initializes the database schema
func (db *ServiceDB) InitSchema() error {
	db.mu.Lock()
	defer db.mu.Unlock()

	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS workers (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			worker_id TEXT NOT NULL,
			server_type TEXT NOT NULL,
			name TEXT NOT NULL,
			labels TEXT, -- 存储为JSON格式的标签数组
			host TEXT NOT NULL, -- api地址
			host_ip TEXT NOT NULL,
			domain_suffix TEXT, -- 基础域名
			nodes TEXT, -- 存储为JSON格式的节点列表
			status TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create workers table: %v", err)
		return fmt.Errorf("failed to create workers table: %w", err)
	}

	// Create deploy_record table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS deploy_record (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			service_id TEXT NOT NULL, -- 为用户层创建的唯一id
			domain_prefix TEXT NOT NULL, -- 域名的个性化前缀，后缀由部署环境以及服务类型决定
			domain_suffix TEXT, -- 域名后缀
			image_name TEXT NOT NULL, -- 镜像名称
			service_type TEXT NOT NULL, -- 服务类型
			expiration TEXT NOT NULL, -- YYYY-MM-DDTHH:mm:ss
			duration_seconds INTEGER NOT NULL, -- 服务持续时间（秒）
			labels TEXT, -- 存储为JSON格式的标签数组
			customer_envs TEXT, --- 用户自定义环境变量 存储为JSON格式的数组 ["VARA='some value'", "VARB=123"]
			api_replica INTEGER,
			api_cpu REAL,
			api_memory INTEGER,
			auto_replica INTEGER,
			auto_cpu REAL,
			auto_memory INTEGER,
			status TEXT NOT NULL, -- QUEUEING 排队中|PROCESSING 操作中|RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			worker_id TEXT,
			node_ip TEXT,
			host_ip TEXT, -- 存储host节点的ip地址
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			remark TEXT -- 额外信息
		)
	`)
	if err != nil {
		logger.Error("Failed to create deploy_record table: %v", err)
		return fmt.Errorf("failed to create deploy_record table: %w", err)
	}

	// 创建镜像类型
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS image_type (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			image_name TEXT NOT NULL, -- 镜像名称，需要全局唯一
			image_url TEXT NOT NULL,
			ports  TEXT, -- 存储为JSON格式的端口数组 [] ["8080", "9000"]
			labels TEXT, -- 存储为JSON格式的标签数组
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create image_type table: %v", err)
		return fmt.Errorf("failed to create image_type table: %w", err)
	}

	return nil
}

// Close closes the database connection
func (db *ServiceDB) Close() error {
	return db.DB.Close()
}
