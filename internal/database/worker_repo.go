package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/zero-ops/service-system/internal/models"
)

// WorkerRepository handles database operations for workers
type WorkerRepository struct {
	db *ServiceDB
}

// NewWorkerRepository creates a new worker repository
func NewWorkerRepository(db *ServiceDB) *WorkerRepository {
	return &WorkerRepository{db: db}
}

// Create inserts a new worker into the database
func (r *WorkerRepository) Create(worker models.WorkerInfo) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(worker.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert nodes to JSON
	nodesJSON, err := json.Marshal(worker.Nodes)
	if err != nil {
		return fmt.Errorf("failed to marshal nodes: %w", err)
	}

	// 获取当前本地时间
	now := time.Now().Format(time.RFC3339)

	result, err := r.db.Exec(`
INSERT INTO workers (worker_id, server_type, name, labels, host, host_ip, domain_suffix, nodes, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, worker.WorkerID, worker.ServerType, worker.Name, string(labelsJSON), worker.Host, worker.HostIP, worker.DomainSuffix, string(nodesJSON), worker.Status, now, now)

	if err != nil {
		return fmt.Errorf("failed to create worker: %w", err)
	}

	// Get the auto-generated ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}
	worker.ID = id

	return nil
}

// GetByID retrieves a worker by ID
func (r *WorkerRepository) GetByID(id string) (models.WorkerInfo, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var worker models.WorkerInfo
	var labelsJSON, nodesJSON string
	var domainSuffix sql.NullString // 使用 sql.NullString 处理可能为 NULL 的字符串

	// First try to find by worker_id
	err := r.db.QueryRow(`
SELECT id, worker_id, server_type, name, labels, host, host_ip, domain_suffix, nodes, status, created_at, updated_at
FROM workers
WHERE worker_id = ?
`, id).Scan(
		&worker.ID, &worker.WorkerID, &worker.ServerType, &worker.Name, &labelsJSON, &worker.Host, &worker.HostIP, &domainSuffix, &nodesJSON, &worker.Status,
		&worker.CreatedAt, &worker.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// Try to find by numeric ID
			if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
				err = r.db.QueryRow(`
SELECT id, worker_id, server_type, name, labels, host, host_ip, domain_suffix, nodes, status, created_at, updated_at
FROM workers
WHERE id = ?
`, numID).Scan(
					&worker.ID, &worker.WorkerID, &worker.ServerType, &worker.Name, &labelsJSON, &worker.Host, &worker.HostIP, &domainSuffix, &nodesJSON, &worker.Status,
					&worker.CreatedAt, &worker.UpdatedAt,
				)
				if err != nil {
					if err == sql.ErrNoRows {
						return models.WorkerInfo{}, fmt.Errorf("worker not found: %s", id)
					}
					return models.WorkerInfo{}, fmt.Errorf("failed to get worker: %w", err)
				}
			} else {
				return models.WorkerInfo{}, fmt.Errorf("worker not found: %s", id)
			}
		} else {
			return models.WorkerInfo{}, fmt.Errorf("failed to get worker: %w", err)
		}
	}

	// 处理 DomainSuffix 字段
	if domainSuffix.Valid {
		// 如果不是 NULL，则设置值
		worker.DomainSuffix = domainSuffix.String
	} else {
		// 如果是 NULL，则设置为空字符串
		worker.DomainSuffix = ""
	}

	// Parse labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &worker.Labels); err != nil {
			return models.WorkerInfo{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// Parse nodes JSON
	if nodesJSON != "" {
		if err := json.Unmarshal([]byte(nodesJSON), &worker.Nodes); err != nil {
			return models.WorkerInfo{}, fmt.Errorf("failed to unmarshal nodes: %w", err)
		}
	}

	return worker, nil
}

// GetAll retrieves all workers with optional filtering
func (r *WorkerRepository) GetAll(filter *models.WorkerFilter) ([]models.WorkerInfo, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// 构建查询语句
	query := `
SELECT id, worker_id, server_type, name, labels, host, host_ip, domain_suffix, nodes, status, created_at, updated_at
FROM workers
WHERE 1=1
`
	var args []interface{}

	// 添加筛选条件
	if filter != nil {
		// 按服务器类型筛选
		if filter.ServerType != "" {
			query += " AND server_type = ?"
			args = append(args, filter.ServerType)
		}

		// 按状态筛选
		if filter.Status != "" {
			query += " AND status = ?"
			args = append(args, filter.Status)
		}

		// 按标签筛选（这需要特殊处理，因为标签存储为 JSON）
		// 注意：这种方式在 SQLite 中可能不是最高效的，但它能工作
		if len(filter.Labels) > 0 {
			for _, label := range filter.Labels {
				// 使用 LIKE 操作符查找包含特定标签的记录
				// 这里假设标签存储为 JSON 数组，如 ["label1", "label2"]
				query += " AND labels LIKE ?"
				args = append(args, "%"+label+"%")
			}
		}
	}

	// 添加排序
	query += " ORDER BY id"

	// 执行查询
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query workers: %w", err)
	}
	defer rows.Close()

	var workers []models.WorkerInfo
	for rows.Next() {
		var worker models.WorkerInfo
		var labelsJSON, nodesJSON string
		var domainSuffix sql.NullString // 使用 sql.NullString 处理可能为 NULL 的字符串

		if err := rows.Scan(
			&worker.ID, &worker.WorkerID, &worker.ServerType, &worker.Name, &labelsJSON, &worker.Host, &worker.HostIP, &domainSuffix, &nodesJSON, &worker.Status,
			&worker.CreatedAt, &worker.UpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("failed to scan worker: %w", err)
		}

		// 处理 DomainSuffix 字段
		if domainSuffix.Valid {
			// 如果不是 NULL，则设置值
			worker.DomainSuffix = domainSuffix.String
		} else {
			// 如果是 NULL，则设置为空字符串
			worker.DomainSuffix = ""
		}

		// Parse labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &worker.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// Parse nodes JSON
		if nodesJSON != "" {
			if err := json.Unmarshal([]byte(nodesJSON), &worker.Nodes); err != nil {
				return nil, fmt.Errorf("failed to unmarshal nodes: %w", err)
			}
		}

		workers = append(workers, worker)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating workers: %w", err)
	}

	return workers, nil
}

// Update updates a worker's information
func (r *WorkerRepository) Update(worker models.WorkerInfo) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(worker.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert nodes to JSON
	nodesJSON, err := json.Marshal(worker.Nodes)
	if err != nil {
		return fmt.Errorf("failed to marshal nodes: %w", err)
	}

	// 获取当前本地时间
	now := time.Now().Format(time.RFC3339)

	// 首先尝试使用 worker_id 更新
	result, err := r.db.Exec(`
UPDATE workers
SET server_type = ?, name = ?, labels = ?, host = ?, host_ip = ?, domain_suffix = ?, nodes = ?, status = ?, updated_at = ?
WHERE worker_id = ?
`, worker.ServerType, worker.Name, string(labelsJSON), worker.Host, worker.HostIP, worker.DomainSuffix, string(nodesJSON), worker.Status, now, worker.WorkerID)

	if err != nil {
		return fmt.Errorf("failed to update worker: %w", err)
	}

	// 检查是否有行被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// 如果没有行被更新，尝试使用 id 更新
	if rowsAffected == 0 && worker.ID > 0 {
		_, err = r.db.Exec(`
UPDATE workers
SET server_type = ?, name = ?, labels = ?, host = ?, host_ip = ?, domain_suffix = ?, nodes = ?, status = ?, updated_at = ?
WHERE id = ?
`, worker.ServerType, worker.Name, string(labelsJSON), worker.Host, worker.HostIP, worker.DomainSuffix, string(nodesJSON), worker.Status, now, worker.ID)

		if err != nil {
			return fmt.Errorf("failed to update worker by ID: %w", err)
		}
	}

	return nil
}

// UpdateStatus updates a worker's status and last seen time
func (r *WorkerRepository) UpdateStatus(id string, status string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// First try to update by worker_id
	// 获取当前本地时间
	now := time.Now().Format(time.RFC3339)

	result, err := r.db.Exec(`
UPDATE workers
SET status = ?, updated_at = ?
WHERE worker_id = ?
`, status, now, id)

	if err != nil {
		return fmt.Errorf("failed to update worker status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// If no rows were affected, try to update by numeric ID
	if rowsAffected == 0 {
		if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
			_, err = r.db.Exec(`
UPDATE workers
SET status = ?, updated_at = ?
WHERE id = ?
`, status, now, numID)

			if err != nil {
				return fmt.Errorf("failed to update worker status by ID: %w", err)
			}
		} else {
			return fmt.Errorf("worker not found: %s", id)
		}
	}

	return nil
}

// Delete removes a worker from the database
func (r *WorkerRepository) Delete(id string) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// First try to delete by worker_id
	result, err := r.db.Exec("DELETE FROM workers WHERE worker_id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete worker: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// If no rows were affected, try to delete by numeric ID
	if rowsAffected == 0 {
		if numID, err := strconv.ParseInt(id, 10, 64); err == nil {
			_, err = r.db.Exec("DELETE FROM workers WHERE id = ?", numID)
			if err != nil {
				return fmt.Errorf("failed to delete worker by ID: %w", err)
			}
		} else {
			return fmt.Errorf("worker not found: %s", id)
		}
	}

	return nil
}
