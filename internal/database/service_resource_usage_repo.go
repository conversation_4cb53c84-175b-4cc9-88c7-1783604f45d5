package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// ServiceResourceUsageRepository 服务资源使用统计数据库操作
type ServiceResourceUsageRepository struct {
	db *ServiceDB
}

// NewServiceResourceUsageRepository 创建新的资源使用统计仓库
func NewServiceResourceUsageRepository(db *ServiceDB) *ServiceResourceUsageRepository {
	return &ServiceResourceUsageRepository{db: db}
}

// GetByServiceID 通过service_id获取资源使用统计
func (r *ServiceResourceUsageRepository) GetByServiceID(serviceID string) (*models.ServiceResourceUsage, error) {
	logger.Info("获取服务资源使用统计: serviceID=%s", serviceID)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       last_access_time, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE service_id = ?
	`

	row := r.db.QueryRow(query, serviceID)

	usage := &models.ServiceResourceUsage{}
	var lastAccessTime sql.NullTime

	err := row.Scan(
		&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
		&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
		&usage.NetworkInTraffic, &usage.NetworkOutTraffic,
		&lastAccessTime, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			logger.Info("未找到服务资源使用统计: serviceID=%s", serviceID)
			return nil, nil
		}
		logger.Error("查询服务资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询服务资源使用统计失败: %w", err)
	}

	if lastAccessTime.Valid {
		usage.LastAccessTime = &lastAccessTime.Time
	}

	logger.Info("成功获取服务资源使用统计: serviceID=%s", serviceID)
	return usage, nil
}

// Create 创建新的资源使用统计记录
func (r *ServiceResourceUsageRepository) Create(usage *models.ServiceResourceUsage) error {
	logger.Info("创建服务资源使用统计: serviceID=%s", usage.ServiceID)

	query := `
		INSERT INTO service_resource_usage (
			service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
			oss_network_traffic, network_in_traffic, network_out_traffic,
			last_access_time, metadata, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var lastAccessTime interface{}
	if usage.LastAccessTime != nil {
		lastAccessTime = usage.LastAccessTime
	}

	now := time.Now()
	usage.CreatedAt = now
	usage.UpdatedAt = now

	result, err := r.db.Exec(query,
		usage.ServiceID, usage.CPUUsage, usage.MemoryUsage, usage.DiskUsage,
		usage.OSSDiskUsage, usage.OSSNetworkTraffic, usage.NetworkInTraffic,
		usage.NetworkOutTraffic, lastAccessTime, usage.Metadata,
		usage.CreatedAt, usage.UpdatedAt,
	)

	if err != nil {
		logger.Error("创建服务资源使用统计失败: %v", err)
		return fmt.Errorf("创建服务资源使用统计失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		logger.Error("获取插入ID失败: %v", err)
		return fmt.Errorf("获取插入ID失败: %w", err)
	}

	usage.ID = id
	logger.Info("成功创建服务资源使用统计: ID=%d, serviceID=%s", id, usage.ServiceID)
	return nil
}

// UpdateByServiceID 通过service_id更新资源使用统计
func (r *ServiceResourceUsageRepository) UpdateByServiceID(serviceID string, usage *models.ServiceResourceUsage) error {
	logger.Info("更新服务资源使用统计: serviceID=%s", serviceID)

	// 构建动态更新语句
	var setParts []string
	var args []interface{}

	if usage.CPUUsage != 0 {
		setParts = append(setParts, "cpu_usage = ?")
		args = append(args, usage.CPUUsage)
	}
	if usage.MemoryUsage != 0 {
		setParts = append(setParts, "memory_usage = ?")
		args = append(args, usage.MemoryUsage)
	}
	if usage.DiskUsage != 0 {
		setParts = append(setParts, "disk_usage = ?")
		args = append(args, usage.DiskUsage)
	}
	if usage.OSSDiskUsage != 0 {
		setParts = append(setParts, "oss_disk_usage = ?")
		args = append(args, usage.OSSDiskUsage)
	}
	if usage.OSSNetworkTraffic != 0 {
		setParts = append(setParts, "oss_network_traffic = ?")
		args = append(args, usage.OSSNetworkTraffic)
	}
	if usage.NetworkInTraffic != 0.0 {
		setParts = append(setParts, "network_in_traffic = ?")
		args = append(args, usage.NetworkInTraffic)
	}
	if usage.NetworkOutTraffic != 0.0 {
		setParts = append(setParts, "network_out_traffic = ?")
		args = append(args, usage.NetworkOutTraffic)
	}
	if usage.LastAccessTime != nil {
		setParts = append(setParts, "last_access_time = ?")
		args = append(args, usage.LastAccessTime)
	}
	if usage.Metadata != "" {
		setParts = append(setParts, "metadata = ?")
		args = append(args, usage.Metadata)
	}

	// 总是更新 updated_at
	setParts = append(setParts, "updated_at = ?")
	args = append(args, time.Now())

	if len(setParts) == 1 { // 只有 updated_at
		logger.Warn("没有需要更新的字段: serviceID=%s", serviceID)
		return nil
	}

	// 添加 WHERE 条件参数
	args = append(args, serviceID)

	query := fmt.Sprintf(`
		UPDATE service_resource_usage
		SET %s
		WHERE service_id = ?
	`, strings.Join(setParts, ", "))

	result, err := r.db.Exec(query, args...)
	if err != nil {
		logger.Error("更新服务资源使用统计失败: %v", err)
		return fmt.Errorf("更新服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取更新行数失败: %v", err)
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		logger.Warn("没有找到要更新的记录: serviceID=%s", serviceID)
		return fmt.Errorf("没有找到要更新的记录: serviceID=%s", serviceID)
	}

	logger.Info("成功更新服务资源使用统计: serviceID=%s, rowsAffected=%d", serviceID, rowsAffected)
	return nil
}

// UpsertByServiceID 通过service_id创建或更新资源使用统计
func (r *ServiceResourceUsageRepository) UpsertByServiceID(usage *models.ServiceResourceUsage) error {
	logger.Info("创建或更新服务资源使用统计: serviceID=%s", usage.ServiceID)

	// 先尝试获取现有记录
	existing, err := r.GetByServiceID(usage.ServiceID)
	if err != nil {
		return fmt.Errorf("检查现有记录失败: %w", err)
	}

	if existing == nil {
		// 记录不存在，创建新记录
		return r.Create(usage)
	} else {
		// 记录存在，更新记录
		return r.UpdateByServiceID(usage.ServiceID, usage)
	}
}

// DeleteByServiceID 删除指定服务的资源使用统计
func (r *ServiceResourceUsageRepository) DeleteByServiceID(serviceID string) error {
	logger.Info("删除服务资源使用统计: serviceID=%s", serviceID)

	query := `DELETE FROM service_resource_usage WHERE service_id = ?`

	result, err := r.db.Exec(query, serviceID)
	if err != nil {
		logger.Error("删除服务资源使用统计失败: %v", err)
		return fmt.Errorf("删除服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取删除行数失败: %v", err)
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	logger.Info("成功删除服务资源使用统计: serviceID=%s, rowsAffected=%d", serviceID, rowsAffected)
	return nil
}

// GetAll 获取所有资源使用统计
func (r *ServiceResourceUsageRepository) GetAll(limit int, offset int) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取所有资源使用统计: limit=%d, offset=%d", limit, offset)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       last_access_time, metadata, created_at, updated_at
		FROM service_resource_usage
		ORDER BY updated_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := r.db.Query(query, limit, offset)
	if err != nil {
		logger.Error("查询所有资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询所有资源使用统计失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var lastAccessTime sql.NullTime

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic,
			&lastAccessTime, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
		)
		if err != nil {
			logger.Error("扫描资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描资源使用统计失败: %w", err)
		}

		if lastAccessTime.Valid {
			usage.LastAccessTime = &lastAccessTime.Time
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历资源使用统计结果失败: %w", err)
	}

	logger.Info("成功获取所有资源使用统计: count=%d", len(usages))
	return usages, nil
}
