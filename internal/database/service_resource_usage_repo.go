package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// ServiceResourceUsageRepository 服务资源使用统计数据库操作
type ServiceResourceUsageRepository struct {
	db *ServiceDB
}

// NewServiceResourceUsageRepository 创建新的资源使用统计仓库
func NewServiceResourceUsageRepository(db *ServiceDB) *ServiceResourceUsageRepository {
	return &ServiceResourceUsageRepository{db: db}
}

// GetByServiceID 通过service_id获取资源使用统计
func (r *ServiceResourceUsageRepository) GetByServiceID(serviceID string, statsType string) (*models.ServiceResourceUsage, error) {
	logger.Info("获取服务资源使用统计: serviceID=%s, statsType=%s", serviceID, statsType)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       container_count, uptime_seconds, last_access_time, stats_type,
		       collection_method, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE service_id = ? AND stats_type = ?
	`

	row := r.db.QueryRow(query, serviceID, statsType)

	usage := &models.ServiceResourceUsage{}
	var lastAccessTime sql.NullTime

	err := row.Scan(
		&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
		&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
		&usage.NetworkInTraffic, &usage.NetworkOutTraffic, &usage.ContainerCount,
		&usage.UptimeSeconds, &lastAccessTime, &usage.StatsType,
		&usage.CollectionMethod, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			logger.Info("未找到服务资源使用统计: serviceID=%s, statsType=%s", serviceID, statsType)
			return nil, nil
		}
		logger.Error("查询服务资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询服务资源使用统计失败: %w", err)
	}

	if lastAccessTime.Valid {
		usage.LastAccessTime = &lastAccessTime.Time
	}

	logger.Info("成功获取服务资源使用统计: serviceID=%s, statsType=%s", serviceID, statsType)
	return usage, nil
}

// GetAllByServiceID 获取指定服务的所有统计类型数据
func (r *ServiceResourceUsageRepository) GetAllByServiceID(serviceID string) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取服务所有资源使用统计: serviceID=%s", serviceID)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       container_count, uptime_seconds, last_access_time, stats_type,
		       collection_method, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE service_id = ?
		ORDER BY stats_type, updated_at DESC
	`

	rows, err := r.db.Query(query, serviceID)
	if err != nil {
		logger.Error("查询服务所有资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询服务所有资源使用统计失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var lastAccessTime sql.NullTime

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic, &usage.ContainerCount,
			&usage.UptimeSeconds, &lastAccessTime, &usage.StatsType,
			&usage.CollectionMethod, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
		)
		if err != nil {
			logger.Error("扫描服务资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描服务资源使用统计失败: %w", err)
		}

		if lastAccessTime.Valid {
			usage.LastAccessTime = &lastAccessTime.Time
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历服务资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历服务资源使用统计结果失败: %w", err)
	}

	logger.Info("成功获取服务所有资源使用统计: serviceID=%s, count=%d", serviceID, len(usages))
	return usages, nil
}

// Create 创建新的资源使用统计记录
func (r *ServiceResourceUsageRepository) Create(usage *models.ServiceResourceUsage) error {
	logger.Info("创建服务资源使用统计: serviceID=%s, statsType=%s", usage.ServiceID, usage.StatsType)

	// 验证数据
	if !usage.IsValidStatsType() {
		return fmt.Errorf("无效的统计类型: %s", usage.StatsType)
	}
	if !usage.IsValidCollectionMethod() {
		return fmt.Errorf("无效的收集方式: %s", usage.CollectionMethod)
	}

	query := `
		INSERT INTO service_resource_usage (
			service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
			oss_network_traffic, network_in_traffic, network_out_traffic,
			container_count, uptime_seconds, last_access_time, stats_type,
			collection_method, metadata, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var lastAccessTime interface{}
	if usage.LastAccessTime != nil {
		lastAccessTime = usage.LastAccessTime
	}

	now := time.Now()
	usage.CreatedAt = now
	usage.UpdatedAt = now

	result, err := r.db.Exec(query,
		usage.ServiceID, usage.CPUUsage, usage.MemoryUsage, usage.DiskUsage,
		usage.OSSDiskUsage, usage.OSSNetworkTraffic, usage.NetworkInTraffic,
		usage.NetworkOutTraffic, usage.ContainerCount, usage.UptimeSeconds,
		lastAccessTime, usage.StatsType, usage.CollectionMethod, usage.Metadata,
		usage.CreatedAt, usage.UpdatedAt,
	)

	if err != nil {
		logger.Error("创建服务资源使用统计失败: %v", err)
		return fmt.Errorf("创建服务资源使用统计失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		logger.Error("获取插入ID失败: %v", err)
		return fmt.Errorf("获取插入ID失败: %w", err)
	}

	usage.ID = id
	logger.Info("成功创建服务资源使用统计: ID=%d, serviceID=%s, statsType=%s", id, usage.ServiceID, usage.StatsType)
	return nil
}

// UpdateByServiceID 通过service_id更新资源使用统计
func (r *ServiceResourceUsageRepository) UpdateByServiceID(serviceID string, statsType string, usage *models.ServiceResourceUsage) error {
	logger.Info("更新服务资源使用统计: serviceID=%s, statsType=%s", serviceID, statsType)

	// 验证数据
	if !usage.IsValidStatsType() {
		return fmt.Errorf("无效的统计类型: %s", usage.StatsType)
	}
	if !usage.IsValidCollectionMethod() {
		return fmt.Errorf("无效的收集方式: %s", usage.CollectionMethod)
	}

	// 构建动态更新语句
	var setParts []string
	var args []interface{}

	if usage.CPUUsage != 0 {
		setParts = append(setParts, "cpu_usage = ?")
		args = append(args, usage.CPUUsage)
	}
	if usage.MemoryUsage != 0 {
		setParts = append(setParts, "memory_usage = ?")
		args = append(args, usage.MemoryUsage)
	}
	if usage.DiskUsage != 0 {
		setParts = append(setParts, "disk_usage = ?")
		args = append(args, usage.DiskUsage)
	}
	if usage.OSSDiskUsage != 0 {
		setParts = append(setParts, "oss_disk_usage = ?")
		args = append(args, usage.OSSDiskUsage)
	}
	if usage.OSSNetworkTraffic != 0 {
		setParts = append(setParts, "oss_network_traffic = ?")
		args = append(args, usage.OSSNetworkTraffic)
	}
	if usage.NetworkInTraffic != 0 {
		setParts = append(setParts, "network_in_traffic = ?")
		args = append(args, usage.NetworkInTraffic)
	}
	if usage.NetworkOutTraffic != 0 {
		setParts = append(setParts, "network_out_traffic = ?")
		args = append(args, usage.NetworkOutTraffic)
	}
	if usage.ContainerCount != 0 {
		setParts = append(setParts, "container_count = ?")
		args = append(args, usage.ContainerCount)
	}
	if usage.UptimeSeconds != 0 {
		setParts = append(setParts, "uptime_seconds = ?")
		args = append(args, usage.UptimeSeconds)
	}
	if usage.LastAccessTime != nil {
		setParts = append(setParts, "last_access_time = ?")
		args = append(args, usage.LastAccessTime)
	}
	if usage.CollectionMethod != "" {
		setParts = append(setParts, "collection_method = ?")
		args = append(args, usage.CollectionMethod)
	}
	if usage.Metadata != "" {
		setParts = append(setParts, "metadata = ?")
		args = append(args, usage.Metadata)
	}

	// 总是更新 updated_at
	setParts = append(setParts, "updated_at = ?")
	args = append(args, time.Now())

	if len(setParts) == 1 { // 只有 updated_at
		logger.Warn("没有需要更新的字段: serviceID=%s, statsType=%s", serviceID, statsType)
		return nil
	}

	// 添加 WHERE 条件参数
	args = append(args, serviceID, statsType)

	query := fmt.Sprintf(`
		UPDATE service_resource_usage
		SET %s
		WHERE service_id = ? AND stats_type = ?
	`, strings.Join(setParts, ", "))

	result, err := r.db.Exec(query, args...)
	if err != nil {
		logger.Error("更新服务资源使用统计失败: %v", err)
		return fmt.Errorf("更新服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取更新行数失败: %v", err)
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		logger.Warn("没有找到要更新的记录: serviceID=%s, statsType=%s", serviceID, statsType)
		return fmt.Errorf("没有找到要更新的记录: serviceID=%s, statsType=%s", serviceID, statsType)
	}

	logger.Info("成功更新服务资源使用统计: serviceID=%s, statsType=%s, rowsAffected=%d", serviceID, statsType, rowsAffected)
	return nil
}

// UpsertByServiceID 通过service_id创建或更新资源使用统计
func (r *ServiceResourceUsageRepository) UpsertByServiceID(usage *models.ServiceResourceUsage) error {
	logger.Info("创建或更新服务资源使用统计: serviceID=%s, statsType=%s", usage.ServiceID, usage.StatsType)

	// 先尝试获取现有记录
	existing, err := r.GetByServiceID(usage.ServiceID, usage.StatsType)
	if err != nil {
		return fmt.Errorf("检查现有记录失败: %w", err)
	}

	if existing == nil {
		// 记录不存在，创建新记录
		return r.Create(usage)
	} else {
		// 记录存在，更新记录
		return r.UpdateByServiceID(usage.ServiceID, usage.StatsType, usage)
	}
}

// DeleteByServiceID 删除指定服务的资源使用统计
func (r *ServiceResourceUsageRepository) DeleteByServiceID(serviceID string, statsType string) error {
	logger.Info("删除服务资源使用统计: serviceID=%s, statsType=%s", serviceID, statsType)

	query := `DELETE FROM service_resource_usage WHERE service_id = ? AND stats_type = ?`

	result, err := r.db.Exec(query, serviceID, statsType)
	if err != nil {
		logger.Error("删除服务资源使用统计失败: %v", err)
		return fmt.Errorf("删除服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取删除行数失败: %v", err)
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	logger.Info("成功删除服务资源使用统计: serviceID=%s, statsType=%s, rowsAffected=%d", serviceID, statsType, rowsAffected)
	return nil
}

// DeleteAllByServiceID 删除指定服务的所有资源使用统计
func (r *ServiceResourceUsageRepository) DeleteAllByServiceID(serviceID string) error {
	logger.Info("删除服务所有资源使用统计: serviceID=%s", serviceID)

	query := `DELETE FROM service_resource_usage WHERE service_id = ?`

	result, err := r.db.Exec(query, serviceID)
	if err != nil {
		logger.Error("删除服务所有资源使用统计失败: %v", err)
		return fmt.Errorf("删除服务所有资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取删除行数失败: %v", err)
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	logger.Info("成功删除服务所有资源使用统计: serviceID=%s, rowsAffected=%d", serviceID, rowsAffected)
	return nil
}

// GetByStatsType 获取指定统计类型的所有记录
func (r *ServiceResourceUsageRepository) GetByStatsType(statsType string, limit int, offset int) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取指定统计类型的资源使用统计: statsType=%s, limit=%d, offset=%d", statsType, limit, offset)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       container_count, uptime_seconds, last_access_time, stats_type,
		       collection_method, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE stats_type = ?
		ORDER BY updated_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := r.db.Query(query, statsType, limit, offset)
	if err != nil {
		logger.Error("查询指定统计类型的资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询指定统计类型的资源使用统计失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var lastAccessTime sql.NullTime

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic, &usage.ContainerCount,
			&usage.UptimeSeconds, &lastAccessTime, &usage.StatsType,
			&usage.CollectionMethod, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
		)
		if err != nil {
			logger.Error("扫描资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描资源使用统计失败: %w", err)
		}

		if lastAccessTime.Valid {
			usage.LastAccessTime = &lastAccessTime.Time
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历资源使用统计结果失败: %w", err)
	}

	logger.Info("成功获取指定统计类型的资源使用统计: statsType=%s, count=%d", statsType, len(usages))
	return usages, nil
}

// GetRecentUpdated 获取最近更新的资源使用统计
func (r *ServiceResourceUsageRepository) GetRecentUpdated(hours int, limit int) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取最近更新的资源使用统计: hours=%d, limit=%d", hours, limit)

	cutoffTime := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       container_count, uptime_seconds, last_access_time, stats_type,
		       collection_method, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE updated_at >= ?
		ORDER BY updated_at DESC
		LIMIT ?
	`

	rows, err := r.db.Query(query, cutoffTime, limit)
	if err != nil {
		logger.Error("查询最近更新的资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询最近更新的资源使用统计失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var lastAccessTime sql.NullTime

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic, &usage.ContainerCount,
			&usage.UptimeSeconds, &lastAccessTime, &usage.StatsType,
			&usage.CollectionMethod, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
		)
		if err != nil {
			logger.Error("扫描最近更新的资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描最近更新的资源使用统计失败: %w", err)
		}

		if lastAccessTime.Valid {
			usage.LastAccessTime = &lastAccessTime.Time
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历最近更新的资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历最近更新的资源使用统计结果失败: %w", err)
	}

	logger.Info("成功获取最近更新的资源使用统计: hours=%d, count=%d", hours, len(usages))
	return usages, nil
}

// GetTopResourceUsage 获取资源使用量最高的服务
func (r *ServiceResourceUsageRepository) GetTopResourceUsage(resourceType string, statsType string, limit int) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取资源使用量最高的服务: resourceType=%s, statsType=%s, limit=%d", resourceType, statsType, limit)

	var orderBy string
	switch resourceType {
	case "cpu":
		orderBy = "cpu_usage DESC"
	case "memory":
		orderBy = "memory_usage DESC"
	case "disk":
		orderBy = "disk_usage DESC"
	case "oss_disk":
		orderBy = "oss_disk_usage DESC"
	case "oss_traffic":
		orderBy = "oss_network_traffic DESC"
	case "total_traffic":
		orderBy = "(network_in_traffic + network_out_traffic + oss_network_traffic) DESC"
	case "total_disk":
		orderBy = "(disk_usage + oss_disk_usage) DESC"
	default:
		return nil, fmt.Errorf("不支持的资源类型: %s", resourceType)
	}

	query := fmt.Sprintf(`
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, network_in_traffic, network_out_traffic,
		       container_count, uptime_seconds, last_access_time, stats_type,
		       collection_method, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE stats_type = ?
		ORDER BY %s
		LIMIT ?
	`, orderBy)

	rows, err := r.db.Query(query, statsType, limit)
	if err != nil {
		logger.Error("查询资源使用量最高的服务失败: %v", err)
		return nil, fmt.Errorf("查询资源使用量最高的服务失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var lastAccessTime sql.NullTime

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic, &usage.ContainerCount,
			&usage.UptimeSeconds, &lastAccessTime, &usage.StatsType,
			&usage.CollectionMethod, &usage.Metadata, &usage.CreatedAt, &usage.UpdatedAt,
		)
		if err != nil {
			logger.Error("扫描资源使用量最高的服务失败: %v", err)
			return nil, fmt.Errorf("扫描资源使用量最高的服务失败: %w", err)
		}

		if lastAccessTime.Valid {
			usage.LastAccessTime = &lastAccessTime.Time
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历资源使用量最高的服务结果失败: %v", err)
		return nil, fmt.Errorf("遍历资源使用量最高的服务结果失败: %w", err)
	}

	logger.Info("成功获取资源使用量最高的服务: resourceType=%s, statsType=%s, count=%d", resourceType, statsType, len(usages))
	return usages, nil
}

// GetStatsSummary 获取资源使用统计汇总
func (r *ServiceResourceUsageRepository) GetStatsSummary(statsType string) (*models.ResourceUsageSummary, error) {
	logger.Info("获取资源使用统计汇总: statsType=%s", statsType)

	query := `
		SELECT
			COUNT(*) as service_count,
			AVG(cpu_usage) as avg_cpu_usage,
			SUM(memory_usage) as total_memory_usage,
			SUM(disk_usage) as total_disk_usage,
			SUM(oss_disk_usage) as total_oss_disk_usage,
			SUM(oss_network_traffic) as total_oss_network_traffic,
			SUM(network_in_traffic) as total_network_in_traffic,
			SUM(network_out_traffic) as total_network_out_traffic,
			SUM(container_count) as total_container_count,
			MAX(cpu_usage) as max_cpu_usage,
			MAX(memory_usage) as max_memory_usage,
			MAX(disk_usage) as max_disk_usage,
			MAX(oss_disk_usage) as max_oss_disk_usage
		FROM service_resource_usage
		WHERE stats_type = ?
	`

	row := r.db.QueryRow(query, statsType)

	summary := &models.ResourceUsageSummary{StatsType: statsType}
	err := row.Scan(
		&summary.ServiceCount, &summary.AvgCPUUsage, &summary.TotalMemoryUsage,
		&summary.TotalDiskUsage, &summary.TotalOSSDiskUsage, &summary.TotalOSSNetworkTraffic,
		&summary.TotalNetworkInTraffic, &summary.TotalNetworkOutTraffic, &summary.TotalContainerCount,
		&summary.MaxCPUUsage, &summary.MaxMemoryUsage, &summary.MaxDiskUsage, &summary.MaxOSSDiskUsage,
	)

	if err != nil {
		logger.Error("获取资源使用统计汇总失败: %v", err)
		return nil, fmt.Errorf("获取资源使用统计汇总失败: %w", err)
	}

	logger.Info("成功获取资源使用统计汇总: statsType=%s, serviceCount=%d", statsType, summary.ServiceCount)
	return summary, nil
}
