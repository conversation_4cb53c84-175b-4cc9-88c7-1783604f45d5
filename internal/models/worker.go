package models

type DeployRecordW struct {
	ID              int64    `json:"id"`         // Auto-incremented primary key
	ServiceID       string   `json:"service_id"` // 用户层传递的id
	Name            string   `json:"name"`       // 名称
	ImageName       string   `json:"image_name"`
	ImageURL        string   `json:"image_url"`    // 镜像地址
	ServiceType     string   `json:"service_type"` // 服务类型
	DomainPrefix    string   `json:"domain_prefix"`
	DomainSuffix    string   `json:"domain_suffix"`
	NodeIP          string   `json:"node_ip"`          // 部署服务器IP
	HostIP          string   `json:"host_ip"`          // host节点的IP地址
	WorkerID        string   `json:"worker_id"`        // Worker ID
	Expiration      string   `json:"expiration"`       // YYYY-MM-DDTHH:mm:ss
	DurationSeconds int64    `json:"duration_seconds"` // 服务持续时间（秒）
	Status          string   `json:"status"`           // RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
	Labels          []string `json:"labels"`
	CustomerEnvs    []string `json:"customer_envs,omitempty"` // 用户自定义环境变量
	Ports           []string `json:"ports,omitempty"`         // 容器暴露的端口列表，如 ["8080", "9000"]
	PortsMapping    []string `json:"ports_mapping,omitempty"` // 端口映射列表，如 ["8080:30001", "9000:30002"]
	Remark          string   `json:"remark,omitempty"`
	CreatedAt       string   `json:"created_at,omitempty"`
	UpdatedAt       string   `json:"updated_at,omitempty"`
	VisitedAt       string   `json:"visited_at,omitempty"` // 最后访问时间，用于活动检测
	ApiReplica      int      `json:"api_replica"`
	ApiCpu          float64  `json:"api_cpu"`
	ApiMemory       int      `json:"api_memory"`
	AutoReplica     int      `json:"auto_replica"`
	AutoCpu         float64  `json:"auto_cpu"`
	AutoMemory      int      `json:"auto_memory"`
}

// WorkerDeployRequest 表示部署容器的请求参数
type WorkerDeployRequest struct {
	// 部署参数
	ServiceId       string   `json:"service_id"`
	ImageName       string   `json:"image_name"` // 镜像名称（服务的名称）
	ImageURL        string   `json:"image_url"`  // 镜像地址
	DomainPrefix    string   `json:"domain_prefix"`
	DomainSuffix    string   `json:"domain_suffix"`
	NodeIP          string   `json:"node_ip"` // 部署服务器IP
	HostIP          string   `json:"host_ip"` // host节点的IP地址
	Expiration      string   `json:"expiration"`
	DurationSeconds int64    `json:"duration_seconds"` // 服务持续时间（秒）
	ApiCpu          float64  `json:"api_cpu"`          // CPU核心数要求
	ApiMemory       int      `json:"api_memory"`       // 内存要求(MB)
	ApiReplica      int      `json:"api_replica"`
	AutoCpu         float64  `json:"auto_cpu"`
	AutoMemory      int      `json:"auto_memory"`
	AutoReplica     int      `json:"auto_replica"`
	Labels          []string `json:"labels"`                  // 容器标签
	CustomerEnvs    []string `json:"customer_envs,omitempty"` // 用户自定义环境变量
	Ports           []string `json:"ports,omitempty"`         // 容器暴露的端口列表，如 ["8080", "9000"]
	PortsMapping    []string `json:"ports_mapping,omitempty"` // 端口映射列表，如 ["8080:30001", "9000:30002"]
}

// WorkerResponse 表示Worker操作的响应
type WorkerResponse struct {
	Code int    `json:"code"` // 状态码
	Data any    `json:"data"` // 响应数据
	Msg  string `json:"msg"`  // 响应消息
}

// ServerStatusResponse 表示服务器状态响应
type ServerStatusResponse struct {
	Code int    `json:"code"` // 状态码
	Data any    `json:"data"` // 响应数据
	Msg  string `json:"msg"`  // 响应消息
}

// ServerMetrics 表示服务器指标
type ServerMetrics struct {
	CPU     CPUMetrics     `json:"cpu"`     // CPU指标
	Memory  MemoryMetrics  `json:"memory"`  // 内存指标
	Disk    DiskMetrics    `json:"disk"`    // 磁盘指标
	Network NetworkMetrics `json:"network"` // 网络指标
}

// CPUMetrics 表示CPU指标
type CPUMetrics struct {
	Usage       float64 `json:"usage"`       // CPU使用率
	Temperature float64 `json:"temperature"` // CPU温度
	Cores       int     `json:"cores"`       // CPU核心数
}

// MemoryMetrics 表示内存指标
type MemoryMetrics struct {
	Total     int64   `json:"total"`     // 总内存
	Used      int64   `json:"used"`      // 已使用内存
	Free      int64   `json:"free"`      // 空闲内存
	UsageRate float64 `json:"usageRate"` // 内存使用率
}

// DiskMetrics 表示磁盘指标
type DiskMetrics struct {
	Total     int64   `json:"total"`     // 总磁盘空间
	Used      int64   `json:"used"`      // 已使用磁盘空间
	Free      int64   `json:"free"`      // 空闲磁盘空间
	UsageRate float64 `json:"usageRate"` // 磁盘使用率
}

// NetworkMetrics 表示网络指标
type NetworkMetrics struct {
	RxBytes int64 `json:"rxBytes"` // 接收字节数
	TxBytes int64 `json:"txBytes"` // 发送字节数
}

// WorkerInfo 表示Worker节点信息
type WorkerInfo struct {
	ID           int64    `json:"id"`                  // Auto-incremented primary key
	WorkerID     string   `json:"workerId"`            // Worker ID (UUID)
	Name         string   `json:"name"`                // 节点名称
	ServerType   string   `json:"server_type"`         // 服务器类型
	Labels       []string `json:"labels,omitempty"`    // 节点标签
	Host         string   `json:"host"`                // 节点api地址
	HostIP       string   `json:"host_ip"`             // host节点的IP地址
	DomainSuffix string   `json:"domain_suffix"`       // 基础域名，如 ".bpmax.cn",
	Nodes        []Node   `json:"nodes,omitempty"`     // 节点列表
	Status       string   `json:"status"`              // 状态
	CreatedAt    string   `json:"createdAt,omitempty"` // 创建时间
	UpdatedAt    string   `json:"updatedAt,omitempty"` // 更新时间
}

// WorkerListResponse 表示Worker列表响应
type WorkerListResponse struct {
	Code    int          `json:"code"`    // 状态码
	Workers []WorkerInfo `json:"workers"` // Worker列表
	Msg     string       `json:"msg"`     // 响应消息
}

// RegisterWorkerRequest 表示注册Worker的请求
type RegisterWorkerRequest struct {
	Name         string   `json:"name"`        // 节点名称
	ServerType   string   `json:"server_type"` // 服务器类型
	Labels       []string `json:"labels"`      // 节点标签
	Host         string   `json:"host"`        // 节点主机地址
	HostIP       string   `json:"host_ip"`     // host节点的IP地址
	DomainSuffix string   `json:"domain_suffix"`
	Nodes        []Node   `json:"nodes"`            // 节点列表
	Status       string   `json:"status,omitempty"` // 状态
}

// UpdateWorkerRequest 表示更新Worker的请求
type UpdateWorkerRequest struct {
	WorkerID     string   `json:"workerId"`
	Name         string   `json:"name,omitempty"`        // 节点名称
	ServerType   string   `json:"server_type,omitempty"` // 服务器类型
	Labels       []string `json:"labels,omitempty"`      // 节点标签
	Host         string   `json:"host,omitempty"`        // 节点主机地址
	HostIP       string   `json:"host_ip,omitempty"`     // host节点的IP地址
	DomainSuffix string   `json:"domain_suffix,omitempty"`
	Nodes        []Node   `json:"nodes,omitempty"`  // 节点列表
	Status       string   `json:"status,omitempty"` // 状态
}

// Node 表示节点信息
type Node struct {
	IP              string  `json:"ip"` // 节点IP地址
	Instance        string  `json:"instance"`
	Memory          int64   `json:"memory"` // 内存大小(MB)
	Memory_use_rate float64 `json:"memory_use_rate"`
	CPU             float64 `json:"cpu"` // CPU核心数
	Cpu_use_rate    float64 `json:"cpu_use_rate"`
	Disk            int64   `json:"disk"` // 磁盘大小(MB)
	Disk_use_rate   float64 `json:"disk_use_rate"`
}

// ResourceCheckResponse 表示资源检查响应
type ResourceCheckResponse struct {
	Code int    `json:"code"` // 状态码
	Data any    `json:"data"` // 响应数据
	Msg  string `json:"msg"`  // 响应消息
}

// WorkerFilter 表示 Worker 查询筛选条件
type WorkerFilter struct {
	ServerType string   `json:"server_type,omitempty"` // 服务器类型
	Status     string   `json:"status,omitempty"`      // 状态
	Labels     []string `json:"labels,omitempty"`      // 标签
}

// ContainerStatus 表示容器状态信息
type ContainerStatus struct {
	ServiceID    string   `json:"service_id"`              // 服务ID
	NodeIP       string   `json:"node_ip"`                 // 节点IP
	Status       string   `json:"status"`                  // 状态
	ContainerID  string   `json:"container_id"`            // 容器ID
	StatusDetail string   `json:"status_detail"`           // 状态详情
	CPUUsage     string   `json:"cpu_usage"`               // CPU使用率
	MemoryUsage  string   `json:"memory_usage"`            // 内存使用率
	NetworkIO    string   `json:"network_io"`              // 网络IO
	DiskIO       string   `json:"disk_io"`                 // 磁盘IO
	InspectData  string   `json:"inspect_data"`            // 详细信息
	Message      string   `json:"message"`                 // 消息
	PortMappings []string `json:"port_mappings,omitempty"` // 端口映射列表，格式为 ["hostPort:containerPort", ...]
}

// DockerContainerInfo 表示Docker容器信息
type DockerContainerInfo struct {
	ContainerID string            `json:"containerId"` // 容器ID
	Image       string            `json:"image"`       // 镜像名称
	Status      string            `json:"status"`      // 容器状态
	Name        string            `json:"name"`        // 容器名称
	ServerIP    string            `json:"serverIP"`    // 服务器IP
	Labels      map[string]string `json:"labels"`      // 容器标签
}
