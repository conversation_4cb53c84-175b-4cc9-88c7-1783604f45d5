package models

import "github.com/gin-gonic/gin"

// ImageType 表示镜像类型
type ImageType struct {
	ID        int64    `json:"id"`                   // Auto-incremented primary key
	ImageName string   `json:"image_name"`           // 镜像名称
	ImageURL  string   `json:"image_url"`            // 镜像URL
	Ports     []string `json:"ports,omitempty"`      // 镜像暴露的端口
	Labels    []string `json:"labels,omitempty"`     // 镜像标签
	CreatedAt string   `json:"created_at,omitempty"` // 创建时间
	UpdatedAt string   `json:"updated_at,omitempty"` // 更新时间
}

// CreateImageTypeRequest 表示创建镜像类型的请求
type CreateImageTypeRequest struct {
	ImageName string   `json:"image_name" binding:"required"` // 镜像名称
	ImageURL  string   `json:"image_url" binding:"required"`  // 镜像URL
	Ports     []string `json:"ports,omitempty"`               // 镜像暴露的端口
	Labels    []string `json:"labels,omitempty"`              // 镜像标签
}

// UpdateImageTypeRequest 表示更新镜像类型的请求
type UpdateImageTypeRequest struct {
	ImageName string   `json:"image_name,omitempty"` // 镜像名称
	ImageURL  string   `json:"image_url,omitempty"`  // 镜像URL
	Ports     []string `json:"ports,omitempty"`      // 镜像暴露的端口
	Labels    []string `json:"labels,omitempty"`     // 镜像标签
}

// ImageTypeResponse 表示镜像类型响应
type ImageTypeResponse struct {
	Code int         `json:"code"` // 状态码
	Data interface{} `json:"data"` // 响应数据
	Msg  string      `json:"msg"`  // 响应消息
}

// ImageTypeListResponse 表示镜像类型列表响应
type ImageTypeListResponse struct {
	Code int `json:"code"` // 状态码
	Data struct {
		ImageList []ImageType `json:"image_list"` // 镜像类型列表
	} `json:"data"`
	Msg string `json:"msg"` // 响应消息
}

// BatchImageTypeResponse 表示批量操作镜像类型的响应
type BatchImageTypeResponse struct {
	Code int `json:"code"` // 状态码
	Data struct {
		SuccessCount int         `json:"success_count"` // 成功数量
		FailedCount  int         `json:"failed_count"`  // 失败数量
		Results      []ImageType `json:"results"`       // 成功创建的镜像类型列表
		Errors       []string    `json:"errors"`        // 失败的错误信息列表
	} `json:"data"`
	Msg string `json:"msg"` // 响应消息
}

// ToResponse 将 ImageType 转换为响应格式
func (i *ImageType) ToResponse() gin.H {
	return gin.H{
		"id":         i.ID,
		"image_name": i.ImageName,
		"image_url":  i.ImageURL,
		"ports":      i.Ports,
		"labels":     i.Labels,
		"created_at": i.CreatedAt,
		"updated_at": i.UpdatedAt,
	}
}
