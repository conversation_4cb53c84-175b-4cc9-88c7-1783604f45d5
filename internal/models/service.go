package models

type ServiceInfo struct {
	ServiceID    string   `json:"service_id"`
	DomainPrefix string   `json:"domain_prefix"`
	DomainSuffix string   `json:"domain_suffix"`
	ImageName    string   `json:"image_name"`
	Expiration   string   `json:"expiration"`
	ServiceType  string   `json:"service_type"`
	Labels       []string `json:"labels"`
	CustomerEnvs []string `json:"customer_envs,omitempty"` // 用户自定义环境变量
	APIReplica   int      `json:"api_replica"`
	APICPU       float64  `json:"api_cpu"`
	APIMemory    int      `json:"api_memory"`
	AutoReplica  int      `json:"auto_replica"`
	AutoCPU      float64  `json:"auto_cpu"`
	AutoMemory   int      `json:"auto_memory"`
}
type CreateServiceRequest struct {
	ServiceInfo ServiceInfo `json:"serviceInfo"`
}

// UpdateServiceRequest 表示更新服务的请求，仅包含必要的字段
type UpdateServiceRequest struct {
	ServiceInfo UpdateServiceInfo `json:"serviceInfo"`
}

// UpdateServiceInfo 表示更新服务的信息，仅包含可更新的字段
type UpdateServiceInfo struct {
	ServiceID string `json:"service_id"` // 服务ID（必需）
	ImageName string `json:"image_name"` // 新的镜像名（必需）
}

// 服务部署相关模型
type DeployServiceRequest struct {
	ServiceID   string            `json:"serviceId"`   // 服务ID
	Environment string            `json:"environment"` // 环境
	Version     string            `json:"version"`     // 版本
	Config      map[string]string `json:"config"`      // 配置
	// 部署参数
	Image         string            `json:"image"`                  // 容器镜像
	ServerIP      string            `json:"serverIP"`               // 部署服务器IP
	CPUCores      float64           `json:"cpuCores"`               // CPU核心数要求
	MemoryMB      float64           `json:"memoryMB"`               // 内存要求(MB)
	Labels        map[string]string `json:"labels"`                 // 容器标签
	CustomerEnvs  []string          `json:"customerEnvs,omitempty"` // 用户自定义环境变量
	ContainerName string            `json:"containerName"`          // 容器名称
}

// 通用响应模型
type ServiceResponse struct {
	Code int    `json:"code"`
	Data any    `json:"data"`
	Msg  string `json:"msg"`
}

type DeployRecord struct {
	ID              int64    `json:"id"`         // Auto-incremented primary key
	ServiceID       string   `json:"service_id"` // 用户层传递的id
	Name            string   `json:"name"`       // 名称
	ImageName       string   `json:"image_name"`
	ImageURL        string   `json:"image_url"`    // 镜像地址
	ServiceType     string   `json:"service_type"` // 服务类型
	DomainPrefix    string   `json:"domain_prefix"`
	DomainSuffix    string   `json:"domain_suffix"`
	NodeIP          string   `json:"node_ip"`          // 部署服务器IP
	HostIP          string   `json:"host_ip"`          // host节点的IP地址
	WorkerID        string   `json:"worker_id"`        // Worker ID
	Expiration      string   `json:"expiration"`       // YYYY-MM-DDTHH:mm:ss
	DurationSeconds int64    `json:"duration_seconds"` // 服务持续时间（秒）
	Status          string   `json:"status"`           // RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
	Labels          []string `json:"labels"`
	CustomerEnvs    []string `json:"customer_envs,omitempty"` // 用户自定义环境变量
	Ports           []string `json:"ports,omitempty"`         // 容器暴露的端口列表，如 ["8080", "9000"]
	PortsMapping    []string `json:"ports_mapping,omitempty"` // 端口映射列表，如 ["8080:30001", "9000:30002"]
	Remark          string   `json:"remark,omitempty"`
	CreatedAt       string   `json:"created_at,omitempty"`
	UpdatedAt       string   `json:"updated_at,omitempty"`
	ApiReplica      int      `json:"api_replica"`
	ApiCpu          float64  `json:"api_cpu"`
	ApiMemory       int      `json:"api_memory"`
	AutoReplica     int      `json:"auto_replica"`
	AutoCpu         float64  `json:"auto_cpu"`
	AutoMemory      int      `json:"auto_memory"`
}

// DeployRecordListRequest 表示获取部署记录列表的请求参数
type DeployRecordListRequest struct {
	Page       int      `json:"page" form:"page"`               // 当前页码，从1开始
	PageSize   int      `json:"page_size" form:"page_size"`     // 每页记录数
	ServiceIDs []string `json:"service_ids" form:"service_ids"` // 服务ID列表，用于过滤
}

// DeployRecordListResponse 表示部署记录列表的响应
type DeployRecordListResponse struct {
	Total    int                `json:"total"`     // 总记录数
	Page     int                `json:"page"`      // 当前页码
	PageSize int                `json:"page_size"` // 每页记录数
	Records  []DeployRecordItem `json:"records"`   // 记录列表
}

// DeployRecordItem 表示部署记录列表中的单个记录
type DeployRecordItem struct {
	ID              int64    `json:"id"`
	ServiceID       string   `json:"service_id"`
	Name            string   `json:"name"`
	DomainPrefix    string   `json:"domain_prefix"`
	DomainSuffix    string   `json:"domain_suffix"`
	ImageName       string   `json:"image_name"`
	ImageURL        string   `json:"image_url"`
	ServiceType     string   `json:"service_type"`
	Expiration      string   `json:"expiration"`
	DurationSeconds int64    `json:"duration_seconds"` // 服务持续时间（秒）
	Status          string   `json:"status"`
	Labels          []string `json:"labels"`
	CustomerEnvs    []string `json:"customer_envs,omitempty"`
	WorkerID        string   `json:"worker_id"`
	NodeIP          string   `json:"node_ip"`
	HostIP          string   `json:"host_ip"`
	ApiReplica      int      `json:"api_replica"`
	ApiCpu          float64  `json:"api_cpu"`
	ApiMemory       int      `json:"api_memory"`
	AutoReplica     int      `json:"auto_replica"`
	AutoCpu         float64  `json:"auto_cpu"`
	AutoMemory      int      `json:"auto_memory"`
	CreatedAt       string   `json:"created_at"`
	UpdatedAt       string   `json:"updated_at"`
}
