package service

import (
	"context"
	"fmt"
	"log"
	"sort"
	"sync"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// StartQueueProcessor 启动队列处理器，使用独立的 goroutine 处理队列和更新状态
func (s *Service) StartQueueProcessor(ctx context.Context) {
	// 使用互斥锁保护状态检查和修改
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	// 检查是否已经在运行
	if s.queueProcessorRunning {
		log.Println("Queue processor is already running, ignoring start request")
		return
	}

	log.Println("Starting queue processor...")

	// 创建可取消的上下文
	processorCtx, cancel := context.WithCancel(ctx)
	s.queueProcessorCancel = cancel
	s.queueProcessorRunning = true

	// 启动一个独立的协程来管理队列处理和状态更新
	go func() {
		// 创建一个等待组，用于等待所有 goroutine 完成
		var wg sync.WaitGroup

		// 启动队列处理 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 队列处理器每 1 分钟运行一次
			queueTicker := time.NewTicker(1 * time.Minute)
			defer queueTicker.Stop()

			// 立即处理一次队列
			s.processQueue(processorCtx)

			// 然后每 3 分钟处理一次
			for {
				select {
				case <-queueTicker.C:
					s.processQueue(processorCtx)
				case <-processorCtx.Done():
					log.Println("Queue processor stopped")
					return
				}
			}
		}()

		// 启动状态更新 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 状态更新器每 2 分钟运行一次
			statusTicker := time.NewTicker(2 * time.Minute)
			defer statusTicker.Stop()

			// 立即更新一次状态
			s.updateServiceStatus(processorCtx)

			// 然后每 2 分钟更新一次
			for {
				select {
				case <-statusTicker.C:
					s.updateServiceStatus(processorCtx)
				case <-processorCtx.Done():
					log.Println("Status updater stopped")
					return
				}
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			// 创建一个停止通道
			stopChan := make(chan struct{})
			// 启动一个独立的 goroutine 来处理停止信号
			go func() {
				<-processorCtx.Done()
				close(stopChan)
			}()
			// 立即执行一次应用资源检查
			s.checkResourceUsage(processorCtx)

			// 启动定时检查
			s.startResourceUsageChecker(processorCtx, stopChan)

			log.Println("Application resoureuage checker stopped")
		}()

		// 等待上下文取消
		<-processorCtx.Done()
		log.Println("Queue processor context canceled, waiting for goroutines to complete")

		// 等待所有 goroutine 完成
		wg.Wait()

		// 更新状态标志
		s.queueProcessorMu.Lock()
		s.queueProcessorRunning = false
		s.queueProcessorCancel = nil
		s.queueProcessorMu.Unlock()

		log.Println("Queue processor stopped completely")
	}()
}

// StopQueueProcessor 停止队列处理器
func (s *Service) StopQueueProcessor() {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	if !s.queueProcessorRunning {
		log.Println("Queue processor is not running, ignoring stop request")
		return
	}

	log.Println("Stopping queue processor...")

	// 调用取消函数来停止处理器
	if s.queueProcessorCancel != nil {
		s.queueProcessorCancel()
		// 状态更新会在 goroutine 退出时完成
	}
}

// IsQueueProcessorRunning 返回队列处理器是否正在运行
func (s *Service) IsQueueProcessorRunning() bool {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	return s.queueProcessorRunning
}

// processQueue 处理队列中的部署记录
func (s *Service) processQueue(ctx context.Context) {
	log.Println("Processing deployment queue...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil {
		log.Println("Database connection not available, skipping queue processing")
		return
	}

	// 获取队列中的下一条记录
	record, err := s.deployRecordRepo.GetNextQueueingRecord()
	if err != nil {
		log.Printf("No queueing records found or error: %v", err)
		return
	}

	log.Printf("Processing deployment record: %s (%s)", record.ServiceID, record.Name)

	// 获取全部同类型、可用的 worker
	workers, err := s.selectAvailableWorker(&record)
	if err != nil {
		// 如果 selectAvailableWorker 已经更新了记录状态，这里不需要再次更新
		// 否则，保持状态不变
		log.Printf("Failed to select available worker: %v", err)
		return
	}

	// 获取部署记录中的资源需求
	// 处理 API 服务的资源需求
	apiReplica := record.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1
		log.Printf("No API replica specified, using default value: 1")
	}
	apiCPU := record.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求
	apiMemory := record.ApiMemory * apiReplica    // API 服务总内存需求

	// 处理 Auto 服务的资源需求
	autoReplica := record.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
		log.Printf("No Auto replica specified, using default value: 0")
	}
	autoCPU := record.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求
	autoMemory := record.AutoMemory * autoReplica    // Auto 服务总内存需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU          // 总 CPU 需求
	totalMemory := apiMemory + autoMemory // 总内存需求

	log.Printf("API service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		apiCPU, record.ApiCpu, apiReplica, apiMemory, record.ApiMemory, apiReplica)
	log.Printf("Auto service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		autoCPU, record.AutoCpu, autoReplica, autoMemory, record.AutoMemory, autoReplica)
	log.Printf("Total deployment requires: CPU=%.2f cores, Memory=%dMB",
		totalCPU, totalMemory)

	// 定义一个结构体来存储节点信息和资源利用率
	type NodeCandidate struct {
		Worker        models.WorkerInfo // Worker 信息
		NodeIP        string            // 节点 IP
		ResourceUsage float64           // 部署后的资源利用率
		CPUUsage      float64           // 部署后的 CPU 利用率
		MemoryUsage   float64           // 部署后的内存利用率
	}

	// 用于存储所有满足条件的节点
	var candidates []NodeCandidate

	// 遍历所有可用的 worker
	for _, worker := range workers {
		// 从数据库获取 worker 的详细信息
		workerInfo, err := s.workerRepo.GetByID(worker.WorkerID)
		if err != nil {
			log.Printf("Failed to get worker details from database: %v", err)
			continue // 跳过这个 worker，尝试下一个
		}

		log.Printf("Checking worker: %s (%s) with %d nodes",
			workerInfo.Name, workerInfo.WorkerID, len(workerInfo.Nodes))

		// 遍历 worker 的所有节点
		for _, node := range workerInfo.Nodes {
			// 计算节点的可用资源
			// 注意：CPU 使用率和内存使用率是百分比值 (0-1)
			availableCPU := node.CPU*(1-node.Cpu_use_rate) - 0.5
			availableMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - 500

			log.Printf("Node %s has: Total CPU=%v cores (usage: %.2f%%), Available CPU=%.2f cores",
				node.IP, node.CPU, node.Cpu_use_rate, availableCPU)
			log.Printf("Node %s has: Total Memory=%vMB (usage: %.2f%%), Available Memory=%vMB",
				node.IP, node.Memory, node.Memory_use_rate, availableMemory)

			// 判断节点是否有足够的资源来满足部署需求
			if availableCPU >= totalCPU && availableMemory >= totalMemory {
				// 计算部署后的资源利用率
				// 资源利用率 = 已使用资源 / 总资源
				// 已使用资源 = 当前已使用 + 新部署需要的资源

				// 计算部署后的 CPU 利用率
				newCPUUsage := (node.CPU*node.Cpu_use_rate + totalCPU) / node.CPU

				// 计算部署后的内存利用率
				newMemoryUsage := (float64(node.Memory)*node.Memory_use_rate + float64(totalMemory)) / float64(node.Memory)

				// 计算综合利用率（CPU 和内存的平均值）
				newResourceUsage := (newCPUUsage + newMemoryUsage) / 2

				log.Printf("Node %s after deployment: CPU usage=%.2f%%, Memory usage=%.2f%%, Average usage=%.2f%%",
					node.IP, newCPUUsage*100, newMemoryUsage*100, newResourceUsage*100)

				// 将满足条件的节点添加到候选列表
				candidates = append(candidates, NodeCandidate{
					Worker:        workerInfo,
					NodeIP:        node.IP,
					ResourceUsage: newResourceUsage,
					CPUUsage:      newCPUUsage,
					MemoryUsage:   newMemoryUsage,
				})

				log.Printf("Added node %s to candidates list with resource usage %.2f%%",
					node.IP, newResourceUsage*100)
			} else {
				log.Printf("Node %s does not have enough resources. Required: CPU=%.2f, Memory=%d; Available: CPU=%.2f, Memory=%d",
					node.IP, totalCPU, totalMemory, availableCPU, availableMemory)
			}
		}
	}

	// 如果没有找到合适的节点，更新记录状态为失败并返回
	if len(candidates) == 0 {
		log.Printf("No suitable node found for deployment %s", record.ServiceID)
		// 不需要其他处理，继续排队等待
		return
	}

	// 按照资源利用率排序（从低到高）
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].ResourceUsage < candidates[j].ResourceUsage
	})

	// 输出所有候选节点的信息
	log.Printf("Found %d suitable nodes for deployment:", len(candidates))
	for i, candidate := range candidates {
		log.Printf("  %d. Worker=%s, Node=%s, Resource usage=%.2f%%, CPU usage=%.2f%%, Memory usage=%.2f%%",
			i+1, candidate.Worker.WorkerID, candidate.NodeIP,
			candidate.ResourceUsage*100, candidate.CPUUsage*100, candidate.MemoryUsage*100)
	}

	// 遍历候选节点，检查 Worker 的健康状态
	var selectedCandidate *NodeCandidate

	for i, candidate := range candidates {
		// 创建 Worker 客户端，调用健康检查接口
		workerClient := NewWorkerHTTPClient(candidate.Worker.Host)

		// 调用 Worker 的健康检查接口
		log.Printf("Checking health status of worker %s (%s)",
			candidate.Worker.WorkerID, candidate.Worker.Host)

		healthResp, err := workerClient.HealthCheck(ctx)
		if err != nil {
			log.Printf("Failed to check health status of worker %s: %v",
				candidate.Worker.WorkerID, err)
			continue // 如果调用失败，尝试下一个候选节点
		}

		// 检查 Worker 的状态
		statusData, ok := healthResp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid health response format from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果响应格式不正确，尝试下一个候选节点
		}

		status, ok := statusData["status"].(string)
		if !ok {
			log.Printf("Invalid status format in health response from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果状态格式不正确，尝试下一个候选节点
		}

		log.Printf("Worker %s health status: %s", candidate.Worker.WorkerID, status)

		// 如果 Worker 状态为 AVAILABLE，选择该节点
		if status == "AVAILABLE" {
			selectedCandidate = &candidates[i]
			log.Printf("Selected worker %s with AVAILABLE status",
				candidate.Worker.WorkerID)
			break
		} else {
			log.Printf("Worker %s is in FREEZE status, checking next candidate",
				candidate.Worker.WorkerID)
		}
	}

	// 如果没有找到可用的 Worker，返回错误
	if selectedCandidate == nil {
		log.Printf("No worker with AVAILABLE status found for deployment %s",
			record.ServiceID)
		// 不需要其他处理，继续排队等待
		return
	}

	log.Printf("Final node selection: Worker=%s, Node=%s, Resource usage after deployment=%.2f%%",
		selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP, selectedCandidate.ResourceUsage*100)

	// 获取选中的 worker 和节点
	selectedWorker := selectedCandidate.Worker
	selectedNodeIP := selectedCandidate.NodeIP
	selectedLabels := selectedCandidate.Worker.Labels

	// 更新部署记录的 worker ID 和节点 IP
	record.WorkerID = selectedWorker.WorkerID
	record.NodeIP = selectedNodeIP
	record.Status = "PROCESSING" // 更新状态为处理中
	record.HostIP = selectedWorker.HostIP

	// 获取 worker 的 domain_suffix
	if record.DomainSuffix == "" && selectedWorker.DomainSuffix != "" {
		record.DomainSuffix = selectedWorker.DomainSuffix
		log.Printf("Using domain suffix from worker: %s", record.DomainSuffix)
	}

	// 通过 record.ImageName 获取数据库中对应的 imageURL 和 ports 数据
	image, err := s.imageTypeRepo.GetByName(record.ImageName)
	if err != nil {
		log.Printf("Failed to get image information for image name %s: %v", record.ImageName, err)
		return
	}

	// 记录获取到的镜像信息
	log.Printf("Found image for %s: URL=%s, Ports=%v", record.ImageName, image.ImageURL, image.Ports)

	// 创建 worker 客户端
	workerClient := NewWorkerHTTPClient(selectedWorker.Host)

	// 创建基础标签
	baseLabels := []string{
		"system=ops-system",
		"service-type=" + record.ServiceType,
	}

	// 将 image.Labels 聚合到标签中，并进行排重处理
	allLabels := utils.MergeLabelsWithDeduplication(baseLabels, selectedLabels, record.Labels, image.Labels)
	record.Labels = allLabels

	// 创建部署请求
	workerReq := &models.WorkerDeployRequest{
		ImageName:       record.ImageName, // 使用镜像类型作为镜像名称
		ImageURL:        image.ImageURL,
		DomainPrefix:    record.DomainPrefix, // 从 deploy_record 表获取的域名前缀
		DomainSuffix:    record.DomainSuffix, // 从 workers 表获取的域名后缀
		NodeIP:          selectedNodeIP,      // 使用选中的节点 IP
		HostIP:          record.HostIP,
		Expiration:      record.Expiration,      // 设置过期时间
		DurationSeconds: record.DurationSeconds, // 设置持续时间（秒）
		ApiCpu:          record.ApiCpu,          // 使用 API CPU 作为 CPU 核心数
		ApiMemory:       record.ApiMemory,       // 使用 API 内存作为内存大小
		ApiReplica:      record.ApiReplica,
		AutoCpu:         record.AutoCpu,
		AutoMemory:      record.AutoMemory,
		AutoReplica:     record.AutoReplica,
		ServiceId:       record.ServiceID,    // 使用服务 ID 作为容器名称
		CustomerEnvs:    record.CustomerEnvs, // 用户自定义环境变量
		Ports:           image.Ports,         // 从 image_type 表获取的容器端口列表
		Labels:          allLabels,           // 添加聚合后的标签
	}

	// 记录所有标签
	log.Printf("Using %d labels for deployment: %v", len(allLabels), allLabels)

	// 记录将要发送的域名和端口信息
	log.Printf("Sending domain information to worker: prefix=%s, suffix=%s",
		record.DomainPrefix, record.DomainSuffix)
	log.Printf("Sending ports information to worker: %v", image.Ports)

	// 调用 worker 的 DeployContainer 函数
	log.Printf("Sending deployment request to worker %s, node %s", selectedWorker.WorkerID, selectedNodeIP)
	workerResp, err := workerClient.DeployContainer(ctx, workerReq)
	if err != nil {
		log.Printf("Failed to deploy service: %v", err)
		return
	}

	// 更新service record
	if err = s.deployRecordRepo.Update(record); err != nil {
		log.Printf("Failed to update deploy record with selected worker and node: %v", err)
		return
	}

	// 检查响应
	if workerResp.Code != 200 {
		log.Printf("Worker returned error: %s", workerResp.Msg)
		return
	}

	// 更新记录状态为运行中
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
	}

	log.Printf("Successfully deployed service %s", record.ServiceID)

}

// selectAvailableWorker 选择可用的 worker 列表
func (s *Service) selectAvailableWorker(record *models.DeployRecord) ([]models.WorkerInfo, error) {
	if s.workerRepo == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// 如果 record.WorkerID 不为空，检查 workers 表里是否有这个 worker
	if record.WorkerID != "" {
		log.Printf("Record has WorkerID: %s, checking if worker exists", record.WorkerID)

		// 尝试从数据库获取指定的 worker
		worker, err := s.workerRepo.GetByID(record.WorkerID)
		if err == nil && worker.WorkerID == record.WorkerID {
			// worker 存在直接返回这个 worker
			log.Printf("Found existing worker %s for record %s", worker.WorkerID, record.ServiceID)
			return []models.WorkerInfo{worker}, nil
		}

		// worker 不存在，记录到 remark 字段并标记为 FAILED
		log.Printf("Worker %s not found, marking record as FAILED", record.WorkerID)
		record.Remark = fmt.Sprintf("Worker %s not found or not AVAILABLE", record.WorkerID)
		record.Status = "FAILED"

		// 更新数据库中的记录
		if updateErr := s.deployRecordRepo.Update(*record); updateErr != nil {
			log.Printf("Failed to update deploy record status: %v", updateErr)
		}

		return nil, fmt.Errorf("worker %s not found or not AVAILABLE", record.WorkerID)
	}

	// 获取所有与部署记录服务类型匹配的 worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{
		ServerType: record.ServiceType,
		Status:     "AVAILABLE", // 只选择活跃状态的 worker
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get workers from database: %w", err)
	}

	// 检查是否有可用的 worker
	if len(workers) == 0 {
		return nil, fmt.Errorf("no AVAILABLE workers available for service type: %s", record.ServiceType)
	}

	log.Printf("Found %d AVAILABLE workers for service type: %s", len(workers), record.ServiceType)
	return workers, nil
}

// updateServiceStatus 更新服务状态
//  1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
//  2. 按数据中 worker_id 字段进行分组
//     a. 通过 worker_id 从 workers 表获取 host
//     b. 把同组的 service_id 用逗号拼接到一起
//  3. 调用 worker 层接口 worker.GET("/status", workerCtrl.GetDeployStatus) 获取相关数据
func (s *Service) updateServiceStatus(ctx context.Context) error {
	log.Println("Updating service status...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil || s.workerRepo == nil {
		log.Println("Database connection not available, skipping service status update")
		return fmt.Errorf("database connection not available")
	}

	// 1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
	records, err := s.deployRecordRepo.GetRecordsByStatus([]string{"PROCESSING", "RUNNING"})
	if err != nil {
		log.Printf("Failed to get deploy records: %v", err)
		return fmt.Errorf("failed to get deploy records: %w", err)
	}

	if len(records) == 0 {
		log.Println("No PROCESSING or RUNNING records found, skipping service status update")
		return nil
	}

	log.Printf("Found %d PROCESSING or RUNNING records", len(records))

	// 2. 按数据中 worker_id 字段进行分组
	workerGroups := make(map[string][]models.DeployRecord)
	for _, record := range records {
		if record.WorkerID != "" {
			workerGroups[record.WorkerID] = append(workerGroups[record.WorkerID], record)
		}
	}

	if len(workerGroups) == 0 {
		log.Println("No records with valid worker_id found, skipping service status update")
		return nil
	}

	// 遍历每个 worker 组
	for workerID, workerRecords := range workerGroups {
		// a. 通过 worker_id 从 workers 表获取 host
		workerInfo, err := s.workerRepo.GetByID(workerID)
		if err != nil {
			log.Printf("Failed to get worker info for worker_id %s: %v", workerID, err)
			continue
		}

		if workerInfo.Host == "" {
			log.Printf("Worker %s has no host information, skipping", workerID)
			continue
		}

		// b. 把同组的 service_id 用逗号拼接到一起
		var serviceIDs []string
		for _, record := range workerRecords {
			serviceIDs = append(serviceIDs, record.ServiceID)
		}

		log.Printf("Querying status for %d services from worker %s (%s)", len(serviceIDs), workerID, workerInfo.Host)

		// 3. 调用 worker 层接口获取相关数据
		workerClient := NewWorkerHTTPClient(workerInfo.Host)
		resp, err := workerClient.GetDeployStatus(ctx, serviceIDs)
		if err != nil {
			log.Printf("Failed to get deploy status from worker %s: %v", workerID, err)
			continue
		}

		// 处理响应
		if resp.Code != 200 {
			log.Printf("Worker %s returned error: %s", workerID, resp.Msg)
			continue
		}

		// 解析响应数据
		data, ok := resp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid response format from worker %s", workerID)
			continue
		}

		// 统一处理记录，将单条记录转换为数组格式
		var recordsToProcess []map[string]interface{}

		// 检查是否是单条记录格式（直接包含 service_id 和 status）
		if serviceID, hasServiceID := data["service_id"].(string); hasServiceID {
			if status, hasStatus := data["status"].(string); hasStatus {
				// 将单条记录转换为数组格式
				recordsToProcess = []map[string]interface{}{
					{
						"service_id": serviceID,
						"status":     status,
					},
				}
			}
		} else if records, hasRecords := data["records"].([]interface{}); hasRecords {
			// 多条记录的情况，转换为统一格式
			for _, record := range records {
				if recordMap, isMap := record.(map[string]interface{}); isMap {
					recordsToProcess = append(recordsToProcess, recordMap)
				}
			}
		} else {
			log.Printf("Response from worker %s does not contain valid records", workerID)
			continue
		}

		// 处理所有记录
		for _, recordMap := range recordsToProcess {
			serviceID, ok := recordMap["service_id"].(string)
			if !ok {
				log.Printf("Invalid service_id format in record from worker %s", workerID)
				continue
			}

			status, ok := recordMap["status"].(string)
			if !ok {
				log.Printf("Invalid status format in record from worker %s", workerID)
				continue
			}

			// 这里添加判断，如果 status 的值为以下几种情况之一，才进行更新，否则跳过
			// RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			validStatuses := map[string]bool{
				"RUNNING":     true,
				"TERMINATION": true,
				"STOPPED":     true,
				"FAILED":      true,
			}

			if !validStatuses[status] {
				log.Printf("Skipping update for service %s with invalid status: %s", serviceID, status)
				continue
			}

			// 更新数据库中的状态
			if err := s.deployRecordRepo.UpdateStatus(serviceID, status); err != nil {
				log.Printf("Failed to update status for service %s: %v", serviceID, err)
			} else {
				log.Printf("Updated status for service %s to %s", serviceID, status)
			}
		}

		// 处理未找到的记录
		notFound, ok := data["not_found"].([]interface{})
		if ok && len(notFound) > 0 {
			for _, nfID := range notFound {
				serviceID, ok := nfID.(string)
				if !ok {
					continue
				}
				log.Printf("Service %s not found on worker %s", serviceID, workerID)

				// 将未找到的记录标记为 UNKNOWN 状态
				if err := s.deployRecordRepo.UpdateStatus(serviceID, "UNKNOWN"); err != nil {
					log.Printf("Failed to update status for not found service %s: %v", serviceID, err)
				}
			}
		}
	}

	log.Println("Service status update completed")
	return nil
}

// checkResourceUsage 检查所有运行中服务的资源使用情况
func (s *Service) checkResourceUsage(ctx context.Context) {
	log.Println("开始检查服务资源使用情况...")

	// 检查数据库连接
	if s.deployRecordRepo == nil {
		log.Println("数据库连接不可用，跳过资源使用检查")
		return
	}

	if s.resourceUsageRepo == nil {
		log.Println("资源使用统计仓库不可用，跳过资源使用检查")
		return
	}

	// 1. 从 deploy_record 表获取所有 status==RUNNING 的记录
	runningRecords, err := s.deployRecordRepo.GetRecordsByStatus([]string{"RUNNING"})
	if err != nil {
		log.Printf("获取运行中的部署记录失败: %v", err)
		return
	}

	if len(runningRecords) == 0 {
		log.Println("没有找到运行中的服务")
		return
	}

	log.Printf("找到 %d 个运行中的服务，开始检查资源使用情况", len(runningRecords))

	// 2. 依次调用 CheckOSSStatus 方法并更新资源使用统计
	for _, record := range runningRecords {
		log.Printf("检查服务 %s 的资源使用情况", record.ServiceID)

		// 调用 CheckOSSStatus 方法获取 OSS 状态信息
		err := s.CheckOSSStatus(ctx, record.ServiceID)
		if err != nil {
			log.Printf("获取服务 %s 的 OSS 状态失败: %v", record.ServiceID, err)
			continue
		}

		// 由于 CheckOSSStatus 方法目前只返回 error，我们需要重新实现 OSS 状态检查
		// 或者直接调用内部的 OSS 检查逻辑来获取数据
		ossData, err := s.getOSSStatusData(ctx, record.ServiceID)
		if err != nil {
			log.Printf("获取服务 %s 的 OSS 状态数据失败: %v", record.ServiceID, err)
			continue
		}

		// 3. 解析 OSS 状态响应并更新 service_resource_usage 表
		if err := s.updateResourceUsageFromOSSStatus(record.ServiceID, ossData); err != nil {
			log.Printf("更新服务 %s 的资源使用统计失败: %v", record.ServiceID, err)
			continue
		}

		log.Printf("成功更新服务 %s 的资源使用统计", record.ServiceID)
	}

	log.Printf("资源使用检查完成，处理了 %d 个服务", len(runningRecords))
}

// updateResourceUsageFromOSSStatus 从 OSS 状态响应中解析数据并更新资源使用统计表
func (s *Service) updateResourceUsageFromOSSStatus(serviceID string, ossData interface{}) error {
	// 解析 OSS 状态响应数据
	dataMap, ok := ossData.(map[string]interface{})
	if !ok {
		return fmt.Errorf("OSS 状态数据格式无效")
	}

	log.Printf("开始解析OSS数据，serviceID=%s", serviceID)
	log.Printf("OSS数据内容: %+v", dataMap)

	// 创建资源使用统计对象
	resourceUsage := &models.ServiceResourceUsage{
		ServiceID: serviceID,
	}

	// 解析 OSS API 统计数据
	if ossStatsAPI, exists := dataMap["oss_stats_api"]; exists {
		log.Printf("找到 oss_stats_api 数据")
		if apiStats, ok := ossStatsAPI.(map[string]interface{}); ok {
			log.Printf("API统计数据: %+v", apiStats)
			// 解析 OSS 磁盘使用量 (MB)
			if totalSizeMB, ok := apiStats["total_size_mb"].(float64); ok {
				resourceUsage.OSSDiskUsage = int64(totalSizeMB)
				log.Printf("从API解析到OSS磁盘使用量: %.2fMB -> %dMB", totalSizeMB, resourceUsage.OSSDiskUsage)
			} else {
				log.Printf("API数据中未找到 total_size_mb 字段")
			}

			// 解析 OSS 网络流量 (MB)
			if trafficMB, ok := apiStats["traffic_usage_mb"].(float64); ok {
				resourceUsage.OSSNetworkTraffic = trafficMB
				log.Printf("从API解析到OSS网络流量: %.2fMB", resourceUsage.OSSNetworkTraffic)
			} else {
				log.Printf("API数据中未找到 traffic_usage_mb 字段")
			}
		} else {
			log.Printf("oss_stats_api 数据格式错误")
		}
	} else {
		log.Printf("未找到 oss_stats_api 数据")
	}

	// 解析 OSS 配置信息作为元数据
	metadata := &models.ResourceUsageMetadata{}
	if ossConfig, exists := dataMap["oss_config"]; exists {
		if configMap, ok := ossConfig.(map[string]interface{}); ok {
			if bucket, ok := configMap["bucket"].(string); ok {
				metadata.OSSBucket = bucket
			}
			if endpoint, ok := configMap["endpoint"].(string); ok {
				metadata.OSSEndpoint = endpoint
			}
			if region, ok := configMap["region"].(string); ok {
				metadata.OSSRegion = region
			}
			if filePath, ok := configMap["file_path"].(string); ok {
				metadata.OSSFilePath = filePath
			}
		}
	}

	// 设置收集时间和数据源
	metadata.CollectionTime = time.Now()
	metadata.DataSources = []string{"oss_api"}

	// 如果有 OSSUtil 统计数据，也解析它
	if ossUtilStats, exists := dataMap["oss_stats_ossutil"]; exists {
		log.Printf("找到 oss_stats_ossutil 数据")
		if utilStats, ok := ossUtilStats.(map[string]interface{}); ok {
			log.Printf("OSSUtil统计数据: %+v", utilStats)
			// 优先使用 OSSUtil 的磁盘使用量数据（通常更准确）
			if totalSizeMB, ok := utilStats["total_size_mb"].(float64); ok {
				resourceUsage.OSSDiskUsage = int64(totalSizeMB)
				log.Printf("从OSSUtil解析到OSS磁盘使用量: %.2fMB -> %dMB", totalSizeMB, resourceUsage.OSSDiskUsage)
			} else {
				log.Printf("OSSUtil数据中未找到 total_size_mb 字段")
			}

			// 解析文件数量
			if objectCount, ok := utilStats["object_count"].(float64); ok {
				metadata.OSSFileCount = int64(objectCount)
				log.Printf("从OSSUtil解析到文件数量: %.0f", objectCount)
			} else {
				log.Printf("OSSUtil数据中未找到 object_count 字段")
			}

			// 解析执行时间
			if execTime, ok := utilStats["execution_time"].(string); ok {
				metadata.ExecutionTime = execTime
				log.Printf("从OSSUtil解析到执行时间: %s", execTime)
			}

			// 更新数据源
			metadata.DataSources = append(metadata.DataSources, "ossutil")
		} else {
			log.Printf("oss_stats_ossutil 数据格式错误")
		}
	} else {
		log.Printf("未找到 oss_stats_ossutil 数据")
	}

	// 设置元数据
	if err := resourceUsage.SetMetadata(metadata); err != nil {
		log.Printf("设置元数据失败: %v", err)
		// 不中断流程，继续更新其他数据
	}

	// 设置最后访问时间为当前时间
	now := time.Now()
	resourceUsage.LastAccessTime = &now

	// 检查是否已存在记录
	existingUsage, err := s.resourceUsageRepo.GetByServiceID(serviceID)
	if err != nil {
		return fmt.Errorf("检查现有资源使用记录失败: %w", err)
	}

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("创建服务 %s 的新资源使用记录", serviceID)
		if err := s.resourceUsageRepo.Create(resourceUsage); err != nil {
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}
	} else {
		// 记录存在，更新现有记录
		log.Printf("更新服务 %s 的资源使用记录", serviceID)

		// 保留现有的 CPU、内存、磁盘使用量等数据，只更新 OSS 相关数据
		log.Printf("更新前 - existingUsage.OSSDiskUsage: %d", existingUsage.OSSDiskUsage)
		log.Printf("更新前 - resourceUsage.OSSDiskUsage: %d", resourceUsage.OSSDiskUsage)

		existingUsage.OSSDiskUsage = resourceUsage.OSSDiskUsage
		existingUsage.OSSNetworkTraffic = resourceUsage.OSSNetworkTraffic
		existingUsage.LastAccessTime = resourceUsage.LastAccessTime
		existingUsage.Metadata = resourceUsage.Metadata

		log.Printf("更新后 - existingUsage.OSSDiskUsage: %d", existingUsage.OSSDiskUsage)

		if err := s.resourceUsageRepo.UpdateByServiceID(serviceID, existingUsage); err != nil {
			return fmt.Errorf("更新资源使用记录失败: %w", err)
		}
	}

	log.Printf("成功更新服务 %s 的资源使用统计: OSS磁盘=%dMB, OSS流量=%.2fMB",
		serviceID, resourceUsage.OSSDiskUsage, resourceUsage.OSSNetworkTraffic)

	return nil
}

// getOSSStatusData 获取 OSS 状态数据（内部方法，返回数据而不是错误）
func (s *Service) getOSSStatusData(ctx context.Context, serviceID string) (map[string]interface{}, error) {
	log.Printf("开始获取服务 %s 的OSS状态数据", serviceID)

	// 1. 从数据库获取部署记录
	if s.deployRecordRepo == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	record, err := s.deployRecordRepo.GetByID(serviceID)
	if err != nil {
		log.Printf("无法找到部署记录: %v", err)
		return nil, fmt.Errorf("无法找到部署记录: %w", err)
	}

	// 2. 构造 WorkerDeployRequest 以解析环境变量
	workerReq := &models.WorkerDeployRequest{
		ServiceId:    record.ServiceID,
		DomainPrefix: record.DomainPrefix,
		DomainSuffix: record.DomainSuffix,
		Labels:       record.Labels,
		CustomerEnvs: record.CustomerEnvs,
	}

	// 3. 使用 utils 中的方法解析环境变量
	_, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(workerReq)
	if err != nil {
		log.Printf("解析环境变量失败: %v", err)
		return nil, fmt.Errorf("解析环境变量失败: %w", err)
	}

	// 4. 从环境变量中解析OSS配置
	ossConfig, err := utils.ParseOSSConfigFromEnvMap(envMap)
	if err != nil {
		log.Printf("解析OSS配置失败: %v", err)
		return nil, fmt.Errorf("解析OSS配置失败: %w", err)
	}

	// 5. 测试OSS连接
	if err := utils.TestOSSConnection(ossConfig); err != nil {
		log.Printf("OSS连接测试失败: %v", err)
		return nil, fmt.Errorf("OSS连接测试失败: %w", err)
	}

	// 6. 获取OSS统计信息（使用API方式）
	ossStats, err := utils.GetBucketStats(ossConfig)
	if err != nil {
		log.Printf("获取OSS统计信息失败: %v", err)
		return nil, fmt.Errorf("获取OSS统计信息失败: %w", err)
	}

	// 7. 获取OSS磁盘使用量（使用OSSUtil工具）
	ossUtilStats, err := utils.GetOSSDiskUsage(ossConfig)
	if err != nil {
		log.Printf("获取OSSUtil磁盘使用量失败: %v", err)
		// 不中断流程，继续返回API统计结果，但记录错误
		log.Printf("将使用API统计结果代替OSSUtil结果")
		ossUtilStats = nil
	}

	log.Printf("OSS状态检查完成: 服务=%s, 文件数=%d, 总大小=%.2fMB",
		serviceID, ossStats.FileCount, ossStats.TotalSizeMB)

	// 8. 构造返回数据
	responseData := map[string]interface{}{
		"service_id": serviceID,
		"oss_config": map[string]interface{}{
			"bucket":    ossConfig.BucketName,
			"endpoint":  ossConfig.Endpoint,
			"region":    ossConfig.Region,
			"file_path": ossConfig.FilePath,
		},
		"oss_stats_api": ossStats, // API方式获取的统计信息
	}

	// 如果OSSUtil统计成功，添加到返回结果中
	if ossUtilStats != nil {
		responseData["oss_stats_ossutil"] = ossUtilStats
		log.Printf("OSSUtil统计成功: 对象数=%d, 总大小=%.2fMB, 执行时间=%s",
			ossUtilStats.ObjectCount, ossUtilStats.TotalSizeMB, ossUtilStats.ExecutionTime)
	} else {
		log.Printf("OSSUtil统计失败，只使用API统计结果")
	}

	return responseData, nil
}

func (s *Service) startResourceUsageChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting activity checker with 6 hours interval")

	// 创建一个 360 分钟的定时器
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			// 执行应用资源使用检查
			log.Println("Ticker triggered, performing resource usage check")
			s.checkResourceUsage(ctx)
		case <-stopChan:
			// 收到停止信号，立即退出
			log.Println("Received stop signal, stopping resource usage checker")
			return
		}
	}
}
