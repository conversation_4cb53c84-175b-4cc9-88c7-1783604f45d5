package service

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// Service represents the management node that interacts with users
type Service struct {
	// Database repositories
	db               *database.ServiceDB
	workerRepo       *database.WorkerRepository
	deployRecordRepo *database.DeployRecordRepository
	imageTypeRepo    *database.ImageTypeRepository

	// 队列处理器状态
	queueProcessorMu      sync.Mutex
	queueProcessorRunning bool
	queueProcessorCancel  context.CancelFunc
}

// ServiceConfig holds configuration for the service
// Deprecated: Use Config instead
type ServiceConfig struct {
	DBConfig database.ServiceDBConfig
}

// DefaultServiceConfig returns the default service configuration
// Deprecated: Use DefaultConfig instead
func DefaultServiceConfig() ServiceConfig {
	return ServiceConfig{
		DBConfig: database.DefaultServiceDBConfig(),
	}
}

// NewService creates a new service instance
func NewService() *Service {
	config := DefaultConfig()
	return NewServiceWithNewConfig(config)
}

// NewServiceWithConfig creates a new service instance with the given configuration
// Deprecated: Use NewServiceWithNewConfig instead
func NewServiceWithConfig(config ServiceConfig) *Service {
	// Convert old config to new config
	newConfig := Config{
		LogDir:  "log",
		DBPath:  config.DBConfig.DBPath,
		DBType:  "sqlite",
		APIPort: "8080",
	}

	return NewServiceWithNewConfig(newConfig)
}

// NewServiceWithNewConfig creates a new service instance with the given configuration
func NewServiceWithNewConfig(config Config) *Service {
	// Create service instance
	svc := &Service{
		queueProcessorMu:      sync.Mutex{},
		queueProcessorRunning: false,
		queueProcessorCancel:  nil,
	}

	// Initialize database if we're in service mode
	dbConfig := database.ServiceDBConfig{
		DBPath: config.DBPath,
	}

	db, err := database.NewServiceDB(dbConfig)
	if err != nil {
		log.Printf("Failed to initialize database: %v", err)
	} else {
		// Initialize schema
		if err := db.InitSchema(); err != nil {
			log.Printf("Failed to initialize database schema: %v", err)
		}

		// Initialize repositories
		svc.db = db
		svc.workerRepo = database.NewWorkerRepository(db)
		svc.deployRecordRepo = database.NewDeployRecordRepository(db)
		svc.imageTypeRepo = database.NewImageTypeRepository(db)

		// Start the queue processor
		svc.StartQueueProcessor(context.Background())
		log.Println("Queue processor started")

		// Start the worker monitor
		svc.StartWorkerMonitor(context.Background())
		log.Println("Worker monitor started")
	}

	return svc
}

// RestartService handles service restart requests from users
func (s *Service) RestartService(ctx context.Context, service_id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(service_id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 对于重启操作，不需要检查 Worker 状态
	// 重启操作不会增加资源占用，只是重新启动现有容器，即使 Worker 处于 FREEZE 状态也可以安全执行
	log.Printf("Restart operation for service %s on worker %s (status: %s)", record.ServiceID, workerID, workerInfo.Status)

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		ServiceId: record.ServiceID, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 RestartContainer 函数
	workerResp, err := worker.RestartContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 PROCESSING
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// UpdateService handles service update requests from users
// 当前仅支持更新镜像名和镜像地址，其他参数保持不变
func (s *Service) UpdateService(ctx context.Context, req *models.UpdateServiceRequest) (*models.ServiceResponse, error) {
	log.Printf("Starting service update for service %s with new image %s", req.ServiceInfo.ServiceID, req.ServiceInfo.ImageName)
	// Get deploy record from database
	var workerID string
	var record models.DeployRecord

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(req.ServiceInfo.ServiceID)
		if err == nil {
			workerID = record.WorkerID
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 对于升级操作，不需要检查 Worker 状态
	// 升级操作不会增加资源占用，只是替换镜像，即使 Worker 处于 FREEZE 状态也可以安全执行
	log.Printf("Update operation for service %s on worker %s (status: %s)", req.ServiceInfo.ServiceID, workerID, workerInfo.Status)

	// 通过 ImageName 获取 ImageURL、Labels 和 Ports（更新镜像相关信息）
	var imageURL string
	var imageLabels []string
	var imagePorts []string
	if req.ServiceInfo.ImageName != "" {
		// 查询 image_type 表获取 ImageURL、Labels 和 Ports
		imageType, err := s.imageTypeRepo.GetByName(req.ServiceInfo.ImageName)
		if err != nil {
			log.Printf("Failed to get image information for image name %s: %v", req.ServiceInfo.ImageName, err)
			return &models.ServiceResponse{
				Code: 400,
				Msg:  fmt.Sprintf("无法获取镜像信息: %v", err),
			}, fmt.Errorf("无法获取镜像信息: %w", err)
		}
		imageURL = imageType.ImageURL
		imageLabels = imageType.Labels
		imagePorts = imageType.Ports
		log.Printf("Found image information for %s: URL=%s, Labels=%v, Ports=%v",
			req.ServiceInfo.ImageName, imageURL, imageLabels, imagePorts)
	}

	// 创建基础标签
	baseLabels := []string{
		"system=ops-system",
		"service-type=" + record.ServiceType,
	}

	// 将镜像标签与已存在的记录标签进行聚合，并进行排重处理
	allLabels := utils.MergeLabelsWithDeduplication(baseLabels, imageLabels, record.Labels)

	// 记录标签聚合过程和端口信息
	log.Printf("Label aggregation for service %s:", req.ServiceInfo.ServiceID)
	log.Printf("  Base labels: %v", baseLabels)
	log.Printf("  Image labels: %v", imageLabels)
	log.Printf("  Record labels: %v", record.Labels)
	log.Printf("  Final labels: %v", allLabels)
	log.Printf("  Image ports: %v", imagePorts)

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information for image update
	// 传递镜像更新所需的信息，包括聚合后的标签和端口信息
	workerReq := &models.WorkerDeployRequest{
		ServiceId: req.ServiceInfo.ServiceID,
		ImageName: req.ServiceInfo.ImageName, // 更新镜像名
		ImageURL:  imageURL,                  // 更新镜像地址
		Labels:    allLabels,                 // 传递聚合后的标签
		Ports:     imagePorts,                // 传递镜像端口信息

		// TODO: 扩展点 - 如需支持更多字段更新，可在此处添加
		// 例如：
		// DomainPrefix:    req.ServiceInfo.DomainPrefix,    // 域名前缀更新
		// DomainSuffix:    req.ServiceInfo.DomainSuffix,    // 域名后缀更新
		// Expiration:      req.ServiceInfo.Expiration,      // 过期时间更新
		// DurationSeconds: durationSeconds,                 // 持续时间更新
		// ApiReplica:      req.ServiceInfo.APIReplica,      // API副本数更新
		// ApiCpu:          req.ServiceInfo.APICPU,          // API CPU更新
		// ApiMemory:       req.ServiceInfo.APIMemory,       // API内存更新
		// AutoReplica:     req.ServiceInfo.AutoReplica,     // Auto副本数更新
		// AutoCpu:         req.ServiceInfo.AutoCPU,         // Auto CPU更新
		// AutoMemory:      req.ServiceInfo.AutoMemory,      // Auto内存更新
		// CustomerEnvs:    req.ServiceInfo.CustomerEnvs,    // 环境变量更新
	}

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		workerReq.NodeIP = workerInfo.Nodes[0].IP
	}

	workerResp, err := worker.UpdateContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录的状态和标签
	// 1. 更新状态为 PROCESSING
	// 2. 更新镜像名称（如果有变化）
	// 3. 更新聚合后的标签
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 更新部署记录中的镜像名称和标签
	if req.ServiceInfo.ImageName != "" {
		record.ImageName = req.ServiceInfo.ImageName
	}
	record.Labels = allLabels
	record.Ports = imagePorts

	if updateErr := s.deployRecordRepo.Update(record); updateErr != nil {
		log.Printf("Failed to update deploy record with new image and labels: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	} else {
		log.Printf("Successfully updated deploy record for service %s with new image %s, %d labels, and %d ports",
			record.ServiceID, record.ImageName, len(allLabels), len(imagePorts))
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// StopService stops a service on a worker node
func (s *Service) StopService(ctx context.Context, id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 对于停止操作，不需要检查 Worker 状态
	// 停止操作会释放资源，即使 Worker 处于 FREEZE 状态也可以安全执行，甚至有助于释放资源
	log.Printf("Stop operation for service %s on worker %s (status: %s)", id, workerID, workerInfo.Status)

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		ServiceId: id, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 StopContainer 函数
	workerResp, err := worker.StopContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 STOPPED
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "STOPPED"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// GetWorkerList returns a list of all registered worker nodes with optional filtering
func (s *Service) GetWorkerList(ctx context.Context, filter *models.WorkerFilter) (*models.WorkerListResponse, error) {
	// 从数据库获取 worker 列表
	if s.workerRepo != nil {
		workers, err := s.workerRepo.GetAll(filter)
		if err != nil {
			log.Printf("Failed to get workers from database: %v", err)
			return &models.WorkerListResponse{
				Code:    500,
				Workers: nil,
				Msg:     fmt.Sprintf("无法从数据库获取 worker 列表: %v", err),
			}, fmt.Errorf("无法从数据库获取 worker 列表: %w", err)
		}

		return &models.WorkerListResponse{
			Code:    200,
			Workers: workers,
			Msg:     "Service: 获取Worker列表成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.WorkerListResponse{
		Code:    500,
		Workers: nil,
		Msg:     "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// RegisterWorker registers a new worker node with the service
func (s *Service) RegisterWorker(ctx context.Context, req *models.RegisterWorkerRequest) (*models.ServiceResponse, error) {
	// Generate a unique ID for the worker using ULID
	workerID, err := utils.GeneratePrefixedULID("worker-")
	if err != nil {
		log.Printf("Failed to generate ULID for worker: %v", err)
		// Fallback to the old method if ULID generation fails
		workerID = fmt.Sprintf("worker-%d", time.Now().Unix())
	}

	// Convert nodes array to Node struct array
	var nodes []models.Node
	nodes = append(nodes, req.Nodes...)

	// Create worker info
	workerInfo := models.WorkerInfo{
		WorkerID:     workerID,
		Name:         req.Name,
		ServerType:   req.ServerType,
		Labels:       req.Labels,
		Host:         req.Host,
		HostIP:       req.HostIP,
		DomainSuffix: req.DomainSuffix,
		Nodes:        nodes,
		Status:       "AVAILABLE",
	}

	// 存储到数据库
	if s.workerRepo != nil {
		if err := s.workerRepo.Create(workerInfo); err != nil {
			log.Printf("Failed to store worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法将 worker 保存到数据库: %v", err),
			}, fmt.Errorf("无法将 worker 保存到数据库: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: gin.H{"workerId": workerID},
		Msg:  "Service: Worker注册成功",
	}, nil
}

// RemoveWorker removes a worker node from the service
func (s *Service) RemoveWorker(ctx context.Context, worker_id string) (*models.ServiceResponse, error) {
	// 从数据库中删除 worker
	if s.workerRepo != nil {
		if err := s.workerRepo.Delete(worker_id); err != nil {
			log.Printf("Failed to remove worker from database: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker移除成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// UpdateWorker updates a worker node
func (s *Service) UpdateWorker(ctx context.Context, worker_id string, req *models.UpdateWorkerRequest) (*models.ServiceResponse, error) {
	// 从数据库获取 worker 信息
	if s.workerRepo != nil {
		worker, err := s.workerRepo.GetByID(worker_id)
		if err != nil {
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		// 更新 worker 信息
		if req.Name != "" {
			worker.Name = req.Name
		}
		if req.ServerType != "" {
			worker.ServerType = req.ServerType
		}
		if req.Labels != nil {
			worker.Labels = req.Labels
		}
		if req.Host != "" {
			worker.Host = req.Host
		}
		if req.DomainSuffix != "" {
			worker.DomainSuffix = req.DomainSuffix
		}
		if req.Nodes != nil {
			worker.Nodes = req.Nodes
		}
		if req.Status != "" {
			worker.Status = req.Status
		}

		// 更新数据库中的 worker 信息
		if err := s.workerRepo.Update(worker); err != nil {
			log.Printf("Failed to update worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法更新数据库中的 worker: %v", err),
			}, fmt.Errorf("无法更新数据库中的 worker: %w", err)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker更新成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}
