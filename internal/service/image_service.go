package service

import (
	"context"
	"fmt"
	"log"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/models"
)

// CreateImageTypes 批量创建新的镜像类型
func (s *Service) CreateImageTypes(ctx context.Context, reqs []*models.CreateImageTypeRequest) (*models.BatchImageTypeResponse, error) {
	// 检查数据库连接
	if s.imageTypeRepo == nil {
		return &models.BatchImageTypeResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果请求为空，返回错误
	if len(reqs) == 0 {
		return &models.BatchImageTypeResponse{
			Code: 400,
			Msg:  "请求不能为空",
		}, fmt.Errorf("请求不能为空")
	}

	// 准备响应数据
	response := &models.BatchImageTypeResponse{
		Code: 200,
		Data: struct {
			SuccessCount int                `json:"success_count"`
			FailedCount  int                `json:"failed_count"`
			Results      []models.ImageType `json:"results"`
			Errors       []string           `json:"errors"`
		}{
			Results: make([]models.ImageType, 0, len(reqs)),
			Errors:  make([]string, 0),
		},
		Msg: "Service: 批量创建镜像类型完成",
	}

	// 首先获取所有现有的镜像类型名称
	existingImageTypes, err := s.imageTypeRepo.GetAll()
	if err != nil {
		errMsg := "获取现有镜像类型失败"
		log.Printf("%s: %v", errMsg, err)
		response.Data.Errors = append(response.Data.Errors, errMsg)
		response.Data.FailedCount = len(reqs)
		response.Code = 500
		response.Msg = "Service: 创建镜像类型失败，数据库查询错误"
		return response, nil
	}

	// 创建一个映射，用于快速查找镜像名称是否已存在
	existingNames := make(map[string]bool)
	for _, imageType := range existingImageTypes {
		existingNames[imageType.ImageName] = true
	}

	// 创建一个映射，用于记录请求内部的镜像名称
	requestNames := make(map[string]int) // 镜像名称 -> 第一次出现的索引

	// 标记哪些请求是有效的（不重复且参数有效）
	validRequests := make([]bool, len(reqs))

	// 第一轮：检查参数有效性和重复性
	for i, req := range reqs {
		// 默认标记为有效
		validRequests[i] = true

		// 验证请求参数
		if req.ImageName == "" || req.ImageURL == "" {
			errMsg := fmt.Sprintf("第 %d 个请求参数无效: ImageName 和 ImageURL 不能为空", i+1)
			log.Println(errMsg)
			response.Data.Errors = append(response.Data.Errors, errMsg)
			response.Data.FailedCount++
			validRequests[i] = false
			continue
		}

		// 检查是否与现有镜像类型重名
		if existingNames[req.ImageName] {
			errMsg := fmt.Sprintf("第 %d 个请求的镜像名称 '%s' 已存在于数据库中", i+1, req.ImageName)
			log.Println(errMsg)
			response.Data.Errors = append(response.Data.Errors, errMsg)
			response.Data.FailedCount++
			validRequests[i] = false
			continue
		}

		// 检查请求内部是否有重名
		if firstIdx, exists := requestNames[req.ImageName]; exists {
			errMsg := fmt.Sprintf("第 %d 个请求的镜像名称 '%s' 与第 %d 个请求重复", i+1, req.ImageName, firstIdx+1)
			log.Println(errMsg)
			response.Data.Errors = append(response.Data.Errors, errMsg)
			response.Data.FailedCount++
			validRequests[i] = false
			continue
		}

		// 记录这个名称及其首次出现的位置
		requestNames[req.ImageName] = i
	}

	// 第二轮：处理有效的请求
	for i, req := range reqs {
		// 跳过无效的请求
		if !validRequests[i] {
			continue
		}

		// 创建镜像类型对象
		imageType := models.ImageType{
			ImageName: req.ImageName,
			ImageURL:  req.ImageURL,
			Ports:     req.Ports,
			Labels:    req.Labels,
		}

		// 存储到数据库
		if err := s.imageTypeRepo.Create(&imageType); err != nil {
			errMsg := fmt.Sprintf("第 %d 个请求创建失败: %v", i+1, err)
			log.Printf("Failed to store image type in database: %v", err)
			response.Data.Errors = append(response.Data.Errors, errMsg)
			response.Data.FailedCount++
			continue
		}

		// 添加到成功结果列表
		response.Data.Results = append(response.Data.Results, imageType)
		response.Data.SuccessCount++
	}

	// 根据请求数量和成功/失败情况设置响应码和消息
	if len(reqs) == 1 {
		// 单个镜像类型的情况
		if response.Data.SuccessCount == 1 {
			response.Msg = "Service: 创建镜像类型成功"
		} else {
			response.Code = 500
			response.Msg = "Service: 创建镜像类型失败"
		}
	} else {
		// 多个镜像类型的情况
		if response.Data.SuccessCount == 0 && response.Data.FailedCount > 0 {
			response.Code = 500
			response.Msg = "Service: 批量创建镜像类型全部失败"
		} else if response.Data.FailedCount > 0 {
			// 如果部分失败，设置响应码为 207 (Multi-Status)
			response.Code = 207
			response.Msg = "Service: 批量创建镜像类型部分成功"
		}
	}

	return response, nil
}

// GetImageTypeList 获取所有镜像类型
func (s *Service) GetImageTypeList(ctx context.Context) (*models.ImageTypeListResponse, error) {
	// 检查数据库连接
	if s.imageTypeRepo == nil {
		return &models.ImageTypeListResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 从数据库获取所有镜像类型
	imageTypes, err := s.imageTypeRepo.GetAll()
	if err != nil {
		log.Printf("Failed to get image types from database: %v", err)
		return &models.ImageTypeListResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取镜像类型列表: %v", err),
		}, fmt.Errorf("无法获取镜像类型列表: %w", err)
	}

	return &models.ImageTypeListResponse{
		Code: 200,
		Data: struct {
			ImageList []models.ImageType `json:"image_list"`
		}{
			ImageList: imageTypes,
		},
		Msg: "Service: 获取镜像类型列表成功",
	}, nil
}

// DeleteImageType 删除镜像类型
func (s *Service) DeleteImageType(ctx context.Context, id string) (*models.ImageTypeResponse, error) {
	// 检查数据库连接
	if s.imageTypeRepo == nil {
		return &models.ImageTypeResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 将 ID 转换为 int64
	imageID, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return &models.ImageTypeResponse{
			Code: 400,
			Msg:  "无效的镜像类型 ID",
		}, fmt.Errorf("无效的镜像类型 ID: %w", err)
	}

	// 从数据库删除镜像类型
	if err := s.imageTypeRepo.Delete(imageID); err != nil {
		log.Printf("Failed to delete image type from database: %v", err)
		return &models.ImageTypeResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法删除镜像类型: %v", err),
		}, fmt.Errorf("无法删除镜像类型: %w", err)
	}

	return &models.ImageTypeResponse{
		Code: 200,
		Data: gin.H{"id": imageID},
		Msg:  "Service: 删除镜像类型成功",
	}, nil
}

// UpdateImageType 更新镜像类型
func (s *Service) UpdateImageType(ctx context.Context, id string, req *models.UpdateImageTypeRequest) (*models.ImageTypeResponse, error) {
	// 检查数据库连接
	if s.imageTypeRepo == nil {
		return &models.ImageTypeResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 将 ID 转换为 int64
	imageID, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return &models.ImageTypeResponse{
			Code: 400,
			Msg:  "无效的镜像类型 ID",
		}, fmt.Errorf("无效的镜像类型 ID: %w", err)
	}

	// 从数据库获取镜像类型
	imageType, err := s.imageTypeRepo.GetByID(imageID)
	if err != nil {
		log.Printf("Failed to get image type from database: %v", err)
		return &models.ImageTypeResponse{
			Code: 404,
			Msg:  fmt.Sprintf("无法找到镜像类型: %v", err),
		}, fmt.Errorf("无法找到镜像类型: %w", err)
	}

	// 更新镜像类型信息
	if req.ImageName != "" {
		imageType.ImageName = req.ImageName
	}
	if req.ImageURL != "" {
		imageType.ImageURL = req.ImageURL
	}
	if req.Ports != nil {
		imageType.Ports = req.Ports
	}
	if req.Labels != nil {
		imageType.Labels = req.Labels
	}

	// 更新数据库中的镜像类型
	if err := s.imageTypeRepo.Update(imageType); err != nil {
		log.Printf("Failed to update image type in database: %v", err)
		return &models.ImageTypeResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法更新镜像类型: %v", err),
		}, fmt.Errorf("无法更新镜像类型: %w", err)
	}

	return &models.ImageTypeResponse{
		Code: 200,
		Data: imageType.ToResponse(),
		Msg:  "Service: 更新镜像类型成功",
	}, nil
}
