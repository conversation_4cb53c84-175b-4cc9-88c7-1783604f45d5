package service

// Config 保存 service 的配置
type Config struct {
	// service 特定的配置项
	LogDir     string   // 日志目录
	DBPath     string   // 数据库路径
	DBType     string   // 数据库类型 (sqlite, mysql, etc.)
	APIPort    string   // API 服务端口
	ServerType []string // 服务类型
}

// DefaultConfig 返回默认的 service 配置
func DefaultConfig() Config {
	return Config{
		LogDir:  "log",
		DBPath:  "dbdata/service.db",
		DBType:  "sqlite",
		APIPort: "8080",
		ServerType: []string{
			"TRIAL",
			"STANDARD",
		},
	}
}
