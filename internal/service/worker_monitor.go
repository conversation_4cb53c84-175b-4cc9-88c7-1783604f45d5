package service

import (
	"context"
	"log"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// NodesResourceResult 表示 worker 资源结果
type NodesResourceResult = []models.Node

// StartWorkerMonitor 启动 worker 监控器，每3分钟执行一次
func (s *Service) StartWorkerMonitor(ctx context.Context) {
	log.Println("Starting worker monitor...")

	// 启动一个独立的协程来监控 worker
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()

		// 立即执行一次监控
		s.monitorWorkers(ctx)

		// 然后每3分钟执行一次
		for {
			select {
			case <-ticker.C:
				s.monitorWorkers(ctx)
			case <-ctx.Done():
				log.Println("Worker monitor stopped due to context cancellation")
				return
			}
		}
	}()
}

// monitorWorkers 监控所有 worker 的资源情况
func (s *Service) monitorWorkers(ctx context.Context) {
	log.Println("Monitoring workers...")

	// 如果数据库连接不可用，直接返回
	if s.workerRepo == nil {
		log.Println("Database connection not available, skipping worker monitoring")
		return
	}

	// 获取所有 worker
	workers, err := s.workerRepo.GetAll(nil)
	if err != nil {
		log.Printf("Failed to get workers from database: %v", err)
		return
	}

	// 遍历所有 worker
	for i := range workers {
		// 使用指针直接修改原始切片中的元素
		worker := &workers[i]

		// 为每个 worker 启动一个协程，避免一个 worker 响应慢影响其他 worker
		go func(w *models.WorkerInfo) {

			// 获取 worker 的资源情况
			nodesResource, err := s.getWorkerResource(ctx, w)
			if err != nil {
				log.Printf("Failed to get worker resource info: %v", err)

				// 更新 worker 状态为 DISCONNECT
				w.Status = "DISCONNECT"

				// 更新 worker 信息到数据库
				if updateErr := s.workerRepo.UpdateStatus(w.WorkerID, "DISCONNECT"); updateErr != nil {
					log.Printf("Failed to update worker status: %v", updateErr)
				} else {
					log.Printf("Successfully updated worker %s status to DISCONNECT", w.WorkerID)
				}

				return
			}

			if len(*nodesResource) == 0 {
				log.Printf("No resource information available for worker %s", w.WorkerID)

				// 更新 worker 状态为 DISCONNECT
				w.Status = "DISCONNECT"

				// 更新 worker 信息到数据库
				if updateErr := s.workerRepo.UpdateStatus(w.WorkerID, "DISCONNECT"); updateErr != nil {
					log.Printf("Failed to update worker status: %v", updateErr)
				} else {
					log.Printf("Successfully updated worker %s status to DISCONNECT", w.WorkerID)
				}

				return
			}

			w.Nodes = *nodesResource
			w.Status = "AVAILABLE" // 确保状态为 AVAILABLE

			// 更新 worker 信息到数据库
			if updateErr := s.workerRepo.Update(*w); updateErr != nil {
				log.Printf("Failed to update worker info: %v", updateErr)
			} else {
				log.Printf("Successfully updated worker %s status to %s", w.WorkerID, w.Status)
			}

		}(worker)
	}
}

// getWorkerResource 获取 worker 的资源情况
func (s *Service) getWorkerResource(ctx context.Context, worker *models.WorkerInfo) (*NodesResourceResult, error) {
	log.Printf("Getting resource information for worker %s (%s)", worker.Name, worker.WorkerID)

	// 检查 worker 是否有节点
	if len(worker.Nodes) == 0 {
		log.Printf("Worker %s has no nodes, using empty resource list", worker.WorkerID)
		// 返回空切片而不是错误
		emptyResult := make(NodesResourceResult, 0)
		return &emptyResult, nil
	}

	// 创建资源列表
	resources := make([]models.Node, 0, len(worker.Nodes))

	// 遍历 worker 的所有节点
	for i := range worker.Nodes {
		// 使用指针直接修改原始切片中的元素
		node := &worker.Nodes[i]

		// 检查节点是否有 instance 字段
		if node.Instance == "" {
			log.Printf("Node %s has no instance field, skipping", node.IP)
			continue
		}

		// 获取节点 CPU 使用率
		cpuUsage, err := utils.GetNodeCPUUsage(node.Instance)
		if err != nil {
			log.Printf("Failed to get CPU usage for node %s: %v", node.Instance, err)
			// 继续处理其他节点，不要因为一个节点失败而中断整个过程
			continue
		}

		// 获取节点内存使用率
		memUsage, err := utils.GetNodeMemoryUsage(node.Instance)
		if err != nil {
			log.Printf("Failed to get memory usage for node %s: %v", node.Instance, err)
			// 继续处理其他节点，不要因为一个节点失败而中断整个过程
			continue
		}

		// 计算磁盘使用率（假设为 1%，暂时没有处理该节点的磁盘使用情况）
		diskUsage := 0.01

		// 添加到资源列表
		resource := models.Node{
			IP:              node.IP,
			Instance:        node.Instance,
			Memory:          node.Memory,
			CPU:             node.CPU,
			Disk:            node.Disk,
			Cpu_use_rate:    cpuUsage,
			Memory_use_rate: memUsage,
			Disk_use_rate:   diskUsage,
		}
		resources = append(resources, resource)

		log.Printf("Got resource usage for node %s: CPU=%.2f%%, Memory=%.2f%%, Disk=%.2f%%",
			node.Instance, cpuUsage*100, memUsage*100, diskUsage*100)
	}

	// 返回资源信息
	return &resources, nil
}
