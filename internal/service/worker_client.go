package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/interfaces"
	"github.com/zero-ops/service-system/internal/models"
)

// WorkerHTTPClient 是一个通过 HTTP 调用 Worker 服务的客户端
type WorkerHTTPClient struct {
	baseURL    string       // Worker 服务的基础 URL，例如 "http://worker-server:8080"
	httpClient *http.Client // HTTP 客户端，可配置超时等
	config     HTTPConfig   // HTTP 客户端配置
	username   string       // 认证用户名
	password   string       // 认证密码
}

// HTTPConfig 保存 HTTP 客户端的配置
type HTTPConfig struct {
	Timeout    time.Duration // HTTP 请求超时时间
	RetryCount int           // 重试次数
	RetryDelay time.Duration // 重试延迟
}

// DefaultHTTPConfig 返回默认的 HTTP 配置
func DefaultHTTPConfig() HTTPConfig {
	return HTTPConfig{
		Timeout:    30 * time.Second,
		RetryCount: 3,
		RetryDelay: 2 * time.Second,
	}
}

// NewWorkerHTTPClient 创建一个新的 Worker HTTP 客户端
func NewWorkerHTTPClient(baseURL string) interfaces.WorkerClient {
	return NewWorkerHTTPClientWithConfig(baseURL, DefaultHTTPConfig())
}

// NewWorkerHTTPClientWithAuth 创建一个带认证信息的新 Worker HTTP 客户端
func NewWorkerHTTPClientWithAuth(baseURL string, username, password string) interfaces.WorkerClient {
	return NewWorkerHTTPClientWithConfigAndAuth(baseURL, DefaultHTTPConfig(), username, password)
}

// NewWorkerHTTPClientWithConfig 使用自定义配置创建一个新的 Worker HTTP 客户端
func NewWorkerHTTPClientWithConfig(baseURL string, config HTTPConfig) interfaces.WorkerClient {
	// 使用默认的认证信息
	return NewWorkerHTTPClientWithConfigAndAuth(baseURL, config, "devops", "3-546-_iuhh5498")
}

// NewWorkerHTTPClientWithConfigAndAuth 使用自定义配置和认证信息创建一个新的 Worker HTTP 客户端
func NewWorkerHTTPClientWithConfigAndAuth(baseURL string, config HTTPConfig, username, password string) interfaces.WorkerClient {
	return &WorkerHTTPClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		config:   config,
		username: username,
		password: password,
	}
}

// doRequest 执行 HTTP 请求，包含重试逻辑
func (c *WorkerHTTPClient) doRequest(req *http.Request) (*http.Response, error) {
	var resp *http.Response
	var err error

	// 添加认证信息
	if c.username != "" && c.password != "" {
		// 对于需要认证的接口，添加 Basic 认证头
		if req.Method == "POST" ||
			(req.Method == "PUT" && (strings.Contains(req.URL.Path, "/stop") ||
				strings.Contains(req.URL.Path, "/restart") ||
				strings.Contains(req.URL.Path, "/update"))) {
			auth := base64.StdEncoding.EncodeToString([]byte(c.username + ":" + c.password))
			req.Header.Set("Authorization", "Basic "+auth)
		}
	}

	// 实现重试逻辑
	for i := 0; i <= c.config.RetryCount; i++ {
		resp, err = c.httpClient.Do(req)
		if err == nil {
			return resp, nil
		}

		// 如果不是最后一次尝试，则等待后重试
		if i < c.config.RetryCount {
			time.Sleep(c.config.RetryDelay)
			// 创建新的请求，因为原请求的 Body 可能已经被关闭
			req = req.Clone(req.Context())

			// 重新添加认证信息，因为 Clone 可能不会复制所有头信息
			if c.username != "" && c.password != "" {
				if req.Method == "POST" ||
					(req.Method == "PUT" && (strings.Contains(req.URL.Path, "/stop") || strings.Contains(req.URL.Path, "/restart"))) {
					auth := base64.StdEncoding.EncodeToString([]byte(c.username + ":" + c.password))
					req.Header.Set("Authorization", "Basic "+auth)
				}
			}
		}
	}

	return nil, fmt.Errorf("请求失败，已重试 %d 次: %w", c.config.RetryCount, err)
}

// addQueryParams 添加查询参数到 URL
func addQueryParams(baseURL string, params map[string]string) string {
	if len(params) == 0 {
		return baseURL
	}

	u, err := url.Parse(baseURL)
	if err != nil {
		// 如果解析失败，回退到手动构建
		urlWithParams := baseURL + "?"
		for key, value := range params {
			urlWithParams += fmt.Sprintf("%s=%s&", url.QueryEscape(key), url.QueryEscape(value))
		}
		return urlWithParams[:len(urlWithParams)-1] // 移除最后的 &
	}

	// 使用 url.Values 构建查询参数
	q := u.Query()
	for key, value := range params {
		q.Add(key, value)
	}
	u.RawQuery = q.Encode()

	return u.String()
}

// DeployContainer 通过 HTTP 请求部署容器
func (c *WorkerHTTPClient) DeployContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/deploy", c.baseURL)

	fmt.Printf("Service:: url: %s===================", url)

	// 将请求对象序列化为 JSON
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}

// StopContainer 通过 HTTP 请求停止容器
func (c *WorkerHTTPClient) StopContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/%s/stop", c.baseURL, req.ServiceId)

	// 创建查询参数
	queryParams := make(map[string]string)
	if req.NodeIP != "" {
		queryParams["nodeIP"] = req.NodeIP
	}

	// 添加查询参数到 URL
	url = addQueryParams(url, queryParams)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "PUT", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}

// RestartContainer 通过 HTTP 请求重启容器
func (c *WorkerHTTPClient) RestartContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/%s/restart", c.baseURL, req.ServiceId)

	// 创建查询参数
	queryParams := make(map[string]string)
	if req.NodeIP != "" {
		queryParams["nodeIP"] = req.NodeIP
	}

	// 添加查询参数到 URL
	url = addQueryParams(url, queryParams)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "PUT", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}

// UpdateContainer 通过 HTTP 请求升级容器
func (c *WorkerHTTPClient) UpdateContainer(ctx context.Context, req *models.WorkerDeployRequest) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/update", c.baseURL)

	// 将请求对象序列化为 JSON
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}

// HealthCheck 通过 HTTP 请求检查 worker 的健康状态
func (c *WorkerHTTPClient) HealthCheck(ctx context.Context) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/health", c.baseURL)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}

// GetDeployStatus 通过 HTTP 请求获取部署状态
func (c *WorkerHTTPClient) GetDeployStatus(ctx context.Context, serviceIDs []string) (*models.WorkerResponse, error) {
	// 构建请求 URL
	url := fmt.Sprintf("%s/api/v1/worker/status", c.baseURL)

	// 创建查询参数
	queryParams := make(map[string]string)
	if len(serviceIDs) > 0 {
		// 将 service_ids 数组转换为逗号分隔的字符串
		serviceIDsStr := ""
		for i, id := range serviceIDs {
			if i > 0 {
				serviceIDsStr += ","
			}
			serviceIDsStr += id
		}
		queryParams["service_ids"] = serviceIDsStr
	}

	// 添加查询参数到 URL
	url = addQueryParams(url, queryParams)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 发送请求
	resp, err := c.doRequest(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var workerResp models.WorkerResponse
	if err := json.NewDecoder(resp.Body).Decode(&workerResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &workerResp, nil
}
