// Package logger 提供日志记录功能
package logger

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"sync"
	"syscall"
	"time"
)

// 日志级别
const (
	LevelInfo  = "INFO"
	LevelWarn  = "WARN"
	LevelError = "ERROR"
)

// 日志文件
var (
	outLogFile  *os.File
	errLogFile  *os.File
	logMutex    sync.Mutex
	initialized bool
)

// 日志文件路径
var (
	outLogPath = "log/out.log"
	errLogPath = "log/error.log"
)

// Init 初始化日志系统
func Init() error {
	logMutex.Lock()
	defer logMutex.Unlock()

	if initialized {
		return nil
	}

	// 创建日志目录
	logDir := filepath.Dir(outLogPath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// 打开日志文件
	var err error
	outLogFile, err = os.OpenFile(outLogPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open out log file: %w", err)
	}

	errLogFile, err = os.OpenFile(errLogPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		outLogFile.Close()
		return fmt.Errorf("failed to open error log file: %w", err)
	}

	// 设置信号处理，自动监听 SIGHUP 信号
	setupSignalHandler()

	initialized = true
	return nil
}

// SetLogPath 设置日志文件路径
func SetLogPath(outPath, errPath string) {
	logMutex.Lock()
	defer logMutex.Unlock()

	if initialized {
		log.Println("Warning: Changing log paths after initialization may not take effect until Reopen() is called")
	}

	outLogPath = outPath
	errLogPath = errPath
}

// Reopen 重新打开日志文件（用于日志轮转）
func Reopen() error {
	logMutex.Lock()
	defer logMutex.Unlock()

	// 刷新并关闭旧文件
	if outLogFile != nil {
		outLogFile.Sync()
		outLogFile.Close()
	}
	if errLogFile != nil {
		errLogFile.Sync()
		errLogFile.Close()
	}

	// 打开新文件
	var err error
	outLogFile, err = os.OpenFile(outLogPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		initialized = false
		return fmt.Errorf("failed to reopen out log file: %w", err)
	}

	errLogFile, err = os.OpenFile(errLogPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		outLogFile.Close()
		initialized = false
		return fmt.Errorf("failed to reopen error log file: %w", err)
	}

	return nil
}

// Close 关闭日志文件
func Close() {
	logMutex.Lock()
	defer logMutex.Unlock()

	if outLogFile != nil {
		outLogFile.Close()
		outLogFile = nil
	}
	if errLogFile != nil {
		errLogFile.Close()
		errLogFile = nil
	}
	initialized = false
}

// 格式化日志消息
func formatLogMessage(level, format string, args ...interface{}) string {
	// 获取调用者信息
	_, file, line, ok := runtime.Caller(2) // 跳过两层调用栈：formatLogMessage 和 log 函数
	caller := "unknown"
	if ok {
		caller = fmt.Sprintf("%s:%d", filepath.Base(file), line)
	}

	// 格式化消息
	message := fmt.Sprintf(format, args...)
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	return fmt.Sprintf("[%s] [%s] [%s] %s\n", timestamp, level, caller, message)
}

// 写入日志
func writeLog(level, format string, args ...interface{}) {
	if !initialized {
		if err := Init(); err != nil {
			log.Printf("Failed to initialize logger: %v", err)
			return
		}
	}

	logMutex.Lock()
	defer logMutex.Unlock()

	logMessage := formatLogMessage(level, format, args...)

	// 根据级别选择日志文件
	if level == LevelError {
		if errLogFile != nil {
			fmt.Fprint(errLogFile, logMessage)
		}
	} else {
		if outLogFile != nil {
			fmt.Fprint(outLogFile, logMessage)
		}
	}
}

// Info 记录信息日志
func Info(format string, args ...interface{}) {
	writeLog(LevelInfo, format, args...)
}

// Warn 记录警告日志
func Warn(format string, args ...interface{}) {
	writeLog(LevelWarn, format, args...)
}

// Error 记录错误日志
func Error(format string, args ...interface{}) {
	writeLog(LevelError, format, args...)
}

// 内部信号处理函数
func setupSignalHandler() {
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGHUP)

	go func() {
		for sig := range sigs {
			log.Printf("Received signal: %v", sig)
			if err := Reopen(); err != nil {
				log.Printf("Error reopening log files: %v", err)
			} else {
				log.Printf("Successfully reopened log files")
			}
		}
	}()
}

// SetupSignalHandler 设置信号处理函数（向后兼容，现在 Init() 会自动设置信号处理）
func SetupSignalHandler(signals chan os.Signal) {
	go func() {
		for sig := range signals {
			log.Printf("Received signal: %v", sig)
			if err := Reopen(); err != nil {
				log.Printf("Error reopening log files: %v", err)
			} else {
				log.Printf("Successfully reopened log files")
			}
		}
	}()
}
