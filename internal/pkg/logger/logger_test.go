package logger

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLogger(t *testing.T) {
	// 设置测试日志路径
	testDir := "test_logs"
	outPath := filepath.Join(testDir, "out.log")
	errPath := filepath.Join(testDir, "error.log")
	
	// 确保测试目录存在
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir) // 测试结束后清理
	
	// 设置日志路径
	SetLogPath(outPath, errPath)
	
	// 初始化日志系统
	if err := Init(); err != nil {
		t.Fatalf("Failed to initialize logger: %v", err)
	}
	defer Close()
	
	// 写入测试日志
	Info("This is an info message")
	Warn("This is a warning message")
	Error("This is an error message")
	
	// 检查日志文件是否存在
	if _, err := os.Stat(outPath); os.IsNotExist(err) {
		t.<PERSON>("Out log file does not exist: %s", outPath)
	}
	
	if _, err := os.Stat(errPath); os.IsNotExist(err) {
		t.Errorf("Error log file does not exist: %s", errPath)
	}
	
	// 读取日志文件内容进行验证
	outContent, err := os.ReadFile(outPath)
	if err != nil {
		t.Errorf("Failed to read out log file: %v", err)
	}
	
	errContent, err := os.ReadFile(errPath)
	if err != nil {
		t.Errorf("Failed to read error log file: %v", err)
	}
	
	// 验证日志内容
	if len(outContent) == 0 {
		t.Error("Out log file is empty")
	}
	
	if len(errContent) == 0 {
		t.Error("Error log file is empty")
	}
}
