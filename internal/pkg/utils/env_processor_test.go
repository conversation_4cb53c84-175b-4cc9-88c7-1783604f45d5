package utils

import (
	"fmt"
	"strings"
	"testing"

	"github.com/zero-ops/service-system/internal/models"
)

func TestProcessEnvironmentVariablesFromRequest(t *testing.T) {
	// 创建测试用的请求
	req := &models.WorkerDeployRequest{
		ServiceId:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_TYPE=standard",
			"SYS_ENVS=APP_NAME:myapp|||PORT:8080|||DEBUG:true|||DATABASE_URL:mysql://user:pass@localhost:3306/db|||ES_SERVER:elasticsearch.example.com",
			"SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123",
		},
		CustomerEnvs: []string{
			"CUSTOM_VAR1=value1",
			"CUSTOM_VAR2=value with spaces",
			"CUSTOM_VAR3=value=with=equals",
		},
	}

	// 处理环境变量
	result, err := ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariablesFromRequest failed: %v", err)
	}

	// 验证结果不为空
	if result == "" {
		t.Fatal("Expected non-empty result")
	}

	// 验证结果包含预期的环境变量
	expectedVars := []string{
		"--env ES_NAMESPACE=\"myapp_example_com\"",
		"--env ES_SERVER=\"http://elasticsearch.example.com\"",
		"--env MYSQL_DATABASE=\"myapp_example_com\"",
		"--env RUNTIME_ENV=\"myapp_example_com\"",
		"--env SECRET_KEY=\"myapp_example_com^secret123\"",
		"--env CUSTOM_VAR1=\"value1\"",
		"--env CUSTOM_VAR2=\"value with spaces\"",
		"--env CUSTOM_VAR3=\"value=with=equals\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result: %s", expected, result)
		}
	}

	t.Logf("Generated environment string: %s", result)
}

func TestDockerCommandFormat(t *testing.T) {
	// 测试生成的环境变量字符串是否能正确用于Docker命令
	req := &models.WorkerDeployRequest{
		ServiceId:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_ENVS=MYSQL_HOST:**************|||MYSQL_PORT:33068|||ES_SERVER:**************:9200",
			"SYS_ENVS_TPL=MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PORT:_{MYSQL_PORT}_|||ES_SERVER:http://_{ES_SERVER}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_",
		},
		CustomerEnvs: []string{
			"VARA=1456",
			"VARB=s d fd  24999",
			"PPORT=234",
		},
	}

	// 处理环境变量
	envString, err := ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariablesFromRequest failed: %v", err)
	}

	// 验证环境变量字符串格式
	if !strings.HasPrefix(envString, " --env ") {
		t.Errorf("Environment string should start with ' --env ', got: %s", envString[:20])
	}

	// 验证没有重复的环境变量
	envParts := strings.Split(envString, " --env ")
	envKeys := make(map[string]bool)
	duplicates := []string{}

	for _, part := range envParts {
		if part == "" {
			continue
		}
		// 提取环境变量名
		if idx := strings.Index(part, "="); idx > 0 {
			key := part[:idx]
			if envKeys[key] {
				duplicates = append(duplicates, key)
			}
			envKeys[key] = true
		}
	}

	if len(duplicates) > 0 {
		t.Errorf("Found duplicate environment variables: %v", duplicates)
	}

	// 模拟Docker命令构建
	dockerCmd := fmt.Sprintf("docker run -d --name test-container%s test-image", envString)

	// 验证命令格式正确（没有连续的参数）
	if strings.Contains(dockerCmd, "--env--env") {
		t.Error("Docker command contains malformed environment arguments")
	}

	// 验证特定的环境变量存在
	expectedVars := []string{
		"MYSQL_HOST=\"**************\"",
		"MYSQL_PORT=\"33068\"",
		"ES_SERVER=\"http://**************:9200\"",
		"DOMAIN=\"myapp.example.com\"",
		"ENV_TAG=\"myapp_example_com\"",
		"VARA=\"1456\"",
		"VARB=\"s d fd  24999\"",
		"PPORT=\"234\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(envString, expected) {
			t.Errorf("Expected environment variable %q not found in: %s", expected, envString)
		}
	}

	t.Logf("Generated Docker command: %s", dockerCmd)
	t.Logf("Environment variables count: %d", len(envKeys))
}

func TestProcessEnvironmentVariables(t *testing.T) {
	// 创建测试用的部署记录
	record := &models.DeployRecordW{
		ServiceID:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_TYPE=standard",
			"SYS_ENVS=APP_NAME:myapp|||PORT:8080|||DEBUG:true|||DATABASE_URL:mysql://user:pass@localhost:3306/db|||ES_SERVER:elasticsearch.example.com",
			"SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123",
		},
		CustomerEnvs: []string{
			"CUSTOM_VAR1=value1",
			"CUSTOM_VAR2=value with spaces",
			"CUSTOM_VAR3=value=with=equals",
		},
	}

	// 处理环境变量
	result, err := ProcessEnvironmentVariables(record)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariables failed: %v", err)
	}

	// 验证结果不为空
	if result == "" {
		t.Fatal("Expected non-empty result")
	}

	// 验证结果包含预期的环境变量
	expectedVars := []string{
		"--env ES_NAMESPACE=\"myapp_example_com\"",
		"--env ES_SERVER=\"http://elasticsearch.example.com\"",
		"--env MYSQL_DATABASE=\"myapp_example_com\"",
		"--env RUNTIME_ENV=\"myapp_example_com\"",
		"--env SECRET_KEY=\"myapp_example_com^secret123\"",
		"--env CUSTOM_VAR1=\"value1\"",
		"--env CUSTOM_VAR2=\"value with spaces\"",
		"--env CUSTOM_VAR3=\"value=with=equals\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result: %s", expected, result)
		}
	}

	t.Logf("Generated environment string: %s", result)
}

func TestParseSysEnvs(t *testing.T) {
	labels := []string{
		"SYS_TYPE=standard",
		"SYS_ENVS=APP_NAME:myapp|||PORT:8080|||DEBUG:true|||EMPTY_VAR:",
	}

	result, err := parseSysEnvs(labels)
	if err != nil {
		t.Fatalf("parseSysEnvs failed: %v", err)
	}

	expected := map[string]string{
		"APP_NAME":  "myapp",
		"PORT":      "8080",
		"DEBUG":     "true",
		"EMPTY_VAR": "",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestParseSysEnvsTpl(t *testing.T) {
	labels := []string{
		"SYS_TYPE=standard",
		"SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123",
	}

	result, err := parseSysEnvsTpl(labels)
	if err != nil {
		t.Fatalf("parseSysEnvsTpl failed: %v", err)
	}

	expected := map[string]string{
		"ES_NAMESPACE":   "_{DOMAIN_NO_DOT}_",
		"MYSQL_DATABASE": "_{DOMAIN_NO_DOT}_",
		"SECRET_KEY":     "_{DOMAIN_NO_DOT}_^secret123",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestReplacePlaceholders(t *testing.T) {
	tplEnvs := map[string]string{
		"ES_NAMESPACE":   "_{DOMAIN_NO_DOT}_",
		"MYSQL_DATABASE": "_{DOMAIN_NO_DOT}_",
		"SECRET_KEY":     "_{DOMAIN_NO_DOT}_^secret123",
		"ES_SERVER":      "http://_{ES_SERVER}_",
	}

	sysEnvs := map[string]string{
		"ES_SERVER": "elasticsearch.example.com",
	}

	record := &models.DeployRecordW{
		ServiceID:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
	}

	result := replacePlaceholders(tplEnvs, sysEnvs, record)

	expected := map[string]string{
		"ES_NAMESPACE":   "myapp_example_com",
		"MYSQL_DATABASE": "myapp_example_com",
		"SECRET_KEY":     "myapp_example_com^secret123",
		"ES_SERVER":      "http://elasticsearch.example.com",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestParseCustomEnvs(t *testing.T) {
	customerEnvs := []string{
		"CUSTOM_VAR1=value1",
		"CUSTOM_VAR2=value with spaces",
		"CUSTOM_VAR3=value=with=equals",
		"EMPTY_VAR=",
		"NO_VALUE_VAR",
	}

	result, err := parseCustomEnvs(customerEnvs)
	if err != nil {
		t.Fatalf("parseCustomEnvs failed: %v", err)
	}

	expected := map[string]string{
		"CUSTOM_VAR1":  "value1",
		"CUSTOM_VAR2":  "value with spaces",
		"CUSTOM_VAR3":  "value=with=equals",
		"EMPTY_VAR":    "",
		"NO_VALUE_VAR": "",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestBuildEnvString(t *testing.T) {
	tplEnvs := map[string]string{
		"TPL_VAR1": "tpl_value1",
		"TPL_VAR2": "tpl_value2",
	}

	customEnvs := map[string]string{
		"CUSTOM_VAR1": "custom_value1",
		"CUSTOM_VAR2": "custom_value2",
	}

	result := buildEnvString(tplEnvs, customEnvs)

	// 验证结果包含所有变量
	expectedVars := []string{
		"--env TPL_VAR1=\"tpl_value1\"",
		"--env TPL_VAR2=\"tpl_value2\"",
		"--env CUSTOM_VAR1=\"custom_value1\"",
		"--env CUSTOM_VAR2=\"custom_value2\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result: %s", expected, result)
		}
	}

	// 验证模板变量在前，自定义变量在后
	tplIndex := strings.Index(result, "TPL_VAR1")
	customIndex := strings.Index(result, "CUSTOM_VAR1")
	if tplIndex == -1 || customIndex == -1 {
		t.Fatal("Could not find expected variables in result")
	}
	if tplIndex > customIndex {
		t.Error("Template variables should come before custom variables")
	}
}

func TestSpecialCharacters(t *testing.T) {
	// 测试包含特殊字符的环境变量
	record := &models.DeployRecordW{
		ServiceID:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_ENVS=DATABASE_URL:mysql://user:pass@localhost:3306/db?charset=utf8|||JWT_SECRET:abc123!@#$%^&*()_+-=[]{}|;:,.<>?",
			"SYS_ENVS_TPL=DATABASE_URL:_{DATABASE_URL}_|||JWT_SECRET:_{JWT_SECRET}_",
		},
		CustomerEnvs: []string{
			"COMPLEX_VAR=value with \"quotes\" and 'apostrophes'",
			"PIPE_CMD=cat file1 | grep pattern | sort",
		},
	}

	result, err := ProcessEnvironmentVariables(record)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariables failed: %v", err)
	}

	// 验证特殊字符被正确处理
	if !strings.Contains(result, "--env DATABASE_URL=\"mysql://user:pass@localhost:3306/db?charset=utf8\"") {
		t.Error("DATABASE_URL not properly handled")
	}

	if !strings.Contains(result, "--env JWT_SECRET=\"abc123!@#$%^&*()_+-=[]{}|;:,.<>?\"") {
		t.Error("JWT_SECRET not properly handled")
	}

	if !strings.Contains(result, "--env COMPLEX_VAR=\"value with \\\"quotes\\\" and 'apostrophes'\"") {
		t.Error("COMPLEX_VAR quotes not properly escaped")
	}

	t.Logf("Result with special characters: %s", result)
}
