package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

// SSHConfig SSH连接配置
type SSHConfig struct {
	User     string // SSH用户名
	Password string // SSH密码
	KeyPath  string // SSH私钥路径
	Port     int    // SSH端口
	Timeout  int    // 连接超时时间(秒)
}

// DefaultSSHConfig 返回默认SSH配置
func DefaultSSHConfig() *SSHConfig {
	return &SSHConfig{
		User:     "root",
		Password: "",
		KeyPath:  "",
		Port:     22,
		Timeout:  10,
	}
}

// PrometheusResponse Prometheus API 响应结构
type PrometheusResponse struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string `json:"resultType"`
		Result     []struct {
			Metric map[string]string `json:"metric"`
			Value  []interface{}     `json:"value"`
		} `json:"result"`
	} `json:"data"`
}

// queryPrometheus 从 Prometheus 查询指标数据
func queryPrometheus(query string) (float64, error) {
	// 从环境变量获取 Prometheus 认证信息
	promUser := os.Getenv("PROMETHEUS_USER")
	promPassword := os.Getenv("PROMETHEUS_PASSWORD")

	// 如果环境变量未设置，使用默认值（仅用于开发环境）
	if promUser == "" {
		promUser = "admin-bao" // 实际部署时应从环境变量获取
	}

	// 构建请求 URL
	baseURL := "http://pw-prometheus.bpmax.cn/api/v1/query"
	data := url.Values{}
	data.Set("query", query)

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", baseURL, strings.NewReader(data.Encode()))
	if err != nil {
		return 0, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Content-Length", strconv.Itoa(len(data.Encode())))

	// 设置基本认证
	if promUser != "" && promPassword != "" {
		req.SetBasicAuth(promUser, promPassword)
	}

	// 发送请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return 0, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("prometheus API 返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var promResp PrometheusResponse
	if err := json.NewDecoder(resp.Body).Decode(&promResp); err != nil {
		return 0, fmt.Errorf("解析 prometheus 响应失败: %w", err)
	}

	// 检查响应状态
	if promResp.Status != "success" {
		return 0, fmt.Errorf("prometheus 查询失败: %s", promResp.Status)
	}

	// 检查结果是否为空
	if len(promResp.Data.Result) == 0 {
		return 0, fmt.Errorf("prometheus 查询结果为空")
	}

	// 获取值
	valueArr := promResp.Data.Result[0].Value
	if len(valueArr) < 2 {
		return 0, fmt.Errorf("prometheus 查询结果格式错误")
	}

	// 第二个元素是值（字符串格式）
	valueStr, ok := valueArr[1].(string)
	if !ok {
		return 0, fmt.Errorf("prometheus 查询结果类型错误")
	}

	// 转换为 float64
	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		return 0, fmt.Errorf("转换查询结果失败: %w", err)
	}

	return value, nil
}

// GetNodeCPUUsage 获取节点 CPU 最近15分钟的平均使用率
func GetNodeCPUUsage(nodeInstance string) (float64, error) {
	// 构建 Prometheus 查询，计算最近15分钟的平均CPU使用率
	query := fmt.Sprintf("1 - sum(rate(node_cpu_seconds_total{instance=\"%s\", mode=\"idle\"}[15m])) / sum(rate(node_cpu_seconds_total{instance=\"%s\"}[15m]))",
		nodeInstance, nodeInstance)

	// 查询 Prometheus
	cpuUsage, err := queryPrometheus(query)
	if err != nil {
		return 0, fmt.Errorf("获取 CPU 使用率失败: %w", err)
	}

	// 返回 CPU 使用率
	return cpuUsage, nil
}

// GetNodeMemoryUsage 获取节点内存最近15分钟的平均使用率
// nodeInstance 格式为 "hostname:port"，例如 "node73.yitaiyitai.com:1080"
func GetNodeMemoryUsage(nodeInstance string) (float64, error) {
	// 构建 Prometheus 查询，计算最近15分钟的平均内存使用率
	// 使用 node_memory_MemAvailable_bytes 和 node_memory_MemTotal_bytes 的比率
	// 注意：这里我们使用两个独立的查询，然后在 PromQL 中计算它们的比率
	query := fmt.Sprintf(`
avg_over_time(node_memory_MemAvailable_bytes{instance="%s"}[15m]) /
avg_over_time(node_memory_MemTotal_bytes{instance="%s"}[15m])
`, nodeInstance, nodeInstance)

	// 将查询结果转换为内存使用率（1 - 可用/总量）
	availableRatio, err := queryPrometheus(query)
	if err != nil {
		return 0, fmt.Errorf("获取内存使用率失败: %w", err)
	}

	// 计算内存使用率
	memUsage := 1.0 - availableRatio
	return memUsage, nil
}
