package utils

import (
	"bytes"
	"fmt"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// OSSConfig OSS配置结构
type OSSConfig struct {
	Endpoint        string // OSS endpoint
	AccessKeyID     string // 访问密钥ID
	AccessKeySecret string // 访问密钥Secret
	BucketName      string // 存储桶名称
	Region          string // 区域
	UploadBucket    string // 上传存储桶
	FilePath        string // 文件路径
}

// OSSStats OSS统计信息
type OSSStats struct {
	TotalSize      int64   `json:"total_size"`       // 总大小（字节）
	TotalSizeMB    float64 `json:"total_size_mb"`    // 总大小（MB）
	FileCount      int64   `json:"file_count"`       // 文件数量
	LastModified   string  `json:"last_modified"`    // 最后修改时间
	TrafficUsage   int64   `json:"traffic_usage"`    // 流量使用量（字节）
	TrafficUsageMB float64 `json:"traffic_usage_mb"` // 流量使用量（MB）
}

// CreateOSSClient 创建OSS客户端
func CreateOSSClient(config *OSSConfig) (*oss.Client, error) {
	if config.Endpoint == "" || config.AccessKeyID == "" || config.AccessKeySecret == "" {
		return nil, fmt.Errorf("OSS配置不完整: endpoint=%s, accessKeyID=%s", config.Endpoint, config.AccessKeyID)
	}

	client, err := oss.New(config.Endpoint, config.AccessKeyID, config.AccessKeySecret)
	if err != nil {
		logger.Error("创建OSS客户端失败: %v", err)
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	logger.Info("OSS客户端创建成功, endpoint: %s", config.Endpoint)
	return client, nil
}

// GetBucketStats 获取指定bucket中指定路径的文件统计信息
func GetBucketStats(config *OSSConfig) (*OSSStats, error) {
	logger.Info("开始获取OSS统计信息, bucket: %s, path: %s", config.BucketName, config.FilePath)

	// 创建OSS客户端
	client, err := CreateOSSClient(config)
	if err != nil {
		return nil, err
	}

	// 获取bucket
	bucket, err := client.Bucket(config.BucketName)
	if err != nil {
		logger.Error("获取bucket失败: %v", err)
		return nil, fmt.Errorf("获取bucket失败: %w", err)
	}

	// 设置列举参数
	prefix := strings.TrimPrefix(config.FilePath, "/")
	if prefix != "" && !strings.HasSuffix(prefix, "/") {
		prefix += "/"
	}

	var totalSize int64
	var fileCount int64
	var lastModified time.Time

	// 分页列举对象
	marker := ""
	for {
		options := []oss.Option{
			oss.Prefix(prefix),
			oss.MaxKeys(1000),
		}
		if marker != "" {
			options = append(options, oss.Marker(marker))
		}

		result, err := bucket.ListObjects(options...)
		if err != nil {
			logger.Error("列举对象失败: %v", err)
			return nil, fmt.Errorf("列举对象失败: %w", err)
		}

		// 统计文件信息
		for _, object := range result.Objects {
			// 跳过目录（以/结尾的对象）
			if strings.HasSuffix(object.Key, "/") {
				continue
			}

			totalSize += object.Size
			fileCount++

			// 记录最新的修改时间
			if object.LastModified.After(lastModified) {
				lastModified = object.LastModified
			}
		}

		// 检查是否还有更多对象
		if !result.IsTruncated {
			break
		}
		marker = result.NextMarker
	}

	// 计算流量使用量（这里使用文件总大小作为估算）
	// 注意：OSS API 不直接提供流量统计，这里使用文件大小作为估算
	// 实际流量可能包括下载、上传等操作，需要通过OSS控制台或监控API获取
	trafficUsage := totalSize // 简化估算，实际应用中可能需要更复杂的计算

	stats := &OSSStats{
		TotalSize:      totalSize,
		TotalSizeMB:    float64(totalSize) / (1024 * 1024),
		FileCount:      fileCount,
		LastModified:   lastModified.Format("2006-01-02 15:04:05"),
		TrafficUsage:   trafficUsage,
		TrafficUsageMB: float64(trafficUsage) / (1024 * 1024),
	}

	logger.Info("OSS统计完成: 文件数=%d, 总大小=%.2fMB, 流量=%.2fMB",
		stats.FileCount, stats.TotalSizeMB, stats.TrafficUsageMB)

	return stats, nil
}

// GetBucketTrafficStats 获取bucket的流量统计（通过OSS监控API）
// 注意：这个功能需要额外的权限和配置，可能需要使用阿里云监控服务
func GetBucketTrafficStats(config *OSSConfig, startTime, endTime time.Time) (*OSSStats, error) {
	// TODO: 实现通过阿里云监控API获取真实的流量数据
	// 这需要使用阿里云监控服务的SDK
	// 目前先返回基于文件大小的估算值

	logger.Warn("流量统计功能暂未实现真实API调用，返回基于文件大小的估算值")

	// 先获取基本统计信息
	stats, err := GetBucketStats(config)
	if err != nil {
		return nil, err
	}

	// 这里可以添加更复杂的流量计算逻辑
	// 例如：根据时间范围、访问模式等进行估算

	return stats, nil
}

// ParseOSSConfigFromEnvMap 从环境变量map中解析OSS配置
func ParseOSSConfigFromEnvMap(envMap map[string]string) (*OSSConfig, error) {
	config := &OSSConfig{
		Endpoint:        envMap["OSSENDPOINT"],
		AccessKeyID:     envMap["ALIACCESSKEYID"],
		AccessKeySecret: envMap["ALIACCESSKEYSECRET"],
		BucketName:      envMap["OSSBUCKET"],
		Region:          envMap["OSSREGION"],
		UploadBucket:    envMap["OSSUPLOADBUCKET"],
		FilePath:        envMap["OSSFILEPATH"],
	}

	// 验证必需的配置项
	if config.Endpoint == "" {
		return nil, fmt.Errorf("OSSENDPOINT 不能为空")
	}
	if config.AccessKeyID == "" {
		return nil, fmt.Errorf("ALIACCESSKEYID 不能为空")
	}
	if config.AccessKeySecret == "" {
		return nil, fmt.Errorf("ALIACCESSKEYSECRET 不能为空")
	}
	if config.BucketName == "" {
		return nil, fmt.Errorf("OSSBUCKET 不能为空")
	}

	// 设置默认值
	if config.FilePath == "" {
		config.FilePath = "/"
	}

	logger.Info("OSS配置解析成功: bucket=%s, endpoint=%s, path=%s",
		config.BucketName, config.Endpoint, config.FilePath)

	return config, nil
}

// TestOSSConnection 测试OSS连接
func TestOSSConnection(config *OSSConfig) error {
	client, err := CreateOSSClient(config)
	if err != nil {
		return err
	}

	// 测试bucket是否存在
	exists, err := client.IsBucketExist(config.BucketName)
	if err != nil {
		logger.Error("检查bucket存在性失败: %v", err)
		return fmt.Errorf("检查bucket存在性失败: %w", err)
	}

	if !exists {
		return fmt.Errorf("bucket不存在: %s", config.BucketName)
	}

	logger.Info("OSS连接测试成功, bucket: %s", config.BucketName)
	return nil
}

// OSSUtilDiskUsage OSSUtil磁盘使用量统计结果
type OSSUtilDiskUsage struct {
	TotalSize     int64   `json:"total_size"`     // 总大小（字节）
	TotalSizeMB   float64 `json:"total_size_mb"`  // 总大小（MB）
	FileCount     int64   `json:"file_count"`     // 文件数量
	ObjectCount   int64   `json:"object_count"`   // 对象数量
	BucketPath    string  `json:"bucket_path"`    // bucket路径
	ExecutionTime string  `json:"execution_time"` // 执行时间
}

// GetOSSDiskUsage 使用OSSUtil工具统计指定bucket指定目录的磁盘使用量
func GetOSSDiskUsage(config *OSSConfig) (*OSSUtilDiskUsage, error) {
	logger.Info("开始使用OSSUtil统计磁盘使用量, bucket: %s, path: %s", config.BucketName, config.FilePath)

	// 构建bucket路径
	bucketPath := fmt.Sprintf("oss://%s", config.BucketName)
	if config.FilePath != "" && config.FilePath != "/" {
		// 确保路径格式正确
		filePath := strings.TrimPrefix(config.FilePath, "/")
		if filePath != "" {
			bucketPath = fmt.Sprintf("oss://%s/%s", config.BucketName, filePath)
		}
	}

	// OSSUtil工具路径
	// ossutilPath := "./dbdata/ossutil64-linux"
	ossutilPath := "./dbdata/ossutilmac64"

	// 构建命令参数
	// ./ossutilmac64 du --block-size MB oss://pw-garden/hptfile --endpoint oss-cn-shanghai.aliyuncs.com --access-key-id LTAI4GDNf4ut1x4r9ND8dcNc --access-key-secret ******************************
	// ossutil du oss://bucket/path --endpoint endpoint --access-key-id id --access-key-secret secret
	args := []string{
		"du",
		bucketPath,
		"--endpoint", config.Endpoint,
		"--access-key-id", config.AccessKeyID,
		"--access-key-secret", config.AccessKeySecret,
	}

	fmt.Printf("执行OSSUtil命令: %s %v", ossutilPath, args)
	logger.Info("执行OSSUtil命令: %s %v", ossutilPath, args)

	// 执行命令
	cmd := exec.Command(ossutilPath, args...)

	// 设置工作目录
	cmd.Dir = "."

	// 捕获输出
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 记录开始时间
	startTime := time.Now()

	// 执行命令
	err := cmd.Run()
	executionTime := time.Since(startTime).String()

	if err != nil {
		logger.Error("OSSUtil命令执行失败: %v", err)
		logger.Error("错误输出: %s", stderr.String())
		return nil, fmt.Errorf("OSSUtil命令执行失败: %w, 错误输出: %s", err, stderr.String())
	}

	// 解析输出
	output := stdout.String()
	logger.Info("OSSUtil命令输出: %s", output)

	// 解析du命令的输出
	usage, err := parseOSSUtilDuOutput(output, bucketPath, executionTime)
	if err != nil {
		logger.Error("解析OSSUtil输出失败: %v", err)
		return nil, fmt.Errorf("解析OSSUtil输出失败: %w", err)
	}

	logger.Info("OSSUtil磁盘使用量统计完成: 文件数=%d, 总大小=%.2fMB, 执行时间=%s",
		usage.FileCount, usage.TotalSizeMB, usage.ExecutionTime)

	return usage, nil
}

// parseOSSUtilDuOutput 解析OSSUtil du命令的输出
func parseOSSUtilDuOutput(output, bucketPath, executionTime string) (*OSSUtilDiskUsage, error) {
	// OSSUtil du命令的实际输出格式：
	// storage class   object count            sum size(byte)
	// ----------------------------------------------------------
	// Standard        36342                   85354050832
	// ----------------------------------------------------------
	// total object count: 36342                       total object sum size: 85354050832
	// total part count:   0                           total part sum size:   0
	//
	// total du size(byte):85354050832
	//
	// 4.640190(s) elapsed

	usage := &OSSUtilDiskUsage{
		BucketPath:    bucketPath,
		ExecutionTime: executionTime,
	}

	logger.Info("开始解析OSSUtil输出，原始输出: %s", output)

	// 方法1: 优先解析 "total du size(byte):" 行（最准确）
	duSizePattern := `total du size\(byte\):\s*(\d+)`
	duRe := regexp.MustCompile(duSizePattern)
	duMatches := duRe.FindStringSubmatch(output)

	if len(duMatches) >= 2 {
		if totalSize, err := strconv.ParseInt(duMatches[1], 10, 64); err == nil {
			usage.TotalSize = totalSize
			usage.TotalSizeMB = float64(totalSize) / (1024 * 1024)
			logger.Info("从 total du size 解析到总大小: %d字节(%.2fMB)", usage.TotalSize, usage.TotalSizeMB)
		}
	}

	// 方法2: 解析 "total object count:" 和 "total object sum size:" 行
	objectCountPattern := `total object count:\s*(\d+)`
	objectSizePattern := `total object sum size:\s*(\d+)`

	countRe := regexp.MustCompile(objectCountPattern)
	sizeRe := regexp.MustCompile(objectSizePattern)

	countMatches := countRe.FindStringSubmatch(output)
	sizeMatches := sizeRe.FindStringSubmatch(output)

	if len(countMatches) >= 2 {
		if objectCount, err := strconv.ParseInt(countMatches[1], 10, 64); err == nil {
			usage.ObjectCount = objectCount
			usage.FileCount = objectCount // 假设所有对象都是文件
			logger.Info("解析到对象数量: %d", usage.ObjectCount)
		}
	}

	if len(sizeMatches) >= 2 {
		if objectSumSize, err := strconv.ParseInt(sizeMatches[1], 10, 64); err == nil {
			// 如果没有从 total du size 获取到大小，使用 object sum size
			if usage.TotalSize == 0 {
				usage.TotalSize = objectSumSize
				usage.TotalSizeMB = float64(objectSumSize) / (1024 * 1024)
				logger.Info("从 total object sum size 解析到总大小: %d字节(%.2fMB)", usage.TotalSize, usage.TotalSizeMB)
			}
		}
	}

	// 方法3: 解析执行时间（如果输出中包含）
	timePattern := `(\d+\.\d+)\(s\)\s+elapsed`
	timeRe := regexp.MustCompile(timePattern)
	timeMatches := timeRe.FindStringSubmatch(output)

	if len(timeMatches) >= 2 {
		usage.ExecutionTime = timeMatches[1] + "s"
		logger.Info("解析到执行时间: %s", usage.ExecutionTime)
	}

	// 方法4: 如果正则匹配失败，尝试逐行解析
	if usage.TotalSize == 0 && usage.ObjectCount == 0 {
		logger.Warn("正则解析失败，尝试逐行解析")
		lines := strings.Split(output, "\n")

		for _, line := range lines {
			line = strings.TrimSpace(line)

			// 解析 total du size 行
			if strings.Contains(line, "total du size(byte):") {
				parts := strings.Split(line, ":")
				if len(parts) >= 2 {
					sizeStr := strings.TrimSpace(parts[1])
					if size, err := strconv.ParseInt(sizeStr, 10, 64); err == nil {
						usage.TotalSize = size
						usage.TotalSizeMB = float64(size) / (1024 * 1024)
						logger.Info("逐行解析到总大小: %d字节(%.2fMB)", usage.TotalSize, usage.TotalSizeMB)
					}
				}
			}

			// 解析 total object count 行
			if strings.Contains(line, "total object count:") && !strings.Contains(line, "total object sum size:") {
				parts := strings.Split(line, ":")
				if len(parts) >= 2 {
					countStr := strings.TrimSpace(parts[1])
					// 可能包含多个字段，取第一个数字
					fields := strings.Fields(countStr)
					if len(fields) > 0 {
						if count, err := strconv.ParseInt(fields[0], 10, 64); err == nil {
							usage.ObjectCount = count
							usage.FileCount = count
							logger.Info("逐行解析到对象数量: %d", usage.ObjectCount)
						}
					}
				}
			}
		}
	}

	// 验证是否成功解析
	if usage.TotalSize > 0 || usage.ObjectCount > 0 {
		logger.Info("OSSUtil输出解析成功: 对象数=%d, 总大小=%d字节(%.2fMB), 执行时间=%s",
			usage.ObjectCount, usage.TotalSize, usage.TotalSizeMB, usage.ExecutionTime)
		return usage, nil
	}

	// 如果都解析失败，返回详细错误信息
	logger.Error("无法解析OSSUtil输出，原始输出: %s", output)
	return nil, fmt.Errorf("无法解析OSSUtil du命令输出，请检查命令输出格式")
}
