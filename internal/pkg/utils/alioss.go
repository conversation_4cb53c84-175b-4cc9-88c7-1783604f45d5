package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// OSSConfig OSS配置结构
type OSSConfig struct {
	Endpoint        string // OSS endpoint
	AccessKeyID     string // 访问密钥ID
	AccessKeySecret string // 访问密钥Secret
	BucketName      string // 存储桶名称
	Region          string // 区域
	UploadBucket    string // 上传存储桶
	FilePath        string // 文件路径
}

// OSSStats OSS统计信息
type OSSStats struct {
	TotalSize      int64   `json:"total_size"`       // 总大小（字节）
	TotalSizeMB    float64 `json:"total_size_mb"`    // 总大小（MB）
	FileCount      int64   `json:"file_count"`       // 文件数量
	LastModified   string  `json:"last_modified"`    // 最后修改时间
	TrafficUsage   int64   `json:"traffic_usage"`    // 流量使用量（字节）
	TrafficUsageMB float64 `json:"traffic_usage_mb"` // 流量使用量（MB）
}

// CreateOSSClient 创建OSS客户端
func CreateOSSClient(config *OSSConfig) (*oss.Client, error) {
	if config.Endpoint == "" || config.AccessKeyID == "" || config.AccessKeySecret == "" {
		return nil, fmt.Errorf("OSS配置不完整: endpoint=%s, accessKeyID=%s", config.Endpoint, config.AccessKeyID)
	}

	client, err := oss.New(config.Endpoint, config.AccessKeyID, config.AccessKeySecret)
	if err != nil {
		logger.Error("创建OSS客户端失败: %v", err)
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	logger.Info("OSS客户端创建成功, endpoint: %s", config.Endpoint)
	return client, nil
}

// GetBucketStats 获取指定bucket中指定路径的文件统计信息
func GetBucketStats(config *OSSConfig) (*OSSStats, error) {
	logger.Info("开始获取OSS统计信息, bucket: %s, path: %s", config.BucketName, config.FilePath)

	// 创建OSS客户端
	client, err := CreateOSSClient(config)
	if err != nil {
		return nil, err
	}

	// 获取bucket
	bucket, err := client.Bucket(config.BucketName)
	if err != nil {
		logger.Error("获取bucket失败: %v", err)
		return nil, fmt.Errorf("获取bucket失败: %w", err)
	}

	// 设置列举参数
	prefix := strings.TrimPrefix(config.FilePath, "/")
	if prefix != "" && !strings.HasSuffix(prefix, "/") {
		prefix += "/"
	}

	var totalSize int64
	var fileCount int64
	var lastModified time.Time

	// 分页列举对象
	marker := ""
	for {
		options := []oss.Option{
			oss.Prefix(prefix),
			oss.MaxKeys(1000),
		}
		if marker != "" {
			options = append(options, oss.Marker(marker))
		}

		result, err := bucket.ListObjects(options...)
		if err != nil {
			logger.Error("列举对象失败: %v", err)
			return nil, fmt.Errorf("列举对象失败: %w", err)
		}

		// 统计文件信息
		for _, object := range result.Objects {
			// 跳过目录（以/结尾的对象）
			if strings.HasSuffix(object.Key, "/") {
				continue
			}

			totalSize += object.Size
			fileCount++

			// 记录最新的修改时间
			if object.LastModified.After(lastModified) {
				lastModified = object.LastModified
			}
		}

		// 检查是否还有更多对象
		if !result.IsTruncated {
			break
		}
		marker = result.NextMarker
	}

	// 计算流量使用量（这里使用文件总大小作为估算）
	// 注意：OSS API 不直接提供流量统计，这里使用文件大小作为估算
	// 实际流量可能包括下载、上传等操作，需要通过OSS控制台或监控API获取
	trafficUsage := totalSize // 简化估算，实际应用中可能需要更复杂的计算

	stats := &OSSStats{
		TotalSize:      totalSize,
		TotalSizeMB:    float64(totalSize) / (1024 * 1024),
		FileCount:      fileCount,
		LastModified:   lastModified.Format("2006-01-02 15:04:05"),
		TrafficUsage:   trafficUsage,
		TrafficUsageMB: float64(trafficUsage) / (1024 * 1024),
	}

	logger.Info("OSS统计完成: 文件数=%d, 总大小=%.2fMB, 流量=%.2fMB",
		stats.FileCount, stats.TotalSizeMB, stats.TrafficUsageMB)

	return stats, nil
}

// GetBucketTrafficStats 获取bucket的流量统计（通过OSS监控API）
// 注意：这个功能需要额外的权限和配置，可能需要使用阿里云监控服务
func GetBucketTrafficStats(config *OSSConfig, startTime, endTime time.Time) (*OSSStats, error) {
	// TODO: 实现通过阿里云监控API获取真实的流量数据
	// 这需要使用阿里云监控服务的SDK
	// 目前先返回基于文件大小的估算值

	logger.Warn("流量统计功能暂未实现真实API调用，返回基于文件大小的估算值")

	// 先获取基本统计信息
	stats, err := GetBucketStats(config)
	if err != nil {
		return nil, err
	}

	// 这里可以添加更复杂的流量计算逻辑
	// 例如：根据时间范围、访问模式等进行估算

	return stats, nil
}

// ParseOSSConfigFromEnvMap 从环境变量map中解析OSS配置
func ParseOSSConfigFromEnvMap(envMap map[string]string) (*OSSConfig, error) {
	config := &OSSConfig{
		Endpoint:        envMap["OSSENDPOINT"],
		AccessKeyID:     envMap["ALIACCESSKEYID"],
		AccessKeySecret: envMap["ALIACCESSKEYSECRET"],
		BucketName:      envMap["OSSBUCKET"],
		Region:          envMap["OSSREGION"],
		UploadBucket:    envMap["OSSUPLOADBUCKET"],
		FilePath:        envMap["OSSFILEPATH"],
	}

	// 验证必需的配置项
	if config.Endpoint == "" {
		return nil, fmt.Errorf("OSSENDPOINT 不能为空")
	}
	if config.AccessKeyID == "" {
		return nil, fmt.Errorf("ALIACCESSKEYID 不能为空")
	}
	if config.AccessKeySecret == "" {
		return nil, fmt.Errorf("ALIACCESSKEYSECRET 不能为空")
	}
	if config.BucketName == "" {
		return nil, fmt.Errorf("OSSBUCKET 不能为空")
	}

	// 设置默认值
	if config.FilePath == "" {
		config.FilePath = "/"
	}

	logger.Info("OSS配置解析成功: bucket=%s, endpoint=%s, path=%s",
		config.BucketName, config.Endpoint, config.FilePath)

	return config, nil
}

// TestOSSConnection 测试OSS连接
func TestOSSConnection(config *OSSConfig) error {
	client, err := CreateOSSClient(config)
	if err != nil {
		return err
	}

	// 测试bucket是否存在
	exists, err := client.IsBucketExist(config.BucketName)
	if err != nil {
		logger.Error("检查bucket存在性失败: %v", err)
		return fmt.Errorf("检查bucket存在性失败: %w", err)
	}

	if !exists {
		return fmt.Errorf("bucket不存在: %s", config.BucketName)
	}

	logger.Info("OSS连接测试成功, bucket: %s", config.BucketName)
	return nil
}

func getOSSDiskUsage() {

}
