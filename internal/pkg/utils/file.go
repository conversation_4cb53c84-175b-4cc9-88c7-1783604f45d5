package utils

import (
	"io/ioutil"
)

// ReadFile reads the content of a file and returns it as a string.
func ReadFile(path string) (string, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// WriteFile writes the given content to a file at the specified path.
func WriteFile(path string, content string) error {
	return ioutil.WriteFile(path, []byte(content), 0644)
}
