package utils

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"io"
	"net"
	"os"
	"sync/atomic"
	"time"
)

// UUID 类型表示一个 UUID
type UUID [16]byte

// 用于生成序列号的原子计数器
var sequenceCounter uint32

// 存储机器 ID 的缓存
var machineID []byte

// 初始化机器 ID
func init() {
	// 尝试获取 MAC 地址作为机器 ID 的一部分
	interfaces, err := net.Interfaces()
	if err == nil {
		for _, iface := range interfaces {
			if len(iface.HardwareAddr) >= 6 {
				// 使用第一个有效的 MAC 地址
				machineID = make([]byte, 6)
				copy(machineID, iface.HardwareAddr)
				break
			}
		}
	}

	// 如果无法获取 MAC 地址，使用主机名和进程 ID
	if machineID == nil {
		hostname, err := os.Hostname()
		if err != nil {
			hostname = "unknown-host"
		}
		pid := os.Getpid()
		h := md5.New()
		io.WriteString(h, hostname)
		binary.Write(h, binary.LittleEndian, int32(pid))
		machineID = h.Sum(nil)[:6]
	}
}

// String 返回 UUID 的字符串表示
func (uuid UUID) String() string {
	return fmt.Sprintf("%x-%x-%x-%x-%x",
		uuid[0:4], uuid[4:6], uuid[6:8], uuid[8:10], uuid[10:16])
}

// Bytes 返回 UUID 的字节表示
func (uuid UUID) Bytes() []byte {
	return uuid[:]
}

// Hex 返回 UUID 的十六进制表示（不带连字符）
func (uuid UUID) Hex() string {
	return hex.EncodeToString(uuid[:])
}

// NewUUID 生成一个随机的 UUID (v4)
// 这种方法使用加密安全的随机数生成器，冲突概率极低
func NewUUID() (UUID, error) {
	var uuid UUID
	_, err := io.ReadFull(rand.Reader, uuid[:])
	if err != nil {
		return uuid, err
	}

	// 设置版本 (4) 和变体位
	uuid[6] = (uuid[6] & 0x0f) | 0x40 // 版本 4
	uuid[8] = (uuid[8] & 0x3f) | 0x80 // RFC 4122 变体

	return uuid, nil
}

// NewUUIDString 生成一个随机的 UUID (v4) 字符串
// 这是 NewUUID 的便捷包装，直接返回字符串形式
func NewUUIDString() (string, error) {
	uuid, err := NewUUID()
	if err != nil {
		return "", err
	}
	return uuid.String(), nil
}

// NewTimeBasedUUID 生成一个基于时间的 UUID (类似 v1)
// 这种方法使用时间戳、机器 ID 和序列号，适合需要时间排序的场景
func NewTimeBasedUUID() UUID {
	var uuid UUID

	// 获取当前时间戳（纳秒级）
	now := time.Now().UnixNano()

	// 获取并递增序列号
	seq := atomic.AddUint32(&sequenceCounter, 1)

	// 填充 UUID
	binary.BigEndian.PutUint32(uuid[0:4], uint32(now>>32))
	binary.BigEndian.PutUint32(uuid[4:8], uint32(now))
	binary.BigEndian.PutUint16(uuid[8:10], uint16(seq))

	// 添加机器 ID（最多 6 字节）
	copy(uuid[10:], machineID)

	// 设置版本 (1) 和变体位
	uuid[6] = (uuid[6] & 0x0f) | 0x10 // 版本 1
	uuid[8] = (uuid[8] & 0x3f) | 0x80 // RFC 4122 变体

	return uuid
}

// NewTimeBasedUUIDString 生成一个基于时间的 UUID 字符串
func NewTimeBasedUUIDString() string {
	return NewTimeBasedUUID().String()
}

// NewUUIDv7 生成一个符合 UUID v7 标准的 UUID
// UUID v7 使用 Unix 时间戳（毫秒精度）作为高位字段，并结合随机数据
// 参考: https://datatracker.ietf.org/doc/html/draft-peabody-uuid-urn-namespace
func NewUUIDv7() (UUID, error) {
	var uuid UUID

	// 获取当前时间戳（毫秒级）
	now := time.Now().UnixNano() / int64(time.Millisecond)

	// 填充时间戳到前 6 字节
	// UUID v7 格式: 48位时间戳 + 版本(4位) + 随机数据(76位)
	binary.BigEndian.PutUint32(uuid[0:4], uint32(now>>16))
	uuid[4] = byte(now >> 8)
	uuid[5] = byte(now)

	// 填充剩余字节为随机数据
	if _, err := rand.Read(uuid[6:16]); err != nil {
		return uuid, err
	}

	// 设置版本 (7) 和变体位
	uuid[6] = (uuid[6] & 0x0f) | 0x70 // 版本 7
	uuid[8] = (uuid[8] & 0x3f) | 0x80 // RFC 4122 变体

	return uuid, nil
}

// NewUUIDv7String 生成一个 UUID v7 字符串
func NewUUIDv7String() (string, error) {
	uuid, err := NewUUIDv7()
	if err != nil {
		return "", err
	}
	return uuid.String(), nil
}

// 保留旧函数名称以保持向后兼容性
// NewHybridUUID 现在是 NewUUIDv7 的别名
func NewHybridUUID() (UUID, error) {
	return NewUUIDv7()
}

// NewHybridUUIDString 现在是 NewUUIDv7String 的别名
func NewHybridUUIDString() (string, error) {
	return NewUUIDv7String()
}

// GenerateUUID 是一个通用函数，根据需要生成不同类型的 UUID
// 默认使用 UUID v7 方法，这提供了时间排序和高唯一性
func GenerateUUID() (string, error) {
	return NewUUIDv7String()
}

// GenerateShortUUID 生成一个较短的唯一 ID（16 个字符）
// 适用于需要较短标识符但仍需保持唯一性的场景
func GenerateShortUUID() (string, error) {
	uuid, err := NewUUIDv7()
	if err != nil {
		return "", err
	}

	// 返回 16 个字符的十六进制表示（不带连字符）
	return hex.EncodeToString(uuid[:8]), nil
}

// GeneratePrefixedUUID 生成一个带前缀的 UUID
// 例如：GeneratePrefixedUUID("user-") 可能返回 "user-f47ac10b-58cc-4372-a567-0e02b2c3d479"
func GeneratePrefixedUUID(prefix string) (string, error) {
	uuid, err := GenerateUUID()
	if err != nil {
		return "", err
	}
	return prefix + uuid, nil
}
