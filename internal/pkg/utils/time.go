package utils

import (
	"time"
)

// 东八区时区
var cstZone = time.FixedZone("CST", 8*3600) // 东八区

// GetCSTTime 获取东八区（中国标准时间）当前时间
func GetCSTTime() time.Time {
	return time.Now().In(cstZone)
}

// FormatCSTTime 将东八区时间格式化为 RFC3339 格式
func FormatCSTTime(t time.Time) string {
	return t.In(cstZone).Format(time.RFC3339)
}

// GetCSTTimeString 获取当前东八区时间的 RFC3339 格式字符串
func GetCSTTimeString() string {
	return FormatCSTTime(time.Now())
}
