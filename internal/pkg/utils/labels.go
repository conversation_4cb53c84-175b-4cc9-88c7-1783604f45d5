package utils

import (
	"fmt"
	"log"
	"sort"
	"strings"
)

// MergeLabelsWithDeduplication 合并多个标签数组并进行排重
// 对于 key=value 格式的标签，按 key 进行去重，后面的 value 替换前面的
// 对于不包含 = 的标签，按完整标签进行去重
// 这是一个通用的工具函数，可以在任何需要标签排重的地方使用
func MergeLabelsWithDeduplication(labelArrays ...[]string) []string {
	// 使用 map 来存储 key=value 格式的标签，key -> value
	keyValueMap := make(map[string]string)
	// 使用 map 来存储非 key=value 格式的标签
	simpleLabelsMap := make(map[string]bool)
	// 记录标签的处理顺序，用于保持相对顺序
	var processOrder []string

	// 记录处理过程，便于调试
	var totalInputLabels int
	for i, labels := range labelArrays {
		totalInputLabels += len(labels)
		log.Printf("Processing label array %d with %d labels: %v", i+1, len(labels), labels)
	}

	// 按顺序处理每个标签数组
	for _, labels := range labelArrays {
		for _, label := range labels {
			// 去除标签前后的空白字符
			label = strings.TrimSpace(label)
			if label == "" {
				continue // 跳过空标签
			}

			// 检查是否为 key=value 格式
			if strings.Contains(label, "=") {
				// 解析 key=value 格式
				parts := strings.SplitN(label, "=", 2)
				if len(parts) == 2 {
					key := strings.TrimSpace(parts[0])
					value := strings.TrimSpace(parts[1])

					if key != "" { // 确保 key 不为空
						if existingValue, exists := keyValueMap[key]; exists {
							log.Printf("Key '%s' already exists with value '%s', replacing with '%s'",
								key, existingValue, value)
						} else {
							log.Printf("Adding new key-value pair: '%s'='%s'", key, value)
							// 记录处理顺序（只在首次出现时记录）
							processOrder = append(processOrder, key)
						}
						keyValueMap[key] = value
					} else {
						// key 为空，当作普通标签处理
						log.Printf("Empty key in label '%s', treating as simple label", label)
						if !simpleLabelsMap[label] {
							simpleLabelsMap[label] = true
							processOrder = append(processOrder, label)
							log.Printf("Added simple label: '%s'", label)
						} else {
							log.Printf("Duplicate simple label '%s' found, skipping", label)
						}
					}
				} else {
					// 解析失败，当作普通标签处理
					log.Printf("Failed to parse label '%s' as key=value, treating as simple label", label)
					if !simpleLabelsMap[label] {
						simpleLabelsMap[label] = true
						processOrder = append(processOrder, label)
						log.Printf("Added simple label: '%s'", label)
					} else {
						log.Printf("Duplicate simple label '%s' found, skipping", label)
					}
				}
			} else {
				// 不包含 = 的标签，按完整标签进行去重
				if !simpleLabelsMap[label] {
					simpleLabelsMap[label] = true
					processOrder = append(processOrder, label)
					log.Printf("Added simple label: '%s'", label)
				} else {
					log.Printf("Duplicate simple label '%s' found, skipping", label)
				}
			}
		}
	}

	// 按处理顺序构建结果数组
	var result []string
	for _, item := range processOrder {
		if value, isKeyValue := keyValueMap[item]; isKeyValue {
			// 这是一个 key，构建 key=value 格式
			result = append(result, fmt.Sprintf("%s=%s", item, value))
		} else if simpleLabelsMap[item] {
			// 这是一个简单标签
			result = append(result, item)
		}
	}

	log.Printf("Label deduplication completed: %d input labels -> %d unique labels",
		totalInputLabels, len(result))
	log.Printf("Final merged labels: %v", result)

	return result
}

// DeduplicateLabels 对单个标签数组进行排重
// 这是 MergeLabelsWithDeduplication 的简化版本，用于处理单个标签数组
func DeduplicateLabels(labels []string) []string {
	return MergeLabelsWithDeduplication(labels)
}

// FilterEmptyLabels 过滤掉空白标签
// 返回一个新的数组，包含所有非空的标签
func FilterEmptyLabels(labels []string) []string {
	var result []string
	for _, label := range labels {
		label = strings.TrimSpace(label)
		if label != "" {
			result = append(result, label)
		}
	}
	return result
}

// SortLabels 对标签数组进行排序
// 返回一个新的已排序的数组，不修改原数组
func SortLabels(labels []string) []string {
	result := make([]string, len(labels))
	copy(result, labels)
	sort.Strings(result)
	return result
}

// ContainsLabel 检查标签数组中是否包含指定的标签
// 使用完全匹配进行比较
func ContainsLabel(labels []string, target string) bool {
	target = strings.TrimSpace(target)
	for _, label := range labels {
		if strings.TrimSpace(label) == target {
			return true
		}
	}
	return false
}

// AddLabelIfNotExists 如果标签不存在则添加到数组中
// 返回一个新的数组，如果标签已存在则返回原数组的副本
func AddLabelIfNotExists(labels []string, newLabel string) []string {
	newLabel = strings.TrimSpace(newLabel)
	if newLabel == "" {
		return labels
	}

	if ContainsLabel(labels, newLabel) {
		// 标签已存在，返回原数组的副本
		result := make([]string, len(labels))
		copy(result, labels)
		return result
	}

	// 标签不存在，添加到数组中
	result := make([]string, len(labels)+1)
	copy(result, labels)
	result[len(labels)] = newLabel
	return result
}

// RemoveLabel 从标签数组中移除指定的标签
// 返回一个新的数组，移除所有匹配的标签
func RemoveLabel(labels []string, target string) []string {
	target = strings.TrimSpace(target)
	var result []string
	for _, label := range labels {
		if strings.TrimSpace(label) != target {
			result = append(result, label)
		}
	}
	return result
}
