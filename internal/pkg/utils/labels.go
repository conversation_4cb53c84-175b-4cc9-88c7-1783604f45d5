package utils

import (
	"log"
	"sort"
	"strings"
)

// MergeLabelsWithDeduplication 合并多个标签数组并进行排重
// 只对完全相同的标签进行排重，不拆分 key=value 格式
// 这是一个通用的工具函数，可以在任何需要标签排重的地方使用
func MergeLabelsWithDeduplication(labelArrays ...[]string) []string {
	// 使用 map 来存储标签，实现排重
	labelMap := make(map[string]bool)

	// 记录处理过程，便于调试
	var totalInputLabels int
	for i, labels := range labelArrays {
		totalInputLabels += len(labels)
		log.Printf("Processing label array %d with %d labels: %v", i+1, len(labels), labels)
	}

	// 按顺序处理每个标签数组
	for _, labels := range labelArrays {
		for _, label := range labels {
			// 去除标签前后的空白字符
			label = strings.TrimSpace(label)
			if label == "" {
				continue // 跳过空标签
			}

			// 使用完整标签作为 map 的键，实现完全匹配的排重
			if labelMap[label] {
				log.Printf("Duplicate label '%s' found, skipping", label)
			} else {
				labelMap[label] = true
			}
		}
	}

	// 将 map 中的键转换为数组
	var result []string
	for label := range labelMap {
		result = append(result, label)
	}

	// 为了保证结果的一致性，对结果进行排序
	// 这样相同的输入总是产生相同顺序的输出
	sort.Strings(result)

	log.Printf("Label deduplication completed: %d input labels -> %d unique labels",
		totalInputLabels, len(result))
	log.Printf("Final merged labels: %v", result)

	return result
}

// DeduplicateLabels 对单个标签数组进行排重
// 这是 MergeLabelsWithDeduplication 的简化版本，用于处理单个标签数组
func DeduplicateLabels(labels []string) []string {
	return MergeLabelsWithDeduplication(labels)
}

// FilterEmptyLabels 过滤掉空白标签
// 返回一个新的数组，包含所有非空的标签
func FilterEmptyLabels(labels []string) []string {
	var result []string
	for _, label := range labels {
		label = strings.TrimSpace(label)
		if label != "" {
			result = append(result, label)
		}
	}
	return result
}

// SortLabels 对标签数组进行排序
// 返回一个新的已排序的数组，不修改原数组
func SortLabels(labels []string) []string {
	result := make([]string, len(labels))
	copy(result, labels)
	sort.Strings(result)
	return result
}

// ContainsLabel 检查标签数组中是否包含指定的标签
// 使用完全匹配进行比较
func ContainsLabel(labels []string, target string) bool {
	target = strings.TrimSpace(target)
	for _, label := range labels {
		if strings.TrimSpace(label) == target {
			return true
		}
	}
	return false
}

// AddLabelIfNotExists 如果标签不存在则添加到数组中
// 返回一个新的数组，如果标签已存在则返回原数组的副本
func AddLabelIfNotExists(labels []string, newLabel string) []string {
	newLabel = strings.TrimSpace(newLabel)
	if newLabel == "" {
		return labels
	}

	if ContainsLabel(labels, newLabel) {
		// 标签已存在，返回原数组的副本
		result := make([]string, len(labels))
		copy(result, labels)
		return result
	}

	// 标签不存在，添加到数组中
	result := make([]string, len(labels)+1)
	copy(result, labels)
	result[len(labels)] = newLabel
	return result
}

// RemoveLabel 从标签数组中移除指定的标签
// 返回一个新的数组，移除所有匹配的标签
func RemoveLabel(labels []string, target string) []string {
	target = strings.TrimSpace(target)
	var result []string
	for _, label := range labels {
		if strings.TrimSpace(label) != target {
			result = append(result, label)
		}
	}
	return result
}
