package utils

import (
	"regexp"
	"testing"
)

// 测试 UUID 格式的正则表达式
var uuidRegex = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
var shortUUIDRegex = regexp.MustCompile(`^[0-9a-f]{16}$`)

// 测试随机 UUID 生成
func TestNewUUID(t *testing.T) {
	uuid, err := NewUUID()
	if err != nil {
		t.Fatalf("生成随机 UUID 失败: %v", err)
	}
	
	uuidStr := uuid.String()
	if !uuidRegex.MatchString(uuidStr) {
		t.Errorf("生成的 UUID 格式不正确: %s", uuidStr)
	}
	
	// 测试 Hex 方法
	hexStr := uuid.Hex()
	if len(hexStr) != 32 {
		t.Errorf("生成的 UUID Hex 格式不正确: %s", hexStr)
	}
}

// 测试基于时间的 UUID 生成
func TestNewTimeBasedUUID(t *testing.T) {
	uuid := NewTimeBasedUUID()
	uuidStr := uuid.String()
	
	if !uuidRegex.MatchString(uuidStr) {
		t.Errorf("生成的基于时间的 UUID 格式不正确: %s", uuidStr)
	}
	
	// 测试多次生成的 UUID 是否不同
	uuid2 := NewTimeBasedUUID()
	if uuid == uuid2 {
		t.Error("连续生成的两个基于时间的 UUID 相同")
	}
}

// 测试混合 UUID 生成
func TestNewHybridUUID(t *testing.T) {
	uuid, err := NewHybridUUID()
	if err != nil {
		t.Fatalf("生成混合 UUID 失败: %v", err)
	}
	
	uuidStr := uuid.String()
	if !uuidRegex.MatchString(uuidStr) {
		t.Errorf("生成的混合 UUID 格式不正确: %s", uuidStr)
	}
}

// 测试通用 UUID 生成函数
func TestGenerateUUID(t *testing.T) {
	uuidStr, err := GenerateUUID()
	if err != nil {
		t.Fatalf("生成 UUID 失败: %v", err)
	}
	
	if !uuidRegex.MatchString(uuidStr) {
		t.Errorf("生成的 UUID 格式不正确: %s", uuidStr)
	}
}

// 测试短 UUID 生成
func TestGenerateShortUUID(t *testing.T) {
	shortUUID, err := GenerateShortUUID()
	if err != nil {
		t.Fatalf("生成短 UUID 失败: %v", err)
	}
	
	if !shortUUIDRegex.MatchString(shortUUID) {
		t.Errorf("生成的短 UUID 格式不正确: %s", shortUUID)
	}
	
	if len(shortUUID) != 16 {
		t.Errorf("短 UUID 长度不正确，期望 16，实际 %d", len(shortUUID))
	}
}

// 测试带前缀的 UUID 生成
func TestGeneratePrefixedUUID(t *testing.T) {
	prefix := "test-"
	prefixedUUID, err := GeneratePrefixedUUID(prefix)
	if err != nil {
		t.Fatalf("生成带前缀的 UUID 失败: %v", err)
	}
	
	if len(prefixedUUID) <= len(prefix) {
		t.Errorf("生成的带前缀的 UUID 格式不正确: %s", prefixedUUID)
	}
	
	if prefixedUUID[:len(prefix)] != prefix {
		t.Errorf("生成的 UUID 前缀不正确，期望 %s，实际 %s", prefix, prefixedUUID[:len(prefix)])
	}
	
	uuidPart := prefixedUUID[len(prefix):]
	if !uuidRegex.MatchString(uuidPart) {
		t.Errorf("带前缀的 UUID 中的 UUID 部分格式不正确: %s", uuidPart)
	}
}

// 测试 UUID 唯一性
func TestUUIDUniqueness(t *testing.T) {
	// 生成多个 UUID 并检查唯一性
	count := 1000
	uuids := make(map[string]bool)
	
	for i := 0; i < count; i++ {
		uuid, err := GenerateUUID()
		if err != nil {
			t.Fatalf("生成 UUID 失败: %v", err)
		}
		
		if uuids[uuid] {
			t.Errorf("发现重复的 UUID: %s", uuid)
		}
		
		uuids[uuid] = true
	}
}
