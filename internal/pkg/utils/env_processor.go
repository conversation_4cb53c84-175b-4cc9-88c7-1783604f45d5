package utils

import (
	"fmt"
	"strings"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// ProcessEnvironmentVariablesFromRequest 从WorkerDeployRequest处理环境变量
// 保持数据传递的一致性，所有部署信息都从req参数中获取
// 返回值：
// - envString: 用于Docker命令的环境变量字符串
// - envMap: 包含所有环境变量的map，供前置脚本、后置脚本使用
// - error: 错误信息
func ProcessEnvironmentVariablesFromRequest(req *models.WorkerDeployRequest) (string, map[string]string, error) {
	logger.Info("开始处理环境变量，ServiceID: %s", req.ServiceId)

	// 步骤1: 解析SYS_ENVS标签
	sysEnvs, err := parseSysEnvs(req.Labels)
	if err != nil {
		logger.Error("解析SYS_ENVS失败: %v", err)
		return "", nil, fmt.Errorf("failed to parse SYS_ENVS: %w", err)
	}
	logger.Info("解析到SYS_ENVS变量 %d 个", len(sysEnvs))

	// 步骤2: 解析SYS_ENVS_TPL标签
	sysEnvsTpl, err := parseSysEnvsTpl(req.Labels)
	if err != nil {
		logger.Error("解析SYS_ENVS_TPL失败: %v", err)
		return "", nil, fmt.Errorf("failed to parse SYS_ENVS_TPL: %w", err)
	}
	logger.Info("解析到SYS_ENVS_TPL变量 %d 个", len(sysEnvsTpl))

	// 步骤3: 使用SYS_ENVS的值替换SYS_ENVS_TPL中的挖槽
	processedTplEnvs := replacePlaceholdersFromRequest(sysEnvsTpl, sysEnvs, req)
	logger.Info("处理后的模板变量 %d 个", len(processedTplEnvs))

	// 步骤4: 解析custom_envs
	customEnvs, err := parseCustomEnvs(req.CustomerEnvs)
	if err != nil {
		logger.Error("解析custom_envs失败: %v", err)
		return "", nil, fmt.Errorf("failed to parse custom_envs: %w", err)
	}
	logger.Info("解析到custom_envs变量 %d 个", len(customEnvs))

	// 步骤5: 合并所有环境变量到一个map中
	// 优先级：customEnvs > processedTplEnvs（用户自定义环境变量优先级更高）
	allEnvs := make(map[string]string)

	// 先添加模板环境变量
	for key, value := range processedTplEnvs {
		allEnvs[key] = value
	}

	// 再添加用户自定义环境变量（会覆盖同名的模板变量）
	for key, value := range customEnvs {
		allEnvs[key] = value
	}

	logger.Info("合并后的环境变量总数: %d", len(allEnvs))

	// 步骤6: 拼接环境变量字符串
	envString := buildEnvString(processedTplEnvs, customEnvs)
	logger.Info("生成环境变量字符串，长度: %d", len(envString))

	return envString, allEnvs, nil
}

// ProcessEnvironmentVariables 处理环境变量的主要逻辑（保留原有接口兼容性）
// 1. 从deploy_record_w获取记录
// 2. 获取labels中SYS_ENVS字段，SYS_ENVS_TPL字段，获取custom_envs数据
// 3. 解析SYS_ENVS、SYS_ENVS_TPL，格式为"|||分隔key""第一个:分隔key和value"，使用SYS_ENVS解析的结果，替换SYS_ENVS_TPL解析结果中的挖槽_{xxx}_
// 4. 解析custom_envs，存储的结构为["VARA=1456","VARB=s d fd  24999","PPORT=234"]
// 5. 把上述结果拼接为容器使用的环境变量的字符串，先拼接SYS_ENVS_TPL的结果、再拼接custom_envs的结果
func ProcessEnvironmentVariables(record *models.DeployRecordW) (string, error) {
	logger.Info("开始处理环境变量，ServiceID: %s", record.ServiceID)

	// 步骤1: 解析SYS_ENVS标签
	sysEnvs, err := parseSysEnvs(record.Labels)
	if err != nil {
		logger.Error("解析SYS_ENVS失败: %v", err)
		return "", fmt.Errorf("failed to parse SYS_ENVS: %w", err)
	}
	logger.Info("解析到SYS_ENVS变量 %d 个", len(sysEnvs))

	// 步骤2: 解析SYS_ENVS_TPL标签
	sysEnvsTpl, err := parseSysEnvsTpl(record.Labels)
	if err != nil {
		logger.Error("解析SYS_ENVS_TPL失败: %v", err)
		return "", fmt.Errorf("failed to parse SYS_ENVS_TPL: %w", err)
	}
	logger.Info("解析到SYS_ENVS_TPL变量 %d 个", len(sysEnvsTpl))

	// 步骤3: 使用SYS_ENVS的值替换SYS_ENVS_TPL中的挖槽
	processedTplEnvs := replacePlaceholders(sysEnvsTpl, sysEnvs, record)
	logger.Info("处理后的模板变量 %d 个", len(processedTplEnvs))

	// 步骤4: 解析custom_envs
	customEnvs, err := parseCustomEnvs(record.CustomerEnvs)
	if err != nil {
		logger.Error("解析custom_envs失败: %v", err)
		return "", fmt.Errorf("failed to parse custom_envs: %w", err)
	}
	logger.Info("解析到custom_envs变量 %d 个", len(customEnvs))

	// 步骤5: 拼接环境变量字符串
	envString := buildEnvString(processedTplEnvs, customEnvs)
	logger.Info("生成环境变量字符串，长度: %d", len(envString))

	return envString, nil
}

// parseSysEnvs 解析SYS_ENVS标签
// 格式: SYS_ENVS=KEY1:value1|||KEY2:value2|||KEY3:value3
func parseSysEnvs(labels []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, label := range labels {
		if strings.HasPrefix(label, "SYS_ENVS=") {
			encoded := strings.TrimPrefix(label, "SYS_ENVS=")
			if encoded == "" {
				continue
			}

			// 使用|||分隔不同的环境变量
			parts := strings.Split(encoded, "|||")
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if part == "" {
					continue
				}

				// 使用第一个:分隔key和value
				kv := strings.SplitN(part, ":", 2)
				if len(kv) == 2 {
					key := strings.TrimSpace(kv[0])
					value := strings.TrimSpace(kv[1])
					result[key] = value
					logger.Info("解析SYS_ENVS: %s=%s", key, value)
				} else if len(kv) == 1 {
					// 处理空值情况
					key := strings.TrimSpace(kv[0])
					result[key] = ""
					logger.Info("解析SYS_ENVS: %s=(空值)", key)
				}
			}
			break // 只处理第一个SYS_ENVS标签
		}
	}

	return result, nil
}

// parseSysEnvsTpl 解析SYS_ENVS_TPL标签
// 格式: SYS_ENVS_TPL=KEY1:_{PLACEHOLDER1}_|||KEY2:value2|||KEY3:_{PLACEHOLDER2}_
func parseSysEnvsTpl(labels []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, label := range labels {
		if strings.HasPrefix(label, "SYS_ENVS_TPL=") {
			encoded := strings.TrimPrefix(label, "SYS_ENVS_TPL=")
			if encoded == "" {
				continue
			}

			// 使用|||分隔不同的环境变量
			parts := strings.Split(encoded, "|||")
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if part == "" {
					continue
				}

				// 使用第一个:分隔key和value
				kv := strings.SplitN(part, ":", 2)
				if len(kv) == 2 {
					key := strings.TrimSpace(kv[0])
					value := strings.TrimSpace(kv[1])
					result[key] = value
					logger.Info("解析SYS_ENVS_TPL: %s=%s", key, value)
				} else if len(kv) == 1 {
					// 处理空值情况
					key := strings.TrimSpace(kv[0])
					result[key] = ""
					logger.Info("解析SYS_ENVS_TPL: %s=(空值)", key)
				}
			}
			break // 只处理第一个SYS_ENVS_TPL标签
		}
	}

	return result, nil
}

// replacePlaceholdersFromRequest 使用SYS_ENVS的值替换SYS_ENVS_TPL中的挖槽（从WorkerDeployRequest）
// 挖槽格式: _{PLACEHOLDER}_
func replacePlaceholdersFromRequest(tplEnvs map[string]string, sysEnvs map[string]string, req *models.WorkerDeployRequest) map[string]string {
	result := make(map[string]string)

	// 构建替换映射，包括系统变量和请求中的动态变量
	replacements := make(map[string]string)

	// 添加SYS_ENVS中的变量
	for key, value := range sysEnvs {
		replacements[key] = value
	}

	// 添加动态变量
	domain := req.DomainPrefix + req.DomainSuffix
	domainNoDot := strings.ReplaceAll(domain, ".", "_")
	replacements["DOMAIN"] = domain
	replacements["DOMAIN_NO_DOT"] = domainNoDot
	replacements["SERVICE_ID"] = req.ServiceId

	// 处理每个模板变量
	for key, value := range tplEnvs {
		processedValue := value

		// 替换所有挖槽
		for placeholder, replacement := range replacements {
			placeholderPattern := fmt.Sprintf("_{%s}_", placeholder)
			processedValue = strings.ReplaceAll(processedValue, placeholderPattern, replacement)
		}

		result[key] = processedValue
		logger.Info("处理模板变量: %s=%s -> %s", key, value, processedValue)
	}

	return result
}

// replacePlaceholders 使用SYS_ENVS的值替换SYS_ENVS_TPL中的挖槽
// 挖槽格式: _{PLACEHOLDER}_
func replacePlaceholders(tplEnvs map[string]string, sysEnvs map[string]string, record *models.DeployRecordW) map[string]string {
	result := make(map[string]string)

	// 构建替换映射，包括系统变量和记录中的动态变量
	replacements := make(map[string]string)

	// 添加SYS_ENVS中的变量
	for key, value := range sysEnvs {
		replacements[key] = value
	}

	// 添加动态变量
	domain := record.DomainPrefix + record.DomainSuffix
	domainNoDot := strings.ReplaceAll(domain, ".", "_")
	replacements["DOMAIN"] = domain
	replacements["DOMAIN_NO_DOT"] = domainNoDot
	replacements["SERVICE_ID"] = record.ServiceID

	// 处理每个模板变量
	for key, value := range tplEnvs {
		processedValue := value

		// 替换所有挖槽
		for placeholder, replacement := range replacements {
			placeholderPattern := fmt.Sprintf("_{%s}_", placeholder)
			processedValue = strings.ReplaceAll(processedValue, placeholderPattern, replacement)
		}

		result[key] = processedValue
		logger.Info("处理模板变量: %s=%s -> %s", key, value, processedValue)
	}

	return result
}

// parseCustomEnvs 解析custom_envs
// 格式: ["VARA=1456","VARB=s d fd  24999","PPORT=234"]
func parseCustomEnvs(customerEnvs []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, env := range customerEnvs {
		env = strings.TrimSpace(env)
		if env == "" {
			continue
		}

		// 使用第一个=分隔key和value
		kv := strings.SplitN(env, "=", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			result[key] = value
			logger.Info("解析custom_envs: %s=%s", key, value)
		} else if len(kv) == 1 {
			// 处理没有值的情况
			key := strings.TrimSpace(kv[0])
			result[key] = ""
			logger.Info("解析custom_envs: %s=(空值)", key)
		}
	}

	return result, nil
}

// buildEnvString 构建容器使用的环境变量字符串
// 先拼接SYS_ENVS_TPL的结果，再拼接custom_envs的结果
func buildEnvString(tplEnvs map[string]string, customEnvs map[string]string) string {
	var envArgs []string

	// 先添加模板环境变量
	for key, value := range tplEnvs {
		// 对值进行转义，确保特殊字符不会影响命令解析
		escapedValue := strings.ReplaceAll(value, "\"", "\\\"")
		envArg := fmt.Sprintf("--env %s=\"%s\"", key, escapedValue)
		envArgs = append(envArgs, envArg)
		logger.Info("添加模板环境变量: %s", envArg)
	}

	// 再添加用户自定义环境变量
	for key, value := range customEnvs {
		// 对值进行转义，确保特殊字符不会影响命令解析
		escapedValue := strings.ReplaceAll(value, "\"", "\\\"")
		envArg := fmt.Sprintf("--env %s=\"%s\"", key, escapedValue)
		envArgs = append(envArgs, envArg)
		logger.Info("添加用户环境变量: %s", envArg)
	}

	// 如果有环境变量，在前面添加空格
	if len(envArgs) > 0 {
		return " " + strings.Join(envArgs, " ")
	}
	return ""
}

// ReplaceScriptPlaceholders 使用环境变量map替换脚本内容中的挖槽
// 支持的挖槽格式：
// - ${VAR_NAME} - 标准shell变量格式
// - {{VAR_NAME}} - 模板变量格式
// - _{VAR_NAME}_ - 自定义挖槽格式
func ReplaceScriptPlaceholders(scriptContent string, envMap map[string]string) string {
	result := scriptContent

	// 替换所有环境变量
	for key, value := range envMap {
		// 替换 ${VAR_NAME} 格式
		shellPattern := fmt.Sprintf("${%s}", key)
		result = strings.ReplaceAll(result, shellPattern, value)

		// 替换 {{VAR_NAME}} 格式
		templatePattern := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, templatePattern, value)

		// 替换 _{VAR_NAME}_ 格式
		customPattern := fmt.Sprintf("_{%s}_", key)
		result = strings.ReplaceAll(result, customPattern, value)

		logger.Info("脚本挖槽替换: %s -> %s", key, value)
	}

	return result
}

// GetEnvValue 从环境变量字符串中获取指定键的值（用于测试和调试）
func GetEnvValue(envString, key string) string {
	// 简单的解析逻辑，用于调试
	parts := strings.Split(envString, " ")
	for i, part := range parts {
		if part == "--env" && i+1 < len(parts) {
			envPair := parts[i+1]
			if strings.HasPrefix(envPair, key+"=") {
				value := strings.TrimPrefix(envPair, key+"=")
				// 移除引号
				value = strings.Trim(value, "\"")
				return value
			}
		}
	}
	return ""
}
