package utils

import (
	"regexp"
	"testing"
	"time"
)

// 测试 ULID 格式的正则表达式 (26个字符的 Base32 编码)
var ulidRegex = regexp.MustCompile(`^[0-9A-HJKMNP-TV-Z]{26}$`)

// 测试 ULID 生成
func TestNewULID(t *testing.T) {
	ulid, err := NewULID()
	if err != nil {
		t.Fatalf("生成 ULID 失败: %v", err)
	}
	
	ulidStr := ulid.String()
	if !ulidRegex.MatchString(ulidStr) {
		t.<PERSON>rrorf("生成的 ULID 格式不正确: %s", ulidStr)
	}
	
	// 测试 Bytes 方法
	bytes := ulid.Bytes()
	if len(bytes) != 16 {
		t.<PERSON><PERSON><PERSON>("生成的 ULID 字节长度不正确: %d", len(bytes))
	}
}

// 测试 ULID 时间提取
func TestULIDTime(t *testing.T) {
	now := time.Now()
	ulid, err := NewULIDWithTime(now)
	if err != nil {
		t.Fatalf("生成 ULID 失败: %v", err)
	}
	
	// 提取时间
	extractedTime := ulid.Time()
	
	// 由于毫秒精度，允许有 1 秒的误差
	diff := extractedTime.Sub(now)
	if diff < -time.Second || diff > time.Second {
		t.Errorf("提取的时间与原始时间相差太大: %v", diff)
	}
}

// 测试 ULID 字符串生成
func TestNewULIDString(t *testing.T) {
	ulidStr, err := NewULIDString()
	if err != nil {
		t.Fatalf("生成 ULID 字符串失败: %v", err)
	}
	
	if !ulidRegex.MatchString(ulidStr) {
		t.Errorf("生成的 ULID 字符串格式不正确: %s", ulidStr)
	}
}

// 测试 ULID 解析
func TestParseULID(t *testing.T) {
	// 生成一个 ULID 字符串
	original, err := NewULIDString()
	if err != nil {
		t.Fatalf("生成 ULID 字符串失败: %v", err)
	}
	
	// 解析 ULID
	parsed, err := ParseULID(original)
	if err != nil {
		t.Fatalf("解析 ULID 失败: %v", err)
	}
	
	// 转回字符串并比较
	if parsed.String() != original {
		t.Errorf("解析后的 ULID 与原始值不匹配: %s != %s", parsed.String(), original)
	}
}

// 测试 GenerateULID
func TestGenerateULID(t *testing.T) {
	ulidStr, err := GenerateULID()
	if err != nil {
		t.Fatalf("生成 ULID 失败: %v", err)
	}
	
	if !ulidRegex.MatchString(ulidStr) {
		t.Errorf("生成的 ULID 格式不正确: %s", ulidStr)
	}
}

// 测试单调递增的 ULID
func TestGenerateMonotonicULID(t *testing.T) {
	// 生成两个 ULID
	ulid1, err := GenerateMonotonicULID()
	if err != nil {
		t.Fatalf("生成单调 ULID 失败: %v", err)
	}
	
	ulid2, err := GenerateMonotonicULID()
	if err != nil {
		t.Fatalf("生成单调 ULID 失败: %v", err)
	}
	
	// 确保第二个大于第一个
	if ulid2 <= ulid1 {
		t.Errorf("单调 ULID 没有递增: %s <= %s", ulid2, ulid1)
	}
}

// 测试带前缀的 ULID
func TestGeneratePrefixedULID(t *testing.T) {
	prefix := "test-"
	prefixedULID, err := GeneratePrefixedULID(prefix)
	if err != nil {
		t.Fatalf("生成带前缀的 ULID 失败: %v", err)
	}
	
	if len(prefixedULID) <= len(prefix) {
		t.Errorf("生成的带前缀的 ULID 长度不正确: %s", prefixedULID)
	}
	
	if prefixedULID[:len(prefix)] != prefix {
		t.Errorf("生成的 ULID 前缀不正确，期望 %s，实际 %s", prefix, prefixedULID[:len(prefix)])
	}
	
	ulidPart := prefixedULID[len(prefix):]
	if !ulidRegex.MatchString(ulidPart) {
		t.Errorf("带前缀的 ULID 中的 ULID 部分格式不正确: %s", ulidPart)
	}
}

// 测试 ULID 唯一性
func TestULIDUniqueness(t *testing.T) {
	// 生成多个 ULID 并检查唯一性
	count := 1000
	ulids := make(map[string]bool)
	
	for i := 0; i < count; i++ {
		ulid, err := GenerateULID()
		if err != nil {
			t.Fatalf("生成 ULID 失败: %v", err)
		}
		
		if ulids[ulid] {
			t.Errorf("发现重复的 ULID: %s", ulid)
		}
		
		ulids[ulid] = true
	}
}

// 测试 ULID 排序特性
func TestULIDSorting(t *testing.T) {
	// 生成两个间隔一段时间的 ULID
	ulid1, err := NewULIDWithTime(time.Now())
	if err != nil {
		t.Fatalf("生成 ULID 失败: %v", err)
	}
	
	// 等待一秒
	time.Sleep(time.Second)
	
	ulid2, err := NewULIDWithTime(time.Now())
	if err != nil {
		t.Fatalf("生成 ULID 失败: %v", err)
	}
	
	// 确保第二个大于第一个（字典序）
	if ulid2.String() <= ulid1.String() {
		t.Errorf("ULID 排序不正确: %s <= %s", ulid2.String(), ulid1.String())
	}
}
