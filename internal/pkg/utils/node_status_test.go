package utils

import (
	"os"
	"testing"
)

func TestGetNodeCPUUsage(t *testing.T) {
	// 设置环境变量
	os.Setenv("PROMETHEUS_USER", "admin-bao")
	// 实际密码应该从环境变量获取，这里仅用于测试
	os.Setenv("PROMETHEUS_PASSWORD", "e&;c]{;A<JO[Yv'dpIrlSyrJbs4Kc2bY")
	
	// 测试获取 CPU 使用率
	nodeInstance := "node73.yitaiyitai.com:1080"
	cpuUsage, err := GetNodeCPUUsage(nodeInstance)
	
	if err != nil {
		t.<PERSON><PERSON><PERSON>("获取 CPU 使用率失败: %v", err)
	}
	
	t.Logf("节点 %s 的 CPU 使用率: %.2f%%", nodeInstance, cpuUsage)
	
	// CPU 使用率应该在 0-100% 之间
	if cpuUsage < 0 || cpuUsage > 100 {
		t.<PERSON><PERSON><PERSON>("CPU 使用率超出范围: %.2f%%", cpuUsage)
	}
}

func TestGetNodeMemoryUsage(t *testing.T) {
	// 设置环境变量
	os.Setenv("PROMETHEUS_USER", "admin-bao")
	// 实际密码应该从环境变量获取，这里仅用于测试
	os.Setenv("PROMETHEUS_PASSWORD", "e&;c]{;A<JO[Yv'dpIrlSyrJbs4Kc2bY")
	
	// 测试获取内存使用率
	nodeInstance := "node73.yitaiyitai.com:1080"
	memUsage, err := GetNodeMemoryUsage(nodeInstance)
	
	if err != nil {
		t.Errorf("获取内存使用率失败: %v", err)
	}
	
	t.Logf("节点 %s 的内存使用率: %.2f%%", nodeInstance, memUsage)
	
	// 内存使用率应该在 0-100% 之间
	if memUsage < 0 || memUsage > 100 {
		t.Errorf("内存使用率超出范围: %.2f%%", memUsage)
	}
}
