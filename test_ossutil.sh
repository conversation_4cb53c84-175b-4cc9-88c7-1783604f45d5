#!/bin/bash

# 测试 OSSUtil 工具的脚本

echo "=== OSSUtil 工具测试 ==="

# 检查 OSSUtil 工具是否存在
OSSUTIL_PATH="./dbdata/ossutil64-linux"

if [ ! -f "$OSSUTIL_PATH" ]; then
    echo "❌ OSSUtil 工具不存在: $OSSUTIL_PATH"
    echo "请确保已下载并放置 OSSUtil 工具到 dbdata 目录"
    exit 1
fi

# 检查工具是否可执行
if [ ! -x "$OSSUTIL_PATH" ]; then
    echo "🔧 设置 OSSUtil 工具为可执行..."
    chmod +x "$OSSUTIL_PATH"
fi

# 显示工具版本
echo "📋 OSSUtil 工具信息:"
$OSSUTIL_PATH --version

echo ""
echo "📋 OSSUtil du 命令帮助:"
$OSSUTIL_PATH help du

echo ""
echo "✅ OSSUtil 工具测试完成"
echo ""
echo "💡 使用示例:"
echo "   $OSSUTIL_PATH du oss://your-bucket/path \\"
echo "     --endpoint oss-cn-hangzhou.aliyuncs.com \\"
echo "     --access-key-id YOUR_ACCESS_KEY_ID \\"
echo "     --access-key-secret YOUR_ACCESS_KEY_SECRET"
