openapi: 3.0.0
info:
  title: Zero Ops Service System API
  description: API documentation for the Zero Ops Service System
  version: 1.0.0
servers:
  - url: /api/v1
    description: Service API base URL
paths:
  /ping:
    get:
      summary: Health check endpoint
      description: Returns a simple response to verify the service is running
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: pong

  # Service Management
  /service/create:
    post:
      summary: Create a new service
      description: Creates a new service deployment record
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateServiceRequest'
      responses:
        '200':
          description: Service created or updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
              examples:
                createSuccess:
                  summary: Service created successfully
                  value:
                    code: 200
                    data:
                      url: "my-service"
                      serviceId: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                      updated: false
                    msg: "Service: 创建服务成功"
                updateSuccess:
                  summary: Service updated successfully
                  value:
                    code: 200
                    data:
                      url: "my-service"
                      serviceId: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                      updated: true
                    msg: "Service: 更新服务成功"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - $ref: '#/components/schemas/ServiceResponse'
              examples:
                databaseError:
                  summary: Database connection error
                  value:
                    error: "数据库连接不可用"
                createRecordError:
                  summary: Failed to create deploy record
                  value:
                    code: 500
                    msg: "无法创建部署记录: database error"
                    data: null

  /service/list:
    get:
      summary: Get service list
      description: Returns a list of services with pagination and filtering
      parameters:
        - name: page
          in: query
          description: Page number (starting from 1)
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: service_ids
          in: query
          description: Comma-separated list of service IDs to filter
          schema:
            type: string
      responses:
        '200':
          description: List of services
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
              example:
                code: 200
                data:
                  total: 25
                  page: 1
                  page_size: 10
                  records:
                    - id: 1
                      service_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
                      name: "my-service"
                      domain_prefix: "my-service"
                      domain_suffix: ".example.com"
                      image_name: "nginx"
                      image_url: "nginx:latest"
                      service_type: "web"
                      status: "RUNNING"
                      worker_id: "worker-001"
                      node_ip: "*************"
                      created_at: "2024-01-01T10:00:00Z"
                      updated_at: "2024-01-01T10:00:00Z"
                msg: "Service: 获取部署记录列表成功"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/{service_id}:
    delete:
      summary: Delete a service
      description: Deletes a service by its ID
      security:
        - basicAuth: []
      parameters:
        - name: service_id
          in: path
          required: true
          description: ID of the service to delete
          schema:
            type: string
      responses:
        '200':
          description: Service deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/{service_id}/restart:
    put:
      summary: Restart a service
      description: Restarts a service by its ID
      security:
        - basicAuth: []
      parameters:
        - name: service_id
          in: path
          required: true
          description: ID of the service to restart
          schema:
            type: string
      responses:
        '200':
          description: Service restarted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/update:
    put:
      summary: Update a service
      description: Updates a service by updating only the image name and image URL. Other parameters remain unchanged.
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateServiceRequest'
      responses:
        '200':
          description: Service updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
              example:
                code: 200
                data:
                  deploymentId: "container_12345"
                  containerInfo:
                    containerId: "container_12345"
                    image: "nginx:1.21"
                    status: "running"
                    name: "my-service"
                    serverIP: "*************"
                  resources:
                    cpu: 1.0
                    memory: 1024
                msg: "服务已更新"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "无法获取镜像 URL: image not found"
        '404':
          description: Service not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
              example:
                code: 404
                msg: "无法找到部署记录: record not found"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Image Type Management
  /service/image/create:
    post:
      summary: Create one or multiple image types
      description: Creates one or multiple image types in a single request
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/CreateImageTypeRequest'
                - type: array
                  items:
                    $ref: '#/components/schemas/CreateImageTypeRequest'
      responses:
        '200':
          description: Image type(s) created successfully
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ImageTypeResponse'
                  - $ref: '#/components/schemas/BatchImageTypeResponse'
              examples:
                singleSuccess:
                  summary: Single image type created successfully
                  value:
                    code: 200
                    data:
                      id: 1
                      image_name: "nginx"
                      image_url: "nginx:latest"
                      ports: ["80", "443"]
                      labels: ["web", "proxy"]
                    msg: "Service: 创建镜像类型成功"
                batchSuccess:
                  summary: Multiple image types created successfully
                  value:
                    code: 200
                    data:
                      success_count: 2
                      failed_count: 0
                      results:
                        - id: 1
                          image_name: "nginx"
                          image_url: "nginx:latest"
                          ports: ["80", "443"]
                          labels: ["web", "proxy"]
                        - id: 2
                          image_name: "mysql"
                          image_url: "mysql:8.0"
                          ports: ["3306"]
                          labels: ["database"]
                      errors: []
                    msg: "Service: 批量创建镜像类型成功"
        '207':
          description: Partial success (some image types created, some failed)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchImageTypeResponse'
              example:
                code: 207
                data:
                  success_count: 1
                  failed_count: 1
                  results:
                    - id: 1
                      image_name: "nginx"
                      image_url: "nginx:latest"
                      ports: ["80", "443"]
                      labels: ["web", "proxy"]
                  errors: ["第 2 个请求创建失败: 镜像名称已存在"]
                msg: "Service: 部分镜像类型创建成功"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/image/list:
    get:
      summary: Get image type list
      description: Returns a list of all image types
      responses:
        '200':
          description: List of image types
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageTypeListResponse'
              example:
                code: 200
                data:
                  image_list:
                    - id: 1
                      image_name: "nginx"
                      image_url: "nginx:latest"
                      ports: ["80", "443"]
                      labels: ["web", "proxy"]
                    - id: 2
                      image_name: "mysql"
                      image_url: "mysql:8.0"
                      ports: ["3306"]
                      labels: ["database"]
                msg: "Service: 获取镜像类型列表成功"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/image/{id}:
    delete:
      summary: Delete an image type
      description: Deletes an image type by its ID
      security:
        - basicAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the image type to delete
          schema:
            type: string
      responses:
        '200':
          description: Image type deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageTypeResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Update an image type
      description: Updates an image type by its ID
      security:
        - basicAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the image type to update
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateImageTypeRequest'
      responses:
        '200':
          description: Image type updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageTypeResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

# Worker Management
  /service/worker/list:
    get:
      summary: Get worker list
      description: Returns a list of all registered worker nodes with optional filtering
      parameters:
        - name: server_type
          in: query
          description: Filter by server type
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
        - name: labels
          in: query
          description: Filter by labels (comma-separated)
          schema:
            type: string
      responses:
        '200':
          description: List of workers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkerListResponse'
              example:
                code: 200
                workers:
                  - id: "worker-001"
                    name: "Worker Node 1"
                    server_type: "production"
                    status: "AVAILABLE"
                    host: "http://*************:8080"
                    host_ip: "*************"
                    domain_suffix: ".example.com"
                    labels: ["production", "web"]
                    nodes:
                      - ip: "*************"
                        instance: "node-1"
                        memory: 8192
                        memory_use_rate: 45.2
                        cpu: 4.0
                        cpu_use_rate: 23.1
                        disk: 102400
                        disk_use_rate: 67.8
                msg: "Service: 获取Worker列表成功"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/worker/register:
    post:
      summary: Register a new worker
      description: Registers a new worker node with the service
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterWorkerRequest'
      responses:
        '200':
          description: Worker registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /service/worker/{worker_id}:
    delete:
      summary: Remove a worker
      description: Removes a worker node from the service
      security:
        - basicAuth: []
      parameters:
        - name: worker_id
          in: path
          required: true
          description: ID of the worker to remove
          schema:
            type: string
      responses:
        '200':
          description: Worker removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Update a worker
      description: Updates a worker node
      security:
        - basicAuth: []
      parameters:
        - name: worker_id
          in: path
          required: true
          description: ID of the worker to update
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWorkerRequest'
      responses:
        '200':
          description: Worker updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication using username 'devops' and password '3-546-_iuhh5498'
  schemas:
    # Error Response
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message

    # Service Models
    CreateServiceRequest:
      type: object
      properties:
        serviceInfo:
          $ref: '#/components/schemas/ServiceInfo'
      required:
        - serviceInfo

    UpdateServiceRequest:
      type: object
      properties:
        serviceInfo:
          $ref: '#/components/schemas/UpdateServiceInfo'
      required:
        - serviceInfo

    UpdateServiceInfo:
      type: object
      properties:
        service_id:
          type: string
          description: Unique identifier for the service to update
        image_name:
          type: string
          description: New image name to use (only field that will be updated)
      required:
        - service_id
        - image_name
      example:
        service_id: "svc-01H1VTZR8JTCBM9VS2Q9XN9WGZ"
        image_name: "nginx:1.21"

    ServiceInfo:
      type: object
      properties:
        service_id:
          type: string
          description: Unique identifier for the service
        domain_prefix:
          type: string
          description: Domain prefix for the service
        domain_suffix:
          type: string
          description: Domain suffix for the service
        image_name:
          type: string
          description: Name of the image to use
        expiration:
          type: string
          description: Expiration date (YYYY-MM-DDTHH:mm:ss)
        service_type:
          type: string
          description: Type of service
        labels:
          type: array
          items:
            type: string
          description: Labels for the service
        customer_envs:
          type: array
          items:
            type: string
          description: Custom environment variables
        api_replica:
          type: integer
          description: Number of API replicas
        api_cpu:
          type: number
          format: float
          description: CPU cores for API
        api_memory:
          type: integer
          description: Memory for API (MB)
        auto_replica:
          type: integer
          description: Number of Auto replicas
        auto_cpu:
          type: number
          format: float
          description: CPU cores for Auto
        auto_memory:
          type: integer
          description: Memory for Auto (MB)
      required:
        - service_id
        - domain_prefix
        - image_name
        - service_type

    ServiceResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          properties:
            url:
              type: string
              description: Domain prefix of the service
            serviceId:
              type: string
              description: Unique identifier for the service
            updated:
              type: boolean
              description: Whether this was an update to an existing service
          description: Response data
        msg:
          type: string
          description: Response message

    DeployRecord:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Auto-incremented primary key
        service_id:
          type: string
          description: Service ID
        name:
          type: string
          description: Service name
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        service_type:
          type: string
          description: Service type
        domain_prefix:
          type: string
          description: Domain prefix
        domain_suffix:
          type: string
          description: Domain suffix
        node_ip:
          type: string
          description: Deployment server IP
        host_ip:
          type: string
          description: Host node IP address
        worker_id:
          type: string
          description: Worker ID
        expiration:
          type: string
          description: Expiration date (YYYY-MM-DDTHH:mm:ss)
        status:
          type: string
          description: Service status
          enum: [QUEUEING, PROCESSING, RUNNING, TERMINATION, STOPPED, FAILED, UNKNOWN]
        labels:
          type: array
          items:
            type: string
          description: Service labels
        customer_envs:
          type: array
          items:
            type: string
          description: Custom environment variables
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports (e.g. ["8080", "9000"])
        ports_mapping:
          type: array
          items:
            type: string
          description: Port mappings (e.g. ["30001:8080", "30002:9000"])
        remark:
          type: string
          description: Remarks
        created_at:
          type: string
          description: Creation time
        updated_at:
          type: string
          description: Update time
        api_replica:
          type: integer
          description: Number of API replicas
        api_cpu:
          type: number
          format: float
          description: CPU cores for API
        api_memory:
          type: integer
          description: Memory for API (MB)
        auto_replica:
          type: integer
          description: Number of Auto replicas
        auto_cpu:
          type: number
          format: float
          description: CPU cores for Auto
        auto_memory:
          type: integer
          description: Memory for Auto (MB)

    DeployRecordListResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          properties:
            total:
              type: integer
              description: Total number of records
            page:
              type: integer
              description: Current page number
            page_size:
              type: integer
              description: Number of items per page
            records:
              type: array
              items:
                $ref: '#/components/schemas/DeployRecordItem'
              description: List of deployment records
        msg:
          type: string
          description: Response message

    DeployRecordItem:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Auto-incremented primary key
        service_id:
          type: string
          description: Service ID
        name:
          type: string
          description: Service name
        domain_prefix:
          type: string
          description: Domain prefix
        domain_suffix:
          type: string
          description: Domain suffix
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        service_type:
          type: string
          description: Service type
        expiration:
          type: string
          description: Expiration date (YYYY-MM-DDTHH:mm:ss)
        status:
          type: string
          description: Service status
          enum: [QUEUEING, PROCESSING, RUNNING, TERMINATION, STOPPED, FAILED, UNKNOWN]
        labels:
          type: array
          items:
            type: string
          description: Service labels
        customer_envs:
          type: array
          items:
            type: string
          description: Custom environment variables
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports (e.g. ["8080", "9000"])
        ports_mapping:
          type: array
          items:
            type: string
          description: Port mappings (e.g. ["30001:8080", "30002:9000"])
        worker_id:
          type: string
          description: Worker ID
        node_ip:
          type: string
          description: Deployment server IP
        host_ip:
          type: string
          description: Host node IP address
        api_replica:
          type: integer
          description: Number of API replicas
        api_cpu:
          type: number
          format: float
          description: CPU cores for API
        api_memory:
          type: integer
          description: Memory for API (MB)
        auto_replica:
          type: integer
          description: Number of Auto replicas
        auto_cpu:
          type: number
          format: float
          description: CPU cores for Auto
        auto_memory:
          type: integer
          description: Memory for Auto (MB)
        remark:
          type: string
          description: Remarks
        created_at:
          type: string
          description: Creation time
        updated_at:
          type: string
          description: Update time

    # Image Type Models
    CreateImageTypeRequest:
      type: object
      properties:
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports (e.g. ["8080", "9000"])
        labels:
          type: array
          items:
            type: string
          description: Image labels
      required:
        - image_name
        - image_url

    UpdateImageTypeRequest:
      type: object
      properties:
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports (e.g. ["8080", "9000"])
        labels:
          type: array
          items:
            type: string
          description: Image labels

    ImageType:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Auto-incremented primary key
        image_name:
          type: string
          description: Image name
        image_url:
          type: string
          description: Image URL
        ports:
          type: array
          items:
            type: string
          description: Container exposed ports (e.g. ["8080", "9000"])
        labels:
          type: array
          items:
            type: string
          description: Image labels
        created_at:
          type: string
          description: Creation time
        updated_at:
          type: string
          description: Update time

    ImageTypeResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          description: Response data
        msg:
          type: string
          description: Response message

    ImageTypeListResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          properties:
            image_list:
              type: array
              items:
                $ref: '#/components/schemas/ImageType'
              description: List of image types
        msg:
          type: string
          description: Response message

    BatchImageTypeResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        data:
          type: object
          properties:
            success_count:
              type: integer
              description: Number of successfully created image types
            failed_count:
              type: integer
              description: Number of failed image type creations
            results:
              type: array
              items:
                $ref: '#/components/schemas/ImageType'
              description: List of successfully created image types
            errors:
              type: array
              items:
                type: string
              description: List of error messages for failed creations
        msg:
          type: string
          description: Response message

    # Worker Models
    WorkerInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Auto-incremented primary key
        workerId:
          type: string
          description: Worker ID (UUID)
        name:
          type: string
          description: Node name
        server_type:
          type: string
          description: Server type
        labels:
          type: array
          items:
            type: string
          description: Node labels
        host:
          type: string
          description: Node API address
        host_ip:
          type: string
          description: Host node IP address
        domain_suffix:
          type: string
          description: Base domain, e.g. ".bpmax.cn"
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/Node'
          description: Node list
        status:
          type: string
          description: Status
        createdAt:
          type: string
          description: Creation time
        updatedAt:
          type: string
          description: Update time

    Node:
      type: object
      properties:
        ip:
          type: string
          description: Node IP address
        instance:
          type: string
          description: Instance name
        memory:
          type: integer
          format: int64
          description: Memory size (MB)
        memory_use_rate:
          type: number
          format: float
          description: Memory usage rate
        cpu:
          type: number
          format: float
          description: CPU cores
        cpu_use_rate:
          type: number
          format: float
          description: CPU usage rate
        disk:
          type: integer
          format: int64
          description: Disk size (MB)
        disk_use_rate:
          type: number
          format: float
          description: Disk usage rate

    RegisterWorkerRequest:
      type: object
      properties:
        name:
          type: string
          description: Node name
        server_type:
          type: string
          description: Server type
        labels:
          type: array
          items:
            type: string
          description: Node labels
        host:
          type: string
          description: Node host address
        host_ip:
          type: string
          description: Host node IP address
        domain_suffix:
          type: string
          description: Base domain
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/Node'
          description: Node list
        status:
          type: string
          description: Status
      required:
        - name
        - server_type
        - host
        - host_ip
        - domain_suffix
        - nodes

    UpdateWorkerRequest:
      type: object
      properties:
        workerId:
          type: string
          description: Worker ID
        name:
          type: string
          description: Node name
        server_type:
          type: string
          description: Server type
        labels:
          type: array
          items:
            type: string
          description: Node labels
        host:
          type: string
          description: Node host address
        host_ip:
          type: string
          description: Host node IP address
        domain_suffix:
          type: string
          description: Base domain
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/Node'
          description: Node list
        status:
          type: string
          description: Status
      required:
        - workerId

    WorkerListResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
        workers:
          type: array
          items:
            $ref: '#/components/schemas/WorkerInfo'
          description: Worker list
        msg:
          type: string
          description: Response message

