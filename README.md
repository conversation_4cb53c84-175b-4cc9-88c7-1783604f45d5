# Zero Ops Service System

Zero Ops Service System是一个用于管理和部署服务的系统，具有两层架构：

1. **Service层**：管理节点，用于用户交互和服务管理
2. **Worker层**：工作节点，运行在各个服务器上，负责实际的服务部署和管理


## 环境变量

系统支持以下环境变量：

- `NODE_TYPE`: 节点类型，可选值为`service`或`worker`，默认为`worker`
- `PORT`: 服务启动的端口，默认为`8080`

## 启动服务

### 启动Service节点

```bash
# 在默认端口(8080)启动
NODE_TYPE=service ./zero-ops-service-system
NODE_TYPE=worker ./zero-ops-service-system

# 在指定端口启动
NODE_TYPE=service PORT=9090 ./zero-ops-service-system
NODE_TYPE=worker PORT=9091 ./zero-ops-service-system
```

### 启动Worker节点

```bash
# 在默认端口(8080)启动
NODE_TYPE=worker ./zero-ops-service-system

# 在指定端口启动
NODE_TYPE=worker PORT=8081 ./zero-ops-service-system
```




## todo:

- 4小时nginx无日志，关闭对应服务容器 
    - 有一点问题，nginx没有域名，所以需要检测容器内的nginx日志
- 到达Expiration，关闭对应服务容器
- 创建用户数据及必要数据
- 升级用户的数据迁移
- 有几种类型的环境就要起几种队列


